/******/ (() => { // webpackBootstrap
/******/ 	"use strict";


self.fallback = async request => {
  // https://developer.mozilla.org/en-US/docs/Web/API/RequestDestination
  switch (request.destination) {
    case 'document':
      if (true) return caches.match("/offline", {
        ignoreSearch: true
      });
    case 'image':
      if (true) return caches.match("/static/images/offline.png", {
        ignoreSearch: true
      });
    case 'audio':
      if (true) return caches.match("/static/audio/offline.mp3", {
        ignoreSearch: true
      });
    case 'video':
      if (true) return caches.match("/static/video/offline.mp4", {
        ignoreSearch: true
      });
    case 'font':
      if (true) return caches.match("/static/fonts/offline.woff2", {
        ignoreSearch: true
      });
    case '':
      if (false) {}
    default:
      return Response.error();
  }
};
/******/ })()
;