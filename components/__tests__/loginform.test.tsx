import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import LoginForm from '../LoginForm';

test('renders LoginForm', () => {
    render(<LoginForm />);
    const linkElement = screen.getByText(/login/i);
    expect(linkElement).toBeInTheDocument();
});

test('allows user to input username', () => {
    render(<LoginForm />);
    const inputElement = screen.getByLabelText(/username/i);
    fireEvent.change(inputElement, { target: { value: 'testuser' } });
    expect(inputElement.value).toBe('testuser');
});

test('allows user to input password', () => {
    render(<LoginForm />);
    const inputElement = screen.getByLabelText(/password/i);
    fireEvent.change(inputElement, { target: { value: 'password123' } });
    expect(inputElement.value).toBe('password123');
});
test('renders email input', () => {
  render(<LoginForm />);
  const emailInput = screen.getByLabelText(/email/i);
  expect(emailInput).toBeInTheDocument();
});

test('renders password input', () => {
  render(<LoginForm />);
  const passwordInput = screen.getByLabelText(/password/i);
  expect(passwordInput).toBeInTheDocument();
});

test('allows user to input email', () => {
  render(<LoginForm />);
  const emailInput = screen.getByLabelText(/email/i);
  fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
  expect(emailInput.value).toBe('<EMAIL>');
});

test('allows user to input password', () => {
  render(<LoginForm />);
  const passwordInput = screen.getByLabelText(/password/i);
  fireEvent.change(passwordInput, { target: { value: 'password123' } });
  expect(passwordInput.value).toBe('password123');
});

test('shows error message when email is not provided', async () => {
  render(<LoginForm />);
  const submitButton = screen.getByText(/sign in/i);
  fireEvent.click(submitButton);
  const errorMessage = await screen.findByText(/email is required/i);
  expect(errorMessage).toBeInTheDocument();
});

test('shows error message when password is not provided', async () => {
  render(<LoginForm />);
  const submitButton = screen.getByText(/sign in/i);
  fireEvent.click(submitButton);
  const errorMessage = await screen.findByText(/password is required/i);
  expect(errorMessage).toBeInTheDocument();
});

test('calls handleGoogleSignIn when Google button is clicked', () => {
  render(<LoginForm />);
  const googleButton = screen.getByText(/google/i);
  fireEvent.click(googleButton);
  expect(signIn).toHaveBeenCalledWith('google', expect.any(Object));
});

test('calls handleFacebookSignIn when Facebook button is clicked', () => {
  render(<LoginForm />);
  const facebookButton = screen.getByText(/facebook/i);
  fireEvent.click(facebookButton);
  expect(handleFacebookSignIn).toHaveBeenCalled();
});