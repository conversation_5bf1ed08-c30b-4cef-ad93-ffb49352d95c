/* Third-party CSS first so Tailwind utilities win in cascade */
@import "react-big-calendar/lib/css/react-big-calendar.css" layer(base);
@import "antd/dist/reset.css" layer(base);
/* Tailwind CSS v4 import */
@import "tailwindcss";

@layer utilities {
  /* .rbc-current .rbc-button-link {
  padding: 5px ;
  background-color: #2c3e50 ;
  color: white ;
  border-radius: 100% ;
} */

  .rbc-date-cell button {
    padding: 5px !important;
    width: 30px !important;
    height: 30px !important;
  }
  .connector {
    transform: translateY(-2rem) !important;
  }

  .rbc-now button {
    background-color: transparent !important;
    color: black !important;
  }

  .my-masonry-grid {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-left: -30px; /* gutter size offset */
    width: auto;
  }

  .rbc-toolbar {
    display: none !important;
  }

  /* Custom styling for react-big-calendar events */
  .rbc-event {
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    border-radius: 0 !important;
  }

  .rbc-event-content {
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Improve month view layout */
  .rbc-month-view .rbc-event {
    display: none !important; /* Hide default events since we render custom ones */
  }

  /* Ensure proper cell height for custom events */
  .rbc-month-view .rbc-date-cell {
    min-height: 100px !important;
    position: relative !important;
    overflow: visible !important;
  }

  /* Ensure calendar cells don't clip the plus button */
  .rbc-month-view .rbc-date-cell {
    position: relative !important;
    overflow: visible !important;
  }

  .pb-btn {
    color: white !important;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
  }

  /* Custom thin scrollbar styles for modal */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
    border: none;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
  }

  .scrollbar-thumb-gray-300 {
    scrollbar-color: rgba(209, 213, 219, 0.8) transparent;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: rgba(209, 213, 219, 0.8);
  }

  .scrollbar-thumb-gray-600 {
    scrollbar-color: rgba(75, 85, 99, 0.8) transparent;
  }

  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.8);
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background: transparent;
  }

  .my-masonry-grid_column {
    padding-left: 30px; /* gutter size */
    background-clip: padding-box;
  }

  .my-masonry-grid_column > div {
    background: grey;
    margin-bottom: 30px;
  }

  .react-grid-item {
    transition: all 200ms ease;
    min-height: 10vh !important;
    min-width: 20vw !important;
    transition-property: left, top;
  }

  .react-grid-item.cssTransforms {
    transition-property: transform;
  }

  .react-grid-item.resizing {
    z-index: 1;

    will-change: width, height;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Dashboard Grid Animations */
  @keyframes widget-enter {
    0% {
      opacity: 0;
      transform: scale(0.8) translateY(20px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes widget-exit {
    0% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
    100% {
      opacity: 0;
      transform: scale(0.8) translateY(-20px);
    }
  }

  .widget-enter {
    animation: widget-enter 0.3s ease-out;
  }

  .widget-exit {
    animation: widget-exit 0.3s ease-in;
  }

  /* Grid Layout Improvements */
  .dashboard-grid {
    display: grid;
    gap: 1rem;
    transition: all 0.3s ease-in-out;
    padding: 0.5rem;
    min-height: 100%;
    width: 100%;
  }
  .dashboard-shadow {
    box-shadow: 0px 4px 4px 0px #00000040;
    border: 1.5px solid #cccccc;
  }

  .dashboard-grid-item {
    transition: all 0.3s ease-in-out;
    will-change: transform, opacity;
    box-shadow: 0px 4px 4px 0px #00000040;
    min-height: 200px;
  }

  .dashboard-grid-item:hover {
    transform: translateY(-2px);
    box-shadow: 0px 4px 4px 0px #00000040;
  }

  /* Responsive grid adjustments */
  @media (max-width: 768px) {
    .dashboard-grid {
      gap: 0.75rem;
      padding: 0.25rem;
    }

    .dashboard-grid-item {
      min-height: 180px;
    }
  }

  /* Toast Animation Keyframes */
  @keyframes toast-enter {
    0% {
      transform: translateX(100%) scale(0.9);
      opacity: 0;
    }
    100% {
      transform: translateX(0) scale(1);
      opacity: 1;
    }
  }

  @keyframes toast-leave {
    0% {
      transform: translateX(0) scale(1);
      opacity: 1;
    }
    100% {
      transform: translateX(100%) scale(0.9);
      opacity: 0;
    }
  }

  @keyframes toast-progress {
    0% {
      width: 100%;
    }
    100% {
      width: 0%;
    }
  }

  /* Toast Animation Classes */
  .animate-enter {
    animation: toast-enter 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-leave {
    animation: toast-leave 0.2s cubic-bezier(0.4, 0, 1, 1) forwards;
  }

  .toast-progress {
    animation: toast-progress linear forwards;
  }

  /* Mobile toast responsiveness */
  @media (max-width: 640px) {
    .toast-enter {
      animation: toast-enter-mobile 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    }

    .toast-leave {
      animation: toast-leave-mobile 0.2s cubic-bezier(0.4, 0, 1, 1) forwards;
    }
  }

  @keyframes toast-enter-mobile {
    0% {
      transform: translateX(100%) scale(0.95);
      opacity: 0;
    }
    100% {
      transform: translateX(0) scale(1);
      opacity: 1;
    }
  }

  @keyframes toast-leave-mobile {
    0% {
      transform: translateX(0) scale(1);
      opacity: 1;
    }
    100% {
      transform: translateX(100%) scale(0.95);
      opacity: 0;
    }
  }

  .react-grid-item.react-draggable-dragging {
    transition: none;
    z-index: 3;
    will-change: transform;
  }

  .react-grid-item.react-grid-placeholder {
    background: #273648 !important;
    opacity: 0.2;

    transition-duration: 100ms;
    z-index: 2;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
  }

  .Toastify__toast {
    padding: 0 !important;
    border-radius: 6px !important;
  }

  .subscription-container {
    background: linear-gradient(
      117deg,
      rgba(198, 213, 159, 1) 9%,
      rgba(200, 224, 176, 1) 55%,
      rgba(227, 235, 218, 1) 100%
    );
  }

  .newpost {
    background: linear-gradient(
      117deg,
      rgb(213, 240, 144) 9%,
      rgb(200, 251, 150) 55%,
      rgb(189, 221, 155) 100%
    );
    background-size: 200% 200%;
    background-position: 0% 0%;
    transition: background-position 0.3s ease-in-out;
  }

  .newpost:hover {
    background-position: 100% 100%;
  }

  .bttn[data-state="open"] span {
    transform: rotate(90deg);
  }

  .bttn[data-state="closed"] {
    background-color: red !important;
    color: white;
  }

  .sendbtn:hover svg {
    color: white;
  }
  .main-chat::-webkit-scrollbar-track {
    margin-top: 10rem;
  }

  /* Shared surface shadow utility and default application */
  .shadow-surface {
    box-shadow: 0px 4px 4px 0px #00000040;
    border: 1.5px solid #cccccc;
  }

  /* Apply the same shadow to widgets, charts, and overview cards */
}
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}
@layer base {
  * {
    /* Tailwind v4 no longer recognizes `border-border` here. Use CSS var directly. */
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

.lucide {
  background: black;
  color: white;
  border-radius: 100%;
  width: 28px;
  height: 28px;
  padding: 6px;
}

.faq .lucide {
  display: none;
}

input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  outline: none;
  content: none;
}

/* Analytics Dashboard Optimizations */
.analytics-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.analytics-card {
  contain: layout style paint;
  will-change: transform;
}

.analytics-card:hover {
  transform: translateY(-2px);
}

/* Top Posts Grid Optimizations */
.top-posts-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

@media (min-width: 640px) {
  .top-posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (min-width: 1024px) {
  .top-posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (min-width: 1536px) {
  .top-posts-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

/* Prevent content overflow */
.analytics-content {
  overflow: hidden;
  word-wrap: break-word;
  hyphens: auto;
}

/* Image loading optimization */
.analytics-image {
  content-visibility: auto;
  contain-intrinsic-size: 200px 200px;
}

/* Performance optimizations */
.analytics-chart {
  contain: layout style paint;
}

/* Line clamp utility for better text overflow handling */
.line-clamp-2 {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.text-white {
  color: white !important;
}
.text-xs {
  font-size: 0.75rem !important; /* 12px */
  line-height: 1rem !important; /* 16px */
}

.text-sm {
  font-size: 0.875rem !important; /* 14px */
  line-height: 1.25rem !important; /* 20px */
}

.text-base {
  font-size: 1rem !important; /* 16px */
  line-height: 1.5rem !important; /* 24px */
}

.text-lg {
  font-size: 1.125rem !important; /* 18px */
  line-height: 1.75rem !important; /* 28px */
}

.text-xl {
  font-size: 1.25rem !important; /* 20px */
  line-height: 1.75rem !important; /* 28px */
}

.text-2xl {
  font-size: 1.5rem !important; /* 24px */
  line-height: 2rem !important; /* 32px */
}

.text-3xl {
  font-size: 1.875rem !important; /* 30px */
  line-height: 2.25rem !important; /* 36px */
}

.text-4xl {
  font-size: 2.25rem !important; /* 36px */
  line-height: 2.5rem !important; /* 40px */
}

.text-5xl {
  font-size: 3rem !important; /* 48px */
  line-height: 1 !important;
}

.text-6xl {
  font-size: 3.75rem !important; /* 60px */
  line-height: 1 !important;
}

.text-7xl {
  font-size: 4.5rem !important; /* 72px */
  line-height: 1 !important;
}

.text-8xl {
  font-size: 6rem !important; /* 96px */
  line-height: 1 !important;
}

.text-9xl {
  font-size: 8rem !important; /* 128px */
  line-height: 1 !important;
}
