// Test file to verify Tailwind CSS is working
import React from "react";

const TailwindTest = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-light to-secondary-normal flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <h1 className="text-3xl font-bold text-primary-normal mb-4 animate-bounce-up">
          Tailwind CSS v4 Test
        </h1>
        <p className="text-gray-600 mb-6 line-clamp-2">
          This component tests various Tailwind CSS classes including custom
          colors, animations, and utilities to ensure everything is working
          properly with Tailwind CSS version 4.
        </p>
        <div className="flex gap-4">
          <button className="bg-primary-normal hover:bg-primary-normal-hover text-white px-4 py-2 rounded-md animate-soft-glow">
            Primary Button
          </button>
          <button className="bg-secondary-normal hover:bg-secondary-normal-hover text-white px-4 py-2 rounded-md">
            Secondary Button
          </button>
        </div>
        <div className="mt-6 p-4 bg-dark-blue-light rounded-md">
          <p className="text-dark-blue-normal text-sm">
            ✅ Custom colors working
            <br />
            ✅ Custom animations working
            <br />✅ Utility classes working
          </p>
        </div>
      </div>
    </div>
  );
};

export default TailwindTest;
