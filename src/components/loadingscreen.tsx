import React, { useEffect, useState } from "react";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { motion } from "framer-motion";

interface LoadingScreenProps {
  message?: string;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = "Loading...",
}) => {
  const [isPWA, setIsPWA] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const _isPWA = window.matchMedia("(display-mode: standalone)").matches;
    setIsPWA(_isPWA);
  }, []);

  // Faster looping
  const waveVariants = {
    animate: {
      rotate: [0, 360],
      scale: [1, 1.05, 1],
      opacity: [0.22, 0.38, 0.22],
      transition: {
        rotate: { duration: 12, repeat: Infinity, ease: "linear" }, // faster rotation (was 24s)
        scale: {
          duration: 3,
          repeat: Infinity,
          repeatType: "mirror",
          ease: "easeInOut",
        }, // was 6s
        opacity: {
          duration: 3,
          repeat: Infinity,
          repeatType: "mirror",
          ease: "easeInOut",
        }, // was 6s
      },
    },
  };

  const pulseVariants = {
    animate: {
      scale: [1, 1.06, 1],
      opacity: [0.28, 0.55, 0.28],
      transition: {
        duration: 3, // was 7s
        repeat: Infinity,
        repeatType: "mirror",
        ease: "easeInOut",
      },
    },
  };

  const logoVariants = {
    initial: { scale: 0.9, opacity: 0 },
    animate: {
      scale: 1,
      opacity: 1,
      transition: { duration: 0.6, ease: "backOut" },
    },
  };

  const textVariants = {
    animate: {
      opacity: [0.6, 1, 0.6],
      transition: { duration: 1.5, repeat: Infinity, ease: "easeInOut" },
    },
  };

  return (
    <>
      <section className="bg-white relative grid place-items-center h-screen w-screen overflow-hidden">
        {/* Larger animated gradient layers */}
        <motion.div
          variants={waveVariants}
          animate="animate"
          className="absolute rounded-full"
          style={{
            width: "18rem",
            height: "18rem",
            background:
              "conic-gradient(from 0deg, #104057, #1a5670, #2c6b82, #104057)",
            filter: "blur(44px)",
            willChange: "transform, opacity",
          }}
        />

        <motion.div
          variants={waveVariants}
          animate="animate"
          className="absolute rounded-full"
          style={{
            width: "15rem",
            height: "15rem",
            background:
              "radial-gradient(circle, #104057 0%, #1a5670 50%, #2c6b82 100%)",
            filter: "blur(34px)",
            willChange: "transform, opacity",
            animationDelay: "0.6s",
          }}
        />

        <motion.div
          variants={pulseVariants}
          animate="animate"
          className="absolute rounded-full"
          style={{
            width: "12rem",
            height: "12rem",
            background:
              "linear-gradient(45deg, #104057, #1a5670, #2c6b82, #3d7a94)",
            filter: "blur(26px)",
            willChange: "transform, opacity",
          }}
        />

        {/* Inner rings */}
        <motion.div
          variants={pulseVariants}
          animate="animate"
          className="absolute rounded-full bg-gradient-to-r from-[#104057]/15 to-[#2c6b82]/15"
          style={{ width: "9rem", height: "9rem", animationDelay: "0.3s" }}
        />

        <motion.div
          variants={pulseVariants}
          animate="animate"
          className="absolute rounded-full bg-gradient-to-l from-[#104057]/20 to-[#1a5670]/20"
          style={{ width: "7.5rem", height: "7.5rem", animationDelay: "0.9s" }}
        />

        <motion.div
          variants={pulseVariants}
          animate="animate"
          className="absolute rounded-full bg-gradient-to-r from-[#104057]/25 to-[#3d7a94]/25"
          style={{ width: "6.5rem", height: "6.5rem", animationDelay: "1.2s" }}
        />

        {/* Central white circle */}
        <motion.div
          initial={{ scale: 0.95, opacity: 0.8 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="absolute w-36 h-36 rounded-full bg-white shadow-2xl z-10 border-4 border-white/40"
          style={{ willChange: "transform, opacity" }}
        />

        {/* Logo */}
        <motion.div
          variants={logoVariants}
          initial="initial"
          animate="animate"
          className="z-30 relative"
        >
          <Image
            src="/icons/logo.svg"
            width={64}
            height={64}
            className="w-16 h-16 md:w-24 md:h-24 drop-shadow-lg"
            alt="Business Insight AI Logo"
            priority
          />
        </motion.div>
      </section>

      {/* Loading message */}
      <motion.section
        variants={textVariants}
        animate="animate"
        className="absolute bottom-50 left-1/2 -translate-x-1/2 text-loading text-[#104057] font-medium z-30"
        style={{ willChange: "opacity" }}
      >
        {message}
      </motion.section>
    </>
  );
};

export default LoadingScreen;
