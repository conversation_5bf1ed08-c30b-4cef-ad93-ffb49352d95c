import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import SplashScreen from './SplashScreen';
interface LoadingScreenProps {
  message?: string;
}
const LoadingScreen: React.FC<LoadingScreenProps> = ({ message = 'Loading...' }) => {
  const [isPWA, setIsPWA] = useState(false);
  const pathname = usePathname();
  useEffect(() => {
    // Check if running as PWA
    const isPWA = window.matchMedia('(display-mode: standalone)').matches;
    setIsPWA(isPWA);
  }, []);
  // if (isPWA || pathname?.includes('/login') || pathname?.includes('/signup') || pathname?.includes('/password-recovery')) {
  //   return (
  //     <div className="fixed inset-0 z-50 flex items-center justify-center bg-[
#2c3e50]">
  //       <div className="flex flex-col items-center space-y-4">
  //         <div className="relative h-24 w-24 animate-pulse p-4 bg-white rounded-full">
  //           <Image
  //             src="/pwa/ios/192.png"
  //             alt="Business Insight AI"
  //             fill
  //             className="object-contain"
  //           />
  //         </div>
  //         <div className="absolute bottom-10 left-1/2 -translate-x-1/2 flex flex-col items-center space-y-2 w-full">
  //           <p className="text-md font-semibold text-white">Social Media Marketing with AI</p>
  //         </div>
  //       </div>
  //     </div>
  //   );
  // }
  return (
    <>
      <section className="bg-white relative place-items-center grid h-screen w-screen">
        <div className="bg-linear-to-r from-[
#104057] blur w-48 h-48 absolute animate-ping rounded-full delay-5s shadow-2xl"></div>
        <div className="bg-linear-to-l from-[
#104057] w-32 h-32 absolute animate-ping rounded-full shadow-xl"></div>
        <div className="bg-linear-to-r from-[
#104057] w-24 h-24 absolute animate-ping rounded-full delay-5s shadow-xl"></div>
        <div className="bg-white w-24 h-24 absolute rounded-full shadow-xl"></div>
        <Image
          src="/icons/logo.svg"
          width={32}
          height={32}
          className="w-[8%] md:w-[3%] z-10"
          alt="Business Insight AI Logo"
          priority
        />
      </section>
      <section className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-loading text-[
#104057] blur-md invert drop-shadow-xl md:filter-none">
          {message}
      </section>
    </>
  );
}
export default LoadingScreen;