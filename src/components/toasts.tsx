import React from 'react';
import toast from 'react-hot-toast';
import { enhancedToast } from './enhanced-toast-utils';

interface ToastProps {
  message: string;
  subMessage?: string;
}

export const SuccessToast: React.FC<ToastProps> = ({ message, subMessage }) => (
  <div className="flex flex-col bg-[#f8fff9] rounded-lg p-4 w-full">
    <span className="text-[#00c853] text-md font-semibold">Success</span>
    <span className="text-[#4caf50] text-sm mt-1">{message}</span>
    {subMessage && <span className="text-[#4caf50] text-sm">{subMessage}</span>}
  </div>
);

export const ErrorToast: React.FC<ToastProps> = ({ message, subMessage }) => (
  <div className="flex flex-col bg-[#fff8f8] rounded-2xl p-4 w-full">
    <span className="text-red-600 text-md font-semibold">Error</span>
    <span className="text-red-400 text-sm mt-1">{message}</span>
    {subMessage && <span className="text-red-400 text-sm">{subMessage}</span>}
  </div>
);

export const showSuccessToast = (message: string, subMessage?: string) => {
  return enhancedToast.success(message, subMessage);
};

export const showErrorToast = (message: string, subMessage?: string) => {
  return enhancedToast.error(message, subMessage);
};

export const showInfoToast = (message: string, subMessage?: string) => {
  return enhancedToast.info(message, subMessage);
};

export const showWarningToast = (message: string, subMessage?: string) => {
  return enhancedToast.warning(message, subMessage);
};

// Minimal wrapper to maintain backwards compatibility with existing imports.
// Root Toaster is configured in `src/app/layout.tsx`, so this component renders nothing.
export const ToastWrapper: React.FC<{ containerId?: string }>= () => null;
