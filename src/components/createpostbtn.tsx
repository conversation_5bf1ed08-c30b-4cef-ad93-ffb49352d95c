import React from "react";
import { PlusIcon } from "@radix-ui/react-icons";
import Link from "next/link";
import { motion } from "framer-motion";

const MotionLink = motion(Link);

interface CreatePostBtnProps {
  onClick: () => void;
  link: string;
}

export const CreatePostBtn: React.FC<CreatePostBtnProps> = ({ onClick, link }) => {
  return (
    <MotionLink
      whileTap={{ scale: 0.98 }}
      
      onClick={onClick}
      href={link}
      className="inline-flex items-center justify-center gap-2 rounded-full px-4 py-2
      bg-[linear-gradient(117deg,rgb(170,192,115)_9%,rgb(160,201,120)_55%,rgb(151,177,124)_100%)]
      bg-size-[200%_200%] bg-position-[0%_0%]
      hover:bg-position-[100%_100%]
      transition-[background-position,transform,box-shadow] duration-300 ease-in-out
      text-white font-semibold shadow-sm hover:shadow-md
      shrink-0 whitespace-nowrap leading-none min-h-[40px]
      focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-lime-500"
    >
      <span>Create New Post</span>
      <PlusIcon className="ml-2 h-5 w-5" />
    </MotionLink>
  );
};
