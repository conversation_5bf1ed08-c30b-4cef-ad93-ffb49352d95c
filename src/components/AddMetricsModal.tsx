"use client";

import React from "react";
import { motion } from "framer-motion";
import * as Dialog from "@radix-ui/react-dialog";

// Types for the metrics modal
type DashboardMetricKey =
  | "followers"
  | "following"
  | "media_count"
  | "new_followers"
  | "unfollowers"
  | "profile_links_taps"
  | "replies"
  | "saves"
  | "accounts_engaged"
  | "reach"
  | "likes"
  | "comments"
  | "total_interactions"
  | "views";

// Metric labels and icons
const METRIC_LABELS: Record<
  DashboardMetricKey,
  { label: string; icon: string }
> = {
  followers: { label: "Followers", icon: "fa-user-friends" },
  following: { label: "Following", icon: "fa-user-plus" },
  media_count: { label: "Media Count", icon: "fa-photo-video" },
  new_followers: { label: "New Followers", icon: "fa-user-plus" },
  unfollowers: { label: "Unfollowers", icon: "fa-user-minus" },
  profile_links_taps: { label: "Profile Links Taps", icon: "fa-link" },
  replies: { label: "Replies", icon: "fa-reply" },
  saves: { label: "Saves", icon: "fa-bookmark" },
  accounts_engaged: { label: "Accounts Engaged", icon: "fa-users" },
  reach: { label: "Total Reach", icon: "fa-eye" },
  likes: { label: "Total Likes", icon: "fa-heart" },
  comments: { label: "Total Comments", icon: "fa-comment" },
  total_interactions: { label: "Total Interactions", icon: "fa-chart-line" },
  views: { label: "Total Views", icon: "fa-play" },
};

// Props interface
interface AddMetricsModalProps {
  isOpen: boolean;
  onClose: () => void;
  availableMetrics: DashboardMetricKey[];
  onAddMetric: (metric: DashboardMetricKey) => void;
  maxReached: boolean;
  // Optional preview data map with real values and optional change info
  previewData?: Partial<
    Record<
      DashboardMetricKey,
      {
        label: string;
        value: string | number;
        icon?: string;
        change?: { valueStr?: string; value?: number; isPositive?: boolean };
      }
    >
  >;
  // Newly added props to show already-added metrics with disabled state
  selectedMetrics: ReadonlyArray<DashboardMetricKey>;
  allMetrics: ReadonlyArray<DashboardMetricKey>;
}

export const AddMetricsModal: React.FC<AddMetricsModalProps> = ({
  isOpen,
  onClose,
  availableMetrics,
  onAddMetric,
  maxReached,
  previewData,
  selectedMetrics,
  allMetrics,
}) => {
  const renderPreviewCard = (key: DashboardMetricKey) => {
    const fallback = METRIC_LABELS[key];
    const data = previewData?.[key];
    const title = data?.label || fallback.label;
    const value =
      typeof data?.value === "number"
        ? data!.value.toLocaleString()
        : data?.value || "—";

    return (
      <div
        className="h-full w-full bg-white rounded-xl p-3 shadow-sm transition-shadow hover:shadow-md flex flex-col justify-center min-w-0"
        style={{ minHeight: "140px" }}
      >
        <div className="flex items-center gap-2 mb-2">
          <div className="min-w-0 flex-1">
            <h3 className="text-xs font-semibold text-gray-900 truncate">
              {title}
            </h3>
          </div>
        </div>
        <div className="text-lg font-bold text-[#1565C0] truncate">{value}</div>
      </div>
    );
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        <Dialog.Overlay asChild>
          <motion.div
            className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
          />
        </Dialog.Overlay>

        <Dialog.Content asChild>
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.1, ease: "easeOut" }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-3xl w-full mx-4 max-h-[90vh] flex flex-col">
              {/* Header */}
              <div className="flex justify-between items-center p-6 border-b border-gray-100 dark:border-gray-700">
                <Dialog.Title asChild>
                  <h3 className="text-xl font-semibold text-white">
                    Add Metrics Cards
                  </h3>
                </Dialog.Title>
                <Dialog.Close asChild>
                  <motion.button
                    className="text-white hover:text-gray-300 w-10 h-10 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    whileTap={{ scale: 0.95 }}
                  >
                    <i className="fas fa-times text-lg" />
                  </motion.button>
                </Dialog.Close>
              </div>

              {/* Warning Message */}
              {maxReached && (
                <motion.div className="mx-6 mt-4 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl">
                  <p className="text-amber-800 dark:text-amber-200 text-sm flex items-center gap-3">
                    <i className="fas fa-exclamation-triangle text-amber-600" />
                    Maximum of 6 metrics cards reached. Remove a card to add a
                    new one.
                  </p>
                </motion.div>
              )}

              {/* Content - Scrollable Area with Rounded Corners */}
              <div className="flex-1 overflow-hidden min-h-0">
                <div className="p-6 max-h-[60vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
                  {allMetrics.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {allMetrics.map((metric) => {
                        const hasData = !!previewData?.[metric];
                        const isAlreadyAdded = selectedMetrics.includes(metric);
                        const isDisabled = isAlreadyAdded || maxReached;
                        // Distinguish styles: brighter for already added, muted for maxReached-only
                        const baseEnabled =
                          "border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50/30 dark:hover:bg-blue-900/10 text-white hover:shadow-md";
                        const baseDisabled =
                          "border-gray-200 dark:border-gray-600 text-gray-500 cursor-not-allowed bg-gray-50 dark:bg-gray-800/50 opacity-60";
                        const alreadyAddedDisabled =
                          "border-blue-200 bg-blue-50/70 dark:border-blue-600/50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 cursor-not-allowed";

                        return (
                          <motion.button
                            key={metric}
                            onClick={() => !isDisabled && onAddMetric(metric)}
                            disabled={isDisabled}
                            className={`w-full text-left rounded-xl border transition-all duration-200 group ${
                              isDisabled
                                ? isAlreadyAdded
                                  ? alreadyAddedDisabled
                                  : baseDisabled
                                : baseEnabled
                            }`}
                            whileHover={!isDisabled ? { scale: 1.01 } : {}}
                            whileTap={!isDisabled ? { scale: 0.99 } : {}}
                          >
                            <div className="relative p-3">
                              {/* Keep the preview card itself white */}
                              {renderPreviewCard(metric)}
                              {!hasData && (
                                <div className="mt-2 text-xs text-gray-500">Preview based on latest data not available</div>
                              )}
                              {isAlreadyAdded && (
                                <div className="absolute top-2 right-2 inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-[10px] font-semibold bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                                  <i className="fas fa-check"></i>
                                  Added
                                </div>
                              )}
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  ) : (
                    <motion.div className="text-center py-12 text-white">
                      <motion.div>
                        <i className="fas fa-check-circle text-4xl mb-4 text-green-500" />
                      </motion.div>
                      <p className="text-lg font-medium text-white">
                        All available metrics are already added!
                      </p>
                      <p className="text-sm mt-2 text-white">
                        You can remove existing metrics to add different ones.
                      </p>
                    </motion.div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export type { DashboardMetricKey };
export default AddMetricsModal;
