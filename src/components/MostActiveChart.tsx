"use client";

import { motion } from "framer-motion";
import { useMemo } from "react";

interface MostActiveData {
  time: string;
  value: number;
  color: string;
}

const MostActiveChart = () => {
  // Sample data for most active times
  const data: MostActiveData[] = useMemo(
    () => [
      { time: "6 PM", value: 85, color: "#3b82f6" },
      { time: "8 PM", value: 72, color: "#6366f1" },
      { time: "7 PM", value: 68, color: "#8b5cf6" },
      { time: "9 PM", value: 45, color: "#a855f7" },
      { time: "5 PM", value: 38, color: "#c084fc" },
    ],
    []
  );

  const maxValue = Math.max(...data.map((d) => d.value));

  return (
    <div className="w-full bg-white rounded-lg p-4 border border-slate-100">
      {/* Header */}
      <div className="mb-4">
        <h4 className="text-slate-950 text-sm font-semibold mb-1">
          Most Active Hours
        </h4>
        <p className="text-slate-500 text-xs">Peak engagement times</p>
      </div>

      {/* Chart */}
      <div className="space-y-3">
        {data.map((item, index) => (
          <motion.div
            key={item.time}
            className="flex items-center gap-3"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            {/* Time Label */}
            <div className="w-12 text-xs font-medium text-slate-600 text-right">
              {item.time}
            </div>

            {/* Bar Container */}
            <div className="flex-1 relative">
              <div className="w-full h-6 bg-slate-100 rounded-full overflow-hidden">
                <motion.div
                  className="h-full rounded-full relative"
                  style={{ backgroundColor: item.color }}
                  initial={{ width: 0 }}
                  animate={{ width: `${(item.value / maxValue) * 100}%` }}
                  transition={{
                    duration: 0.8,
                    delay: index * 0.1,
                    ease: "easeOut",
                  }}
                >
                  {/* Gradient overlay for better visual appeal */}
                  <div
                    className="absolute inset-0 rounded-full opacity-20"
                    style={{
                      background: `linear-gradient(90deg, transparent 0%, ${item.color} 100%)`,
                    }}
                  />
                </motion.div>
              </div>

              {/* Value Label */}
              <motion.div
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 + 0.5 }}
              >
                <span className="text-xs font-medium text-white drop-shadow-sm">
                  {item.value}%
                </span>
              </motion.div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Footer */}
      <div className="mt-4 pt-3 border-t border-slate-100">
        <div className="flex items-center justify-between text-xs text-slate-500">
          <span>Engagement Rate</span>
          <span className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            +12% from last week
          </span>
        </div>
      </div>
    </div>
  );
};

export default MostActiveChart;
