"use client";

import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { useState, memo, useCallback } from "react";
import { FiInstagram } from "react-icons/fi";

const Header = memo(() => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const closeMenu = useCallback(() => {
    setIsOpen(false);
  }, []);



  return (
    <>
      <motion.header
        className="w-full h-[6.3rem] header z-50 fixed top-0 left-0 transition-all duration-300"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="w-full h-full bg-white/80 backdrop-blur-lg">
          <div className="max-w-7xl h-full mx-auto px-4 lg:px-8 flex items-center justify-between">
            <Link href="/"> <motion.div
              className="flex items-center gap-4 justify-center"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <img src="/icons/logo.svg" alt="Business Insight Logo" className="w-10 h-auto" />
              <span className="text-[#2c3e50] text-[1.375rem] font-semibold">Business Insight</span>
            </motion.div>
            </Link>
           

            {/* Desktop Navigation */}
            <motion.nav
              className="hidden lg:flex items-center"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <ul className="flex items-center space-x-2 mb-0">
                {['Features', 'Trust us', 'Why Business Insight?', 'Plans', 'Privacy Policy'].map((item, index) => (
                  <motion.li
                    key={item}
                    className="h-10 flex items-center"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                  >
                    <motion.a
                      href={item === 'Privacy Policy' ? '/privacy' : `#${item.toLowerCase().replace(/\s+/g, '-').replace(/[?]/g, '')}`}
                      className="inline-flex h-full items-center px-4 rounded-lg text-slate-800 text-sm leading-none hover:bg-slate-50 transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {item}
                    </motion.a>
                  </motion.li>
                ))}
              </ul>
            </motion.nav>

            {/* Login Button */}
            <motion.button
              onClick={() => window.location.assign('/login')}
              className="hidden lg:inline-flex h-10 items-center justify-center px-6 bg-[#2c3e50] text-white rounded-lg text-sm font-medium leading-none transition-all duration-300 ease-in-out hover:bg-slate-700"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              whileHover={{ scale: 0.95 }}
              whileTap={{ scale: 0.9 }}
            >
              Login / Signup
            </motion.button>

            {/* Mobile Menu Button */}
            <motion.button
              onClick={toggleMenu}
              className="lg:hidden inline-flex items-center justify-center h-10 w-10 z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="w-6 h-5 flex flex-col justify-between">
                <motion.span
                  className="w-full h-0.5 bg-slate-800"
                  animate={isOpen ? { rotate: 45, y: 8 } : { rotate: 0, y: 0 }}
                  transition={{ duration: 0.3 }}
                />
                <motion.span
                  className="w-full h-0.5 bg-slate-800"
                  animate={isOpen ? { opacity: 0 } : { opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
                <motion.span
                  className="w-full h-0.5 bg-slate-800"
                  animate={isOpen ? { rotate: -45, y: -8 } : { rotate: 0, y: 0 }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            </motion.button>
          </div>
        </div>
      </motion.header>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="lg:hidden fixed top-0 left-0 w-full h-dvh bg-white z-40"
            initial={{ y: "-100%" }}
            animate={{ y: 0 }}
            exit={{ y: "-100%" }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          >
            <div className="container mx-auto px-4 pt-28 pb-8 h-full flex flex-col">
              {/* Navigation Links */}
              <nav className="flex-1">
                <ul className="space-y-6">
                  {['Features', 'Trust us', 'Why Business Insight?', 'Plans', 'Privacy Policy'].map((item, index) => (
                    <motion.li
                      key={item}
                      className="border-b border-slate-100 pb-2"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                    >
                      <motion.a
                        href={item === 'Privacy Policy' ? '/privacy' : `#${item.toLowerCase().replace(/\s+/g, '-').replace(/[?]/g, '')}`}
                        className="text-xl font-medium text-slate-800 hover:text-slate-600 transition-colors block"
                        onClick={closeMenu}
                        whileHover={{ x: 10 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {item}
                      </motion.a>
                    </motion.li>
                  ))}
                  <motion.li
                    className="pt-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.6 }}
                  >
                    <motion.button
                      className="inline-flex w-full h-12 items-center justify-center bg-[#2c3e50] text-white rounded-lg text-lg font-medium transition-all duration-300 ease-in-out hover:bg-slate-700"
                      onClick={() => window.location.assign('/login')}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Login / Signup
                    </motion.button>
                  </motion.li>
                </ul>
              </nav>

              {/* Social Links */}
              <motion.div
                className="mt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.8 }}
              >
                <h3 className="text-sm font-semibold text-slate-950 tracking-wide mb-4">
                  Follow us
                </h3>
                <div className="flex justify-center gap-5">
                  {[
                    { name: "twitter", url: "https://x.com/AIBinsight" },
                    {
                      name: "facebook",
                      url: "https://www.facebook.com/profile.php?id=61570911104567",
                    },
                    {
                      name: "instagram",
                      url: "https://www.instagram.com/businessinsight.ai",
                    },
                    {
                      name: "linkedin",
                      url: "https://www.linkedin.com/in/aibusinessinsight/",
                    },
                    { name: "youtube", url: "https://www.youtube.com/@BusinessInsightAI" },
                  ].map(({ name, url }, index) => (
                    <motion.a
                      key={name}
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:bg-slate-100 rounded-lg transition-colors p-2"
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: 1 + index * 0.1 }}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      {name === "instagram" ? (
                        <span className="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center text-black">
                          <FiInstagram className="w-full h-full" aria-hidden="true" />
                          <span className="sr-only">Instagram</span>
                        </span>
                      ) : (
                        <img
                          src={`/${name}.svg`}
                          alt={name}
                          className="w-10 h-10"
                        />
                      )}
                    </motion.a>
                  ))}
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
});

Header.displayName = 'Header';

export default Header;
