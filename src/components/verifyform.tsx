"use client"

import { useState, useRef, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm, SubmitHandler } from "react-hook-form";
import { showErrorToast, showSuccessToast } from "./toasts";
import AuthBtn from "./authbtn";
import axios from "axios";

interface VerifyFormInputs {
  code: string[];
}

interface VerificationResponse {
  success: boolean;
  message: string;
}

interface OTPInputProps {
  length: number;
  onComplete: (value: string) => void;
  disabled?: boolean;
  error?: boolean;
  setValue?: (value: string[]) => void;
}

const OTPInput = ({ length = 5, onComplete, disabled = false, error = false, setValue }: OTPInputProps) => {
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(''));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const updateOTP = (newOtp: string[]) => {
    setOtp(newOtp);
    if (setValue) {
      setValue(newOtp);
    }
  };

  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  const focusInput = useCallback((targetIndex: number) => {
    const targetInput = inputRefs.current[targetIndex];
    if (targetInput) {
      setTimeout(() => {
        targetInput.focus();
      }, 0);
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = e.target.value;
    if (value.length > 1) return;
    
    if (value && !/^\d+$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    updateOTP(newOtp);

    if (value && index < length - 1) {
      focusInput(index + 1);
    }

    if (newOtp.every(v => v) && newOtp.join('').length === length) {
      onComplete(newOtp.join(''));
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'Backspace') {
      e.preventDefault();
      const newOtp = [...otp];
      
      if (newOtp[index]) {
        newOtp[index] = '';
        updateOTP(newOtp);
      } 
      else if (index > 0) {
        newOtp[index - 1] = '';
        updateOTP(newOtp);
        focusInput(index - 1);
      }
    }
    else if (e.key === 'ArrowLeft' && index > 0) {
      e.preventDefault();
      focusInput(index - 1);
    }
    else if (e.key === 'ArrowRight' && index < length - 1) {
      e.preventDefault();
      focusInput(index + 1);
    }
    else if (e.key === 'Enter') {
      e.preventDefault();
      if (otp.every(v => v)) {
        onComplete(otp.join(''));
      }
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const pastedNumbers = pastedData.replace(/[^0-9]/g, '').slice(0, length);
    
    if (pastedNumbers) {
      const newOtp = Array(length).fill('');
      pastedNumbers.split('').forEach((char, index) => {
        if (index < length) {
          newOtp[index] = char;
        }
      });
      updateOTP(newOtp);
      
      const nextEmptyIndex = newOtp.findIndex(val => !val);
      focusInput(nextEmptyIndex >= 0 ? nextEmptyIndex : length - 1);

      if (pastedNumbers.length >= length) {
        onComplete(newOtp.join(''));
      }
    }
  };

  return (
    <div className="flex justify-between gap-3 mb-8">
      {otp.map((digit, index) => (
        <input
          key={index}
          type="text"
          inputMode="numeric"
          maxLength={1}
          value={digit}
          disabled={disabled}
          ref={el => {
            inputRefs.current[index] = el;
          }}
          onChange={(e) => handleChange(e, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          onPaste={handlePaste}
          className={`w-15 h-15 text-center border ${
            error ? 'border-red-500' : 'border-[#cccccc]/80'
          } rounded-lg text-[1.125rem] focus:border-[#2C3E50] focus:outline-none disabled:bg-gray-100 disabled:cursor-not-allowed`}
        />
      ))}
    </div>
  );
};

interface VerifyFormProps {
  onSubmit: (data: any) => void;
  isLoading: boolean;
  inputRefs: React.RefObject<HTMLInputElement>[];
  errors: any;
  register: any;
  cooldownTime: number;
  resendCode: () => void;
  inv?: string;
  passwordChangeCode?: string;
  loadingProgress: number;
}

const MobileVerifyForm = ({
  onSubmit,
  isLoading,
  inputRefs,
  errors,
  register,
  loadingProgress,
  cooldownTime,
  resendCode,
  inv,
  passwordChangeCode
}: VerifyFormProps) => {
  const [codes, setCodes] = useState(['', '', '', '', '']);

  const handleInputChange = (index: number, digit: string) => {
    if (digit && index < 4 && inputRefs[index + 1]?.current) {
      setTimeout(() => {
        inputRefs[index + 1]?.current?.focus();
      }, 0);
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !codes[index]) {
      e.preventDefault();
      const newCodes = [...codes];
      newCodes[index - 1] = '';
      setCodes(newCodes);
      if (inputRefs[index - 1]?.current) {
        setTimeout(() => {
          inputRefs[index - 1]?.current?.focus();
        }, 0);
      }
    } else if (e.key === 'Enter') {
      if (codes.every(code => code !== '')) {
        handleFormSubmit(e);
      }
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text').replace(/[^0-9]/g, '').slice(0, 5);
    const newCodes = [...codes];
    for (let i = 0; i < pastedText.length && i < 5; i++) {
      newCodes[i] = pastedText[i] || '';
    }
    setCodes(newCodes);
    const lastIndex = Math.min(pastedText.length - 1, 4);
    if (lastIndex >= 0 && inputRefs[lastIndex]?.current) {
      inputRefs[lastIndex].current?.focus();
    }
  };

  const clearCodes = () => {
    setCodes(['', '', '', '', '']);
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({ code: codes, clearCodes });
  };

  const handleResendCode = () => {
    clearCodes();
    resendCode();
  };

  const handleOTPComplete = (value: string) => {
    onSubmit({ code: value.split(''), clearCodes: () => {} });
  };

  return (
    <div className="w-full h-dvh bg-white md:hidden">
      <div className="p-6 flex flex-col h-full justify-between">

        <div className="">
        <div className="mb-10.5">
          <h1 className="text-[#2c3e50] text-[1.75rem] font-semibold">Verify Your Email</h1>
          <p className="text-[#0f0f0f] text-base mt-2.5">
            Please enter the 5-digit verification code sent to your email.
          </p>
        </div>

        <form onSubmit={handleFormSubmit} className="flex-1">
          <OTPInput
            length={5}
            onComplete={handleOTPComplete}
            disabled={isLoading}
            error={!!errors.code}
            setValue={(values) => values.forEach((value, index) => register(`code.${index}`).onChange({ target: { value } }))}
          />
          {errors.code && (
            <p className="text-red-500 text-[0.688rem] mb-4">All digits are required</p>
          )}
        </form>

        </div>

        <div>
        <div className="w-full flex items-center justify-center mt-8">
          <AuthBtn
            text={isLoading ? "Verifying..." : "Verify"}
            style="default"
            onClick={handleFormSubmit}
            disabled={isLoading}
            isLoading={isLoading}
            loadingProgress={loadingProgress}
          />
        </div>

          <div className="mt-6 flex justify-center">
            {cooldownTime > 0 ? (
              <p className="text-[#2c3e50] text-[0.813rem]">
                Resend code available in {Math.floor(cooldownTime / 60)}:
                {(cooldownTime % 60).toString().padStart(2, '0')}
              </p>
            ) : (
              <button
                type="button"
                onClick={handleResendCode}
                className="text-[#2c3e50] text-[0.813rem] hover:underline"
              >
                Resend Code
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const VerifyForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const inv = searchParams.get('inv') || '/dashboard';
  const [loadingProgress, setLoadingProgress] = useState(0);
  const passwordChangeCode = searchParams.get('password-change') || undefined;
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<VerifyFormInputs>({
    defaultValues: { code: ["", "", "", "", ""] },
  });
  const [isLoading, setIsLoading] = useState(false);
  const inputRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ];
  const [cooldownTime, setCooldownTime] = useState(120);

  useEffect(() => {
    // Check if tokens are in sessionStorage
    const accessToken = sessionStorage.getItem("accessToken");
    const refreshToken = sessionStorage.getItem("refreshToken");
  }, [router]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (cooldownTime > 0) {
      timer = setInterval(() => {
        setCooldownTime((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [cooldownTime]);

  const clearAllInputs = () => {
    // Clear form inputs
    [0, 1, 2, 3, 4].forEach(index => {
      setValue(`code.${index}`, '');
      if (inputRefs[index]?.current) {
        inputRefs[index].current.value = '';
      }
    });
  };

  const onSubmit: SubmitHandler<VerifyFormInputs> = async (data) => {
    setIsLoading(true);
    const combinedCode = data.code.join("");
    const accessToken2 = sessionStorage.getItem("accessToken") || searchParams.get("access");
    const passwordChangeCode = searchParams.get('password-change');
    setLoadingProgress(0);
    
    const simulateProgress = () => {
      setLoadingProgress((prevProgress) => {
        const newProgress = prevProgress + 10;
        return newProgress > 90 ? 90 : newProgress;
      });
    };
  
    const progressInterval = setInterval(simulateProgress, 300);

    try {
      const response = await axios.post("/api/auth/verify", 
        { code: combinedCode },
        {
          headers: {
            ...(passwordChangeCode 
              ? { "password-change": passwordChangeCode }
              : { Authorization: `Bearer ${accessToken2 ?? ''}` }
            ),
          }
        }
      );

      const result = response.data as VerificationResponse;
      if (result.success) {
        showSuccessToast("Email verified successfully!", "verify-toast");
        router.push(searchParams.get("inv") ? "/login?inv=" + inv : "/login");
      } 
    } catch (error) {
      const result = error.response.data;
      console.error("Verification error:", result);
      if (result.message?.toLowerCase().includes('expired')) {
        clearAllInputs();
        resendCode();
      }
      showErrorToast(result.message || "Verification failed. Please try again.", "verify-toast");
    } finally {
      setIsLoading(false);
      clearInterval(progressInterval);
    }
  };

  const handleInputChange = (index: number, value: string) => {
    if (value.length <= 1) {
      setValue(`code.${index}`, value);
      if (value.length === 1 && index < 4) {
        inputRefs[index + 1]?.current?.focus();
      }
    }
  };

  const resendCode = async () => {
    clearAllInputs();
    const accessToken3 = sessionStorage.getItem("accessToken");
    const passwordChangeCode = searchParams.get('password-change');

    try {
      const response = await axios.post("/api/auth/resend-verification/", {}, {
        headers: {
          ...(passwordChangeCode 
            ? { "password-change": passwordChangeCode }
            : { Authorization: `Bearer ${accessToken3 ?? ''}` }
          ),
        },
      });

      const result = response.data as VerificationResponse;
      if (result.success) {
        showSuccessToast("Verification code resent successfully!", "verify-toast");
        setCooldownTime(120);
      } else {
        showErrorToast(result.message || "Failed to resend code. Please try again.", "verify-toast");
      }
    } catch (error) {
      const result = error?.response.data;
      console.error("Resend code error:", result);
      showErrorToast(result.message || "An unexpected error occurred. Please try again.", "verify-toast");
    }
  };

  return (
    <>
      {/* Mobile version */}
      <div className="md:hidden">
        <MobileVerifyForm
          onSubmit={handleSubmit(onSubmit)}
          isLoading={isLoading}
          inputRefs={inputRefs}
          errors={errors}
          register={register}
          cooldownTime={cooldownTime}
          resendCode={resendCode}
          inv={inv}
          passwordChangeCode={passwordChangeCode}
          loadingProgress={loadingProgress}
        />
      </div>

      {/* Desktop version - keep existing code */}
      <div className="hidden md:flex flex-col h-full w-full justify-between">
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="space-y-4 md:space-y-6 w-full md:w-full md:h-full md:flex md:flex-col md:justify-between"
        >

          <div className="md:h-[10%] w-full"> </div>
          <div className="md:h-[60%] w-full p-4 overflow-y-auto flex flex-col justify-center">
          <h2 className="text-2xl font-bold mb-4">Verify Your Email</h2>
          <p className="mb-4">
            Please enter the 5-digit verification code sent to your email.
          </p>
          <OTPInput
            length={5}
            onComplete={(value) => {
              const values = value.split('');
              values.forEach((val, idx) => setValue(`code.${idx}`, val));
              handleSubmit(onSubmit)();
            }}
            disabled={isLoading}
            error={!!errors.code}
            setValue={(values) => values.forEach((value, index) => setValue(`code.${index}`, value))}
          />
          {errors.code && (
            <p className="text-red-500 text-xs mt-1">All digits are required</p>
          )}
          </div>
          <div className="btn flex justify-center items-center mt-4 flex-col gap-3 md:gap-4">
            <AuthBtn
              text={isLoading ? "Verifying..." : "Verify"}
              style="default"
              onClick={handleSubmit(onSubmit)} 
              disabled={isLoading}
              isLoading={isLoading}
              loadingProgress={loadingProgress}
            />
            {cooldownTime > 0 ? (
              <p className="text-sm text-gray-500">
                Resend code available in {Math.floor(cooldownTime / 60)}:
                {(cooldownTime % 60).toString().padStart(2, '0')}
              </p>
            ) : (
              <button
                type="button"
                onClick={resendCode}
                className="text-sm text-blue-600 hover:underline"
              >
                Resend Code
              </button>
            )}
          </div>
        </form>
      </div>
    </>
  );
};

export default VerifyForm;
