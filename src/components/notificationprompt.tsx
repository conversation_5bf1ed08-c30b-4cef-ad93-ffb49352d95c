import React from 'react';
import { usePushNotification } from '../hooks/usePushNotification';

const NotificationPrompt: React.FC = () => {
  const { permission, requestPermission, error, subscription, isPWAMode } = usePushNotification();

  const handlePermissionRequest = async () => {
    const granted = await requestPermission();
    if (granted && subscription) {
      // Send a test notification
      try {
        const response = await fetch(`/api/notifications/test?subscription=${encodeURIComponent(JSON.stringify(subscription))}`);
        const data = await response.json();
        if (!data.success) {
          console.error('Failed to send test notification:', data.message);
        }
      } catch (err) {
        console.error('Error sending test notification:', err);
      }
    }
  };

  // Don't show if not in PWA mode, or if permission is already granted/denied
  if (!isPWAMode || permission === 'granted' || permission === 'denied') {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 p-4 md:p-0 z-50">
      <div className="w-full max-w-[320px] bg-white rounded-2xl shadow-xl p-6">
        <div className="flex flex-col items-center text-center">
          <div className="mb-4">
            <svg
              className="h-16 w-16 text-blue-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            "Business Insight" Would Like to Send You Notifications
          </h2>
          <p className="text-sm text-gray-500 mb-6">
            Notifications may include alerts, sounds, and icon badges.
          </p>
          {error && (
            <p className="text-sm text-red-600 mb-4">
              {error}
            </p>
          )}
          <div className="w-full space-y-3">
            <button
              type="button"
              onClick={handlePermissionRequest}
              className="w-full px-4 py-3 text-base font-semibold text-blue-600 bg-transparent hover:bg-gray-100 rounded-xl transition-colors"
            >
              Allow
            </button>
            <button
              type="button"
              onClick={() => {
                localStorage.setItem('notificationPromptDismissed', 'true');
                window.location.reload();
              }}
              className="w-full px-4 py-3 text-base font-semibold text-blue-600 bg-transparent hover:bg-gray-100 rounded-xl transition-colors"
            >
              Don't Allow
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationPrompt; 