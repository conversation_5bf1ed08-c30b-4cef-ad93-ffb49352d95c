import toast from 'react-hot-toast';
import { enhancedToast } from './enhanced-toast-utils';

export const showSuccessToast = (message: string, id?: string) => {
  return enhancedToast.success(message);
};

export const showErrorToast = (message: string, id?: string) => {
  return enhancedToast.error(message);
};

export const showWarningToast = (message: string, subMessage?: string) => {
  return enhancedToast.warning(message, subMessage);
};