"use client"

import { useState, useRef, useEffect, use<PERSON>allback } from "react";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter, useSearchParams } from "next/navigation";
import AuthBtn from "./authbtn";
import { showErrorToast, showSuccessToast } from "./toasts";
import { useUserStore } from "~/store/userStore";
import axios from "axios";
 ;


const emailSchema = z.object({
  email: z.string().email("Please enter a valid email address")
});

const otpSchema = z.object({
  code: z.array(z.string().length(1)).length(5),
});

const passwordSchema = z
  .object({
    password: z.string()
      .min(8, "Password must be at least 8 characters")
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/, 
        "Password must contain:\n- At least 8 characters\n- At least one uppercase letter\n- At least one lowercase letter\n- At least one number"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type EmailFormData = z.infer<typeof emailSchema>;
type OTPFormData = z.infer<typeof otpSchema>;
type PasswordFormData = z.infer<typeof passwordSchema>;

interface ApiResponse {
  process_id: string;
  csrf: string;
}

// Add prop type for onStepChange
interface PasswordRecoveryFormProps {
  onStepChange: (step: number) => void;
}

// Add this helper function at the top of your file
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// Update password validation helper
const validatePassword = (password: string) => {
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/;
  return passwordRegex.test(password);
}

const useForceUpdate = () => {
  const [, setValue] = useState(0);
  return () => setValue(value => value + 1);
};

// Add this new component before the MobilePasswordRecoveryForm component
const CooldownTimer = ({ cooldownTimeRef, resendCode }: { cooldownTimeRef: React.RefObject<number>, resendCode: () => Promise<void> }) => {
  const [, forceUpdate] = useState({});

  useEffect(() => {
    const timer = setInterval(() => {
      if (cooldownTimeRef.current > 0) {
        cooldownTimeRef.current -= 1;
        forceUpdate({});
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [cooldownTimeRef]);

  return (
    <div className="mt-6 flex justify-center">
      {cooldownTimeRef.current > 0 ? (
        <p className="text-[#2c3e50] text-[0.813rem]">
          Resend code available in {formatTime(cooldownTimeRef.current ?? 0)}
        </p>
      ) : (
        <button
          type="button"
          onClick={resendCode}
          className="text-[#2c3e50] text-[0.813rem] hover:underline"
        >
          Resend Code
        </button>
      )}
    </div>
  );
};

// Add this new OTP input component
const OTPInput = ({ 
  length = 5,
  onComplete,
  disabled = false,
  error = false
}: {
  length?: number;
  onComplete: (value: string) => void;
  disabled?: boolean;
  error?: boolean;
}) => {
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(''));
  const inputRefs = useRef<Array<HTMLInputElement | null>>([]);

  // Initialize refs on mount
  useEffect(() => {
    inputRefs.current = new Array(length).fill(null);
  }, [length]);

  const focusInput = useCallback((targetIndex: number) => {
    setTimeout(() => {
      if (inputRefs.current[targetIndex]) {
        inputRefs.current[targetIndex]?.focus();
        inputRefs.current[targetIndex]?.select();
      }
    }, 0);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = e.target.value;
    const newOtp = [...otp];
    
    // Only accept numbers
    const sanitizedValue = value.replace(/[^0-9]/g, '');
    const lastChar = sanitizedValue.slice(-1);
    
    if (lastChar) {
      newOtp[index] = lastChar;
      setOtp(newOtp);

      // If we have a value and we're not at the last input, focus next
      if (index < length - 1) {
        focusInput(index + 1);
      }
    } else {
      newOtp[index] = '';
      setOtp(newOtp);
    }

    // Check if all fields are filled
    const newValue = newOtp.join('');
    if (newValue.length === length) {
      onComplete(newValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'Backspace') {
      e.preventDefault();
      const newOtp = [...otp];
      
      // If current input has value, clear it
      if (newOtp[index]) {
        newOtp[index] = '';
        setOtp(newOtp);
      } 
      // If current input is empty and not first input, go to previous input and clear it
      else if (index > 0) {
        newOtp[index - 1] = '';
        setOtp(newOtp);
        focusInput(index - 1);
      }
    }
    // Handle left arrow key
    else if (e.key === 'ArrowLeft' && index > 0) {
      e.preventDefault();
      focusInput(index - 1);
    }
    // Handle right arrow key
    else if (e.key === 'ArrowRight' && index < length - 1) {
      e.preventDefault();
      focusInput(index + 1);
    }
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select();
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const pastedNumbers = pastedData.replace(/[^0-9]/g, '').slice(0, length);
    
    if (pastedNumbers) {
      const newOtp = [...otp];
      pastedNumbers.split('').forEach((char, index) => {
        if (index < length) {
          newOtp[index] = char;
        }
      });
      setOtp(newOtp);
      
      // Focus the next empty input or the last input if all are filled
      const nextEmptyIndex = newOtp.findIndex(val => !val);
      focusInput(nextEmptyIndex >= 0 ? nextEmptyIndex : length - 1);

      // If we have filled all inputs, trigger completion
      if (pastedNumbers.length >= length) {
        onComplete(newOtp.join(''));
      }
    }
  };

  return (
    <div className="flex gap-4 justify-center mb-3 md:mb-4">
      {otp.map((digit, index) => (
        <input
          key={index}
          type="text"
          inputMode="numeric"
          maxLength={1}
          value={digit}
          disabled={disabled}
          ref={el => {
            inputRefs.current[index] = el;
          }}
          onChange={(e) => handleChange(e, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          onFocus={handleFocus}
          onPaste={handlePaste}
          className={`w-15 h-15 text-center border ${
            error ? 'border-red-500' : 'border-[#cccccc]/80'
          } rounded-lg text-[1.125rem] focus:border-[#2C3E50] focus:outline-none disabled:bg-gray-100 disabled:cursor-not-allowed`}
        />
      ))}
    </div>
  );
};

export default function PasswordRecoveryForm({ onStepChange }: PasswordRecoveryFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isValidPassword, setIsValidPassword] = useState(false)
  const [passwordsMatch, setPasswordsMatch] = useState(false)

  const router = useRouter();
  const [step, setStep] = useState<"email" | "otp" | "password">("email");
  const [email, setEmail] = useState("");
  const [processId, setProcessId] = useState("");
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [inputValues, setInputValues] = useState({
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [loadingProgress, setLoadingProgress] = useState(0);

  // Replace cooldownTime state with ref
  const cooldownTimeRef = useRef(0);
  const forceUpdate = useForceUpdate();
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Update cooldown timer effect
  useEffect(() => {
    console.log('Cooldown time changed:', cooldownTimeRef.current);
    
    if (cooldownTimeRef.current > 0) {
      console.log('Starting timer with cooldown:', cooldownTimeRef.current);
      
      timerRef.current = setInterval(() => {
        cooldownTimeRef.current -= 1;
        console.log('Timer tick, new time:', cooldownTimeRef.current);
        forceUpdate(); // Force update UI when timer changes
        
        if (cooldownTimeRef.current <= 0 && timerRef.current) {
          clearInterval(timerRef.current);
        }
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        console.log('Cleaning up timer');
        clearInterval(timerRef.current);
      }
    };
  }, [step]); // Only re-run when step changes

  const searchParams = useSearchParams();
  const invitationCode = searchParams.get('inv');

  const emailForm = useForm<EmailFormData>({
    resolver: zodResolver(emailSchema),
  });

  const otpForm = useForm<OTPFormData>({
    resolver: zodResolver(otpSchema),
    defaultValues: { 
      code: ["", "", "", "", ""] 
    },
  });

  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      password: '',
      confirmPassword: ''
    }
  });

  const inputRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ];

  // Replace handleKeyDown with handleKeyPress
  const handleKeyPress = (event: React.KeyboardEvent, submitFunction: () => void) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      submitFunction();
    }
  };

  // Add these state variables near the top of the component
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [isOtpLoading, setIsOtpLoading] = useState(false);
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Update resend code function
  const resendCode = async () => {
    console.log('Attempting to resend code, current cooldown:', cooldownTimeRef.current);
    if (cooldownTimeRef.current > 0 || isEmailLoading) {
      console.log('Resend prevented - cooldown active or already loading');
      return;
    }

    try {
      setIsEmailLoading(true);
      
      // Clear OTP inputs
      [0, 1, 2, 3, 4].forEach(index => {
        otpForm.setValue(`code.${index}`, '');
        if (inputRefs[index]?.current) {
          inputRefs[index].current.value = '';
        }
      });
      
      // Focus on first input
        if (inputRefs[0]?.current) {
          inputRefs[0].current.focus();
        }
      
      const emailData: EmailFormData = { email };
      await handleEmailSubmit(emailData);
      
      // Set cooldown after successful resend
      cooldownTimeRef.current = 120;
      forceUpdate();
    } finally {
      setIsEmailLoading(false);
    }
  };

  // Update handleEmailSubmit
  const handleEmailSubmit = async (data: EmailFormData) => {
    if (isEmailLoading) return; // Prevent multiple submissions
    
    setLoadingProgress(0);
    setIsLoading(true);
    setIsEmailLoading(true);
    
    const simulateProgress = () => {
      setLoadingProgress((prevProgress) => {
        const newProgress = prevProgress + 10;
        return newProgress > 90 ? 90 : newProgress;
      });
    };
  
    const progressInterval = setInterval(simulateProgress, 300);

    try {
      const csrfToken = useUserStore.getState().csrfToken;
  
      // Log the data to see what's being received
      console.log("Form data:", data);

      // Make sure email exists and is not empty
      if (!data.email?.trim()) {
        return;
      }

      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/reset-password/`,
        {
          params: {
            email: data.email
          },
          headers: {
            csrf_token: csrfToken
          }
        }
      );

      const result = response.data;
      
      showSuccessToast("OTP has been sent to your email", "passwordrecovery-toast");
      
      // Set email and process ID first
      setEmail(data.email);
      setProcessId(result.process_id);
      
      console.log('Setting initial cooldown time');
      cooldownTimeRef.current = 120; // 2 minutes
      forceUpdate();
      setStep("otp");

    } catch (error: any) {
      const result = error.response?.data;
      console.error("Failed to send reset password code");
      showErrorToast(result?.message || "Failed to send reset code", "passwordrecovery-toast");
      console.log(result);

    } finally {
      setIsEmailLoading(false);
      clearInterval(progressInterval);
      setIsLoading(false);
    }
  };

  const handleOTPSubmit = async () => {
    console.log('Starting OTP submission...'); // Debug log
    if (isOtpLoading) {
      console.warn('OTP submission already in progress'); // Debug log
      return; // Prevent multiple submissions
    }
    
    console.log('Setting loading states...'); // Debug log
    setIsLoading(true);
    setIsOtpLoading(true);
    setLoadingProgress(0);
    
    const simulateProgress = () => {
      console.log('Simulating progress...'); // Debug log
      setLoadingProgress((prevProgress) => {
        const newProgress = prevProgress + 10;
        console.log(`Progress update: ${newProgress}%`); // Debug log
        return newProgress > 90 ? 90 : newProgress;
      });
    };
  
    const progressInterval = setInterval(simulateProgress, 300);
    try {
      // Get the OTP values and combine them
      const otpValues = otpForm.getValues().code;
      const otpCode = otpValues.join('');

      // Validate that we have a complete OTP
      if (otpCode.length !== 5) {
        console.error("Invalid OTP length");
        return;
      }

      const csrfToken = useUserStore.getState().csrfToken;

      console.log("Submitting OTP:", {
        process_id: processId,
        code: otpCode
      });

      const response = await axios.put("/api/auth/reset-password", {
        process_id: processId,
        code: otpCode,
      }, {
        headers: {
          csrf_token: csrfToken,
        }
      });

      const result = response.data;

      if (result.success) {
        showSuccessToast("OTP verified successfully",  "passwordrecovery-toast");
        setTimeout(() => {
          setStep("password");
        }, 1000);
      } else {
        const errorData = response.data as { message: string };
        console.error("Failed to verify OTP:", errorData.message);
        
        // Clear OTP inputs
        const clearOTPInputs = () => {
          [0, 1, 2, 3, 4].forEach(index => {
            otpForm.setValue(`code.${index}`, '');
            if (inputRefs[index]?.current) {
              inputRefs[index].current.value = '';
            }
          });
          
          // Focus on first input
          if (inputRefs[0]?.current) {
            inputRefs[0].current.focus();
          }
        };

        // If OTP is expired, automatically resend and clear inputs
        if (errorData.message.toLowerCase().includes('expired')) {
          await resendCode();
        } else {
          showErrorToast(errorData.message, "passwordrecovery-toast");
        }
      }
    } catch (error: any) {
      const clearOTPInputs = () => {
        [0, 1, 2, 3, 4].forEach(index => {
          otpForm.setValue(`code.${index}`, '');
          if (inputRefs[index]?.current) {
            inputRefs[index].current.value = '';
          }
        });
      };
      const result = error.response?.data;
      console.error("Error:", result);
      if (result?.message?.toLowerCase().includes('expired')) {
        clearOTPInputs();
        resendCode();
      }
      showErrorToast(result?.message || "An unexpected error occurred. Please try again.", "verify-toast");
    } finally {
      setIsOtpLoading(false);
      clearInterval(progressInterval);
      setIsLoading(false);
    }
  };

  const handleSuccessfulPasswordChange = () => {
    showSuccessToast("Password changed successfully",  "passwordrecovery-toast");
    setTimeout(() => {
      if (invitationCode) {
        router.push(`/login?inv=${invitationCode}`);
      } else {
        router.push("/login");
      }
    }, 1000);
  };

  const handlePasswordSubmit: SubmitHandler<PasswordFormData> = async (data) => {
    if (isPasswordLoading) return; // Prevent multiple submissions
    
    setIsLoading(true);
    setIsPasswordLoading(true);
    setLoadingProgress(0);
    
    const simulateProgress = () => {
      setLoadingProgress((prevProgress) => {
        const newProgress = prevProgress + 10;
        return newProgress > 90 ? 90 : newProgress;
      });
    };
  
    const progressInterval = setInterval(simulateProgress, 300);
    try {
      console.log("Submitting password data:", {
        new_password: data.password,
        new_password_confirm: data.confirmPassword,
        process_id: processId
      });

      const csrfToken = useUserStore.getState().csrfToken;

      const response = await axios.post("/api/auth/change-password/", {
        new_password: data.password,
        new_password_confirm: data.confirmPassword,
        process_id: processId,
      });

      const result = response.data;

      if (result.success) {
        handleSuccessfulPasswordChange();
      } else {
        const errorData = response.data as { message: string };
        console.error("Failed to change password:", errorData.message);
      }
    } catch (error: any) {
      const result = error.response?.data;
      console.error("Error:", result);
      showErrorToast(result?.message || "An unexpected error occurred. Please try again.", "verify-toast");
    } finally {
      setIsPasswordLoading(false);
      clearInterval(progressInterval);
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues(prev => {
      const newValues = { ...prev, [name]: value };
      
      // Validate new password
      if (name === 'password') {
        setIsValidPassword(validatePassword(value));
      }
      
      // Check if passwords match
      setPasswordsMatch(newValues.password === newValues.confirmPassword);
      
      return newValues;
    });
  };

  // Add useEffect to sync step changes with parent
  useEffect(() => {
    const stepMapping = {
      email: 1,
      otp: 2,
      password: 3
    };
    onStepChange(stepMapping[step]);
  }, [step, onStepChange]);

  const backToLogin = () => {
    if (invitationCode) {
      router.push(`/login?inv=${invitationCode}`);
    } else {
      router.push('/login');
    }
  };

  // Let's also add a debug display for development
  // useEffect(() => {
  //   if (process.env.NODE_ENV === 'development') {
  //     console.log('Current cooldown state:', {
  //       cooldownTime,
  //       step,
  //       email,
  //       processId
  //     });
  //   }
  // }, [cooldownTime, step, email, processId]);

  const MobilePasswordRecoveryForm = ({
    step,
    emailForm,
    handleEmailSubmit,
    otpForm,
    handleOTPSubmit,
    passwordForm,
    handlePasswordSubmit,
    focusedField,
    setFocusedField,
    inputValues,
    handleInputChange,
    router,
    setStep,
    inputRefs,
    showPassword,
    setShowPassword,
    showConfirmPassword,
    setShowConfirmPassword,
    email,
    cooldownTimeRef,
    resendCode
  }: {
    step: "email" | "otp" | "password";
    emailForm: any;
    handleEmailSubmit: (data: EmailFormData) => Promise<void>;
    otpForm: any;
    handleOTPSubmit: () => Promise<void>;
    passwordForm: any;
    handlePasswordSubmit: SubmitHandler<PasswordFormData>;
    focusedField: string | null;
    setFocusedField: (field: string | null) => void;
    inputValues: {
      email: string;
      password: string;
      confirmPassword: string;
    };
    handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    router: any;
    setStep: (step: "email" | "otp" | "password") => void;
    inputRefs: React.RefObject<HTMLInputElement>[];
    showPassword: boolean;
    setShowPassword: (show: boolean) => void;
    showConfirmPassword: boolean;
    setShowConfirmPassword: (show: boolean) => void;
    email: string;
    cooldownTimeRef: React.RefObject<number>;
    resendCode: () => Promise<void>;
  }) => {
    return (
      <div className="w-screen h-dvh bg-white md:hidden  ">
        <div className="p-2 flex flex-col h-full w-full">

          <button 
            onClick={() => {
              if (step === "email") {
                router.push(invitationCode ? `/login?inv=${invitationCode}` : '/login');
              } else if (step === "otp" || step === "password") {
                setStep("email");
              }
            }} 
            className="flex flex-row items-start justify-start gap-2 mb-6 p-5"
          >
            <img
              src="/icons/arrow-left.svg"
              alt="Back"
              className="w-5 h-5"
            />
            <span className="text-[#0f0f0f]">Back</span>
          </button>

          {step === "email" && (
            <div className="flex flex-col h-full w-full p-6">
              <div className="w-full">
                <h1 className="text-[#2c3e50] text-[1.75rem] font-semibold">Password reset</h1>
                <p className="mt-4 text-[#0f0f0f] text-xs font-normal leading-[1.17rem]">
                  Enter your email, we will send you a Five-Digit Code so That you can change your password after the authentication proccess
                </p>
              </div>

              <form 
                className="flex-1"
                onKeyPress={(e) => handleKeyPress(e, () => emailForm.handleSubmit(handleEmailSubmit)())}
              >
                <div className="relative mt-8">
                  <input
                    type="email" 
                    inputMode="email"
                    autoComplete="off"
                    onKeyPress={(e) => handleKeyPress(e, () => emailForm.handleSubmit(handleEmailSubmit)())}
                    {...emailForm.register("email")}
                    className={`w-full h-17 px-4 rounded-lg border border-[#cccccc]/80 ${
                      focusedField === 'email' || emailForm.formState.errors.email || inputValues.email || emailForm.register("email") ? '' : ''
                    }`}
                    onFocus={() => setFocusedField('email')}
                    onBlur={() => setFocusedField(null)}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      emailForm.setValue('email', e.target.value);
                    }}
                  />
                  <label className={`absolute left-4 transition-all duration-200 ${
                    focusedField === 'email' || emailForm.formState.errors.email || inputValues.email || emailForm.register("email")
                      ? '-top-2 text-xs bg-white px-1'
                      : 'top-5.5'
                  } text-[#0f0f0f] text-sm font-normal`}>
                    Email
                  </label>
                </div>
              </form>

              <div className="mt-auto mb-6 w-full flex justify-center">
                <button
                  className={`py-4 md:px-32 w-full bg-[#263645] md:text-xl text-nowrap text-md px-16 rounded-md text-white hover:bg-[#DADDE1] hover:text-[#263645] font-bold transition-all duration-200 relative overflow-hidden ${isEmailLoading ? 'cursor-wait' : ''}`}
                  onClick={emailForm.handleSubmit(handleEmailSubmit)}
                  disabled={isEmailLoading}
                >
                  <span className="relative z-10 flex items-center justify-center">
                    {isEmailLoading && (
                      <div className="inline-block w-5 h-5 mr-3 align-middle">
                        <svg className="animate-spin" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
                        </svg>
                      </div>
                    )}
                    {isEmailLoading ? "Sending..." : "Send Email"}
                  </span>
                </button>
              </div>
            </div>
          )}

          {step === "otp" && (
            <div className="flex flex-col h-full w-full p-6">
              <div className="mb-10.5">
                <h1 className="text-[#2c3e50] text-[1.75rem] font-semibold">Email Verification</h1>
                <p className="text-[#0f0f0f] text-base mt-2.5">
                  Enter the code sent to {email}.
                </p>
              </div>

              <form 
                className="flex-1"
                onSubmit={(e) => {
                  e.preventDefault();
                  handleOTPSubmit();
                }}
              >
                <OTPInput
                  length={5}
                  disabled={isOtpLoading}
                  error={!!otpForm.formState.errors.code}
                  onComplete={(value) => {
                    otpForm.setValue('code', value.split(''), { shouldValidate: true });
                    handleOTPSubmit();
                  }}
                />
                {otpForm.formState.errors.code && (
                  <p className="text-red-500 text-[0.688rem] mb-4">All digits are required</p>
                )}
              </form>

              <div className="mt-auto mb-6 w-full flex flex-col items-center">
                <button
                  className={`py-4 md:px-32 w-full bg-[#263645] md:text-xl text-nowrap text-md px-16 rounded-md text-white hover:bg-[#DADDE1] hover:text-[#263645] font-bold transition-all duration-200 relative overflow-hidden ${isOtpLoading ? 'cursor-wait' : ''}`}
                  onClick={handleOTPSubmit}
                  disabled={otpForm.getValues().code.some((value: string) => !value) || isOtpLoading}
                >
                  <span className="relative z-10 flex items-center justify-center">
                    {isOtpLoading && (
                      <div className="inline-block w-5 h-5 mr-3 align-middle">
                        <svg className="animate-spin" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
                        </svg>
                      </div>
                    )}
                    {isOtpLoading ? "Verifying..." : "Verify"}
                  </span>
                </button>

                <CooldownTimer cooldownTimeRef={cooldownTimeRef} resendCode={resendCode} />
              </div>
            </div>
          )}

          {step === "password" && (
            <div className="flex flex-col h-full w-full p-6">
              <div className="mb-8 w-full">
                <h1 className="text-[#2c3e50] text-[1.75rem] font-semibold">Password Recovery</h1>
              </div>

              <div className="text-[#2c3e50] text-[0.938rem] font-medium">Enter your new password</div>

              <form 
                className="flex-1 mt-8"
                onKeyPress={(e) => handleKeyPress(e, () => passwordForm.handleSubmit(handlePasswordSubmit)())}
              >
                <div className="space-y-4">
                  <div className="relative flex flex-col w-full">
                    <div className="w-full flex flex-row justify-center relative">
                      <input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        onKeyPress={(e) => handleKeyPress(e, () => emailForm.handleSubmit(handleEmailSubmit)())}
                        className={`w-full relative px-3 py-4 md:py-6 border ${
                          !isValidPassword && inputValues.password ? 'border-red-500' : 'border-gray-300'
                        } rounded-lg pr-10 text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                          focusedField === 'password' || inputValues.password ? 'pt-6' : ''
                        }`}
                        {...passwordForm.register("password")}
                        onChange={(e) => {
                          handleInputChange(e);
                          passwordForm.setValue('password', e.target.value);
                        }}
                        onFocus={() => setFocusedField('password')}
                        onBlur={() => setFocusedField(null)}
                      />
                      <label
                        htmlFor="password"
                        className={`absolute left-3 transition-all duration-200 ${
                          focusedField === 'password' || inputValues.password
                            ? '-top-1 text-xs px-2 text-gray-500 bg-white'
                            : 'top-1/2 -translate-y-1/2 text-gray-400'
                        }`}
                      >
                        New Password
                      </label>
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute top-1/2 -translate-y-1/2 right-0 pr-3 flex items-center"
                      >
                        <img
                          src={showPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                          alt={showPassword ? "Hide password" : "Show password"}
                          className="w-4 h-4 md:w-5 md:h-5"
                        />
                      </button>
                    </div>
                    {inputValues.password && !isValidPassword && (
                      <p className="text-red-500 text-xs mt-1">
                        Password must contain at least one uppercase letter, one lowercase letter, and one special character
                      </p>
                    )}
                  </div>

                  <div className="relative flex flex-col w-full">
                    <div className="w-full flex flex-row justify-center relative">
                      <input
                        id="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        onKeyPress={(e) => handleKeyPress(e, () => passwordForm.handleSubmit(handlePasswordSubmit)())}
                        className={`w-full px-3 py-4 md:py-6 border ${
                          !passwordsMatch && inputValues.confirmPassword ? 'border-red-500' : 'border-gray-300'
                        } rounded-lg pr-10 text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                          focusedField === 'confirmPassword' || inputValues.confirmPassword ? 'pt-6' : ''
                        }`}
                        {...passwordForm.register("confirmPassword")}
                        onChange={(e) => {
                          handleInputChange(e);
                          passwordForm.setValue('confirmPassword', e.target.value);
                        }}
                        onFocus={() => setFocusedField('confirmPassword')}
                        onBlur={() => setFocusedField(null)}
                      />
                      <label
                        htmlFor="confirmPassword"
                        className={`absolute left-3 transition-all duration-200 ${
                          focusedField === 'confirmPassword' || inputValues.confirmPassword
                            ? '-top-1 text-xs px-2 text-gray-500 bg-white'
                            : 'top-1/2 -translate-y-1/2 text-gray-400'
                        }`}
                      >
                        Confirm New Password
                      </label>
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute top-1/2 -translate-y-1/2 right-0 pr-3 flex items-center"
                      >
                        <img
                          src={showConfirmPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                          alt={showConfirmPassword ? "Hide password" : "Show password"}
                          className="w-4 h-4 md:w-5 md:h-5"
                        />
                      </button>
                    </div>
                    {inputValues.confirmPassword && !passwordsMatch && (
                      <p className="text-red-500 text-xs mt-1">
                        Passwords do not match
                      </p>
                    )}
                  </div>
                </div>
              </form>

              <div className="mt-auto mb-6">
                <button
                  className={`py-4 md:px-32 w-full bg-[#263645] md:text-xl text-nowrap text-md px-16 rounded-md text-white hover:bg-[#DADDE1] hover:text-[#263645] font-bold transition-all duration-200 relative overflow-hidden ${isPasswordLoading ? 'cursor-wait' : ''}`}
                  onClick={passwordForm.handleSubmit(handlePasswordSubmit)}
                  disabled={
                    !passwordForm.getValues().password || 
                    !passwordForm.getValues().confirmPassword ||
                    !isValidPassword ||
                    !passwordsMatch ||
                    passwordForm.formState.isSubmitting ||
                    isPasswordLoading
                  }
                  loading={isPasswordLoading}
                  loadingProgress={loadingProgress}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Mobile version */}
      <div className="md:hidden">
        <MobilePasswordRecoveryForm
          step={step}
          emailForm={emailForm}
          handleEmailSubmit={handleEmailSubmit}
          otpForm={otpForm}
          handleOTPSubmit={handleOTPSubmit}
          passwordForm={passwordForm}
          handlePasswordSubmit={handlePasswordSubmit}
          focusedField={focusedField}
          setFocusedField={setFocusedField}
          inputValues={inputValues}
          handleInputChange={handleInputChange}
          router={router}
          setStep={setStep}
          inputRefs={inputRefs}
          showPassword={showPassword}
          setShowPassword={setShowPassword}
          showConfirmPassword={showConfirmPassword}
          setShowConfirmPassword={setShowConfirmPassword}
          email={email}
          cooldownTimeRef={cooldownTimeRef}
          resendCode={resendCode}
        />
      </div>

      {/* Desktop version - existing code */}
      <div className="hidden md:flex flex-col h-full w-full justify-between">
        {/* Top section - 10% */}
        <div className="h-[10%] flex flex-col justify-start items-start p-4 md:p-10">
        {step === "email"  ? (
          <>
          <button onClick={backToLogin} className="flex flex-row items-start justify-start gap-2">
          <img
            src="/icons/arrow-left.svg"
            alt="Back"
            className="w-5 h-5 md:w-6 md:h-6"
          />
          Return to login page
          
        </button>
          </>
          ) : null}

{step === "otp"  ? (
          <>
          <button onClick={() => setStep("email")} className="flex flex-row items-start justify-start gap-2">
          <img
            src="/icons/arrow-left.svg"
            alt="Back"
            className="w-5 h-5 md:w-6 md:h-6"
          />
          Return to change password page
          
        </button>
          </>
          ) : null}

{step === "password"  ? (
          <>
          <button onClick={() => setStep("otp")} className="flex flex-row items-start justify-start gap-2">
          <img
            src="/icons/arrow-left.svg"
            alt="Back"
            className="w-5 h-5 md:w-6 md:h-6"
          />
          Return to change password page
        </button>
          </>
          ) : null}
              

        
        
        
        </div>

        {/* Middle section - 80% */}
        <div className="h-[60%] p-4 md:p-8 overflow-y-auto flex flex-col justify-center">
          {step === "email" && (
            <>
              <div className="text-login w-full">
                <div className="text mb-6 md:mb-8">
                  <h1 className="text-3xl md:text-4xl font-bold mb-2">
                    Password Reset
                  </h1>
                  <p className="text-gray-600 text-sm md:text-base">
                    Enter your email to receive a reset code
                  </p>
                </div>
              </div>

              <form 
                className="space-y-4 md:space-y-6 w-full " 
                onKeyPress={(e) => handleKeyPress(e, () => emailForm.handleSubmit(handleEmailSubmit)())}
              >
                <div className="relative mb-3 md:mb-4">
                  <input
                    id="email"
                    {...emailForm.register("email", { required: true })}
                    type="email"
                    name="email"
                    className={`w-full px-3 py-4 md:py-6 border border-gray-300 rounded-lg text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                      focusedField === 'email' || emailForm.formState.errors.email || inputValues.email ? 'pt-6' : ''
                    }`}
                    onFocus={() => setFocusedField('email')}
                    onBlur={() => setFocusedField(null)}
                    onChange={(e) => {
                      handleInputChange(e);
                      emailForm.setValue('email', e.target.value);
                    }}
                  />
                  <label
                    htmlFor="email"
                    className={`absolute left-3 transition-all duration-200 ${
                      focusedField === 'email' || emailForm.formState.errors.email || inputValues.email
                        ? '-top-1 text-xs px-2 text-gray-500 border-3 bg-white'
                        : 'top-1/2 -translate-y-1/2 text-gray-400'
                    }`}
                  >
                    Email
                  </label>
                  {emailForm.formState.errors.email && (
                    <p className="text-red-500 text-xs md:text-sm mt-1">
                      {emailForm.formState.errors.email.message}
                    </p>
                  )}
                </div>
              </form>
            </>
          )}

          {step === "otp" && (
            <>
              <div className="text-login w-full">
                <div className="text mb-6 md:mb-8">
                  <h1 className="text-3xl md:text-4xl font-bold mb-2">
                    Enter OTP
                  </h1>
                  <p className="text-gray-600 text-sm md:text-base">
                    Enter the code sent to {email}
                  </p>
                </div>
              </div>

              <form 
                className="space-y-4 md:space-y-6 w-full md:w-4/5"
                onSubmit={(e) => {
                  e.preventDefault();
                  handleOTPSubmit();
                }}
              >
                <OTPInput
                  length={5}
                  disabled={isOtpLoading}
                  error={!!otpForm.formState.errors.code}
                  onComplete={(value) => {
                    otpForm.setValue('code', value.split(''), { shouldValidate: true });
                    handleOTPSubmit();
                  }}
                />
              </form>
            </>
          )}

          {step === "password" && (
            <>
              <div className="text-login w-full">
                <div className="text mb-6 md:mb-8">
                  <h1 className="text-3xl md:text-4xl font-bold mb-2">
                    New Password
                  </h1>
                  <p className="text-gray-600 text-sm md:text-base">
                    Enter your new password
                  </p>
                </div>
              </div>

              <form 
                className="space-y-4 md:space-y-6 w-full md:w-4/5"
                onKeyPress={(e) => handleKeyPress(e, () => passwordForm.handleSubmit(handlePasswordSubmit)())}
              >
                <div className="relative mb-3 md:mb-4 w-full">
                <div className="w-full flex flex-row justify-center relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    className={`w-full relative px-3 py-4 md:py-6 border ${
                      !isValidPassword && inputValues.password ? 'border-red-500' : 'border-gray-300'
                    } rounded-lg pr-10 text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                      focusedField === 'password' || inputValues.password ? 'pt-6' : ''
                    }`}
                    {...passwordForm.register("password")}
                    onChange={(e) => {
                      handleInputChange(e);
                      passwordForm.setValue('password', e.target.value);
                    }}
                    onFocus={() => setFocusedField('password')}
                    onBlur={() => setFocusedField(null)}
                  />
                  <label
                    htmlFor="password"
                    className={`absolute left-3 transition-all duration-200 ${
                      focusedField === 'password' || inputValues.password
                        ? '-top-1 text-xs px-2 text-gray-500 bg-white'
                        : 'top-1/2 -translate-y-1/2 text-gray-400'
                    }`}
                  >
                    New Password
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute top-1/2 -translate-y-1/2 right-0 pr-3 flex items-center"
                  >
                    <img
                      src={showPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                      alt={showPassword ? "Hide password" : "Show password"}
                      className="w-4 h-4 md:w-5 md:h-5"
                    />
                  </button>
                  </div>
                  {inputValues.password && !isValidPassword && (
                    <p className="text-red-500 text-xs mt-1">
                      Password must contain at least one uppercase letter, one lowercase letter, and one special character
                    </p>
                  )}
                 
                </div>
                <div className="relative mb-3 md:mb-4">
                <div className="w-full flex flex-row justify-center relative">

                  <input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    className={`w-full px-3 py-4 md:py-6 border ${
                      !passwordsMatch && inputValues.confirmPassword ? 'border-red-500' : 'border-gray-300'
                    } rounded-lg pr-10 text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                      focusedField === 'confirmPassword' || inputValues.confirmPassword ? 'pt-6' : ''
                    }`}
                    {...passwordForm.register("confirmPassword")}
                    onChange={(e) => {
                      handleInputChange(e);
                      passwordForm.setValue('confirmPassword', e.target.value);
                    }}
                    onFocus={() => setFocusedField('confirmPassword')}
                    onBlur={() => setFocusedField(null)}
                  />
                  <label
                    htmlFor="confirmPassword"
                    className={`absolute left-3 transition-all duration-200 ${
                      focusedField === 'confirmPassword' || inputValues.confirmPassword
                        ? '-top-1 text-xs px-2 text-gray-500 bg-white'
                        : 'top-1/2 -translate-y-1/2 text-gray-400'
                    }`}
                  >
                    Confirm New Password
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute top-1/2 -translate-y-1/2 right-0 pr-3 flex items-center"
                  >
                    <img
                      src={showConfirmPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                      alt={showConfirmPassword ? "Hide password" : "Show password"}
                      className="w-4 h-4 md:w-5 md:h-5"
                    />
                  </button>
                  </div>
                  {inputValues.confirmPassword && !passwordsMatch && (
                    <p className="text-red-500 text-xs mt-1">
                      Passwords do not match
                    </p>
                  )}
                </div>
              </form>
            </>
          )}
        </div>

        {/* Bottom section - 10% */}
        <div className="h-[10%] p-4 md:p-8 flex flex-col justify-center">
          <div className="btn flex justify-center items-center flex-col gap-3 md:gap-4">
            {step === "email" && (
              <AuthBtn
                text={isEmailLoading ? "Sending..." : "Reset"}
                style="default"
                onClick={emailForm.handleSubmit(handleEmailSubmit)}
                disabled={isEmailLoading}
                isLoading={isEmailLoading}
                loadingProgress={loadingProgress}
              />
            )}
            {step === "otp" && (
                  <>
                  <AuthBtn
                    text={isOtpLoading ? "Verifying..." : "Verify OTP"}
                    style="default"
                    onClick={handleOTPSubmit}
                    disabled={otpForm.getValues().code.some((value: string) => !value) || isOtpLoading}
                    isLoading={isOtpLoading}
                    loadingProgress={isOtpLoading}
                  />

                  <CooldownTimer cooldownTimeRef={cooldownTimeRef} resendCode={resendCode} />
                </>
            )}
            {step === "password" && (
              <AuthBtn
                text={isPasswordLoading ? "Resetting Password..." : "Reset Password"}
                style="default"
                onClick={passwordForm.handleSubmit(handlePasswordSubmit)}
                disabled={
                  !passwordForm.getValues().password || 
                  !passwordForm.getValues().confirmPassword ||
                  !isValidPassword ||
                  !passwordsMatch ||
                  passwordForm.formState.isSubmitting ||
                  isPasswordLoading
                }
                loading={isPasswordLoading}
                loadingProgress={loadingProgress}
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
}

