import { useMemo } from "react";

interface AppleBtnProps {
  style?: "default" | "disabled" | "active";
  inv?: string;
  onClick?: () => void;
}

const AppleBtn: React.FC<AppleBtnProps> = ({ style = "default", inv, ...props }) => {
  const twStyles = useMemo(() => {
    if (style === "default") {
      return " py-4 px-4 bg-white rounded-lg text-[#151515] border border hover:bg-[#DADDE1] font-bold transition-all duration-200 ";
    } else if (style === "disabled") {
      return " py-4 px-4 bg-[#DADDE1] rounded-lg text-[#151515] border border  font-bold transition-all duration-200 grayscale";
    } else if (style === "active") {
      return " py-4 px-4 bg-white shadow-[inset_1px_1px_4px_rgba(0,0,0,0.2)] rounded-lg text-[#151515] border border hover:bg-[#DADDE1] font-bold transition-all duration-200 ";
    }
  }, [style]);

  return (
    <button
      className={`${twStyles} flex flex-row items-center justify-center`}
      disabled={style === "disabled"}
      {...props}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 384 512"
        width="18"
        height="18"
        className="mr-2 inline-block"
      >
        <path
          fill="currentColor"
          d="M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z"
        />
      </svg>
      <span className="text-sm font-normal">Continue with Apple</span>
    </button>
  );
};

export default AppleBtn;
