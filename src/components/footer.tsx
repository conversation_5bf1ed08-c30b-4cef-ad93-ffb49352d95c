"use client";

import React, { useRef, memo } from "react";
import { FiInstagram } from "react-icons/fi";
import { motion, useInView } from "framer-motion";
import Link from "next/link";

// Wrap Next.js Link with Framer Motion
const MotionLink = motion(Link);

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 50 },
  visible: { opacity: 1, y: 0 },
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: { opacity: 1, scale: 1 },
};

const staggerContainer = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const Footer = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, rootMargin: "-100px" });

  // SEO-friendly, explicit labels + hrefs
  const websiteLinks: { label: string; href: string; title?: string }[] = [
    { label: "Features", href: "/#features", title: "Explore key features" },
    { label: "Trust us", href: "/#trust-us", title: "Brands that trust us" },
    {
      label: "Why Business Insight?",
      href: "/#why-business-insight",
      title: "Learn why Business Insight",
    },
    { label: "Plans", href: "/#plans", title: "View pricing plans" },
    { label: "Privacy Policy", href: "/privacy", title: "Read our privacy policy" },
  ];

  const userLinks: { label: string; href: string; title?: string }[] = [
    { label: "Log In", href: "/login", title: "Log in to your account" },
    { label: "Sign Up", href: "/signup", title: "Create a new account" },
    {
      label: "Reset Password",
      href: "/password-recovery",
      title: "Recover your account password",
    },
  ];

  return (
    <motion.footer
      ref={ref}
      className="w-full max-w-7xl mx-auto bg-white py-6 sm:py-8 relative overflow-hidden"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={staggerContainer}
    >
      {/* Background SVG */}
      <motion.div
        className="absolute inset-0 w-full h-full opacity-90"
        style={{
          backgroundImage: "url(/footer-bg.svg)",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          backgroundSize: "cover",
          zIndex: 0,
        }}
        variants={fadeInUp}
        transition={{ duration: 0.6, ease: "easeOut" }}
      />

      {/* Content */}
      <div className="w-full mx-auto px-3 sm:px-4 lg:px-8 relative z-10">
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-8">
          {/* Website Links */}
          <motion.div
            className="text-left"
            variants={fadeInUp}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.1 }}
          >
            <h3 className="text-sm sm:text-base font-semibold text-slate-950 tracking-wide mb-2 sm:mb-4">
              Website
            </h3>
            <nav aria-label="Website">
              <ul className="space-y-1 sm:space-y-2">
                {websiteLinks.map(({ label, href, title }, index) => (
                  <motion.li
                    key={label}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: 0.2 + index * 0.05 }}
                  >
                    <MotionLink
                      href={href}
                      title={title}
                      className="text-sm sm:text-base text-slate-950 hover:text-slate-700 inline-block"
                      whileHover={{ x: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {label}
                    </MotionLink>
                  </motion.li>
                ))}
              </ul>
            </nav>
          </motion.div>

          {/* User Pages */}
          <motion.div
            className="text-left"
            variants={fadeInUp}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          >
            <h3 className="text-sm sm:text-base font-semibold text-slate-950 tracking-wide mb-2 sm:mb-4">
              User Pages
            </h3>
            <nav aria-label="User Pages">
              <ul className="space-y-1 sm:space-y-2">
                {userLinks.map(({ label, href, title }, index) => (
                  <motion.li
                    key={label}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: 0.3 + index * 0.05 }}
                  >
                    <MotionLink
                      href={href}
                      title={title}
                      className="text-sm sm:text-base text-slate-950 hover:text-slate-700 inline-block"
                      whileHover={{ x: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {label}
                    </MotionLink>
                  </motion.li>
                ))}
              </ul>
            </nav>
          </motion.div>

          {/* Newsletter and Social */}
          <motion.div
            className="col-span-2 md:col-span-1 text-center md:text-left"
            variants={fadeInUp}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.3 }}
          >
            <div className="mb-6 sm:mb-8">
              <div className="footer-logo flex items-center justify-center sm:justify-start gap-2 mb-3 sm:mb-4">
                <img
                  src="/icons/logo.svg"
                  alt="Business Insight Logo"
                  className="w-8 sm:w-12 h-auto"
                />
                <span className="text-[#2c3e50] text-base sm:text-[1.375rem] font-semibold">
                  Business Insight
                </span>
              </div>
              <div className="newsletter-section">
                <p className="text-xs sm:text-sm text-slate-950 mb-3 sm:mb-4">
                  Join our newsletter to stay up to date on features and
                  releases.
                </p>
                <div className="relative">
                  <input
                    type="email"
                    placeholder="Enter your Email..."
                    className="w-full px-3 sm:px-4 py-2 sm:py-3 bg-white rounded-xl sm:rounded-2xl border border-slate-200 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-slate-300 focus:border-transparent transition-all duration-200"
                  />
                  <button className="absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 py-1.5 sm:py-2 px-3 sm:px-4 bg-[#2c3e50] text-white rounded-lg text-xs sm:text-sm hover:bg-[#3d5a76] transition-colors">
                    Submit
                  </button>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xs sm:text-sm font-semibold text-slate-950 tracking-wide mb-3 sm:mb-4">
                Follow us
              </h3>
              <div className="flex justify-center sm:justify-start gap-1 sm:gap-2">
                {[
                  { name: "twitter", url: "https://x.com/AIBinsight" },
                  {
                    name: "facebook",
                    url: "https://www.facebook.com/profile.php?id=61570911104567",
                  },
                  {
                    name: "instagram",
                    url: "https://www.instagram.com/businessinsight.ai",
                  },
                  {
                    name: "linkedin",
                    url: "https://www.linkedin.com/in/aibusinessinsight/",
                  },
                ].map(({ name, url }) => {
                  const isInstagram = name === "instagram";
                  const Icon = isInstagram ? FiInstagram : undefined;
                  return (
                    <MotionLink
                      key={name}
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="social-icon hover:bg-slate-100 rounded-lg p-1 transition-colors inline-flex items-center justify-center"
                      whileTap={{ scale: 0.95 }}
                    >
                      {isInstagram ? (
                        <span className="w-5 h-5 flex items-center justify-center text-black">
                          <FiInstagram
                            className="w-full h-full"
                            aria-hidden="true"
                          />
                        </span>
                      ) : (
                        <img
                          src={`/${name}.svg`}
                          alt={name}
                          className="w-6 h-6 sm:w-8 sm:h-8"
                        />
                      )}
                    </MotionLink>
                  );
                })}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Footer Bottom */}
        <motion.div
          className="footer-bottom mt-8 sm:mt-16 pt-4 sm:pt-8 border-t border-slate-100"
          variants={fadeInUp}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.4 }}
        >
          <div className="flex flex-col md:flex-row justify-between items-center gap-2 sm:gap-4 text-center md:text-left">
            <p className="text-[10px] sm:text-xs text-slate-950">
              © Business Insight Ltd. All rights reserved.
            </p>
          </div>
        </motion.div>
      </div>
    </motion.footer>
  );
});

export default Footer;
