import React from "react";
import {
  ResponsiveContainer,
  LineChart,
  Line,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  BarChart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
} from "recharts";

interface AddWidgetPanelProps {
  onAdd: (widgetType: string) => void;
  onClose: () => void;
}

const AddWidgetPanel: React.FC<AddWidgetPanelProps> = ({ onAdd, onClose }) => {
  const widgetTypes = [
    "Gender",
    "Total Followers",
    "Account Reach",
    "Account Engaged",
    "Account Reach / Cities",
    "Account Interactions / Reel Interactions",
    "Most Active Time (Daily)",
    "Most Active Time (Weekly)",
    "Overview of Followers",
    "Account Interactions / Top Reels",
    "Account Interactions / Top Post",
    "Account Reach / Top Post",
    "Account Reach / Content Type",
    "Follower Distribution",
  ];

  const renderWidgetPreview = (type: string) => {
    switch (type) {
      case "Gender":
        return (
          <div className="w-full h-24 flex flex-col justify-center">
            <div className="flex justify-between mb-1">
              <span>Female</span>
              <span>50%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-pink-400 h-2.5 rounded-full"
                style={{ width: "50%" }}
              ></div>
            </div>
            <div className="flex justify-between mt-1">
              <span>Male</span>
              <span>50%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-400 h-2.5 rounded-full"
                style={{ width: "50%" }}
              ></div>
            </div>
          </div>
        );
      case "Total Followers":
      case "Account Reach":
      case "Account Engaged":
        return (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="flex flex-row items-center">
              <span className="text-2xl font-bold">26.8k</span>
              <span className="text-green-500 text-xs ml-2">+420%</span>
            </div>
            <div className="flex items-center mt-2">
              <span className="text-gray-500 text-xs">{type}</span>
            </div>
          </div>
        );
      case "Overview of Followers":
        return (
          <ResponsiveContainer width="100%" height={100}>
            <LineChart
              data={[
                { name: "Mon", value: 10 },
                { name: "Tue", value: 15 },
                { name: "Wed", value: 20 },
                { name: "Thu", value: 25 },
                { name: "Fri", value: 30 },
              ]}
            >
              <XAxis dataKey="name" />
              <YAxis hide />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#1565C0"
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        );
      case "Most Active Time (Daily)":
      case "Most Active Time (Weekly)":
        return (
          <ResponsiveContainer width="100%" height={100}>
            <BarChart
              data={[
                { name: "12p", value: 120 },
                { name: "3p", value: 98 },
                { name: "6p", value: 86 },
                { name: "9p", value: 99 },
              ]}
            >
              <XAxis dataKey="name" />
              <YAxis hide />
              <Bar dataKey="value" fill="#1565c0" />
            </BarChart>
          </ResponsiveContainer>
        );
      case "Follower Distribution":
        return (
          <ResponsiveContainer width="100%" height={100}>
            <PieChart>
              <Pie
                data={[
                  { name: "Non-follower", value: 75 },
                  { name: "Follower", value: 25 },
                ]}
                cx="50%"
                cy="50%"
                innerRadius={30}
                outerRadius={40}
                fill="#8884d8"
                dataKey="value"
              >
                <Cell fill="#1565C0" />
                <Cell fill="#D7EAFF" />
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        );
      case "Account Reach / Cities":
        return (
          <div className="w-full h-24 flex flex-col justify-center">
            <div className="flex justify-between mb-1">
              <span>Tehran</span>
              <span>2628</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-500 h-2.5 rounded-full"
                style={{ width: "100%" }}
              ></div>
            </div>
            <div className="flex justify-between mt-1">
              <span>Tabriz</span>
              <span>1655</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-500 h-2.5 rounded-full"
                style={{ width: "63%" }}
              ></div>
            </div>
          </div>
        );
      case "Account Interactions / Reel Interactions":
      case "Account Interactions / Top Reels":
      case "Account Interactions / Top Post":
        return (
          <div className="w-full h-24 flex flex-col justify-center">
            <div className="flex justify-between mb-1">
              <span>Likes</span>
              <span>2628</span>
            </div>
            <div className="flex justify-between mb-1">
              <span>Comments</span>
              <span>1655</span>
            </div>
            <div className="flex justify-between mb-1">
              <span>Saves</span>
              <span>1209</span>
            </div>
            <div className="flex justify-between">
              <span>Shares</span>
              <span>103</span>
            </div>
          </div>
        );
      case "Account Reach / Top Post":
      case "Account Reach / Content Type":
        return (
          <div className="w-full h-24 flex flex-col justify-center">
            <div className="flex justify-between mb-1">
              <span>Posts</span>
              <span>2628</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
              <div
                className="bg-blue-500 h-2.5 rounded-full"
                style={{ width: "100%" }}
              ></div>
            </div>
            <div className="flex justify-between mb-1">
              <span>Reels</span>
              <span>1655</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-500 h-2.5 rounded-full"
                style={{ width: "63%" }}
              ></div>
            </div>
          </div>
        );
      default:
        return <div className="text-center">Preview not available</div>;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 ">
      <div className="bg-white  rounded-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto relative mx-4 sm:mx-auto no-scrollbar">
        <div className="sticky top-0 z-10 mb-3 sm:mb-4 p-4">
          <div className="absolute inset-0 bg-white bg-opacity-70 backdrop-blur-sm"></div>
          <div className="relative flex justify-between items-center">
            <h2 className="text-lg sm:text-xl font-bold">Add Widget</h2>
            <button
              className="text-gray-500 hover:text-gray-700"
              onClick={onClose}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 sm:h-6 sm:w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
        <div className="flex flex-col gap-3 sm:gap-4 p-4 sm:p-6 animate-bounceUp">
          {widgetTypes.map((type) => (
            <button
              key={type}
              className="bg-gray-100 p-3 sm:p-4 rounded-lg w-full"
              onClick={() => onAdd(type)}
            >
              <h3 className="text-xs sm:text-sm font-semibold mb-1 sm:mb-2">
                {type}
              </h3>
              <div className="h-28 sm:h-24 mb-1 sm:mb-2">
                {renderWidgetPreview(type)}
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AddWidgetPanel;
