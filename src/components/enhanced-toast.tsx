"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import toast from 'react-hot-toast';
// No external icon imports needed - using custom SVG icons

interface ToastProps {
  message: string;
  subMessage?: string;
  type: 'success' | 'error' | 'warning' | 'info';
  visible: boolean;
  onDismiss?: () => void;
  duration?: number;
}

// Custom SVG Icons
const SuccessIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="currentColor" fillOpacity="0.1"/>
    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5"/>
    <path
      d="M8.5 12.5L10.5 14.5L15.5 9.5"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const ErrorIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="currentColor" fillOpacity="0.1"/>
    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5"/>
    <path
      d="M15 9L9 15M9 9L15 15"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const WarningIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none">
    <path
      d="M12 2L22 20H2L12 2Z"
      fill="currentColor"
      fillOpacity="0.1"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
    <path
      d="M12 9V13M12 17H12.01"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const InfoIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="currentColor" fillOpacity="0.1"/>
    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5"/>
    <path
      d="M12 16V12M12 8H12.01"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const CloseIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none">
    {/* Outer ring with subtle gradient effect */}
    <circle
      cx="12"
      cy="12"
      r="10"
      fill="currentColor"
      fillOpacity="0.08"
    />
    <circle
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="1"
      strokeOpacity="0.2"
      fill="none"
    />

    {/* Inner close symbol with enhanced design */}
    <g transform="translate(12, 12)">
      <path
        d="M-3 -3L3 3M3 -3L-3 3"
        stroke="currentColor"
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>

    {/* Subtle inner highlight */}
    <circle
      cx="12"
      cy="12"
      r="8"
      stroke="currentColor"
      strokeWidth="0.5"
      strokeOpacity="0.1"
      fill="none"
    />
  </svg>
);

const toastConfig = {
  success: {
    icon: SuccessIcon,
    bgColor: 'bg-linear-to-br from-green-50 via-emerald-50 to-green-50',
    borderColor: 'border-green-300/60',
    iconColor: 'text-green-600',
    titleColor: 'text-green-900',
    messageColor: 'text-green-700',
    progressColor: 'bg-linear-to-r from-green-500 to-emerald-500',
    shadowColor: 'shadow-lg shadow-green-200/40',
    ringColor: 'ring-green-300/30'
  },
  error: {
    icon: ErrorIcon,
    bgColor: 'bg-linear-to-br from-red-50 via-rose-50 to-red-50',
    borderColor: 'border-red-300/60',
    iconColor: 'text-red-600',
    titleColor: 'text-red-900',
    messageColor: 'text-red-700',
    progressColor: 'bg-linear-to-r from-red-500 to-rose-500',
    shadowColor: 'shadow-lg shadow-red-200/40',
    ringColor: 'ring-red-300/30'
  },
  warning: {
    icon: WarningIcon,
    bgColor: 'bg-linear-to-br from-amber-50 via-yellow-50 to-amber-50',
    borderColor: 'border-amber-300/60',
    iconColor: 'text-amber-600',
    titleColor: 'text-amber-900',
    messageColor: 'text-amber-700',
    progressColor: 'bg-linear-to-r from-amber-500 to-yellow-500',
    shadowColor: 'shadow-lg shadow-amber-200/40',
    ringColor: 'ring-amber-300/30'
  },
  info: {
    icon: InfoIcon,
    bgColor: 'bg-linear-to-br from-blue-50 via-sky-50 to-blue-50',
    borderColor: 'border-blue-300/60',
    iconColor: 'text-blue-600',
    titleColor: 'text-blue-900',
    messageColor: 'text-blue-700',
    progressColor: 'bg-linear-to-r from-blue-500 to-sky-500',
    shadowColor: 'shadow-lg shadow-blue-200/40',
    ringColor: 'ring-blue-300/30'
  }
};

const EnhancedToast: React.FC<ToastProps> = ({ 
  message, 
  subMessage, 
  type, 
  visible, 
  onDismiss,
  duration = 4000 
}) => {
  const config = toastConfig[type];
  const Icon = config.icon;

  const toastVariants = {
    hidden: {
      x: typeof window !== 'undefined' && window.innerWidth < 640 ? 300 : 400,
      opacity: 0,
      scale: 0.9,
      rotateY: typeof window !== 'undefined' && window.innerWidth < 640 ? 20 : 45
    },
    visible: {
      x: 0,
      opacity: 1,
      scale: 1,
      rotateY: 0,
      transition: {
        type: "spring",
        stiffness: typeof window !== 'undefined' && window.innerWidth < 640 ? 400 : 300,
        damping: 25,
        mass: 0.8
      }
    },
    exit: {
      x: typeof window !== 'undefined' && window.innerWidth < 640 ? 300 : 400,
      opacity: 0,
      scale: 0.9,
      rotateY: typeof window !== 'undefined' && window.innerWidth < 640 ? -20 : -45,
      transition: {
        duration: 0.2,
        ease: "easeInOut"
      }
    }
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -180, opacity: 0 },
    visible: {
      scale: 1,
      rotate: 0,
      opacity: 1,
      transition: {
        delay: 0.1,
        type: "spring",
        stiffness: 500,
        damping: 20
      }
    }
  };

  const iconPulseVariants = {
    initial: { scale: 1 },
    animate: {
      scale: [1, 1.05, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const progressVariants = {
    hidden: { width: "100%" },
    visible: { 
      width: "0%",
      transition: {
        duration: duration / 1000,
        ease: "linear",
        delay: 0.3
      }
    }
  };

  return (
    <motion.div
      variants={toastVariants}
      initial="hidden"
      animate={visible ? "visible" : "exit"}
      exit="exit"
      className={`
        max-w-md w-full mx-4 sm:mx-0 ${config.bgColor} ${config.shadowColor}
        rounded-xl pointer-events-auto border ${config.borderColor}
        flex ring-2 ${config.ringColor} backdrop-blur-sm
        relative overflow-hidden min-w-[280px] sm:min-w-[320px]
      `}
      style={{ perspective: "1000px" }}
    >
      {/* Progress bar */}
      <motion.div
        variants={progressVariants}
        initial="hidden"
        animate="visible"
        className={`absolute bottom-0 left-0 h-1.5 ${config.progressColor} rounded-b-xl shadow-sm`}
      />
      
      <div className="flex-1 w-0 p-3 sm:p-4">
        <div className="flex items-start">
          <div className="shrink-0">
            <motion.div
              variants={iconVariants}
              animate="animate"
            >
              <motion.div
                variants={iconPulseVariants}
                initial="initial"
                animate="animate"
              >
                <Icon className={`h-6 w-6 sm:h-7 sm:w-7 ${config.iconColor} drop-shadow-sm`} />
              </motion.div>
            </motion.div>
          </div>
          <div className="ml-3 sm:ml-4 flex-1 min-w-0">
            <motion.p
              className={`text-sm font-bold ${config.titleColor} capitalize tracking-wide`}
              initial={{ opacity: 0, y: -10 }}
              animate={{
                opacity: 1,
                y: 0,
                transition: { delay: 0.15, duration: 0.3 }
              }}
            >
              {type}
            </motion.p>
            <motion.p
              className={`mt-1 text-sm font-medium ${config.messageColor} leading-relaxed break-words`}
              initial={{ opacity: 0, y: 10 }}
              animate={{
                opacity: 1,
                y: 0,
                transition: { delay: 0.2, duration: 0.3 }
              }}
            >
              {message}
            </motion.p>
            {subMessage && (
              <motion.p
                className={`mt-2 text-xs ${config.messageColor} opacity-80 leading-relaxed break-words`}
                initial={{ opacity: 0, y: 10 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  transition: { delay: 0.25, duration: 0.3 }
                }}
              >
                {subMessage}
              </motion.p>
            )}
          </div>
        </div>
      </div>
      
      {/* Close button */}
      <div className="flex border-l border-white/20">
        <motion.button
          onClick={onDismiss}
          className={`
            border border-transparent rounded-none rounded-r-xl
            p-3 sm:p-4 min-w-[52px] sm:min-w-[56px] min-h-[52px] sm:min-h-[56px]
            flex items-center justify-center text-sm font-medium
            ${config.messageColor} hover:bg-white/10 hover:${config.titleColor}
            focus:outline-none focus:ring-2 focus:ring-offset-1 focus:${config.ringColor}
            transition-all duration-200 group relative touch-manipulation
          `}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          initial={{ opacity: 0, rotate: 90 }}
          animate={{
            opacity: 1,
            rotate: 0,
            transition: { delay: 0.3, duration: 0.2 }
          }}
        >
          <motion.div
            whileHover={{
              rotate: 180,
              scale: 1.1
            }}
            whileTap={{
              scale: 0.9
            }}
            transition={{
              duration: 0.3,
              type: "spring",
              stiffness: 300,
              damping: 20
            }}
          >
            <CloseIcon className={`h-7 w-7 sm:h-8 sm:w-8 ${config.messageColor} group-hover:${config.titleColor} transition-colors duration-200 drop-shadow-sm`} />
          </motion.div>
        </motion.button>
      </div>
    </motion.div>
  );
};

export default EnhancedToast;
