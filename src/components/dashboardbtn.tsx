import React from "react";
import { motion } from "framer-motion";

interface DashboardBtnProps {
  variant: "default" | "hover" | "disabled" | "active";
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
}

const Dashboardbtn: React.FC<DashboardBtnProps> = ({
  variant,
  children,
  onClick,
  className,
  disabled
}) => {
  const getButtonClasses = (): string => {
    switch (variant) {
      case "default":
        return "bg-slate-700 text-white hover:bg-slate-600";
      case "hover":
        return "bg-slate-200 text-slate-700 hover:bg-slate-300";
      case "disabled":
        return "bg-slate-200 text-slate-400 cursor-not-allowed";
      case "active":
        return "bg-slate-800 text-white";
      default:
        return "bg-slate-700 text-white hover:bg-slate-600";
    }
  };

  return (
    <motion.button
      whileTap={{ scale: 0.98 }}
      className={`px-8 py-2 rounded-md font-medium ${getButtonClasses()} ${className}`}
      onClick={onClick}
      disabled={disabled ?? variant === "disabled"}
    >
      {children}
    </motion.button>
  );
};

export default Dashboardbtn;
