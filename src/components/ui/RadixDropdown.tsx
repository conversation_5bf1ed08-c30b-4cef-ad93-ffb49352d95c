"use client";

import React from "react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
// eslint-disable-next-line @typescript-eslint/no-var-requires
const FM = require("framer-motion");
const motion = FM.motion as typeof import("framer-motion")["motion"];
const AnimatePresence = FM.AnimatePresence as typeof import("framer-motion")["AnimatePresence"];

interface RadixDropdownProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  align?: "start" | "center" | "end";
  trigger: React.ReactNode;
  children: React.ReactNode;
}

export const RadixDropdown: React.FC<RadixDropdownProps> = ({
  open,
  onOpenChange,
  align = "end",
  trigger,
  children,
}) => {
  return (
    <DropdownMenu.Root open={open} onOpenChange={onOpenChange} modal={false}>
      <DropdownMenu.Trigger asChild>{trigger}</DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <AnimatePresence>
          {open ? (
            <DropdownMenu.Content asChild align={align} sideOffset={8} onCloseAutoFocus={(e) => e.preventDefault()}>
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -6 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.96, y: -6 }}
                transition={{ duration: 0.16, ease: "easeOut" }}
                className="min-w-40 rounded-2xl bg-white shadow-lg ring-1 ring-black/5 z-50 p-1"
              >
                {children}
              </motion.div>
            </DropdownMenu.Content>
          ) : null}
        </AnimatePresence>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
};

export const RadixDropdownItem: React.FC<React.ComponentProps<typeof DropdownMenu.Item>> = ({
  className,
  children,
  ...rest
}) => (
  <DropdownMenu.Item
    className={`px-3 py-2 rounded-md text-sm outline-none select-none cursor-pointer hover:bg-gray-100 ${className ?? ""}`}
    {...rest}
  >
    {children}
  </DropdownMenu.Item>
);

export const RadixDropdownSeparator: React.FC = () => (
  <DropdownMenu.Separator className="my-1 h-px bg-gray-200" />
);

export default RadixDropdown;


