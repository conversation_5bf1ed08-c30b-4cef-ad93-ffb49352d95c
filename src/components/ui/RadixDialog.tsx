"use client";

import React from "react";
import * as Dialog from "@radix-ui/react-dialog";

interface RadixDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  children: React.ReactNode;
}

export const RadixDialog: React.FC<RadixDialogProps> = ({
  open,
  onOpenChange,
  title,
  children,
}) => {
  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-opacity duration-200 data-[state=open]:opacity-100 data-[state=closed]:opacity-0 overflow-x-hidden" />
        <Dialog.Content className="fixed left-1/2 top-1/2 z-50 w-[92vw] max-w-3xl max-h-[85vh] -translate-x-1/2 -translate-y-1/2 rounded-2xl bg-white shadow-lg outline-none overflow-hidden overflow-x-hidden transition-opacity duration-200 data-[state=open]:opacity-100 data-[state=closed]:opacity-0">
          <Dialog.Title className={title ? "text-lg sm:text-xl font-semibold tracking-tight p-4 border-b border-gray-200" : "sr-only"}>
            {title ?? "Dialog"}
          </Dialog.Title>
          <div className="max-h-[calc(85vh-4rem)] overflow-y-auto overflow-x-hidden p-4">
            {children}
          </div>
          <Dialog.Close asChild>
            <button
              aria-label="Close"
              className="absolute right-3 top-3 inline-flex h-8 w-8 items-center justify-center rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100"
            >
              <span className="sr-only">Close</span>
              ✕
            </button>
          </Dialog.Close>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default RadixDialog;
