"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";

interface AnimatedActionsDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  align?: "left" | "right";
  children: React.ReactNode;
  className?: string;
}

export const AnimatedActionsDropdown: React.FC<
  AnimatedActionsDropdownProps
> = ({ isOpen, onClose, align = "right", children, className }) => {
  // Consumers can manage outside-click at page level; avoid adding global listeners here to reduce reflows

  return (
    <div data-dropdown-root className={`relative ${className ?? ""}`}>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -6 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.96, y: -6 }}
            transition={{ duration: 0.16, ease: "easeOut" }}
            className={`absolute mt-2 w-40 bg-white rounded-2xl shadow-lg z-50 overflow-hidden ${
              align === "right" ? "right-0" : "left-0"
            }`}
          >
            <div className="py-1 flex flex-col">{children}</div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AnimatedActionsDropdown;
