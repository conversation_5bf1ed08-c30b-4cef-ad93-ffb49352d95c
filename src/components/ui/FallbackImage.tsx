"use client";

import React from "react";
import Image from "next/image";

interface FallbackImageProps {
  src?: string | null;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  rounded?: boolean;
  fallbackSrc?: string;
  fallbackSize?: number;
}

export const FallbackImage: React.FC<FallbackImageProps> = ({
  src,
  alt,
  width = 48,
  height = 48,
  className,
  rounded = true,
  fallbackSrc = "/default-avatar.png",
  fallbackSize = 28,
}) => {
  const [hasError, setHasError] = React.useState(false);

  const isUsingFallback = hasError || !src;
  const imageSrc = !isUsingFallback ? src! : fallbackSrc;

  if (isUsingFallback) {
    const w = width ?? fallbackSize;
    const h = height ?? fallbackSize;
    const initial = alt?.trim()?.[0]?.toUpperCase() ?? "?";
    return (
      <div
        className={`${rounded ? "rounded-full" : ""} ${
          className ?? ""
        } bg-gray-200 text-gray-600 flex items-center justify-center`}
        style={{ width: w, height: h, minWidth: w, minHeight: h }}
        aria-label={alt}
      >
        <span className="text-xs font-medium">{initial}</span>
      </div>
    );
  }

  return (
    <Image
      src={imageSrc}
      alt={alt}
      width={width}
      height={height}
      className={`${rounded ? "rounded-full" : ""} ${className ?? ""}`}
      onError={() => setHasError(true)}
      loading="lazy"
    />
  );
};

export default FallbackImage;
