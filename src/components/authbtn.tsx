import { useMemo } from "react";

const AuthBtn = ({
  text = "This is a test for button",
  style = "default",
  isLoading = false,
  ...props
}) => {
  const twStyles = useMemo(() => {
    if (style === "default") {
      return "py-4 md:px-32 bg-[#263645] md:text-xl text-nowrap text-md px-16 rounded-full text-white hover:bg-[#DADDE1] hover:text-[#263645] font-bold transition-all duration-200 relative overflow-hidden";
    } else if (style === "disabled") {
      return "py-2 px-16 bg-[#DADDE1] rounded-full text-[#768FA3] font-bold transition-all duration-200 relative overflow-hidden";
    } else if (style === "active") {
      return "py-2 px-16 bg-[#1f2c37] rounded-full text-[white] font-bold transition-all duration-200 relative overflow-hidden";
    }
  }, [style]);

  const loadingAnimation = isLoading ? (
    <div className="inline-block w-5 h-5 mr-3 align-middle">
      <svg className="animate-spin" viewBox="0 0 24 24">
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
          fill="none"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
        />
      </svg>
    </div>
  ) : null;

  return (
    <button
      className={`${twStyles} ${isLoading ? "cursor-wait" : ""}`}
      disabled={style === "disabled" || isLoading}
      {...props}
    >
      <span className="relative z-10 flex items-center justify-center">
        {isLoading && loadingAnimation}
        {text}
      </span>
    </button>
  );
};

export default AuthBtn;
