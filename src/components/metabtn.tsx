import { useMemo } from "react";

interface MetaBtnProps {
  style?: "default" | "disabled" | "active";
  inv?: string;
  onClick?: () => void;
}

const MetaBtn: React.FC<MetaBtnProps> = ({ style = "default", inv, ...props }) => {
  const twStyles = useMemo(() => {
    if (style === "default") {
      return " py-4 px-4 bg-white rounded-lg text-[#151515] border border hover:bg-[#DADDE1] font-bold transition-all duration-200 ";
    } else if (style === "disabled") {
      return " py-4 px-4 bg-[#DADDE1] rounded-lg text-[#151515] border border  font-bold transition-all duration-200 grayscale ";
    } else if (style === "active") {
      return " py-4 px-4 bg-white shadow-[inset_1px_1px_4px_rgba(0,0,0,0.2)] rounded-lg text-[#151515] border border hover:bg-[#DADDE1] font-bold transition-all duration-200 ";
    }
  }, [style]);

  return (
    <button
      className={`${twStyles} flex flex-row items-center justify-center`}
      disabled={style === "disabled"}
      {...props}
    >
      <img
        src="/icons/meta.svg"
        alt="Meta icon"
        width={18}
        height={18}
        className="mr-2 inline-block"
      />
      <span className="text-sm font-normal">Continue with Meta</span>
    </button>
  );
};

export default MetaBtn;
