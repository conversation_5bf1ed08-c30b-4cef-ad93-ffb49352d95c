import React, { useState } from 'react';
import { usePushNotification } from '../hooks/usePushNotification';
import toast from 'react-hot-toast';

const NotificationTest: React.FC = () => {
  const { permission, subscription } = usePushNotification();
  const [isLoading, setIsLoading] = useState(false);

  const sendTestNotification = async () => {
    if (permission !== 'granted') {
      toast.error('Notification permission not granted');
      return;
    }
    
    if (!subscription) {
      toast.error('No notification subscription found');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/notifications/test?subscription=${encodeURIComponent(JSON.stringify(subscription))}`
      );
      const data = await response.json();
      
      if (!data.success) {
        toast.error(data.message || 'Failed to send notification');
        console.error('Failed to send test notification:', data);
      } else {
        toast.success('Test notification sent!');
      }
    } catch (err) {
      console.error('Error sending test notification:', err);
      toast.error('Failed to send notification');
    } finally {
      setIsLoading(false);
    }
  };

  if (permission !== 'granted') {
    return null;
  }

  return (
    <button
      onClick={sendTestNotification}
      disabled={isLoading}
      className={`
        fixed bottom-4 right-20 z-50 px-4 py-2 
        text-white rounded-lg focus:outline-none focus:ring-2 
        focus:ring-blue-500 focus:ring-offset-2
        ${isLoading 
          ? 'bg-blue-400 cursor-not-allowed' 
          : 'bg-blue-600 hover:bg-blue-700'
        }
      `}
    >
      {isLoading ? 'Sending...' : 'Send Test Notification'}
    </button>
  );
};

export default NotificationTest; 