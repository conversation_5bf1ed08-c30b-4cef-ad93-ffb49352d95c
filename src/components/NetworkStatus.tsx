import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import useNetworkStatus from '~/hooks/useNetworkStatus';

const NetworkStatus = () => {
  const isOnline = useNetworkStatus();
  const router = useRouter();

  useEffect(() => {
    if (!isOnline) {
      router.push('/offline');
    }
  }, [isOnline, router]);

  return null; // This is a utility component, it doesn't render anything
};

export default NetworkStatus; 