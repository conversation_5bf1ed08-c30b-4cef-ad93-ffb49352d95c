import { useState, useEffect, TouchEvent } from 'react';
import Image from 'next/image';

const slides = [
  {
    title: "AI-Powered Insights",
    description: "Leverage AI to gain actionable insights from your data. Predict trends, understand customer sentiment, and make better decisions.",
    image: "/icons/bulb.svg"
  },
  {
    title: "Omnichannel Engagement",
    description: "Manage all your social media channels and customer interactions from one unified dashboard.",
    image: "/icons/foolder.svg"
  },
  {
    title: "Collaboration Tools",
    description: "Collaborate with your team seamlessly. Manage workspaces with integrated customisable tools.",
    image: "/icons/tunder.svg"
  }
];

interface SafariNavigator extends Navigator {
  standalone?: boolean;
}

const WelcomeSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showSlider, setShowSlider] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Required minimum swipe distance
  const minSwipeDistance = 50;

  const onTouchStart = (e: TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    }
    if (isRightSwipe && currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  useEffect(() => {
    // Check if running as PWA (in standalone mode)
    const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                 (window.navigator as SafariNavigator).standalone ||
                 document.referrer.includes('android-app://');

    // Check if it's the first visit
    const hasVisited = localStorage.getItem('hasVisitedBefore');

    // Only show if it's PWA and first visit
    setShowSlider(isPWA && !hasVisited);

    // Add listener for display mode changes
    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    const handleDisplayModeChange = (e: MediaQueryListEvent) => {
      if (e.matches && !hasVisited) {
        setShowSlider(true);
      }
    };

    mediaQuery.addEventListener('change', handleDisplayModeChange);

    return () => {
      mediaQuery.removeEventListener('change', handleDisplayModeChange);
    };
  }, []);

  const handleNext = () => {
    if (currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    }
  };

  const handlePrev = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  const handleGetStarted = () => {
    localStorage.setItem('hasVisitedBefore', 'true');
    setShowSlider(false);
  };

  if (!showSlider) return null;

  return (
    <div className="fixed inset-0 w-screen h-screen bg-white z-50 flex flex-col">
      <div className="flex-1 relative overflow-hidden">
        <div 
          className="absolute inset-0 transition-transform duration-500 ease-in-out flex"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
        >
          {slides.map((slide, index) => (
            <div key={index} className="min-w-full h-full flex flex-col items-center justify-center p-8">
              <div className="relative w-64 h-64 mb-8">
                <Image
                  src={slide.image}
                  alt={slide.title}
                  layout="fill"
                  objectFit="contain"
                  priority
                />
              </div>
              <h2 className="text-2xl font-bold text-black mb-4 text-center">
                {slide.title}
              </h2>
              <p className="text-black text-center max-w-md">
                {slide.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation dots */}
      <div className="flex justify-center gap-2 p-4">
        {slides.map((_, index) => (
          <button
            key={index}
            className={`w-2 h-2 rounded-full transition-colors ${
              currentSlide === index ? 'bg-blue-600' : 'bg-gray-300'
            }`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>

      {/* Updated navigation buttons */}
      <div className="flex justify-between items-center p-8">
        <button
          onClick={handlePrev}
          className={`px-4 py-2 rounded-lg ${
            currentSlide === 0
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-[#2C3E50]'
          }`}
          disabled={currentSlide === 0}
        >
          Previous
        </button>
        
        {currentSlide === slides.length - 1 ? (
          <button
            onClick={handleGetStarted}
            className="px-6 py-2 bg-[#2C3E50] text-white rounded-lg"
          >
            Get Started
          </button>
        ) : (
          <button
            onClick={handleNext}
            className="px-4 py-2 text-[#2C3E50] rounded-lg"
          >
            Next
          </button>
        )}
      </div>
    </div>
  );
};

export default WelcomeSlider; 