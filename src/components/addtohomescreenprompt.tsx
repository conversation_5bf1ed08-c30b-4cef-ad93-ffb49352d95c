import { useState, useEffect } from 'react';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

const AddToHomeScreenPrompt = () => {
  // DISABLED: Add to home screen prompt is disabled
  return null;
  
  const [showIOSPrompt, setShowIOSPrompt] = useState(false);
  const [showAndroidPrompt, setShowAndroidPrompt] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);

  useEffect(() => {
    // Check if device is iOS
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    
    // Check if device is Android
    const isAndroid = /Android/.test(navigator.userAgent);
    
    // Check if the app is already installed
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;

    if (isIOS && !isStandalone) {
      // Show iOS prompt after a short delay
      setTimeout(() => setShowIOSPrompt(true), 1000);
    }

    // Listen for beforeinstallprompt event (Android)
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      if (isAndroid && !isStandalone) {
        setShowAndroidPrompt(true);
      }
    });

    return () => {
      window.removeEventListener('beforeinstallprompt', () => {});
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    // Show the install prompt
    deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    const { outcome } = await deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      setShowAndroidPrompt(false);
    }

    // Clear the deferredPrompt for the next time
    setDeferredPrompt(null);
  };

  const dismissPrompt = () => {
    setShowIOSPrompt(false);
    setShowAndroidPrompt(false);
  };

  if (!showIOSPrompt && !showAndroidPrompt) return null;

  return (
    <div className="fixed inset-0 w-screen bg-black/50 backdrop-blur-sm flex items-center p-0 m-0 justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl p-6 max-w-md w-full h-dvh animate-slide-up">
        {showIOSPrompt && (
          <div className="space-y-4 flex flex-col justify-between items-stretch h-full">
            <div className="flex justify-between items-start">
              <div className="flex items-center space-x-3">
                <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                <h2 className="text-xl font-semibold text-gray-900">Install Business Insight</h2>
              </div>
              <button
                onClick={dismissPrompt}
                className="text-gray-400 hover:text-gray-500 transition-colors"
                aria-label="Close"
              >
                <svg className="w-6 h-6" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>

          

            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <p className="text-base text-gray-600">
              Get the best experience by installing Business Insight on your iPhone:
            </p>
              <div className="flex items-center space-x-2">
                <span className="shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">1</span>
                <p className="text-gray-700">Tap the share button below
                  <span className="inline-block w-6 h-6 ml-2 text-blue-500">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M13 5.41V21a1 1 0 01-2 0V5.41l-5.3 5.3a1 1 0 11-1.4-1.42l7-7a1 1 0 011.4 0l7 7a1 1 0 11-1.4 1.42L13 5.41z" />
                    </svg>
                  </span>
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <span className="shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">2</span>
                <p className="text-gray-700">Select "Add to Home Screen"</p>
              </div>
            </div>

            <button
              onClick={dismissPrompt}
              className="w-full mt-4 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Maybe Later
            </button>
          </div>
        )}
        
        {showAndroidPrompt && (
          <div className="space-y-4 flex flex-col justify-between items-stretch h-full">
            <div className="flex justify-between items-start">
              <div className="flex items-center space-x-3">
                <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h2 className="text-xl font-semibold text-gray-900">Install Business Insight</h2>
              </div>
              <button
                onClick={dismissPrompt}
                className="text-gray-400 hover:text-gray-500 transition-colors"
                aria-label="Close"
              >
                <svg className="w-6 h-6" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>

          

            <div className="flex flex-col space-y-3">
            <p className="text-base text-gray-600">
              Install Business Insight for a faster, app-like experience with offline capabilities.
            </p>
              <button
                onClick={handleInstallClick}
                className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Install App
              </button>
              <button
                onClick={dismissPrompt}
                className="w-full px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Maybe Later
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddToHomeScreenPrompt; 