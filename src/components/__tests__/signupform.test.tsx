import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SignupForm from '../signupform';
import { signIn } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { showErrorToast, showSuccessToast } from '../toasts';

// Mock the required modules
jest.mock('next-auth/react');
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));
jest.mock('../toasts');

describe('SignupForm', () => {
  const mockRouter = {
    push: jest.fn(),
  };

  const mockSearchParams = {
    get: jest.fn(),
  };

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
    mockSearchParams.get.mockReturnValue('/dashboard');
  });

  // Test: Successful signup
  it('should successfully sign up with valid information', async () => {
    const user = userEvent.setup();
    render(<SignupForm />);

    // Fill in valid information
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password$/i), 'ValidPass123!');
    await user.type(screen.getByLabelText(/repeat.*password/i), 'ValidPass123!');
    await user.click(screen.getByRole('checkbox'));

    // Submit form
    await user.click(screen.getByRole('button', { name: /sign up/i }));

    await waitFor(() => {
      expect(showSuccessToast).toHaveBeenCalledWith('Signup successful! Please verify your email.', 'signup-toast');
      expect(mockRouter.push).toHaveBeenCalledWith('/verify');
    });
  });

  // Test: Invalid email format
  it('should show error for invalid email format', async () => {
    const user = userEvent.setup();
    render(<SignupForm />);

    // Fill in information with invalid email
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email/i), 'invalid-email');
    await user.type(screen.getByLabelText(/^password$/i), 'ValidPass123!');
    await user.type(screen.getByLabelText(/repeat.*password/i), 'ValidPass123!');
    await user.click(screen.getByRole('checkbox'));

    // Submit form
    await user.click(screen.getByRole('button', { name: /sign up/i }));

    expect(screen.getByText(/invalid email address/i)).toBeInTheDocument();
  });

  // Test: Password mismatch
  it('should show error when passwords do not match', async () => {
    const user = userEvent.setup();
    render(<SignupForm />);

    // Fill in information with mismatched passwords
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password$/i), 'ValidPass123!');
    await user.type(screen.getByLabelText(/repeat.*password/i), 'DifferentPass123!');
    await user.click(screen.getByRole('checkbox'));

    // Submit form
    await user.click(screen.getByRole('button', { name: /sign up/i }));

    expect(screen.getByText(/passwords don't match/i)).toBeInTheDocument();
  });

  // Test: Password requirements
  it('should validate password requirements', async () => {
    const user = userEvent.setup();
    render(<SignupForm />);

    // Test different invalid passwords
    const invalidPasswords = [
      'short', // too short
      'nouppercase123', // no uppercase
      'NOLOWERCASE123', // no lowercase
      'NoNumbers!', // no numbers
      'NoSpecial123' // no special characters
    ];

    for (const password of invalidPasswords) {
      await user.clear(screen.getByLabelText(/^password$/i));
      await user.type(screen.getByLabelText(/^password$/i), password);
      await user.click(screen.getByRole('button', { name: /sign up/i }));

      // Should show at least one error message
      const errorMessages = [
        /at least 8 characters/i,
        /uppercase letter/i,
        /lowercase letter/i,
        /number/i,
        /special character/i
      ];

      const hasError = errorMessages.some(message => 
        screen.queryByText(message) !== null
      );
      expect(hasError).toBe(true);
    }
  });

  // Test: Empty required fields
  it('should show errors for empty required fields', async () => {
    const user = userEvent.setup();
    render(<SignupForm />);

    // Submit form without filling any fields
    await user.click(screen.getByRole('button', { name: /sign up/i }));

    expect(screen.getByText(/first name is required/i)).toBeInTheDocument();
    expect(screen.getByText(/last name is required/i)).toBeInTheDocument();
    expect(screen.getByText(/email.*required/i)).toBeInTheDocument();
    expect(screen.getByText(/password.*required/i)).toBeInTheDocument();
  });

  // Test: Terms agreement checkbox
  it('should require terms agreement', async () => {
    const user = userEvent.setup();
    render(<SignupForm />);

    // Fill in valid information but don't check terms
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password$/i), 'ValidPass123!');
    await user.type(screen.getByLabelText(/repeat.*password/i), 'ValidPass123!');

    // Submit form
    await user.click(screen.getByRole('button', { name: /sign up/i }));

    expect(screen.getByText(/must agree to the terms/i)).toBeInTheDocument();
  });

  // Test: Password visibility toggle
  it('should toggle password visibility', async () => {
    const user = userEvent.setup();
    render(<SignupForm />);

    const passwordInput = screen.getByLabelText(/^password$/i);
    const repeatPasswordInput = screen.getByLabelText(/repeat.*password/i);
    const toggleButtons = screen.getAllByRole('button', { name: /show password/i });

    // Initially passwords should be hidden
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(repeatPasswordInput).toHaveAttribute('type', 'password');

    // Toggle password visibility
    await user.click(toggleButtons[0]);
    expect(passwordInput).toHaveAttribute('type', 'text');

    await user.click(toggleButtons[1]);
    expect(repeatPasswordInput).toHaveAttribute('type', 'text');

    // Toggle back
    await user.click(toggleButtons[0]);
    await user.click(toggleButtons[1]);
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(repeatPasswordInput).toHaveAttribute('type', 'password');
  });

  // Test: Email already exists
  it('should show error when email already exists', async () => {
    const user = userEvent.setup();
    render(<SignupForm />);

    // Fill in information with existing email
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/^password$/i), 'ValidPass123!');
    await user.type(screen.getByLabelText(/repeat.*password/i), 'ValidPass123!');
    await user.click(screen.getByRole('checkbox'));

    // Submit form
    await user.click(screen.getByRole('button', { name: /sign up/i }));

    await waitFor(() => {
      expect(showErrorToast).toHaveBeenCalledWith('Email already exists', 'signup-toast');
    });
  });
}); 