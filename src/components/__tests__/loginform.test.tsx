import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter, useSearchParams } from 'next/navigation';
import LoginForm from '../loginform';
import { showErrorToast, showSuccessToast } from '../toasts';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

// Mock next-auth/react
jest.mock('next-auth/react', () => ({
  signIn: jest.fn(),
}));

// Mock toasts
jest.mock('../toasts', () => ({
  showErrorToast: jest.fn(),
  showSuccessToast: jest.fn(),
}));

// Mock userStore
jest.mock('~/store/userStore', () => ({
  useUserStore: {
    getState: () => ({
      csrfToken: 'mock-csrf-token',
    }),
  },
}));

// Mock process.env
const originalEnv = process.env;
beforeAll(() => {
  process.env = {
    ...originalEnv,
    NEXT_PUBLIC_API_URL: 'http://localhost:3000',
  };
  // Mock fetch globally
  global.fetch = jest.fn();
});

afterAll(() => {
  process.env = originalEnv;
  // Restore fetch
  jest.restoreAllMocks();
});

// Mock sessionStorage
const mockSessionStorage = {
  setItem: jest.fn(),
  getItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
});

describe('LoginForm', () => {
  // Increase the timeout for all tests in this describe block
  jest.setTimeout(10000);

  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
  };

  const mockSearchParams = {
    get: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
    mockSearchParams.get.mockImplementation((param) => {
      if (param === 'inv') return '/dashboard';
      if (param === 'password-change') return null;
      return null;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const getFormElements = () => {
    const emailInput = screen.getByLabelText(/email/i) as HTMLInputElement;
    const passwordInput = screen.getByLabelText(/password/i) as HTMLInputElement;
    const submitButtons = screen.getAllByRole('button', { name: /sign in/i });
    const submitButton = submitButtons[0] as HTMLButtonElement;
    const toggleButtons = screen.getAllByRole('button', { name: /show password/i });
    const toggleButton = toggleButtons[0] as HTMLButtonElement;
    return { emailInput, passwordInput, submitButton, toggleButton };
  };

  const fillAndSubmitForm = async (user: ReturnType<typeof userEvent.setup>, email: string, password: string) => {
    const { emailInput, passwordInput, submitButton } = getFormElements();
    await user.type(emailInput, email);
    await user.type(passwordInput, password);
    await user.click(submitButton);
  };

  it('should show error for unregistered email', async () => {
    const user = userEvent.setup();
    const mockResponse = { message: 'The email or password is incorrect. Please double check' };
    let resolveResponse: (value: any) => void;
    
    const responsePromise = new Promise(resolve => {
      resolveResponse = resolve;
    });

    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        ok: false,
        json: () => responsePromise,
      })
    );

    render(<LoginForm />);
    await fillAndSubmitForm(user, '<EMAIL>', 'password123');
    // Resolve the response after form submission
    resolveResponse!(mockResponse);

    expect(global.fetch).toHaveBeenCalledWith(
      'http://localhost:3000/api/login/',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
        }),
      })
    );
  }, 10000);

  it('should show error for empty email field', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const { submitButton } = getFormElements();
    await act(async () => {
      await user.click(submitButton);
    });

    await waitFor(() => {
      const errorMessage = screen.getByText('Email is required');
      expect(errorMessage).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  it('should show error for empty password field', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const { emailInput, submitButton } = getFormElements();

    await act(async () => {
      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);
    });

    await waitFor(() => {
      const errorMessage = screen.getByText('Password is required');
      expect(errorMessage).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  it('should show error when both fields are empty', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const { submitButton } = getFormElements();
    await act(async () => {
      await user.click(submitButton);
    });

    await waitFor(() => {
      const emailError = screen.getByText('Email is required');
      const passwordError = screen.getByText('Password is required');
      expect(emailError).toBeInTheDocument();
      expect(passwordError).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  it('should toggle password visibility', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const { passwordInput, toggleButton } = getFormElements();

    expect(passwordInput).toHaveAttribute('type', 'password');
    await act(async () => {
      await user.click(toggleButton);
    });
    expect(passwordInput).toHaveAttribute('type', 'text');
    await act(async () => {
      await user.click(toggleButton);
    });
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('should show error for inactive account', async () => {
    const user = userEvent.setup();
    const mockResponse = { 
      message: 'The user is not verified',
      access_token: 'mock-verify-token'
    };
    let resolveResponse: (value: any) => void;
    
    const responsePromise = new Promise(resolve => {
      resolveResponse = resolve;
    });

    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        ok: false,
        json: () => responsePromise,
      })
    );

    render(<LoginForm />);
    await fillAndSubmitForm(user, '<EMAIL>', 'password123');

    // Resolve the response after form submission
    resolveResponse!(mockResponse);


    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('/verify?inv=/dashboard&access=mock-verify-token');
    }, { timeout: 2000 });
  }, 10000);

  it('should successfully log in with password containing special characters', async () => {
    const user = userEvent.setup();
    const mockResponse = { 
      success: true,
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token'
    };
    let resolveResponse: (value: any) => void;
    
    const responsePromise = new Promise(resolve => {
      resolveResponse = resolve;
    });

    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        ok: true,
        json: () => responsePromise,
      })
    );

    render(<LoginForm />);
    await fillAndSubmitForm(user, '<EMAIL>', 'Reza0019');

    // Resolve the response after form submission
    resolveResponse!(mockResponse);


    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('/dashboard');
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith('accessToken', mockResponse.access_token);
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith('refreshToken', mockResponse.refresh_token);
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith('status', 'authenticated');
    }, { timeout: 2000 });

    expect(global.fetch).toHaveBeenCalledWith(
      'http://localhost:3000/api/login/',
      expect.objectContaining({
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'Reza0019',
        }),
      })
    );
  }, 10000);
}); 