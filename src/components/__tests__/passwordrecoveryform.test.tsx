import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import PasswordRecoveryForm from '../passwordrecoveryform';
import { useRouter, useSearchParams } from 'next/navigation';
import { showErrorToast, showSuccessToast } from '../toasts';

// Mock the required modules
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));
jest.mock('../toasts');

describe('PasswordRecoveryForm', () => {
  const mockRouter = {
    push: jest.fn(),
  };

  const mockSearchParams = {
    get: jest.fn(),
  };

  const mockOnStepChange = jest.fn();

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
    mockSearchParams.get.mockReturnValue('/dashboard');
  });

  // Test: Email Step - Valid email submission
  it('should proceed to OTP step when valid email is submitted', async () => {
    const user = userEvent.setup();
    render(<PasswordRecoveryForm onStepChange={mockOnStepChange} />);

    // Fill in valid email
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    
    // Submit form
    await user.click(screen.getByRole('button', { name: /send email/i }));

    await waitFor(() => {
      expect(showSuccessToast).toHaveBeenCalledWith('OTP has been sent to your email', 'passwordrecovery-toast');
      expect(mockOnStepChange).toHaveBeenCalledWith(2); // OTP step
    });
  });

  // Test: Email Step - Invalid email format
  it('should show error for invalid email format', async () => {
    const user = userEvent.setup();
    render(<PasswordRecoveryForm onStepChange={mockOnStepChange} />);

    // Fill in invalid email
    await user.type(screen.getByLabelText(/email/i), 'invalid-email');
    
    // Submit form
    await user.click(screen.getByRole('button', { name: /send email/i }));

    expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
  });

  // Test: Email Step - Empty email field
  it('should show error for empty email field', async () => {
    const user = userEvent.setup();
    render(<PasswordRecoveryForm onStepChange={mockOnStepChange} />);

    // Submit form without filling email
    await user.click(screen.getByRole('button', { name: /send email/i }));

    expect(screen.getByText(/email.*required/i)).toBeInTheDocument();
  });

  // Test: OTP Step - Valid OTP submission
  it('should proceed to password step when valid OTP is submitted', async () => {
    const user = userEvent.setup();
    render(<PasswordRecoveryForm onStepChange={mockOnStepChange} />);

    // Move to OTP step first
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /send email/i }));

    // Fill in OTP
    const otpInputs = screen.getAllByRole('textbox');
    for (let i = 0; i < 5; i++) {
      await user.type(otpInputs[i], i.toString());
    }

    await waitFor(() => {
      expect(showSuccessToast).toHaveBeenCalledWith('OTP verified successfully', 'passwordrecovery-toast');
      expect(mockOnStepChange).toHaveBeenCalledWith(3); // Password step
    });
  });

  // Test: OTP Step - Invalid OTP
  it('should show error for invalid OTP', async () => {
    const user = userEvent.setup();
    render(<PasswordRecoveryForm onStepChange={mockOnStepChange} />);

    // Move to OTP step
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /send email/i }));

    // Fill in invalid OTP
    const otpInputs = screen.getAllByRole('textbox');
    for (let i = 0; i < 5; i++) {
      await user.type(otpInputs[i], '9');
    }

    await user.click(screen.getByRole('button', { name: /verify/i }));

    await waitFor(() => {
      expect(showErrorToast).toHaveBeenCalledWith('Invalid OTP', 'passwordrecovery-toast');
    });
  });

  // Test: OTP Step - Resend code
  it('should allow resending OTP after cooldown', async () => {
    const user = userEvent.setup();
    render(<PasswordRecoveryForm onStepChange={mockOnStepChange} />);

    // Move to OTP step
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /send email/i }));

    // Wait for cooldown
    await waitFor(() => {
      expect(screen.getByText(/resend code/i)).toBeInTheDocument();
    }, { timeout: 5000 });

    // Click resend
    await user.click(screen.getByText(/resend code/i));

    await waitFor(() => {
      expect(showSuccessToast).toHaveBeenCalledWith('OTP has been sent to your email', 'passwordrecovery-toast');
    });
  });

  // Test: Password Step - Valid password change
  it('should successfully change password with valid inputs', async () => {
    const user = userEvent.setup();
    render(<PasswordRecoveryForm onStepChange={mockOnStepChange} />);

    // Move to password step
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /send email/i }));
    
    const otpInputs = screen.getAllByRole('textbox');
    for (let i = 0; i < 5; i++) {
      await user.type(otpInputs[i], i.toString());
    }

    // Fill in new password
    await user.type(screen.getByLabelText(/new password/i), 'NewPass123!');
    await user.type(screen.getByLabelText(/confirm.*password/i), 'NewPass123!');

    await user.click(screen.getByRole('button', { name: /reset password/i }));

    await waitFor(() => {
      expect(showSuccessToast).toHaveBeenCalledWith('Password changed successfully', 'passwordrecovery-toast');
      expect(mockRouter.push).toHaveBeenCalledWith('/login');
    });
  });

  // Test: Password Step - Password mismatch
  it('should show error when passwords do not match', async () => {
    const user = userEvent.setup();
    render(<PasswordRecoveryForm onStepChange={mockOnStepChange} />);

    // Move to password step
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /send email/i }));
    
    const otpInputs = screen.getAllByRole('textbox');
    for (let i = 0; i < 5; i++) {
      await user.type(otpInputs[i], i.toString());
    }

    // Fill in mismatched passwords
    await user.type(screen.getByLabelText(/new password/i), 'NewPass123!');
    await user.type(screen.getByLabelText(/confirm.*password/i), 'DifferentPass123!');

    await user.click(screen.getByRole('button', { name: /reset password/i }));

    expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
  });

  // Test: Password Step - Invalid password format
  it('should validate password requirements', async () => {
    const user = userEvent.setup();
    render(<PasswordRecoveryForm onStepChange={mockOnStepChange} />);

    // Move to password step
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /send email/i }));
    
    const otpInputs = screen.getAllByRole('textbox');
    for (let i = 0; i < 5; i++) {
      await user.type(otpInputs[i], i.toString());
    }

    // Test different invalid passwords
    const invalidPasswords = [
      'short', // too short
      'nouppercase123', // no uppercase
      'NOLOWERCASE123', // no lowercase
      'NoNumbers!', // no numbers
      'NoSpecial123' // no special characters
    ];

    for (const password of invalidPasswords) {
      await user.clear(screen.getByLabelText(/new password/i));
      await user.type(screen.getByLabelText(/new password/i), password);
      await user.click(screen.getByRole('button', { name: /reset password/i }));

      // Should show at least one error message
      const errorMessages = [
        /at least 8 characters/i,
        /uppercase letter/i,
        /lowercase letter/i,
        /number/i,
        /special character/i
      ];

      const hasError = errorMessages.some(message => 
        screen.queryByText(message) !== null
      );
      expect(hasError).toBe(true);
    }
  });

  // Test: Password visibility toggle
  it('should toggle password visibility', async () => {
    const user = userEvent.setup();
    render(<PasswordRecoveryForm onStepChange={mockOnStepChange} />);

    // Move to password step
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /send email/i }));
    
    const otpInputs = screen.getAllByRole('textbox');
    for (let i = 0; i < 5; i++) {
      await user.type(otpInputs[i], i.toString());
    }

    const passwordInput = screen.getByLabelText(/new password/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm.*password/i);
    const toggleButtons = screen.getAllByRole('button', { name: /show password/i });

    // Initially passwords should be hidden
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(confirmPasswordInput).toHaveAttribute('type', 'password');

    // Toggle password visibility
    await user.click(toggleButtons[0]);
    expect(passwordInput).toHaveAttribute('type', 'text');

    await user.click(toggleButtons[1]);
    expect(confirmPasswordInput).toHaveAttribute('type', 'text');

    // Toggle back
    await user.click(toggleButtons[0]);
    await user.click(toggleButtons[1]);
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(confirmPasswordInput).toHaveAttribute('type', 'password');
  });
}); 