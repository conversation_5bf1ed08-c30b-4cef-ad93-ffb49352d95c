"use client";

import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { showErrorToast, showSuccessToast } from "./toasts";
import GBtn from "./gbtn";
import AppleBtn from "./applebtn";
import AuthBtn from "./authbtn";
import MetaBtn from "./metabtn";
import { useForm, SubmitHandler } from "react-hook-form";
import { useState, useEffect } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import {
  initFacebookSdk,
  handleFacebookLogin,
  getFacebookUserData,
} from "~/utils/FacebookSDK";
import type { ReadonlyURLSearchParams } from "next/navigation";
import axios from "axios";

const signupSchema = z
  .object({
    email: z.string().email("Invalid email address"),
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number"
      ),
    repeatPassword: z.string(),
    agreeTerms: z.boolean().refine((val) => val === true, {
      message: "You must agree to the terms",
    }),
  })
  .refine((data) => data.password === data.repeatPassword, {
    message: "Passwords don't match",
    path: ["repeatPassword"],
  });

type SignupFormInputs = z.infer<typeof signupSchema>;

interface MobileSignupFormProps {
  onSubmit: any;
  showPassword: boolean;
  setShowPassword: (show: boolean) => void;
  isLoading: boolean;
  handleGoogleSignIn: () => void;
  inputValues: any;
  focusedField: string | null;
  setFocusedField: (field: string | null) => void;
  loadingProgress: number;
  inv: string;
  passwordChangeCode?: string;
  searchParams: ReadonlyURLSearchParams;
}

const MobileSignupForm = ({
  onSubmit,
  showPassword,
  setShowPassword,
  isLoading,
  handleGoogleSignIn,
  inputValues,
  focusedField,
  setFocusedField,
  loadingProgress,
  inv,
  passwordChangeCode,
  searchParams,
}: MobileSignupFormProps) => {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    trigger,
    watch,
  } = useForm<SignupFormInputs>({
    resolver: zodResolver(signupSchema),
    mode: "onChange",
  });

  const handleFacebookSignIn = async () => {
    console.log("🚀 Starting Facebook sign-up process...");
    try {
      console.log("📱 Initializing Facebook SDK...");
      await initFacebookSdk();

      console.log("🔑 Requesting Facebook login...");
      const loginResult = await handleFacebookLogin();

      if (!loginResult) {
        console.error("❌ Facebook login failed - no login result");
        showErrorToast(
          "Facebook login failed. Please try again.",
          "signup-toast"
        );
        return;
      }

      console.log("✅ Facebook login successful:", loginResult);
      const { accessToken } = loginResult;

      // Exchange Facebook token with backend
      try {
        console.log("🔄 Exchanging Facebook token with backend...");
        const { data } = await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}/api/meta-redirect/`,
          {
            token: accessToken,
          }
        );

        console.log("📡 Backend response:", data);

        if (data.success) {
          console.log("✅ Token exchange successful");
          // Save tokens to sessionStorage
          sessionStorage.setItem("accessToken", data.access_token);
          sessionStorage.setItem("refreshToken", data.refresh_token);
          sessionStorage.setItem("status", "authenticated");

          showSuccessToast(
            "Successfully signed up with Facebook!",
            "signup-toast"
          );

          // Redirect to appropriate page
          const invParam = searchParams.get("inv") || "";
          const redirectUrl = passwordChangeCode
            ? `/user/change-password/${passwordChangeCode}`
            : invParam
            ? `/dashboard/team/${invParam}`
            : "/dashboard";

          console.log("🔄 Redirecting to:", redirectUrl);
          router.push(redirectUrl);
        } else {
          console.error("❌ Token exchange failed:", data);
          showErrorToast(
            "Failed to complete signup. Please try again.",
            "signup-toast"
          );
        }
      } catch (error) {
        console.error("❌ Error exchanging Facebook token:", error);
        showErrorToast(
          "Failed to complete signup. Please try again.",
          "signup-toast"
        );
      }
    } catch (error) {
      console.error("❌ Facebook login error:", error);
      showErrorToast(
        "Failed to sign up with Facebook. Please try again.",
        "signup-toast"
      );
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSubmit(onSubmit)();
    }
  };

  return (
    <div className="w-full h-dvh overflow-auto bg-white md:hidden">
      <div className="p-6 flex flex-col h-full">
        {/* Back button for mobile */}
        <div className="flex justify-start items-start mb-4">
          <button
            onClick={() => router.back()}
            className="flex flex-row items-center gap-2"
          >
            <img src="/icons/arrow-left.svg" alt="Back" className="w-5 h-5" />
            <span className="text-[#0f0f0f]">Back</span>
          </button>
        </div>

        <div className="mb-10.5">
          <h1 className="text-[#2c3e50] text-[1.75rem] font-semibold">
            Welcome
          </h1>
          <p className="text-[#0f0f0f] text-base capitalize mt-2.5">
            One step to membership
          </p>
        </div>

        <div className="mb-4">
          <span className="text-[#2c3e50] text-[0.938rem] font-medium capitalize">
            Sign up with Email
          </span>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="flex-1">
          <div className="flex gap-5 mb-4">
            <div className="flex flex-col gap-5 w-full">
              <div className="w-full relative">
                <input
                  type="text"
                  {...register("firstName")}
                  className={`w-full h-[4.313rem] px-4 rounded-lg border ${
                    errors.firstName ? "border-red-500" : "border-[#cccccc]/80"
                  }`}
                  onFocus={() => setFocusedField("firstName")}
                  onBlur={() => setFocusedField(null)}
                  onKeyDown={handleKeyDown}
                />
                <label
                  className={`absolute left-4 transition-all duration-200 ${
                    focusedField === "firstName" ||
                    errors.firstName ||
                    inputValues.firstName ||
                    register("firstName")
                      ? "-top-2 text-xs bg-white px-1"
                      : "-top-2"
                  } text-[#0f0f0f] text-sm`}
                >
                  First Name
                </label>
              </div>

              <div className="w-full relative">
                <input
                  type="text"
                  {...register("lastName")}
                  className={`w-full h-[4.313rem] px-4 rounded-lg border ${
                    errors.lastName ? "border-red-500" : "border-[#cccccc]/80"
                  }`}
                  onFocus={() => setFocusedField("lastName")}
                  onBlur={() => setFocusedField(null)}
                  onKeyDown={handleKeyDown}
                />
                <label
                  className={`absolute left-4 transition-all duration-200 ${
                    focusedField === "lastName" ||
                    errors.lastName ||
                    inputValues.lastName ||
                    register("lastName")
                      ? "-top-2 text-xs bg-white px-1"
                      : "top-5.5"
                  } text-[#0f0f0f] text-sm`}
                >
                  Last Name
                </label>
              </div>
            </div>
          </div>

          <div className="relative mb-4">
            <input
              type="email"
              {...register("email")}
              className={`w-full h-[4.313rem] px-4 rounded-lg border ${
                errors.email ? "border-red-500" : "border-[#cccccc]/80"
              }`}
              onFocus={() => setFocusedField("email")}
              onBlur={() => setFocusedField(null)}
              onKeyDown={handleKeyDown}
            />
            <label
              className={`absolute left-4 transition-all duration-200 ${
                focusedField === "email" ||
                errors.email ||
                inputValues.email ||
                register("email")
                  ? "-top-2 text-xs bg-white px-1"
                  : "top-5.5"
              } text-[#0f0f0f] text-sm`}
            >
              Email
            </label>
          </div>

          <div className="relative mb-4">
            <input
              type={showPassword ? "text" : "password"}
              {...register("password", {
                required: "Password is required",
                minLength: {
                  value: 8,
                  message: "Password must be at least 8 characters",
                },
                pattern: {
                  value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,
                  message:
                    "Password must contain at least one uppercase letter, one lowercase letter, and one number",
                },
              })}
              className={`w-full h-[4.313rem] px-4 rounded-lg border ${
                errors.password ? "border-red-500" : "border-[#cccccc]/80"
              }`}
              onFocus={() => setFocusedField("password")}
              onBlur={() => setFocusedField(null)}
              onKeyDown={handleKeyDown}
            />
            <label
              className={`absolute left-4 transition-all duration-200 ${
                focusedField === "password" ||
                errors.password ||
                inputValues.password ||
                register("password")
                  ? "-top-2 text-xs bg-white px-1"
                  : "top-5.5"
              } text-[#0f0f0f] text-sm`}
            >
              Password
            </label>
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2"
            >
              <img
                src={showPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                alt={showPassword ? "Hide password" : "Show password"}
                className="w-6 h-6"
              />
            </button>
          </div>

          <div className="relative mb-8">
            <input
              type={showPassword ? "text" : "password"}
              {...register("repeatPassword")}
              className={`w-full h-[4.313rem] px-4 rounded-lg border ${
                errors.repeatPassword ? "border-red-500" : "border-[#cccccc]/80"
              }`}
              onFocus={() => setFocusedField("repeatPassword")}
              onBlur={() => setFocusedField(null)}
              onKeyDown={handleKeyDown}
            />
            <label
              className={`absolute left-4 transition-all duration-200 ${
                focusedField === "repeatPassword" ||
                errors.repeatPassword ||
                inputValues.repeatPassword ||
                register("repeatPassword")
                  ? "-top-2 text-xs bg-white px-1"
                  : "top-5.5"
              } text-[#0f0f0f] text-sm`}
            >
              Repeat the password
            </label>

            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2"
            >
              <img
                src={showPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                alt={showPassword ? "Hide password" : "Show password"}
                className="w-6 h-6"
              />
            </button>
          </div>

          <div className="w-full">
            <div className="flex items-center gap-2">
              <div className="relative">
                <input
                  type="checkbox"
                  id="terms"
                  className={`w-6 h-6 aspect-square rounded-md border-2 appearance-none ${
                    errors.agreeTerms ? "border-red-500" : "border-[#cccccc]"
                  }`}
                  {...register("agreeTerms")}
                />
                {watch("agreeTerms") && (
                  <div
                    className={`absolute inset-0 flex items-center -top-2 justify-center pointer-events-none ${
                      errors.agreeTerms ? "text-red-500" : "text-[#2C3E50]"
                    }`}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </div>
              <label
                htmlFor="terms"
                className={`text-xs text-gray-600 ${
                  errors.agreeTerms ? "text-red-500" : "text-gray-600"
                }`}
              >
                By Continuing With Google, Apple, Or Email, You Agree To
                Business Insight{" "}
                <Link href="/privacy" className="text-blue-500 hover:underline">
                  Terms Of Service And Privacy Policy
                </Link>
                .
              </label>
            </div>
          </div>

          <div className="w-full flex items-center justify-center my-8">
            <button
              className={`py-4 md:px-32 w-full bg-[#263645] md:text-xl text-nowrap text-md px-16 rounded-md text-white hover:bg-[#DADDE1] hover:text-[#263645] font-bold transition-all duration-200 relative overflow-hidden ${
                isLoading ? "cursor-wait" : ""
              }`}
              disabled={isLoading}
              onClick={handleSubmit(onSubmit)}
            >
              <span className="relative z-10 flex items-center justify-center">
                {isLoading && (
                  <div className="inline-block w-5 h-5 mr-3 align-middle">
                    <svg className="animate-spin" viewBox="0 0 24 24">
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                        fill="none"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                      />
                    </svg>
                  </div>
                )}
                {isLoading ? "Signing Up..." : "Sign Up"}
              </span>
            </button>
          </div>

          <div className="text-center mt-6 mb-6">
            <Link href="/login" className="text-[#2c3e50] text-sm font-medium">
              Already have an account?
            </Link>
          </div>

          <div className="flex items-center mb-6 w-full">
            <hr className="grow border border-t-[0.2px] border-[#2c3e50]/40 text-[#2c3e50] " />
            <span className="px-4 text-[#2c3e50] text-[0.813rem]">Or</span>
            <hr className="grow border border-t-[0.2px] border-[#2c3e50]/40 text-[#2c3e50] " />
          </div>

          <div className="flex flex-col gap-3 mb-10">
            <GBtn onClick={handleGoogleSignIn} />
            <MetaBtn onClick={handleFacebookSignIn} />
            <AppleBtn style="disabled" />
          </div>
        </form>
      </div>
    </div>
  );
};

const SignupForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const inv = searchParams.get("inv") || "/dashboard";
  const passwordChangeCode = searchParams.get("password-change") || undefined;
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    trigger,
    watch,
  } = useForm<SignupFormInputs>({
    resolver: zodResolver(signupSchema),
    mode: "onChange",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [inputValues, setInputValues] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    repeatPassword: "",
  });
  const [loadingProgress, setLoadingProgress] = useState(0);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues((prev) => ({ ...prev, [name]: value }));
    setValue(name as keyof SignupFormInputs, value, { shouldValidate: true });
  };

  const onSubmit: SubmitHandler<SignupFormInputs> = async (data) => {
    setIsLoading(true);
    setLoadingProgress(0);

    const simulateProgress = () => {
      setLoadingProgress((prevProgress) => {
        const newProgress = prevProgress + 10;
        return newProgress > 90 ? 90 : newProgress;
      });
    };

    const progressInterval = setInterval(simulateProgress, 300);

    try {
      const response = await axios.post("/api/auth/signup", {
        email: data.email,
        password: data.password,
        firstName: data.firstName,
        lastName: data.lastName,
      });

      const result = response.data;

      if (result.success) {
        showSuccessToast(
          "Signup successful! Please verify your email.",
          "signup-toast"
        );
        if (result.accessToken)
          sessionStorage.setItem("accessToken", result.accessToken);
        if (result.refreshToken)
          sessionStorage.setItem("refreshToken", result.refreshToken);
        router.push(searchParams.get("inv") ? "/verify?inv=" + inv : "/verify");
        setLoadingProgress(100);
      } else {
        showErrorToast(
          result.message ?? "Signup failed. Please try again.",
          "signup-toast"
        );
        console.error("Signup error:", result.message);
      }
    } catch (error) {
      const result = error.response.data;
      console.error("Signup error:", result);
      showErrorToast(
        result.message || "An unexpected error occurred. Please try again.",
        "signup-toast"
      );
    } finally {
      clearInterval(progressInterval);
      setIsLoading(false);
      setLoadingProgress(0);
    }
  };
  const handleGoogleSignIn = () => {
    signIn("google", {
      callbackUrl: passwordChangeCode
        ? `/user/change-password/${passwordChangeCode}`
        : searchParams.get("inv")
        ? "/dashboard/team/" + inv
        : "/dashboard",
    });
  };

  const handleFacebookSignIn = async () => {
    console.log("🚀 Starting Facebook sign-up process...");
    try {
      console.log("📱 Initializing Facebook SDK...");
      await initFacebookSdk();

      console.log("🔑 Requesting Facebook login...");
      const loginResult = await handleFacebookLogin();

      if (!loginResult) {
        console.error("❌ Facebook login failed - no login result");
        showErrorToast(
          "Facebook login failed. Please try again.",
          "signup-toast"
        );
        return;
      }

      console.log("✅ Facebook login successful:", loginResult);
      const { accessToken } = loginResult;

      // Exchange Facebook token with backend
      try {
        console.log("🔄 Exchanging Facebook token with backend...");
        const { data } = await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}/api/meta-redirect/`,
          {
            token: accessToken,
          }
        );

        console.log("📡 Backend response:", data);

        if (data.success) {
          console.log("✅ Token exchange successful");
          // Save tokens to sessionStorage
          sessionStorage.setItem("accessToken", data.access_token);
          sessionStorage.setItem("refreshToken", data.refresh_token);
          sessionStorage.setItem("status", "authenticated");

          showSuccessToast(
            "Successfully signed up with Facebook!",
            "signup-toast"
          );

          // Redirect to appropriate page
          const invParam = searchParams.get("inv") || "";
          const redirectUrl = passwordChangeCode
            ? `/user/change-password/${passwordChangeCode}`
            : invParam
            ? `/dashboard/team/${invParam}`
            : "/dashboard";

          console.log("🔄 Redirecting to:", redirectUrl);
          router.push(redirectUrl);
        } else {
          console.error("❌ Token exchange failed:", data);
          showErrorToast(
            "Failed to complete signup. Please try again.",
            "signup-toast"
          );
        }
      } catch (error) {
        console.error("❌ Error exchanging Facebook token:", error);
        showErrorToast(
          "Failed to complete signup. Please try again.",
          "signup-toast"
        );
      }
    } catch (error) {
      console.error("❌ Facebook login error:", error);
      showErrorToast(
        "Failed to sign up with Facebook. Please try again.",
        "signup-toast"
      );
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSubmit(onSubmit)();
    }
  };

  return (
    <>
      {/* Mobile version */}
      <div className="md:hidden">
        <MobileSignupForm
          onSubmit={onSubmit}
          showPassword={showPassword}
          setShowPassword={setShowPassword}
          isLoading={isLoading}
          handleGoogleSignIn={handleGoogleSignIn}
          inputValues={inputValues}
          focusedField={focusedField}
          setFocusedField={setFocusedField}
          loadingProgress={loadingProgress}
          inv={inv}
          passwordChangeCode={passwordChangeCode}
          searchParams={searchParams}
        />
      </div>

      {/* Desktop version */}
      <div className="hidden md:flex flex-col h-screen overflow-hidden justify-between items-stretch">
        {/* Top section */}
        <div className="h-[10%] flex justify-start items-start p-4 md:p-8">
          <button onClick={() => router.back()}>
            <img src="/icons/arrow-left.svg" alt="Back" className="w-6 h-6" />
          </button>
        </div>

        {/* Middle section */}
        <div className="px-8 relative flex flex-col justify-center h-[60%]">
          <div className="text-login w-full">
            <div className="text mb-4">
              <h1 className="text-3xl font-bold mb-1 text-[#2C3E50]">
                Welcome!
              </h1>
              <p className="text-gray-600 text-sm">One step to Membership</p>
            </div>

            <div className="flex flex-row w-full gap-3 mb-4">
              <GBtn onClick={handleGoogleSignIn} />
              <MetaBtn onClick={handleFacebookSignIn} />
              <AppleBtn style="disabled" />
            </div>
          </div>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-4 w-full md:w-4/5"
          >
            <span className="text-base block text-[#2C3E50] mb-2">
              Sign Up With Email
            </span>

            {/* Name fields */}
            <div className="flex flex-wrap gap-4">
              <div className="w-full md:w-[calc(50%-0.5rem)]">
                <div className="relative">
                  <input
                    id="firstName"
                    type="text"
                    className={`w-full px-3 py-4 md:py-6 border border-gray-300 rounded-lg text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                      focusedField === "firstName" ||
                      errors.firstName ||
                      inputValues.firstName
                        ? "pt-6"
                        : ""
                    }`}
                    {...register("firstName")}
                    onFocus={() => setFocusedField("firstName")}
                    onBlur={() => setFocusedField(null)}
                    onChange={handleInputChange}
                    value={inputValues.firstName}
                    onKeyDown={handleKeyDown}
                  />
                  <label
                    htmlFor="firstName"
                    className={`absolute left-3 transition-all duration-200 ${
                      focusedField === "firstName" ||
                      errors.firstName ||
                      inputValues.firstName
                        ? "-top-1 text-xs px-2 text-gray-500 border-3 bg-white"
                        : "top-1/2 -translate-y-1/2 text-gray-400"
                    }`}
                  >
                    First Name
                  </label>
                </div>
                {errors.firstName && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.firstName.message}
                  </p>
                )}
              </div>
              <div className="w-full md:w-[calc(50%-0.5rem)]">
                <div className="relative">
                  <input
                    id="lastName"
                    type="text"
                    className={`w-full px-3 py-4 md:py-6 border border-gray-300 rounded-lg text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                      focusedField === "lastName" ||
                      errors.lastName ||
                      inputValues.lastName
                        ? "pt-6"
                        : ""
                    }`}
                    {...register("lastName")}
                    onFocus={() => setFocusedField("lastName")}
                    onBlur={() => setFocusedField(null)}
                    onChange={handleInputChange}
                    value={inputValues.lastName}
                    onKeyDown={handleKeyDown}
                  />
                  <label
                    htmlFor="lastName"
                    className={`absolute left-3 transition-all duration-200 ${
                      focusedField === "lastName" ||
                      errors.lastName ||
                      inputValues.lastName
                        ? "-top-1 text-xs px-2 text-gray-500 border-3 bg-white"
                        : "top-1/2 -translate-y-1/2 text-gray-400"
                    }`}
                  >
                    Last Name
                  </label>
                </div>
                {errors.lastName && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.lastName.message}
                  </p>
                )}
              </div>
            </div>

            {/* Email field */}
            <div className="w-full">
              <div className="relative">
                <input
                  id="email"
                  type="email"
                  className={`w-full px-3 py-4 md:py-6 border border-gray-300 rounded-lg text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                    focusedField === "email" ||
                    errors.email ||
                    inputValues.email
                      ? "pt-6"
                      : ""
                  }`}
                  {...register("email")}
                  onFocus={() => setFocusedField("email")}
                  onBlur={() => setFocusedField(null)}
                  onChange={handleInputChange}
                  value={inputValues.email}
                  onKeyDown={handleKeyDown}
                />
                <label
                  htmlFor="email"
                  className={`absolute left-3 transition-all duration-200 ${
                    focusedField === "email" ||
                    errors.email ||
                    inputValues.email
                      ? "-top-1 text-xs px-2 text-gray-500 border-3 bg-white"
                      : "top-1/2 -translate-y-1/2 text-gray-400"
                  }`}
                >
                  Email
                </label>
              </div>
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Password fields */}
            <div className="flex flex-wrap gap-4">
              <div className="w-full md:w-[calc(50%-0.5rem)]">
                <div className="relative">
                  <input
                    id="password"
                    {...register("password", {
                      required: "Password is required",
                      minLength: {
                        value: 8,
                        message: "Password must be at least 8 characters",
                      },
                      pattern: {
                        value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,
                        message:
                          "Password must contain at least one uppercase letter, one lowercase letter, and one number",
                      },
                    })}
                    type={showPassword ? "text" : "password"}
                    className={`w-full px-3 py-4 md:py-6 border border-gray-300 rounded-lg pr-10 text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                      focusedField === "password" ||
                      errors.password ||
                      inputValues.password
                        ? "pt-6"
                        : ""
                    }`}
                    onFocus={() => setFocusedField("password")}
                    onBlur={() => setFocusedField(null)}
                    onChange={handleInputChange}
                    value={inputValues.password}
                    onKeyDown={handleKeyDown}
                  />
                  <label
                    htmlFor="password"
                    className={`absolute left-3 transition-all duration-200 ${
                      focusedField === "password" ||
                      errors.password ||
                      inputValues.password
                        ? "-top-1 text-xs px-2 text-gray-500 border-3 bg-white"
                        : "top-1/2 -translate-y-1/2 text-gray-400"
                    }`}
                  >
                    Password
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <img
                      src={
                        showPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"
                      }
                      alt={showPassword ? "Hide password" : "Show password"}
                      className="w-4 h-4 md:w-5 md:h-5"
                    />
                  </button>
                </div>
                {errors.password && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.password.message}
                  </p>
                )}
              </div>
              <div className="w-full md:w-[calc(50%-0.5rem)]">
                <div className="relative">
                  <input
                    id="repeatPassword"
                    {...register("repeatPassword")}
                    type={showPassword ? "text" : "password"}
                    className={`w-full px-3 py-4 md:py-6 border border-gray-300 rounded-lg pr-10 text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                      focusedField === "repeatPassword" ||
                      errors.repeatPassword ||
                      inputValues.repeatPassword
                        ? "pt-6"
                        : ""
                    }`}
                    onFocus={() => setFocusedField("repeatPassword")}
                    onBlur={() => setFocusedField(null)}
                    onChange={handleInputChange}
                    value={inputValues.repeatPassword}
                    onKeyDown={handleKeyDown}
                  />
                  <label
                    htmlFor="repeatPassword"
                    className={`absolute left-3 transition-all duration-200 ${
                      focusedField === "repeatPassword" ||
                      errors.repeatPassword ||
                      inputValues.repeatPassword
                        ? "-top-1 text-xs px-2 text-gray-500 border-3 bg-white"
                        : "top-1/2 -translate-y-1/2 text-gray-400"
                    }`}
                  >
                    Repeat The Password
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <img
                      src={
                        showPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"
                      }
                      alt={showPassword ? "Hide password" : "Show password"}
                      className="w-4 h-4 md:w-5 md:h-5"
                    />
                  </button>
                </div>
                {errors.repeatPassword && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.repeatPassword.message}
                  </p>
                )}
              </div>
            </div>

            {/* Terms checkbox */}
            <div className="w-full">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <input
                    type="checkbox"
                    id="terms"
                    className={`w-6 h-6 aspect-square rounded-md border-2 appearance-none ${
                      errors.agreeTerms ? "border-red-500" : "border-[#cccccc]"
                    }`}
                    {...register("agreeTerms")}
                  />
                  {watch("agreeTerms") && (
                    <div
                      className={`absolute inset-0 flex items-center -top-2 justify-center pointer-events-none ${
                        errors.agreeTerms ? "text-red-500" : "text-[#2C3E50]"
                      }`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  )}
                </div>
                <label
                  htmlFor="terms"
                  className={`text-xs text-gray-600 ${
                    errors.agreeTerms ? "text-red-500" : "text-gray-600"
                  }`}
                >
                  By Continuing With Google, Apple, Or Email, You Agree To
                  Business Insight{" "}
                  <Link
                    href="/privacy"
                    className="text-blue-500 hover:underline"
                  >
                    Terms Of Service And Privacy Policy
                  </Link>
                  .
                </label>
              </div>
            </div>
          </form>
        </div>

        {/* Bottom section */}
        <div className="px-8 pb-6">
          <div className="flex flex-col items-center gap-2">
            <AuthBtn
              text={isLoading ? "Signing Up..." : "Sign Up"}
              style="default"
              onClick={handleSubmit(onSubmit)}
              disabled={isLoading}
              isLoading={isLoading}
              loadingProgress={loadingProgress}
            />
            <div className="text-center">
              <a
                href={`/login${inv !== "/dashboard" ? `?inv=${inv}` : ""}`}
                className="text-sm text-neutral-500 font-extralight hover:underline hover:text-blue-500"
              >
                Already have an account?
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SignupForm;
