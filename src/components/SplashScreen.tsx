import Image from 'next/image';
import { useEffect, useState } from 'react';

const SplashScreen = () => {
  // const [isVisible, setIsVisible] = useState(true);

  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     setIsVisible(false);
  //   }, 2000); // Hide after 2 seconds

  //   return () => clearTimeout(timer);
  // }, []);

  // if (!isVisible) return null;

  return (
    <div className="w-screen h-screen inset-0 z-50 flex items-center justify-center bg-[#2c3e50]">
      <div className="flex flex-col items-center space-y-4 ">
        <div className="relative h-24 w-24 animate-pulse p-4 bg-white rounded-full">
          <Image
            src="/pwa/ios/192.png"
            alt="Business Insight AI"
            fill
            className="object-contain"
          />
        </div>
        <div className="absolute bottom-10 left-1/2 -translate-x-1/2 flex flex-col items-center space-y-2 w-full">
        <p className="text-md font-semibold text-gray-700 text-white ">Social Media Marketing with AI</p>
        </div>
      </div>
    </div>

  );
};

export default SplashScreen; 