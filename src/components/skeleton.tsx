import { cn } from "~/lib/utils";

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  variant?: "rectangular" | "circular" | "text";
  animation?: "pulse" | "wave";
}

const Skeleton = ({ className, variant = "rectangular", animation = "pulse", ...props }: SkeletonProps) => {
  const baseClasses = "bg-gray-200";
  const variantClasses = {
    rectangular: "rounded",
    circular: "rounded-full",
    text: "rounded h-4 w-3/4",
  } as const;

  if (animation === "wave") {
    // Use an inner shimmer bar so the container doesn't translate (prevents layout shifting)
    return (
      <div
        className={cn(
          baseClasses,
          "relative overflow-hidden",
          variantClasses[variant],
          className
        )}
        {...props}
      >
        <span
          aria-hidden
          className={cn(
            "absolute inset-0 -translate-x-full bg-linear-to-r from-transparent via-white/60 to-transparent",
            // Reuse tailwind's shimmer keyframes on the inner bar
            "animate-shimmer"
          )}
        />
      </div>
    );
  }

  // Default pulse animation
  return (
    <div
      className={cn(
        baseClasses,
        "animate-pulse",
        variantClasses[variant],
        className
      )}
      {...props}
    />
  );
};

export default Skeleton;