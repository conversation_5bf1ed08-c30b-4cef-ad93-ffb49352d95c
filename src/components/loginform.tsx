"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm, SubmitHandler } from "react-hook-form";
import toast from "react-hot-toast";
import GBtn from "./gbtn";
import AppleBtn from "./applebtn";
import AuthBtn from "./authbtn";
import MetaBtn from "./metabtn";
import Link from "next/link";
import { signIn } from "next-auth/react";
import { useUserStore } from "~/store/userStore";
import { showErrorToast, showSuccessToast } from "./toasts";
import {
  initFacebookSdk,
  handleFacebookLogin,
  getFacebookUserData,
} from "~/utils/FacebookSDK";
import type { ReadonlyURLSearchParams } from "next/navigation";
import axios from "axios";

interface LoginFormInputs {
  email: string;
  password: string;
  agreeTerms: boolean;
}

interface MobileLoginFormProps {
  onSubmit: SubmitHandler<LoginFormInputs>;
  showPassword: boolean;
  setShowPassword: (show: boolean) => void;
  isLoading: boolean;
  handleGoogleSignIn: () => void;
  emailValue: string;
  passwordValue: string;
  focusedField: string | null;
  setFocusedField: (field: string | null) => void;
  loadingProgress: number;
  inv: string;
  passwordChangeCode?: string;
  searchParams: ReadonlyURLSearchParams;
  handleKeyPress: (event: React.KeyboardEvent) => void;
}

interface GBtnProps {
  onClick: () => void;
  inv?: string;
}

const LoginForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormInputs>();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [emailValue, setEmailValue] = useState("");
  const [passwordValue, setPasswordValue] = useState("");
  const inv = searchParams.get("inv") || "/dashboard";
  const passwordChangeCode = searchParams.get("password-change") || undefined;

  useEffect(() => {
    // Any other initialization logic you need
  }, []);

  const handleFacebookSignIn = async () => {
    console.log("🚀 Starting Facebook sign-in process...");
    try {
      console.log("📱 Initializing Facebook SDK...");
      await initFacebookSdk();

      console.log("🔑 Requesting Facebook login...");
      const loginResult = await handleFacebookLogin();

      if (!loginResult) {
        console.error("❌ Facebook login failed - no login result");
        showErrorToast(
          "Facebook login failed. Please try again.",
          "login-toast"
        );
        return;
      }

      console.log("✅ Facebook login successful:", loginResult);
      const { accessToken } = loginResult;

      // Exchange Facebook token with backend
      try {
        console.log("🔄 Exchanging Facebook token with backend...");
        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}/api/meta-redirect/`,
          {
            token: accessToken,
          }
        );

        const data = response.data;
        console.log("📡 Backend response:", data);

        if (data.success) {
          console.log("✅ Token exchange successful");
          // Save tokens to sessionStorage and localStorage
          sessionStorage.setItem("accessToken", data.access_token);
          sessionStorage.setItem("refreshToken", data.refresh_token);
          sessionStorage.setItem("status", "authenticated");

          localStorage.setItem("accessToken", data.access_token);
          localStorage.setItem("refreshToken", data.refresh_token);
          localStorage.setItem("status", "authenticated");

          showSuccessToast(
            "Successfully logged in with Facebook!",
            "login-toast"
          );

          // Redirect to appropriate page
          const invParam = searchParams.get("inv") || "";
          const redirectUrl = passwordChangeCode
            ? `/user/change-password/${passwordChangeCode}`
            : invParam
            ? `/dashboard/team/${invParam}`
            : "/dashboard";

          console.log("🔄 Redirecting to:", redirectUrl);
          router.push(redirectUrl);
        } else {
          console.error("❌ Token exchange failed:", data);
          showErrorToast(
            "Failed to complete login. Please try again.",
            "login-toast"
          );
        }
      } catch (error) {
        console.error("❌ Error exchanging Facebook token:", error);
        showErrorToast(
          "Failed to complete login. Please try again.",
          "login-toast"
        );
      }
    } catch (error) {
      console.error("❌ Facebook login error:", error);
      showErrorToast(
        "Failed to login with Facebook. Please try again.",
        "login-toast"
      );
    }
  };

  const onSubmit: SubmitHandler<LoginFormInputs> = async (data) => {
    setIsLoading(true);
    setLoadingProgress(0);

    const simulateProgress = () => {
      setLoadingProgress((prevProgress) => {
        const newProgress = prevProgress + 10;
        return newProgress > 90 ? 90 : newProgress;
      });
    };

    const progressInterval = setInterval(simulateProgress, 300);

    try {
      // Get CSRF token from props or context instead of hook
      const csrfToken = useUserStore.getState().csrfToken;

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
        {
          email: data.email,
          password: data.password,
        }
      );

      const result = response.data;

      console.log("Result:", result);

      if (result.success) {
        // Save to both sessionStorage and localStorage
        sessionStorage.setItem("accessToken", result.access_token);
        sessionStorage.setItem("refreshToken", result.refresh_token);
        sessionStorage.setItem("status", "authenticated");

        localStorage.setItem("accessToken", result.access_token);
        localStorage.setItem("refreshToken", result.refresh_token);
        localStorage.setItem("status", "authenticated");

        showSuccessToast("Login successful!", "login-toast");

        // Redirect based on password-change code if present
        if (passwordChangeCode) {
          router.push(`/user/change-password/${passwordChangeCode}`);
        } else {
          router.push(
            searchParams.get("inv") ? `/dashboard/team/${inv}` : "/dashboard"
          );
        }
        setLoadingProgress(100);
      }
    } catch (error) {
      const result = error.response.data;
      console.log("Login error:", result);
      if (result.message === "The user is not verified") {
        showErrorToast(result.message, "login-toast");
        const verifyPath = `/verify${inv ? `?inv=${inv}` : ""}${
          result.access_token ? `&access=${result.access_token}` : ""
        }`;
        setTimeout(() => {
          router.push(verifyPath);
        }, 1000);
      } else {
        console.log("Login error:", result);
        showErrorToast(result.message, "login-toast");
      }
    } finally {
      clearInterval(progressInterval);
      setIsLoading(false);
      setLoadingProgress(0);
    }
  };

  const handleGoogleSignIn = () => {
    signIn("google", {
      callbackUrl: passwordChangeCode
        ? `/user/change-password/${passwordChangeCode}`
        : searchParams.get("inv")
        ? "/dashboard/team/" + inv
        : "/dashboard",
    });
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      handleSubmit(onSubmit)(event);
    }
  };

  return (
    <>
      {/* Mobile version */}
      <div className="md:hidden">
        <MobileLoginForm
          onSubmit={onSubmit}
          showPassword={showPassword}
          setShowPassword={setShowPassword}
          isLoading={isLoading}
          handleGoogleSignIn={handleGoogleSignIn}
          emailValue={emailValue}
          passwordValue={passwordValue}
          focusedField={focusedField}
          setFocusedField={setFocusedField}
          loadingProgress={loadingProgress}
          inv={inv}
          passwordChangeCode={passwordChangeCode}
          searchParams={searchParams}
          handleKeyPress={handleKeyPress}
        />
      </div>

      {/* Desktop version - existing code */}
      <div className="hidden md:flex flex-col h-full">
        {/* Top section - 20% */}
        <div className="h-[10%] flex justify-start items-start p-4 md:p-8">
          <button onClick={() => router.back()} className="h-full w-full">
            <img
              src="/icons/arrow-left.svg"
              alt="Back"
              className="w-5 h-5 md:w-6 md:h-6"
            />
          </button>
        </div>

        {/* Middle section - 60% */}
        <div className="h-[75%] p-4 md:p-8 overflow-y-auto flex flex-col justify-center">
          <div className="text-login w-full">
            <div className="text mb-6 md:mb-8">
              <h1 className="text-3xl md:text-4xl font-bold mb-2 text-[#2C3E50]">
                Welcome Back!
              </h1>
              <p className="text-gray-600 text-sm md:text-base">
                One step to Login
              </p>
            </div>

            <div className="flex flex-col md:flex-row w-full gap-3 md:gap-4 mb-6">
              <GBtn onClick={handleGoogleSignIn} inv={inv} />
              <MetaBtn onClick={handleFacebookSignIn} inv={inv} />
              <AppleBtn style="disabled" />
            </div>
          </div>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-4 md:space-y-6 w-full md:w-4/5"
          >
            <span className="text-base md:text-lg mb-4 md:mb-8 block text-[#2C3E50]">
              Continue With Email
            </span>
            <div className="mb-3 md:mb-4 relative">
              <input
                type="email"
                id="email"
                className={`w-full px-3 py-4 md:py-6 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-0 focus:border-[#2C3E50] focus:border-2 md:text-base ${
                  focusedField === "email" || errors.email || emailValue
                    ? "pt-6"
                    : ""
                }`}
                {...register("email", {
                  required: "Email is required",
                  onChange: (e) => setEmailValue(e.target.value),
                })}
                onFocus={() => setFocusedField("email")}
                onBlur={() => setFocusedField(null)}
                onKeyPress={handleKeyPress}
              />
              <label
                htmlFor="email"
                className={`absolute left-3 transition-all duration-200 ${
                  focusedField === "email" || errors.email || emailValue
                    ? "-top-2 text-xs px-1 text-gray-500 bg-white"
                    : "top-1/2 -translate-y-1/2 text-gray-400"
                }`}
              >
                Email
              </label>
              {errors.email && (
                <p className="text-red-500 text-xs md:text-sm mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>
            <div className="relative">
              <input
                id="password"
                {...register("password", {
                  required: "Password is required",
                  onChange: (e) => setPasswordValue(e.target.value),
                })}
                type={showPassword ? "text" : "password"}
                className={`w-full px-3 py-4 md:py-6 border border-gray-300 rounded-lg pr-10 text-sm focus:outline-none focus:ring-0 focus:border-[#2C3E50] focus:border-2 md:text-base ${
                  focusedField === "password" ||
                  errors.password ||
                  passwordValue
                    ? "pt-6"
                    : ""
                }`}
                onFocus={() => setFocusedField("password")}
                onBlur={() => setFocusedField(null)}
                onKeyPress={handleKeyPress}
              />
              <label
                htmlFor="password"
                className={`absolute left-3 transition-all duration-200 ${
                  focusedField === "password" ||
                  errors.password ||
                  passwordValue
                    ? "-top-1 text-xs px-2 text-gray-500 border-3 bg-white"
                    : "top-1/2 -translate-y-1/2 text-gray-400"
                }`}
              >
                Password
              </label>
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <img
                  src={showPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                  alt={showPassword ? "Hide password" : "Show password"}
                  className="w-4 h-4 md:w-5 md:h-5"
                />
              </button>
              {errors.password && (
                <p className="text-red-500 text-xs md:text-sm mt-1">
                  {errors.password.message}
                </p>
              )}
            </div>
          </form>
        </div>

        {/* Bottom section - 20% */}
        <div className="h-[10%] p-4 md:p-8 flex flex-col justify-center items-center">
          <div className="btn flex justify-center items-center flex-col gap-3 md:gap-4">
            <AuthBtn
              text={isLoading ? "Signing In..." : "Sign In"}
              style="default"
              onClick={handleSubmit(onSubmit)}
              disabled={isLoading}
              isLoading={isLoading}
              loadingProgress={loadingProgress}
            />

            <div className="text-center flex flex-col justify-center gap-2 items-center">
              <Link
                href={`/signup${inv !== "/dashboard" ? `?inv=${inv}` : ""}`}
                className="text-xs md:text-lg text-black hover:underline hover:text-blue-500"
              >
                Don't have an account?
              </Link>
              <Link
                href={`/password-recovery${
                  inv !== "/dashboard" ? `?inv=${inv}` : ""
                }`}
                className="text-xs md:text-sm text-neutral-500 font-extralight hover:underline hover:text-blue-500"
              >
                Forgot Password?
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

const MobileLoginForm = ({
  onSubmit,
  showPassword,
  setShowPassword,
  isLoading,
  handleGoogleSignIn,
  emailValue,
  passwordValue,
  focusedField,
  setFocusedField,
  loadingProgress,
  passwordChangeCode,
  searchParams,
  inv,
  handleKeyPress,
}: MobileLoginFormProps) => {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormInputs>();

  const handleFacebookSignIn = async () => {
    try {
      await initFacebookSdk();
      const loginResult = await handleFacebookLogin();

      if (!loginResult) {
        showErrorToast(
          "Facebook login failed. Please try again.",
          "login-toast"
        );
        return;
      }

      const { accessToken } = loginResult;
      const userData = await getFacebookUserData(accessToken);

      if (!userData) {
        showErrorToast(
          "Could not fetch user data from Facebook.",
          "login-toast"
        );
        return;
      }

      const invParam = searchParams.get("inv") || "";
      const callbackUrl = passwordChangeCode
        ? `/user/change-password/${passwordChangeCode as string}`
        : invParam
        ? `/dashboard/team/${invParam}`
        : "/dashboard";

      // Sign in with NextAuth using the Facebook provider
      const result = await signIn("facebook", {
        redirect: false,
        callbackUrl,
        access_token: accessToken,
        user_data: userData,
      });

      if (result?.error) {
        showErrorToast(result.error, "login-toast");
        return;
      }

      if (result?.url) {
        showSuccessToast(
          "Successfully logged in with Facebook!",
          "login-toast"
        );
        router.push(result.url);
      }
    } catch (error) {
      console.error("Facebook login error:", error);
      showErrorToast(
        "Failed to login with Facebook. Please try again.",
        "login-toast"
      );
    }
  };

  return (
    <div className="w-full h-full bg-white md:hidden flex justify-center items-center">
      <div className="p-6 w-full h-full flex flex-col">
        {/* Back button for mobile */}
        <div className="flex justify-start items-start mb-4">
          <button
            onClick={() => router.back()}
            className="flex flex-row items-center gap-2"
          >
            <img src="/icons/arrow-left.svg" alt="Back" className="w-5 h-5" />
            <span className="text-[#0f0f0f]">Back</span>
          </button>
        </div>

        <div className="flex-1 flex flex-col justify-center">
          <div className="flex flex-col w-full ">
            <div className="text-[#2c3e50] text-[1.75rem] font-semibold ">
              Welcome Back!
            </div>
            <div className="mt-4 text-[#1d1d1d] text-base font-normal ">
              One step to Login
            </div>
          </div>
          <div className="mt-15 w-full">
            <div className="text-[#2c3e50] text-[0.938rem] font-medium  capitalize">
              Continue with Email
            </div>

            <form
              onSubmit={handleSubmit(onSubmit)}
              className="my-4 space-y-4"
            >
              <div className="relative">
                <input
                  type="email"
                  {...register("email")}
                  className="w-full h-[4.313rem] px-4 rounded-lg border border-[#cccccc]/80"
                  onFocus={() => setFocusedField("email")}
                  onBlur={() => setFocusedField(null)}
                  onKeyPress={handleKeyPress}
                />
                <label
                  htmlFor="email"
                  className={`absolute left-3 transition-all duration-200 ${
                    focusedField === "email" ||
                    errors.email ||
                    emailValue ||
                    emailValue?.length > 0 ||
                    register("email")
                      ? "-top-2 text-xs px-1 text-gray-500 bg-white"
                      : "top-1/2 -translate-y-1/2 text-gray-400"
                  }`}
                >
                  Email
                </label>
                {errors.email && (
                  <p className="text-red-500 text-xs md:text-sm mt-1">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  {...register("password")}
                  className="w-full h-[4.313rem] px-4 rounded-lg border border-[#cccccc]/80"
                  onFocus={() => setFocusedField("password")}
                  onBlur={() => setFocusedField(null)}
                  onKeyPress={handleKeyPress}
                />
                <label
                  className={`absolute left-3 transition-all duration-200 ${
                    focusedField === "password" ||
                    passwordValue ||
                    register("password")
                      ? "-top-2 text-xs px-1 text-gray-500 bg-white"
                      : "top-1/2 -translate-y-1/2 text-gray-400"
                  }`}
                >
                  Password
                </label>
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2"
                >
                  <img
                    src={showPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                    alt={showPassword ? "Hide password" : "Show password"}
                    className="w-6 h-6"
                  />
                </button>
              </div>
            </form>
          </div>

          <Link
            href={`/password-recovery${
              inv !== "/dashboard" ? `?inv=${inv}` : ""
            }`}
            className="block mt-4 w-full text-left text-[#2c3e50] text-[0.813rem] font-normal "
          >
            Forgot Password?
          </Link>

          <div className="w-full h-[3.438rem] mt-8  rounded-lg  flex justify-center items-center">
            <button
              className={`py-4 md:px-32 w-full bg-[#263645] md:text-xl text-nowrap text-md px-16 rounded-md text-white hover:bg-[#DADDE1] hover:text-[#263645] font-bold transition-all duration-200 relative overflow-hidden ${
                isLoading ? "cursor-wait" : ""
              }`}
              disabled={isLoading}
              onClick={handleSubmit(onSubmit)}
            >
              <span className="relative z-10 flex items-center justify-center">
                {isLoading && (
                  <div className="inline-block w-5 h-5 mr-3 align-middle">
                    <svg className="animate-spin" viewBox="0 0 24 24">
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                        fill="none"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                      />
                    </svg>
                  </div>
                )}
                {isLoading ? "Signing In..." : "Sign In"}
              </span>
            </button>
          </div>

          <div className="mt-6 mb-6 text-center">
            <Link
              href={`/signup${inv !== "/dashboard" ? `?inv=${inv}` : ""}`}
              className="text-[#2c3e50] text-[0.875rem] font-medium"
            >
              Don't have an account yet?
            </Link>
          </div>

          <div className="flex items-center mb-6 w-full">
            <hr className="grow border border-t-[0.2px] border-[#2c3e50]/40 text-[#2c3e50] " />
            <span className="px-4 text-[#2c3e50] text-[0.813rem] font-normal">
              Or
            </span>
            <hr className="grow border border-t-[0.2px] border-[#2c3e50]/40 text-[#2c3e50] " />
          </div>

          <div className="flex flex-col md:flex-row w-full gap-3 md:gap-4 mb-6">
            <GBtn onClick={handleGoogleSignIn} inv={inv} />
            <MetaBtn onClick={handleFacebookSignIn} inv={inv} />
            <AppleBtn style="disabled" inv={inv} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
