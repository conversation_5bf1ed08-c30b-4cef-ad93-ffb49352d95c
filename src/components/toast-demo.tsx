"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { useEnhancedToast } from './enhanced-toast-utils';

const ToastDemo: React.FC = () => {
  const { showSuccess, showError, showWarning, showInfo, dismiss } = useEnhancedToast();

  const buttonVariants = {
    hover: { 
      scale: 1.02,
      transition: { duration: 0.2 }
    },
    tap: { 
      scale: 0.98,
      transition: { duration: 0.1 }
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div 
      className="p-6 bg-white rounded-xl shadow-lg max-w-md mx-auto"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.h2 
        className="text-2xl font-bold text-gray-800 mb-6 text-center"
        variants={itemVariants}
      >
        Enhanced Toast Demo
      </motion.h2>
      
      <motion.div className="space-y-4" variants={itemVariants}>
        <motion.button
          onClick={() => showSuccess(
            "Operation completed successfully!",
            "Enhanced with custom icons, smooth animations, and gradient progress bars"
          )}
          className="w-full bg-linear-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-lg"
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
        >
          ✅ Show Success Toast
        </motion.button>

        <motion.button
          onClick={() => showError(
            "Failed to process request",
            "Featuring improved error styling with better visual hierarchy and custom SVG icons"
          )}
          className="w-full bg-linear-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-lg"
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
        >
          ❌ Show Error Toast
        </motion.button>

        <motion.button
          onClick={() => showWarning(
            "Storage space running low",
            "Now with contextual colors and framer-motion animations for better user experience"
          )}
          className="w-full bg-linear-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-lg"
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
        >
          ⚠️ Show Warning Toast
        </motion.button>

        <motion.button
          onClick={() => showInfo(
            "New feature available",
            "Beautiful new design with custom SVG icons and enhanced UX patterns"
          )}
          className="w-full bg-linear-to-r from-blue-500 to-sky-500 hover:from-blue-600 hover:to-sky-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-lg"
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
        >
          ℹ️ Show Info Toast
        </motion.button>

        <motion.button
          onClick={() => dismiss()}
          className="w-full bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
        >
          Dismiss All Toasts
        </motion.button>
      </motion.div>

      <motion.div 
        className="mt-6 p-4 bg-gray-50 rounded-lg"
        variants={itemVariants}
      >
        <h3 className="text-sm font-semibold text-gray-700 mb-2">Features:</h3>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• Custom SVG icons with contextual colors</li>
          <li>• Smooth framer-motion animations with spring physics</li>
          <li>• Gradient progress bars with timing visualization</li>
          <li>• Enhanced color schemes and visual hierarchy</li>
          <li>• Interactive close buttons with hover effects</li>
          <li>• Improved typography and spacing</li>
          <li>• Pulse animations and drop shadows</li>
          <li>• Better accessibility and focus states</li>
        </ul>
      </motion.div>
    </motion.div>
  );
};

export default ToastDemo;
