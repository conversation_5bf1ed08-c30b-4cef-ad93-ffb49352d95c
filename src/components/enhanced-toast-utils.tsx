"use client";

import React from 'react';
import toast from 'react-hot-toast';
import EnhancedToast from './enhanced-toast';

interface ToastOptions {
  duration?: number;
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
  id?: string;
}

// Helper function to get mobile-optimized position
const getMobilePosition = (position: ToastOptions['position'] = 'top-right') => {
  if (typeof window !== 'undefined' && window.innerWidth < 640) {
    // On mobile, use top-center for better visibility
    return position.includes('top') ? 'top-center' : 'bottom-center';
  }
  return position;
};

// Enhanced toast functions with framer-motion animations
export const showEnhancedSuccessToast = (
  message: string,
  subMessage?: string,
  options: ToastOptions = {}
) => {
  const { duration = 4000, position = 'top-right', id } = options;
  const optimizedPosition = getMobilePosition(position);

  return toast.custom(
    (t) => (
      <EnhancedToast
        message={message}
        subMessage={subMessage}
        type="success"
        visible={t.visible}
        onDismiss={() => toast.dismiss(t.id)}
        duration={duration}
      />
    ),
    {
      duration,
      position: optimizedPosition,
      id
    }
  );
};

export const showEnhancedErrorToast = (
  message: string,
  subMessage?: string,
  options: ToastOptions = {}
) => {
  const { duration = 5000, position = 'top-right', id } = options;
  const optimizedPosition = getMobilePosition(position);

  return toast.custom(
    (t) => (
      <EnhancedToast
        message={message}
        subMessage={subMessage}
        type="error"
        visible={t.visible}
        onDismiss={() => toast.dismiss(t.id)}
        duration={duration}
      />
    ),
    {
      duration,
      position: optimizedPosition,
      id
    }
  );
};

export const showEnhancedWarningToast = (
  message: string,
  subMessage?: string,
  options: ToastOptions = {}
) => {
  const { duration = 4500, position = 'top-right', id } = options;
  const optimizedPosition = getMobilePosition(position);

  return toast.custom(
    (t) => (
      <EnhancedToast
        message={message}
        subMessage={subMessage}
        type="warning"
        visible={t.visible}
        onDismiss={() => toast.dismiss(t.id)}
        duration={duration}
      />
    ),
    {
      duration,
      position: optimizedPosition,
      id
    }
  );
};

export const showEnhancedInfoToast = (
  message: string,
  subMessage?: string,
  options: ToastOptions = {}
) => {
  const { duration = 3500, position = 'top-right', id } = options;
  const optimizedPosition = getMobilePosition(position);

  return toast.custom(
    (t) => (
      <EnhancedToast
        message={message}
        subMessage={subMessage}
        type="info"
        visible={t.visible}
        onDismiss={() => toast.dismiss(t.id)}
        duration={duration}
      />
    ),
    {
      duration,
      position: optimizedPosition,
      id
    }
  );
};

// Convenience function for quick toasts
export const enhancedToast = {
  success: (message: string, subMessage?: string, options?: ToastOptions) => 
    showEnhancedSuccessToast(message, subMessage, options),
  
  error: (message: string, subMessage?: string, options?: ToastOptions) => 
    showEnhancedErrorToast(message, subMessage, options),
  
  warning: (message: string, subMessage?: string, options?: ToastOptions) => 
    showEnhancedWarningToast(message, subMessage, options),
  
  info: (message: string, subMessage?: string, options?: ToastOptions) => 
    showEnhancedInfoToast(message, subMessage, options),
  
  // Dismiss all toasts
  dismiss: (id?: string) => toast.dismiss(id),
  
  // Remove all toasts
  remove: (id?: string) => toast.remove(id)
};

// Hook for using enhanced toasts
export const useEnhancedToast = () => {
  return {
    showSuccess: showEnhancedSuccessToast,
    showError: showEnhancedErrorToast,
    showWarning: showEnhancedWarningToast,
    showInfo: showEnhancedInfoToast,
    dismiss: toast.dismiss,
    remove: toast.remove,
    toast: enhancedToast
  };
};

export default enhancedToast;
