'use client';

import { useSession } from 'next-auth/react';
import { useEffect } from 'react';
import { Session } from 'next-auth';

interface ExtendedSession extends Session {
  accessToken?: string;
  refreshToken?: string;
  status?: string;
}

export default function SessionInitializer() {
  const { data: session } = useSession();
  const extendedSession = session as ExtendedSession;

  useEffect(() => {
    if (extendedSession?.accessToken) {
      console.log('🔄 Storing session data in SessionInitializer');
      // Store session data in sessionStorage
      sessionStorage.setItem('accessToken', extendedSession.accessToken);
      sessionStorage.setItem('refreshToken', extendedSession.refreshToken || '');
      sessionStorage.setItem('status', 'authenticated');
      console.log('✅ Session data stored successfully');
    }
  }, [extendedSession]);

  return null; // This component doesn't render anything
} 