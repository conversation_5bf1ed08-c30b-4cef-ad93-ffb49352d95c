"use client";

import React from "react";
import {
  AccountReach<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Accounts<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Followers<PERSON><PERSON><PERSON><PERSON>hart,
  <PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>,
  GenderDemographicsChart,
  <PERSON>s<PERSON><PERSON>,
  <PERSON>lies<PERSON>hart,
  ProfileLinksTaps<PERSON>hart,
  Reach<PERSON>hart,
  Saved<PERSON>hart,
  SharesChart,
  FollowsAndUnfollowsChart,
  TotalInteractionsChart,
  TopPostsChart,
  ViewsChart,
  EngagedAudienceDemographicsChart,
  FollowerDemographics<PERSON>hart,
  FollowersCountriesChart,
} from "../app/dashboard/analytics/components/charts";

interface SelectWidgetModalProps {
  onClose: () => void;
  onSelect: (widget: string) => void;
  availableWidgets: string[];
  currentWidgets?: string[];
  maxReached?: boolean;
  // Live analytics props for previews
  analyticsData?: any | null;
  contentTypeData?: { name: string; value: number }[];
  cityData?: { name: string; value: number }[];
  followersOverviewData?: any[];
  selectedSocial?: { platform?: string; [k: string]: any } | null;
  previewHeight?: string;
}

const SelectWidgetModal: React.FC<SelectWidgetModalProps> = ({
  onClose,
  onSelect,
  availableWidgets,
  currentWidgets = [],
  maxReached = false,
  analyticsData = null,
  contentTypeData = [],
  cityData = [],
  followersOverviewData = [],
  selectedSocial = { platform: "instagram" },
  previewHeight = "380px",
}) => {
  // Give previews extra vertical space on small screens for better readability
  const mobilePreviewHeight = "320px";
  const widgets = [
    // Only display widgets provided by parent as available
    ...availableWidgets,
  ];

  const renderPreview = (widget: string) => {
    switch (widget) {
      // Removed numeric-only Total Followers from modal
      case "Overview of Followers":
        return (
          <FollowersOverviewChart
            data={followersOverviewData || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Account Reach":
        return (
          <ReachChart
            data={analyticsData?.reach || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Account Engaged":
        return (
          <AccountsEngagedChart
            data={analyticsData?.accounts_engaged || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Gender":
        return (
          <GenderDemographicsChart
            data={analyticsData?.follower_demographics || []}
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );

      case "Follower Distribution":
        return (
          <FollowerViewsChart
            data={analyticsData?.views || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Account Reach / Content Type":
        return (
          <ContentTypeChart
            data={contentTypeData}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Account Reach / Cities":
        return (
          <AccountReachCitiesChart
            data={cityData}
            timeRange="30d"
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Followers / Countries":
        return (
          <FollowersCountriesChart
            data={analyticsData?.follower_demographics || []}
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Account Interactions / Reel Interactions":
        return (
          <TotalInteractionsChart
            data={analyticsData?.total_interactions || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Account Interactions / Top Post":
        return (
          <TopPostsChart
            data={analyticsData?.top_posts || []}
            type="posts"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Likes Chart":
        return (
          <LikesChart
            data={analyticsData?.likes || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Comments Chart":
        return (
          <CommentsChart
            data={analyticsData?.comments || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Shares Chart":
        return (
          <SharesChart
            data={analyticsData?.shares || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Saved Chart":
        return (
          <SavedChart
            data={analyticsData?.saves || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Profile Links Taps":
        return (
          <ProfileLinksTapsChart
            data={analyticsData?.profile_links_taps || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Follows and Unfollows":
        return (
          <FollowsAndUnfollowsChart
            data={analyticsData?.follows_and_unfollows || []}
            timeRange="30d"
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Engaged Audience Demographics":
        return (
          <EngagedAudienceDemographicsChart
            data={analyticsData?.engaged_audience_demographics || []}
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      case "Audience Age":
        return (
          <FollowerDemographicsChart
            data={analyticsData?.follower_demographics || []}
            selectedSocial={selectedSocial as any}
            height={previewHeight}
            mobileHeight={mobilePreviewHeight}
          />
        );
      default:
        return (
          <div className="bg-white p-4 rounded-lg shadow min-h-[440px] sm:min-h-[380px]">
            <h2 className="text-lg font-semibold">{widget}</h2>
            <div className="text-gray-400">Preview not available</div>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50  flex justify-center items-center z-50">
      <div className="bg-white flex flex-col md:w-[80%] w-[95vw] md:h-[90%] h-[95vh] rounded-3xl animate-slideUp">
        {/* Header */}
        <div className="p-6 flex justify-between items-center border-b">
          <div>
            <h2 className="text-2xl font-semibold">Add New Widget</h2>
            <p className="text-sm text-gray-500 mt-1">
              {maxReached
                ? "Maximum number of widgets reached. Remove a widget to add a new one."
                : "Select a widget to add to your dashboard"}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 no-scrollbar">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 animate-bounceUp">
            {widgets.map((widget) => {
              const isAvailable = availableWidgets.includes(widget);
              const isCurrent = currentWidgets.includes(widget);
              const isDisabled = maxReached || !isAvailable || isCurrent;

              return (
                <div
                  key={widget}
                  className={`bg-white rounded-lg border-2 transition-all cursor-pointer ${
                    isDisabled
                      ? "border-gray-200 opacity-50 cursor-not-allowed"
                      : "border-gray-200 hover:border-blue-500 hover:shadow-lg"
                  }`}
                  onClick={() => {
                    if (!isDisabled) {
                      onSelect(widget);
                    }
                  }}
                >
                  <div className="p-4">
                    {renderPreview(widget)}
                    {isCurrent && (
                      <div className="mt-2 text-center">
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          Already Added
                        </span>
                      </div>
                    )}
                    {maxReached && !isCurrent && (
                      <div className="mt-2 text-center">
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          Max widgets reached
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelectWidgetModal;
