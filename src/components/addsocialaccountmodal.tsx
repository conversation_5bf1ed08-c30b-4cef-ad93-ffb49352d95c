"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { useUserStore } from "~/store/userStore";
import { showErrorToast } from "./toasts";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import axios from "axios";

interface SocialAccount {
  platform: string;
  social_id: string;
  social_name: string;
  username: string;
}

interface WorkspaceDetails {
  social_accounts?: SocialAccount[];
}

interface AddSocialAccountModalProps {
  onClose: () => void;
  onAddAccount: (platform: string) => void;
  dim?: boolean;
  page?: string;
}

const AddSocialAccountModal: React.FC<AddSocialAccountModalProps> = ({
  onClose,
  onAddAccount,
  dim = false,
  page,
}) => {
  const router = useRouter();
  const [selectedPlatform, setSelectedPlatform] = React.useState<string | null>(
    null
  );
  const { selectedWorkspace, selectedWorkspaceDetails } = useUserStore();
  const [loginUrl, setLoginUrl] = React.useState<string | null>(null);
  const pathname = usePathname();
  const [path, setPath] = React.useState<string | null>(null);
  const [isFirstTime, setIsFirstTime] = React.useState(true);
  const [showSecondPulse, setShowSecondPulse] = React.useState(false);
  const [isOnboarding, setIsOnboarding] = useState(false);

  console.log(pathname);

  useEffect(() => {
    setPath(pathname);
    if (pathname?.includes("/onboarding")) {
      setIsOnboarding(true);
    }
  }, [pathname]);

  // Get existing social accounts
  const existingSocialAccounts =
    (selectedWorkspaceDetails as WorkspaceDetails)?.social_accounts || [];
  const existingPlatforms = existingSocialAccounts.map((account) =>
    account.platform.toLowerCase()
  );

  const handlePlatformClick = async (platform: string) => {
    if (!selectedWorkspace) {
      showErrorToast("Please select a workspace first", "newpost-toast");
      return;
    }

    if (platform === "Instagram") {
      const accessToken = sessionStorage.getItem("accessToken");
      const csrfToken = useUserStore.getState().csrfToken;

      console.log(csrfToken);

      if (accessToken) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        try {
          const response = await axios.get(
            `${process.env.NEXT_PUBLIC_API_URL}/api/social/instagram/auth/`,
            {
              params: {
                redirect_url: window.location.href,
                workspace_name: selectedWorkspace,
              },
              headers: {
                Authorization: `Bearer ${accessToken}`,
                csrf_token: csrfToken,
              },
            }
          );
          setLoginUrl(response.data.message);
        } catch (error: any) {
          const result = error.response?.data;
          console.error("Error fetching Instagram auth URL:", result);
          showErrorToast(
            result?.message ||
              "An unexpected error occurred. Please try again.",
            "verify-toast"
          );
        }
      }
    } else if (platform === "Facebook") {
      const accessToken = sessionStorage.getItem("accessToken");

      if (accessToken) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        try {
          const response = await axios.get(
            `${process.env.NEXT_PUBLIC_API_URL}/api/social/facebook/auth/`,
            {
              params: {
                redirect_url: window.location.href,
                workspace_name: selectedWorkspace,
              },
              headers: {
                Authorization: `Bearer ${accessToken}`,
              },
            }
          );
          setLoginUrl(response.data.message);
        } catch (error: any) {
          const result = error.response?.data;
          console.error("Error fetching Facebook auth URL:", result);
          showErrorToast(
            result?.message ||
              "An unexpected error occurred. Please try again.",
            "verify-toast"
          );
        }
      }
    }
  };

  const socialPlatforms = [
    {
      name: "Instagram",
      icon: <i className="fa-brands fa-instagram text-[#E4405F] text-4xl" />,
      // Instagram should always be clickable so users can change account
      disabled: false,
      onClick: () => handlePlatformClick("Instagram"),
      isConnected: existingPlatforms.includes("instagram"),
    },
    {
      name: "Facebook",
      icon: <i className="fa-brands fa-facebook text-[#1877F2] text-4xl" />,
      disabled: existingPlatforms.includes("facebook"),
      onClick: () => handlePlatformClick("Facebook"),
      isConnected: existingPlatforms.includes("facebook"),
    },
  ];

  useEffect(() => {
    console.log(loginUrl);
    if (loginUrl && isFirstTime) {
      const timer = setTimeout(() => {
        if (loginUrl) {
          router.push(loginUrl);
        }
        setIsFirstTime(false);
      }, 2500);
      return () => clearTimeout(timer);
    }
  }, [loginUrl, router]);

  // Add this useEffect to handle the second pulse after return
  useEffect(() => {
    if (!isFirstTime && window.location.search.includes("code")) {
      setShowSecondPulse(true);
    }
  }, [isFirstTime]);

  const handleSocialClick = () => {
    if (loginUrl) {
      setTimeout(() => {
        router.push(loginUrl);
      }, 500);
    }
  };

  return (
    <div
      className={`${
        window.location.pathname.includes("/no-social") || page == "onboarding"
          ? "relative bg-transparent! w-full flex items-center justify-center mx-auto p-0!"
          : "fixed"
      } inset-0 ${
        dim ? "bg-black/30" : ""
      } flex items-center justify-center p-4 z-[100]`}
      style={{ backdropFilter: dim ? "blur(2px)" : "none" }}
    >
      <div
        className={`bg-white p-2 sm:p-4 rounded-2xl shadow-2xl w-full border border-gray-100 add-social-modal-content ${
          page !== "onboarding" ? "max-w-2xl" : ""
        }`}
        style={{
          boxShadow:
            "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)",
        }}
      >
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-linear-to-br from-purple-500 via-pink-500 to-orange-400 rounded-xl flex items-center justify-center shadow-lg">
              <img
                src="/icons/performance/instagram-on.svg"
                alt="Instagram"
                className="w-5 h-5 filter brightness-0 invert"
              />
            </div>
            <h2 className="text-lg sm:text-xl font-bold">
              Let's add some Social accounts
            </h2>
          </div>
          {window.location.pathname == "/dashboard/no-social" ||
          page == "onboarding" ? (
            ""
          ) : (
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-red-500 w-10 h-10 rounded-xl border border-gray-200 hover:border-red-200 flex items-center justify-center transition-all duration-200 bg-gray-50 hover:bg-red-50"
            >
              <svg
                className="w-5 h-5 sm:w-6 sm:h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          )}
        </div>

        <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">
          You only need to add one social account right now. You can always add
          more later!
        </p>
        <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">
          Choose a social media to add an account
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
          {socialPlatforms.map((platform) => (
            <button
              key={platform.name}
              className={`flex flex-col items-center justify-center p-2 sm:p-4 border border-gray-200 rounded-lg transition-colors ${
                platform.disabled && platform.name !== "Instagram"
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:bg-gray-50"
              }`}
              onClick={() => {
                // Allow Instagram clicks even when already connected so users can change the account
                if (platform.name === "Instagram" || !platform.disabled) {
                  handlePlatformClick(platform.name);

                  setTimeout(() => {
                    setSelectedPlatform(platform.name);
                  }, 750);
                }
              }}
              disabled={platform.disabled && platform.name !== "Instagram"}
              title={
                platform.isConnected ? "Change connected account" : undefined
              }
            >
              {platform.icon}
              <span className="mt-1 sm:mt-2 text-xs sm:text-sm">
                {platform.name}
              </span>
            </button>
          ))}

          {!window.location.pathname.includes("/onboarding") && (
            <>
              <button
                key={"addmore"}
                className="flex flex-col items-center justify-center p-2 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="w-10 h-10"
                >
                  <path
                    d="M12 4v16m8-8H4"
                    stroke="black"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="mt-1 sm:mt-2 text-xs sm:text-sm">
                  {"Soon.."}
                </span>
              </button>

              <button
                key={"add more"}
                className="flex flex-col items-center justify-center p-2 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="w-10 h-10"
                >
                  <path
                    d="M12 4v16m8-8H4"
                    stroke="black"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="mt-1 sm:mt-2 text-xs sm:text-sm">
                  {"Soon.."}
                </span>
              </button>
            </>
          )}
        </div>
      </div>

      {/* Platform Connection Modal */}
      {selectedPlatform && (
        <div className="fixed inset-0 bg-black/50 flex items-center md:items-center justify-center z-[110]">
          <div className="bg-white p-4 md:p-8 rounded-[16px] md:rounded-[40px] shadow-md w-full  max-w-[90vw] md:max-w-min  relative platform-connection-modal-content">
            {/* Close button */}
            {path !== "/dashboard/no-social" && (
              <button
                onClick={() => setSelectedPlatform(null)}
                className="absolute right-8 top-8"
              >
                <div className="relative w-5 h-5">
                  <div className="absolute w-7 h-0.5 bg-[#0F0F0F] transform rotate-45"></div>
                  <div className="absolute w-7 h-0.5 bg-[#0F0F0F] transform -rotate-45"></div>
                </div>
              </button>
            )}

            {/* Back button */}

            {/* Header */}
            <div className="mb-6 flex md:flex-row flex-col items-baseline md:items-center gap-4">
              <button
                className=" left-8 top-12 z-10"
                onClick={() => setSelectedPlatform(null)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#292D32"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M15 18l-6-6 6-6" />
                </svg>
              </button>
              <div className="">
                <h2 className="text-lg font-medium text-[#0F0F0F]">
                  Let's add some{" "}
                  <span className="text-[#0D3E4F]">{selectedPlatform}</span>{" "}
                  accounts
                </h2>
                <p className="text-xs text-[#0F0F0F] mt-1">
                  You only need to add one social accounts right now. You can
                  always add more later!
                </p>
              </div>
            </div>

            {/* Connection Steps */}
            <div
              className="flex md:flex-row flex-col items-center justify-between px-12 mt-8 md:gap-0 gap-6"
              onClick={handleSocialClick}
            >
              {/* Facebook Step */}
              <div className="flex flex-col items-center cursor-pointer">
                <div className="w-[70px] h-[70px] bg-[#FAFAFA] rounded-full flex items-center justify-center">
                  {selectedPlatform === "Facebook" ? (
                    <i className="text-[#1877F2] text-4xl fab fa-facebook" />
                  ) : (
                    <i className="text-[#E4405F] text-4xl fab fa-instagram" />
                  )}
                </div>
                <span className="mt-4 text-black text-base font-sm text-center">
                  Log in to {selectedPlatform}
                </span>
              </div>
              {/* Connector 1 */}
              <div className="flex items-center flex-1 justify-center connector">
                <div className="relative flex items-center w-[10vw]">
                  <div
                    className={`w-full border-t-4 border-dotted border-[#E5E5E5] ${
                      isFirstTime
                        ? "animate-[pulse_2s_cubic-bezier(0.5,0,0,1)_infinite]"
                        : ""
                    }`}
                  />
                  <div
                    className={`absolute left-1/2  -translate-x-1/2 w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-sm `}
                  >
                    <i
                      className={`text-2xl fas fa-link ${
                        isFirstTime
                          ? "text-primary-normal animate-pulse"
                          : "text-[#A0A0A0]"
                      }`}
                    />
                  </div>
                </div>
              </div>

              {/* Business Profile */}
              <div className="flex flex-col items-center cursor-pointer">
                <div className="w-[133px] h-[70px] bg-[#FAFAFA] rounded-[100px] flex items-center justify-center gap-2">
                  {selectedPlatform === "Facebook" ? (
                    <i className="text-[#1877F2] text-4xl fab fa-facebook" />
                  ) : (
                    <i className="text-[#E4405F] text-4xl fab fa-instagram" />
                  )}
                  <i className="text-[#1565C0] text-4xl fas fa-flag" />
                </div>
                <span className="mt-4 text-black text-base font-sm text-center">
                  Select your Business Profile
                </span>
              </div>

              {/* Connector 2 */}
              <div className="flex items-center flex-1 justify-center connector">
                <div className="relative flex items-center w-[10vw]">
                  <div
                    className={`w-full border-t-4 border-dotted border-[#E5E5E5] ${
                      showSecondPulse
                        ? "animate-[pulse_2s_cubic-bezier(0.5,0,0,1)_infinite]"
                        : ""
                    }`}
                  />
                  <div
                    className={`absolute left-1/2  -translate-x-1/2 w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-sm `}
                  >
                    <i
                      className={`text-2xl fas fa-link ${
                        showSecondPulse
                          ? "text-primary-normal animate-pulse"
                          : "text-[#A0A0A0]"
                      }`}
                    />
                  </div>
                </div>
              </div>

              {/* Business Insight */}
              <div className="flex flex-col items-center cursor-pointer">
                <div className="w-[70px] h-[70px] bg-[#FAFAFA] rounded-full flex items-center justify-center">
                  <img
                    src="/icons/logo.svg"
                    alt="Business Insight Icon"
                    className="w-12 h-12"
                  />
                </div>
                <span className="mt-4 text-black text-base font-sm text-center">
                  Add Business Insight
                </span>
              </div>
            </div>
          </div>
          <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
          />
        </div>
      )}
      <style jsx global>{`
        @media (max-width: 640px) {
          .add-social-modal-content { zoom: 0.9; }
          .platform-connection-modal-content { zoom: 0.9; }
        }
      `}</style>
    </div>
  );
};

export default AddSocialAccountModal;
