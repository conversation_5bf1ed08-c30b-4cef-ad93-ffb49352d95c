"use client"
import { useEffect, useState, Suspense } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import PasswordRecoveryForm from "../../components/passwordrecoveryform";
import Loadingscreen from "../../components/loadingscreen";
import SplashScreen from "~/components/SplashScreen";

type SidebarContent = {
  [key: number]: {
    icon: string;
    title: string;
    description: string;
  }
}

function PasswordRecoveryContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [step, setStep] = useState(1);

  const sidebarContent: SidebarContent = {
    1: {
      icon: "/icons/sandClock.svg",
      title: "Content Scheduling & Creating",
      description: "Plan your content with an intelligent and visual calendar. Create content and schedule posts across all your platforms using AI.",
    },
    2: {
      icon: "/icons/archer.svg",
      title: "Advanced Analytics & Reporting",
      description: "Track your KPIs and hit your goals with customisable analytics and detailed reports. Stay aware of real-time and AI-driven data.",
    },
    3: {
      icon: "/icons/tunder.svg",
      title: "Collaboration Tools",
      description: "Collaborate with your team seamlessly. Manage workspaces with integrated customisable tools.",
    },
  };

  return (
    <>
      <div className="w-screen h-screen flex flex-row ">
        <div className="form w-full h-full bg-white md:p-6 p-2 flex justify-center items-center">
          <PasswordRecoveryForm onStepChange={setStep} />
        </div>
        <div className="md:block hidden sidebar w-2/5 bg-linear-to-b from-[#A3C1DA] via-[#68AFDF] to-[#BDCBA1] h-full">
          <div className="flex flex-col items-center justify-center h-full p-8">
            <div className="relative">
              <img
                src={sidebarContent[step].icon}
                alt="Password Recovery Step"
                className="w-full h-full"
              />
            </div>
            <h2 className="text-2xl font-bold text-black mb-4 text-center">
              {sidebarContent[step].title}
            </h2>
            <p className="text-black text-center max-w-md">
              {sidebarContent[step].description}
            </p>
          </div>
        </div>
      </div>
    </>
  );
}

  export default function PasswordRecoveryPage() {
    const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;

    return (
        <Suspense fallback={isPWA ? <SplashScreen /> : <Loadingscreen />}>
            <PasswordRecoveryContent />
        </Suspense>
    );
}
