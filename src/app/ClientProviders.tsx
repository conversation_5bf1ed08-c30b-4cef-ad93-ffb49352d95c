"use client";

import { useEffect, useState } from "react";
import { SessionProvider } from "next-auth/react";
import { Toaster } from "react-hot-toast";
import AddToHomeScreenPrompt from "../components/addtohomescreenprompt";
import NotificationPrompt from "../components/notificationprompt";
import WelcomeSlider from "~/components/welcomeslider";
import SessionInitializer from "~/components/sessioninitializer";
import SplashScreen from "~/components/SplashScreen";
import PageTransition from "~/components/PageTransition";
import NetworkStatus from "~/components/NetworkStatus";
import { CookiesProvider } from "react-cookie";

export default function ClientProviders({ children }: { children: React.ReactNode }) {
  const [isPWA, setIsPWA] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [hasShownSplash, setHasShownSplash] = useState(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("hasShownSplash");
      return saved ? JSON.parse(saved) : false;
    }
    return false;
  });

  useEffect(() => {
    // Unregister any existing service workers to fully disable PWA behavior
    if (typeof navigator !== "undefined" && "serviceWorker" in navigator) {
      navigator.serviceWorker
        .getRegistrations?.()
        .then((regs) => {
          regs.forEach((r) => r.unregister());
        })
        .catch(() => {});
    }

    // Check if the app is running as a PWA
    const pwa = window.matchMedia("(display-mode: standalone)").matches;
    setIsPWA(pwa);

    // Set initial load to false after a short delay
    const timer = setTimeout(() => {
      setIsInitialLoad(false);
      setHasShownSplash(true);
      localStorage.setItem("hasShownSplash", "true");
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <CookiesProvider>
      <SessionProvider basePath="/api/auth">
        <SessionInitializer />
        {isPWA && <SplashScreen />}
        <WelcomeSlider />
        {typeof window !== "undefined" && window.location.pathname !== "/" && (
          <AddToHomeScreenPrompt />
        )}
        <NotificationPrompt />
        <NetworkStatus />

        {isPWA ? <PageTransition>{children}</PageTransition> : children}

        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: "transparent",
              boxShadow: "none",
              padding: 0,
              margin: 0,
            },
            success: {
              duration: 4000,
            },
            error: {
              duration: 5000,
            },
          }}
          containerStyle={{
            top: 20,
            right: 20,
            left: 20,
            zIndex: 9999,
          }}
          containerClassName="!fixed inset-x-4! sm:inset-x-auto! sm:right-5! sm:left-auto! top-5! sm:top-5!"
        />
      </SessionProvider>
    </CookiesProvider>
  );
}
