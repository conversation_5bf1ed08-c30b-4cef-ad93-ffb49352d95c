import { NextApiRequest, NextApiResponse } from 'next';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';

// Ensure temp directory exists
const tempDir = path.join(process.cwd(), 'public', 'temp');
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      // Extract base64 blob from request body
      const { base64Blob } = req.body;

      // Validate input
      if (!base64Blob) {
        return res.status(400).json({ error: 'No base64 blob provided' });
      }

      // Generate unique filename
      const filename = `${uuidv4()}.png`;
      const filepath = path.join(tempDir, filename);

      // Write file to temp directory
      fs.writeFileSync(filepath, Buffer.from(base64Blob, 'base64'));

      // Generate temporary URL (relative to public directory)
      const tempImageUrl = `/temp/${filename}`;

      // Optional: Set up cleanup to remove the image after a certain time
      setTimeout(() => {
        try {
          fs.unlinkSync(filepath);
        } catch (cleanupError) {
          console.error('Error cleaning up temp file:', cleanupError);
        }
      }, 10 * 60 * 1000); // 10 minutes

      return res.status(200).json({
        success: true,
        imageUrl: tempImageUrl
      });

    } catch (error) {
      console.error('Error processing base64 blob:', error);
      return res.status(500).json({ 
        error: 'Error processing base64 blob',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  } else if (req.method !== 'POST') {
    // Handle unsupported HTTP methods
    res.setHeader('Allow', ['POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}