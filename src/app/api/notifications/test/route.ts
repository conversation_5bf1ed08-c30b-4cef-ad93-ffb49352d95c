import { NextResponse } from 'next/server';
import webpush from 'web-push';

interface WebPushError extends Error {
  statusCode?: number;
}

// Configure web-push with your VAPID keys
const vapidEmail = process.env.VAPID_EMAIL;
const vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY;
const vapidPrivateKey = process.env.VAPID_PRIVATE_KEY;

console.log('VAPID Configuration:', {
  email: vapidEmail,
  publicKey: vapidPublicKey ? `${vapidPublicKey.slice(0, 10)}...` : undefined,
  privateKey: vapidPrivateKey ? 'present' : undefined
});

if (!vapidEmail || !vapidPublicKey || !vapidPrivateKey) {
  console.error('Missing VAPID credentials:', {
    email: !!vapidEmail,
    publicKey: !!vapidPublicKey,
    privateKey: !!vapidPrivateKey
  });
  throw new Error('Missing required VAPID credentials');
}

try {
  webpush.setVapidDetails(
    'mailto:' + vapidEmail,
    vapidPublicKey,
    vapidPrivateKey
  );
  console.log('VAPID details set successfully');
} catch (error) {
  console.error('Error setting VAPID details:', error);
  throw error;
}

export async function GET(req: Request) {
  try {
    console.log('Received test notification request');
    const url = new URL(req.url);
    const subscriptionParam = url.searchParams.get('subscription');
    
    if (!subscriptionParam) {
      console.error('No subscription provided in request');
      return NextResponse.json(
        { success: false, message: 'No subscription provided' },
        { status: 400 }
      );
    }

    let subscription;
    try {
      subscription = JSON.parse(decodeURIComponent(subscriptionParam));
      console.log('Parsed subscription:', {
        endpoint: subscription.endpoint,
        keys: subscription.keys ? {
          p256dh: subscription.keys.p256dh ? 'present' : 'missing',
          auth: subscription.keys.auth ? 'present' : 'missing'
        } : 'missing'
      });
    } catch (err) {
      console.error('Failed to parse subscription:', err);
      return NextResponse.json(
        { success: false, message: 'Invalid subscription format' },
        { status: 400 }
      );
    }

    // Validate subscription object
    if (!subscription.endpoint || !subscription.keys || !subscription.keys.p256dh || !subscription.keys.auth) {
      console.error('Invalid subscription object:', {
        endpoint: !!subscription.endpoint,
        keys: !!subscription.keys,
        p256dh: subscription.keys?.p256dh,
        auth: subscription.keys?.auth
      });
      return NextResponse.json(
        { success: false, message: 'Invalid subscription object - missing required fields' },
        { status: 400 }
      );
    }

    const payload = JSON.stringify({
      title: 'Test Notification',
      body: 'This is a test notification from Business Insight AI!',
      icon: '/pwa/ios/192.png',
      badge: '/pwa/ios/96.png',
      data: {
        url: '/dashboard'
      }
    });

    console.log('Sending notification with payload:', payload);
    console.log('To subscription endpoint:', subscription.endpoint);

    try {
      await webpush.sendNotification(subscription, payload);
      console.log('Notification sent successfully');
      
      return NextResponse.json({ 
        success: true,
        message: 'Test notification sent successfully' 
      });
    } catch (error) {
      console.error('Push service error:', error);
      // Check if subscription is expired
      const pushError = error as WebPushError;
      if (pushError.statusCode === 404 || pushError.statusCode === 410) {
        return NextResponse.json(
          { 
            success: false, 
            message: 'Subscription has expired or is invalid',
            expired: true
          },
          { status: 410 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error('Error sending test notification:', error);
    return NextResponse.json(
      { 
        success: false,
        message: error instanceof Error ? error.message : 'Failed to send test notification'
      },
      { status: 500 }
    );
  }
} 