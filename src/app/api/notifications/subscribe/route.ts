import { NextResponse } from 'next/server';
import webpush from 'web-push';

// Configure web-push with your VAPID keys
webpush.setVapidDetails(
  'mailto:' + process.env.VAPID_EMAIL!,
  process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!
);

export async function POST(req: Request) {
  try {
    const subscription = await req.json();

    // Here you would typically store the subscription in your database
    // associated with the current user
    // For example:
    // await db.pushSubscriptions.create({
    //   userId: currentUser.id,
    //   subscription: JSON.stringify(subscription)
    // });

    // Send a test notification
    const payload = JSON.stringify({
      title: 'Notification Enabled',
      body: 'You have successfully enabled notifications for Business Insight AI!',
    });

    await webpush.sendNotification(subscription, payload);

    return NextResponse.json({ 
      success: true,
      message: 'Subscription added successfully' 
    });
  } catch (error) {
    console.error('Error in subscription:', error);
    return NextResponse.json(
      { 
        success: false,
        message: 'Failed to add subscription' 
      },
      { status: 500 }
    );
  }
} 