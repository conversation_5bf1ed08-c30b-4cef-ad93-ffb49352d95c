import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    const subscription = await req.json();

    // Here you would typically remove the subscription from your database
    // For example:
    // await db.pushSubscriptions.delete({
    //   where: {
    //     userId: currentUser.id,
    //     subscription: JSON.stringify(subscription)
    //   }
    // });

    return NextResponse.json({ 
      success: true,
      message: 'Subscription removed successfully' 
    });
  } catch (error) {
    console.error('Error in unsubscription:', error);
    return NextResponse.json(
      { 
        success: false,
        message: 'Failed to remove subscription' 
      },
      { status: 500 }
    );
  }
} 