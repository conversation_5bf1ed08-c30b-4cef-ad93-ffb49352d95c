import NextAuth, { AuthOptions, Session } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import FacebookProvider from "next-auth/providers/facebook";
import { NextAuthOptions } from "next-auth";

interface ExtendedSession extends Session {
  refreshToken: any;
  accessToken: any;
  status?: string;
}

const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          redirect_uri: "https://businessinsight.ai/api/auth/callback/google",
        },
      },
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
      authorization: {
        url: `https://www.facebook.com/v21.0/dialog/oauth`,
        params: {
          client_id: process.env.FACEBOOK_CLIENT_ID,
          scope: "email,public_profile",
        },
      },
      userinfo: {
        url: "https://graph.facebook.com/me",
        params: { fields: "id,name,email,picture" },
      },
      profile(profile) {
        return {
          id: profile.id,
          name: profile.name,
          email: profile.email,
          image: profile.picture?.data?.url,
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      console.log("🔑 JWT Callback Started");

      if (user && account) {
        console.log("✅ User and Account present");

        try {
          let endpoint = "";
          let tokenData = {};

          if (account.provider === "google") {
            console.log("🔵 Google Provider detected");
            endpoint = `${process.env.NEXT_PUBLIC_API_URL}/api/google-redirect/`;
            tokenData = {
              token: account.access_token,
              first_name: user.name?.split(" ")[0] || "",
              last_name: user.name?.split(" ").slice(1).join(" ") || "",
            };
          } else if (account.provider === "facebook") {
            console.log("🔵 Facebook Provider detected");
            endpoint = `${process.env.NEXT_PUBLIC_API_URL}/api/meta-redirect/`;
            tokenData = {
              token: account.access_token,
            };
          }

          if (endpoint) {
            console.log("🔄 Attempting token exchange");
            const response = await fetch(endpoint, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(tokenData),
            });

            const data = await response.json();
            console.log("📥 Backend Response:", data);

            if (data.success) {
              console.log("✅ Token exchange successful");
              // Update token
              token.email = user.email;
              token.status = "authenticated";
              token.access_token = data.access_token;
              token.refresh_token = data.refresh_token;
              token.provider = account.provider;
              token.userId = user.id;

              console.log("💾 Updated token:", token);
            }
          }
        } catch (error) {
          console.error("❌ Error exchanging token:", error);
        }
      }

      console.log("🔚 JWT Callback Completed. Returning token:", token);
      return token;
    },
    async session({ session, token }): Promise<ExtendedSession> {
      console.log("🔄 Session Callback Started");

      const extendedSession = session as ExtendedSession;

      if (extendedSession.user) {
        console.log("👤 Updating session user data");
        extendedSession.user.email = token.email!;
        extendedSession.user.name = token.name!;
        extendedSession.user.image = token.picture as string;
        extendedSession.status = "authenticated";
        extendedSession.accessToken = token.access_token;
        extendedSession.refreshToken = token.refresh_token;
      }

      console.log(
        "🔚 Session Callback Completed. Returning session:",
        extendedSession
      );
      return extendedSession;
    },
  },
  pages: {
    signIn: "/login",
  },
  session: {
    strategy: "jwt",
  },
  debug: process.env.NODE_ENV === "development",
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
