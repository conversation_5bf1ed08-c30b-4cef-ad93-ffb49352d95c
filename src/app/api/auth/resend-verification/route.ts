import { NextResponse } from "next/server";

interface CsrfResponse {
  csrf: string;
}

interface ResendVerificationResponse {
  success: boolean;
  message?: string;
}

export async function POST(req: Request) {
  const authHeader = req.headers.get("Authorization");
  const accessToken = authHeader?.split(" ")[1];

  if (!accessToken) {
    return NextResponse.json(
      { success: false, message: "Unauthorized" },
      { status: 401 },
    );
  }

  try {
    // Get CSRF token
    const csrfResponse = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
      {
        method: "GET",
      },
    );
    const csrfData = (await csrfResponse.json()) as CsrfResponse;
    const csrfToken = csrfData.csrf;

    // Perform resend verification
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/verify/`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          csrf_token: csrfToken,
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    const data = (await response.json()) as ResendVerificationResponse;

    if (response.ok && data.success) {
      return NextResponse.json({
        success: true,
        message: "Verification code resent successfully",
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: data.message ?? "Failed to resend verification code",
        },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error("Resend verification error:", error);
    return NextResponse.json(
      { success: false, message: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}
