import { NextResponse } from "next/server";
import { useUserStore } from "~/store/userStore";

interface SignupData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

interface CSRFResponse {
  csrf: string;
}

interface TokenResponse {
  tokens?: {
    refresh: string;
    access: string;
  };
  email?: string;
  message?: string;
  error?: string;
}

export async function POST(req: Request) {
  try {
    const { email, password, firstName, lastName }: SignupData = await req.json() as SignupData;
    const csrfToken = useUserStore.getState().csrfToken;

    // // Get CSRF token
    // const csrfResponse = await fetch(
    //   `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
    //   {
    //     method: "GET",
    //   },
    // );
    // const csrfData = await csrfResponse.json() as CSRFResponse;

    // Perform signup
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/register/`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          csrf_token: csrfToken,
        },
        body: JSON.stringify({
          email,
          password,
          password2: password,
          first_name: firstName,
          last_name: lastName,
        }),
      },
    );

    const data = await response.json() as TokenResponse;

    if (response.ok) {
      // Extract tokens from the response data
      const { refresh, access } = data.tokens ?? { refresh: '', access: '' };

      return NextResponse.json({
        success: true,
        message: "User registered successfully. Please check your email for verification.",
        accessToken: access,
        refreshToken: refresh,
      });
    } else {
      // Log the full error response for debugging
      console.error("Registration failed:", data);

      // Check if there's a specific error message from the backend
      const errorMessage: string =
        data.email ?? data.message ?? data.error ?? "Registration failed. Please try again.";

      return NextResponse.json(
        { success: false, message: errorMessage },
        { status: response.status },
      );
    }
  } catch (error) {
    console.error("Signup error:", error);
    return NextResponse.json(
      { success: false, message: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}