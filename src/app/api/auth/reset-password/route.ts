import { NextResponse } from "next/server";
import { useUserStore } from "~/store/userStore";

// async function getCSRFToken() {
//   const csrfResponse = await fetch(
//     `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
//     {
//       method: "GET",
//     }
//   );
//   const csrfData = await csrfResponse.json();
//   return csrfData.csrf;
// }

export async function GET(req: Request) {
  const url = new URL(req.url);
  const email = url.searchParams.get("email");

  console.log(email);

  if (!email) {
    return NextResponse.json(
      { success: false, message: "Email is required" },
      { status: 400 },
    );
  }

  try {
    const csrfToken = useUserStore.getState().csrfToken;

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/reset-password/?email=${email}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          csrf_token: csrfToken,
        },
      },
    );

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({ success: true, process_id: data.process_id });
    } else {
      return NextResponse.json(
        { success: false, message: data.message },
        { status: response.status },
      );
    }
  } catch (error) {
    console.error("Error:", error);
    return NextResponse.json(
      { success: false, message: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}

export async function PUT(req: Request) {
  const { process_id, code } = await req.json();

  if (!process_id || !code) {
    return NextResponse.json(
      { success: false, message: "Missing required fields" },
      { status: 400 }
    );
  }

  try {
    const csrfToken = useUserStore.getState().csrfToken;

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/reset-password/`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "csrf_token": csrfToken,
          "X-CSRFTOKEN": csrfToken,
        },
        body: JSON.stringify({
          process_id,
          code,
        }),
      }
    );

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({ success: true, message: data.message });
    } else {
      return NextResponse.json(
        { success: false, message: data.message },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error:", error);
    return NextResponse.json(
      { success: false, message: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
