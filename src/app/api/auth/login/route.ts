import { NextResponse } from 'next/server';
import { getSession, useSession } from "next-auth/react";
import { useUserStore } from "~/store/userStore";

export async function POST(req: Request) {
  try {
    console.log('Received login request');
    const { email, password } = await req.json();
    console.log('Email:', email);

    if (!email || !password) {
      console.log('Missing email or password');
      return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
    }

    // console.log('Fetching CSRF token');
    // const csrfResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/login/`, {
    //   method: 'GET',
    // });

    // if (!csrfResponse.ok) {
    //   console.log('Failed to get CSRF token:', csrfResponse.status);
    //   return NextResponse.json({ error: 'Failed to get CSRF token' }, { status: csrfResponse.status });
    // }

    // const csrfData = await csrfResponse.json();
    // const csrfToken = csrfData.csrf;
    const csrfToken = useUserStore.getState().csrfToken;
    console.log('CSRF token:', csrfToken);

    if (!csrfToken) {
      console.log('CSRF token is empty');
      return NextResponse.json({ error: 'Failed to get CSRF token' }, { status: 500 });
    }

    console.log('Sending login request to backend');
    console.log('Sending login request to:', `${process.env.NEXT_PUBLIC_API_URL}/api/login/`);

   
    console.log('Request body:', JSON.stringify({ email, password }));
    // const loginResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/login/`, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Accept': 'application/json',
    //     csrf_token: csrfToken,
    //   },
    //   body: JSON.stringify({ email, password }),
    // });

    const loginResponse = await fetch(`https://${process.env.NEXT_PUBLIC_API_URL}/api/login/`, {
      "headers": {
        "accept": "application/json",
        "accept-language": "en-US,en;q=0.9",
        "content-type": "application/json",
        csrf_token: csrfToken,
      },

      "body": JSON.stringify({ email, password }),
      "method": "POST",
    });
    console.log('Login response status:', loginResponse.status);
    console.log('Login response headers:', Object.fromEntries(loginResponse.headers));

    const data = await loginResponse.json();
    console.log('Login response data:', data);

    if (!loginResponse.ok) {
      console.log('Login failed:', loginResponse.status, data);
      return NextResponse.json({ error: data.detail || 'Login failed' }, { status: loginResponse.status });
    }

    console.log('Login successful');
    
    // Update session status to authenticated
    const session = await getSession();
    if (session) {
      session.status = "authenticated";
    }

    console.log(session)

    return NextResponse.json({
      accessToken: data.access,
      refreshToken: data.refresh,
      status: "authenticated"
    });

    

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
