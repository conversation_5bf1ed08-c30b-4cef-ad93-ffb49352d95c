import { NextResponse } from "next/server";
import { useUserStore } from "~/store/userStore";

// async function getCSRFToken() {
//   const csrfResponse = await fetch(
//     `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
//     {
//       method: "GET",
//     }
//   );
//   const csrfData = await csrfResponse.json();
//   return csrfData.csrf;
// }

export async function POST(req: Request) {
  const { new_password, new_password_confirm, process_id } = await req.json();

  if (!new_password || !new_password_confirm || !process_id) {
    return NextResponse.json(
      { success: false, message: "Missing required fields" },
      { status: 400 }
    );
  }

  try {
    const csrfToken = useUserStore.getState().csrfToken;

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/reset-password/`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "csrf_token": csrfToken,
          "X-CSRFTOKEN": csrfToken,
        },
        body: JSON.stringify({
          new_password,
          new_password_confirm,
          process_id,
        }),
      }
    );

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({ success: true, message: data.message });
    } else {
      return NextResponse.json(
        { success: false, message: data.message },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error:", error);
    return NextResponse.json(
      { success: false, message: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
