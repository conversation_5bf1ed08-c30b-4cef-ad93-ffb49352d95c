import { NextResponse } from "next/server";
import { useUserStore } from "~/store/userStore";
interface CSRFResponse {
  csrf: string;
}

interface VerificationResponse {
  success: boolean;
  message: string;
}

export async function POST(req: Request) {
  const authHeader = req.headers.get("Authorization");
  const accessToken = authHeader?.split(" ")[1];
  console.log(accessToken, "splitted");

  console.log(authHeader, "authHeader");

  if (!accessToken) {
    return NextResponse.json(
      { success: false, message: "Unauthorized" },
      { status: 401 },
    );
  }

  try {
    const { code } = await req.json() as { code: string };
    const csrfToken = useUserStore.getState().csrfToken;

    // // Get CSRF token
    // const csrfResponse = await fetch(
    //   `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
    //   {
    //     method: "GET",
    //   },
    // );
    // const csrfData = await csrfResponse.json() as CSRFResponse;
    // const csrfToken = csrfData.csrf;

    // Perform verification
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/verify/`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          csrf_token: csrfToken,
          Authorization: String(authHeader),
        },
        body: JSON.stringify({
          code: code,
        }),
      },
    );

    console.log(response);

    const contentType = response.headers.get("content-type");
    if (contentType?.includes("application/json")) {
      const data = await response.json() as VerificationResponse;
      console.log(data);

      if (response.ok && data.success) {
        return NextResponse.json({
          success: true,
          message: "Email verified successfully",
        });
      } else {
        return NextResponse.json(
          { success: false, message: data.message },
          { status: 400 },
        );
      }
    } else {
      return NextResponse.json(
        { success: false, message: "Unexpected response from server" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Verification error:", error);
    return NextResponse.json(
      { success: false, message: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}
