"use client"
import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import SignupForm from "../../components/signupform";
import Loadingscreen from "~/components/loadingscreen";
import { Suspense } from "react";
import SplashScreen from "~/components/SplashScreen";

function SignupContent() {
  const { data: session, status } = useSession();
  const router = useRouter();

  return (
    <>
      <div className="w-screen h-dvh flex flex-row overflow-hidden">
        <div className="form w-full h-full bg-white md:p-2 p-2">
          <SignupForm />
        </div>
        <div className=" md:block hidden sidebar w-2/5 bg-linear-to-b from-[#A3C1DA] via-[#68AFDF] to-[#BDCBA1] h-full">
          <div className="flex flex-col items-center justify-center h-full p-8">
            <div className="relative">
              <img
                src="/icons/foolder.svg"
                alt="AI-Powered Insights"
                className="w-full h-full"
              />
            </div>
            <h2 className="text-2xl font-bold text-black mb-4 text-center">
              Omnichannel Engagement
            </h2>
            <p className="text-black text-center max-w-md">
              Manage all your social media channels and customer interactions
              from one unified dashboard
            </p>
          </div>
        </div>
      </div>
    </>
  );
}

export default function SignupPage() {
  const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;
  return (
      <Suspense fallback={isPWA ? <SplashScreen /> : <Loadingscreen />}>
        <SignupContent />
      </Suspense>
  );
}
