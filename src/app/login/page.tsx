"use client"
import { Suspense, useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import LoginForm from "../../components/loginform";
import LoadingScreen from "~/components/loadingscreen";
import Image from 'next/image';
import AddToHomeScreenPrompt from "~/components/addtohomescreenprompt";
import SplashScreen from "~/components/SplashScreen";

function LoginContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isCheckingAuth, setIsCheckingAuth] = useState(false);
  const [authChecked, setAuthChecked] = useState(false);

  useEffect(() => {
    // If already checked auth and not in loading state, don't check again
    if (authChecked && status !== 'loading') {
      return;
    }

    // If NextAuth session is authenticated, redirect immediately
    if (status === 'authenticated' && session) {
      console.log('NextAuth session authenticated, redirecting to dashboard');
      const inv = searchParams?.get('inv') || '/dashboard';
      const passwordChangeCode = searchParams?.get('password-change');

      if (passwordChangeCode) {
        router.push(`/user/change-password/${passwordChangeCode}`);
      } else {
        router.push(searchParams?.get('inv') ? `/dashboard/team/${inv}` : '/dashboard');
      }
      return;
    }

    // Only proceed with token checks if NextAuth is not in loading state or we haven't checked yet
    if ((status !== 'loading' || !authChecked) && !isCheckingAuth) {
      const checkAuth = async () => {
        try {
          setIsCheckingAuth(true);
          console.log('Checking authentication tokens...');

          // Check sessionStorage
          const sessionStatus = sessionStorage.getItem('status');
          const sessionAccessToken = sessionStorage.getItem('accessToken');
          const sessionRefreshToken = sessionStorage.getItem('refreshToken');

          // Check localStorage
          const localStatus = localStorage.getItem('status');
          const localAccessToken = localStorage.getItem('accessToken');
          const localRefreshToken = localStorage.getItem('refreshToken');

          const inv = searchParams?.get('inv') || '/dashboard';
          const passwordChangeCode = searchParams?.get('password-change');

          // Only authenticate if tokens are present in either storage
          const hasSessionTokens = sessionStatus === 'authenticated' && sessionAccessToken && sessionRefreshToken;
          const hasLocalTokens = localStatus === 'authenticated' && localAccessToken && localRefreshToken;
          const isAuthenticated = hasSessionTokens || hasLocalTokens;

          if (isAuthenticated) {
            console.log('Valid authentication tokens found, redirecting to dashboard');
            
            // Ensure tokens are properly synced between storages
            if (hasSessionTokens && !hasLocalTokens) {
              localStorage.setItem('status', sessionStatus);
              localStorage.setItem('accessToken', sessionAccessToken);
              localStorage.setItem('refreshToken', sessionRefreshToken);
            } else if (!hasSessionTokens && hasLocalTokens) {
              sessionStorage.setItem('status', localStatus);
              sessionStorage.setItem('accessToken', localAccessToken);
              sessionStorage.setItem('refreshToken', localRefreshToken);
            }
            
            // Mark as checked to prevent further checks
            setAuthChecked(true);

            if (passwordChangeCode) {
              router.push(`/user/change-password/${passwordChangeCode}`);
            } else {
              router.push(searchParams?.get('inv') ? `/dashboard/team/${inv}` : '/dashboard');
            }
          } else {
            // No valid tokens found, mark as checked and allow login form to show
            console.log('No valid authentication tokens found, showing login form');
            setAuthChecked(true);
            setIsCheckingAuth(false);
          }
        } catch (error) {
          console.error('Error checking auth:', error);
          setAuthChecked(true);
          setIsCheckingAuth(false);
        }
      };

      // Check authentication immediately
      checkAuth();
    }
  }, [router, searchParams, status, session, authChecked, isCheckingAuth]);

  // Show loading screen while checking authentication
  if (status === "loading" || isCheckingAuth) {
    return <LoadingScreen message="Checking authentication..." />;
  }

  return (
    <div className="w-screen h-screen flex flex-row">
      <div className="form w-full h-full bg-white md:p-2 p-2">
        <LoginForm />
        <AddToHomeScreenPrompt />

      </div>
      <div className="md:block hidden sidebar w-2/5 bg-linear-to-b from-[#A3C1DA] via-[#68AFDF] to-[#BDCBA1] h-full">
        <div className="flex flex-col items-center justify-center h-full p-8">
          <div className="relative">
            <Image
              src="/icons/bulb.svg"
              alt="AI-Powered Insights"
              width={400}
              height={400}
              priority
            />
          </div>
          <h2 className="text-2xl font-bold text-black mb-4 text-center">
            AI-Powered Insights
          </h2>
          <p className="text-black text-center max-w-md">
            Leverage AI to gain actionable insights from your data. Predict
            trends, understand customer sentiment, create a mature strategy,
            and make better decisions.
          </p>
        </div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;

  return (
    <Suspense fallback={isPWA ? <SplashScreen /> : <LoadingScreen message="Loading login page..." />}>
      <LoginContent />
    </Suspense>
  );
}
