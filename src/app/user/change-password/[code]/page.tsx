'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import toast from 'react-hot-toast'
import AuthBtn from '../../../../components/authbtn'
import { showErrorToast, showSuccessToast } from '../../../../components/toasts'
import { useUserStore } from '~/store/userStore'
import axios from 'axios'

export default function ChangePassword() {
  const params = useParams()
  const router = useRouter()
  const code = params.code as string
  
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const [inputValues, setInputValues] = useState({
    current: '',
    new: '',
    confirm: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isValidPassword, setIsValidPassword] = useState(false)
  const [passwordsMatch, setPasswordsMatch] = useState(false)

  const validatePassword = (password: string) => {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/;
    return passwordRegex.test(password);
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setInputValues(prev => {
      const newValues = { ...prev, [name]: value }
      
      // Validate new password
      if (name === 'new') {
        setIsValidPassword(validatePassword(value))
      }
      
      // Check if passwords match
      setPasswordsMatch(newValues.new === newValues.confirm)
      
      return newValues
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Add password validation
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/;
    if (!passwordRegex.test(inputValues.new)) {
      toast.error('Password must contain at least one uppercase letter, one lowercase letter, and one number');
      return;
    }

    if (inputValues.new !== inputValues.confirm) {
      toast.error('New passwords do not match')
      return
    }

    setIsLoading(true)
    try {
        // const csrfResponse = await fetch(
        //     `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
        //     {
        //       method: "GET",
        //     },
        //   );
        //   const csrfData = await csrfResponse.json() as CSRFResponse;
        //   const csrfToken: string = csrfData.csrf;
        //   const csrfToken = useUserStore.getState().csrfToken;

        if (sessionStorage.getItem('accessToken')) {
          try {
            const response = await axios.post(
              `${process.env.NEXT_PUBLIC_API_URL}/api/change-password/`,
              {
                current_password: inputValues.current,
                new_password: inputValues.new,
                code: code
              },
              {
                headers: {
                  'Authorization': `Bearer ${sessionStorage.getItem('accessToken')}`
                }
              }
            );

            const data = response.data;
            
            if (data.success) {
              showSuccessToast(data.message);
              router.push("/login");
            } else {
              showErrorToast(data.message);
              router.push(`/login?change-password=${code}`);
            }

          } catch (error) {
            const result = error.response.data;
            console.error('Error during change password:', result);
            showErrorToast(result.message || "An unexpected error occurred. Please try again.", "verify-toast");
            router.push(`/login?change-password=${code}`);
          }
    } else { 
      showErrorToast("Please login to change password")
      router.push(`/login?change-password=${code}`)

    }


      

    } catch (error) {
      if (error instanceof Error) {
        showErrorToast(error.message);
      } else {
        showErrorToast('An unexpected error occurred');
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLoading && isValidPassword && passwordsMatch && inputValues.current) {
      handleSubmit(e as any);
    }
  }

  return (
    <div className="w-screen h-screen flex flex-row">
      <div className="form w-full h-full bg-white flex flex-col">
        {/* Top section - 10% */}

        {/* Middle section - 80% */}
        <div className="h-[80%] w-full p-4 md:p-8 flex flex-col justify-center">
          <div className="w-full max-h-full overflow-y-auto">
            <div className="text mb-6 md:mb-8">
              <h1 className="text-3xl md:text-4xl font-bold mb-2">
                Change Password
              </h1>
              <p className="text-gray-600 text-sm md:text-base">
                Enter your current password and choose a new one
              </p>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="relative">
                <input
                  id="current"
                  name="current"
                  type={showCurrentPassword ? "text" : "password"}
                  required
                  className={`w-full px-3 py-4 md:py-6 border border-gray-300 rounded-lg pr-10 text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                    focusedField === 'current' || inputValues.current ? 'pt-6' : ''
                  }`}
                  value={inputValues.current}
                  onChange={handleInputChange}
                  onFocus={() => setFocusedField('current')}
                  onBlur={() => setFocusedField(null)}
                  onKeyDown={handleKeyDown}
                />
                <label htmlFor="current" className={`absolute left-3 transition-all duration-200 ${
                    focusedField === 'current' || inputValues.current
                      ? '-top-1 text-xs px-2 text-gray-500 bg-white'
                      : 'top-1/2 -translate-y-1/2 text-gray-400'
                  }`}>
                  Current Password *
                </label>
                <button
                  type="button"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <img
                    src={showCurrentPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                    alt={showCurrentPassword ? "Hide password" : "Show password"}
                    className="w-4 h-4 md:w-5 md:h-5"
                  />
                </button>
              </div>

              <div className="relative flex flex-col w-full">
                <div className="w-full flex flex-row justify-center relative">
                <input
                  id="new"
                  name="new"
                  type={showNewPassword ? "text" : "password"}
                  required
                  className={`w-full px-3  py-4 md:py-6 border ${!isValidPassword && inputValues.new ? 'border-red-500' : 'border-gray-300'} rounded-lg pr-10 text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                    focusedField === 'new' || inputValues.new ? 'pt-6' : ''
                  }`}
                  value={inputValues.new}
                  onChange={handleInputChange}
                  onFocus={() => setFocusedField('new')}
                  onBlur={() => setFocusedField(null)}
                  onKeyDown={handleKeyDown}
                />
                <label htmlFor="new" className={`absolute left-3 transition-all duration-200 ${
                    focusedField === 'new' || inputValues.new
                      ? '-top-1 text-xs px-2 text-gray-500 bg-white'
                      : 'top-1/2 -translate-y-1/2 text-gray-400'
                  }`}>
                  New Password *
                </label>
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  className="absolute top-1/2 -translate-y-1/2 right-0 pr-3 flex items-center"
                >
                  <img
                    src={showNewPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                    alt={showNewPassword ? "Hide password" : "Show password"}
                    className="w-4 h-4 md:w-5 md:h-5"
                  />
                </button>
                </div>
                {inputValues.new && !isValidPassword && (
                  <p className="text-red-500 text-xs mt-1">
                    Password must contain at least one uppercase letter, one lowercase letter, and one number
                  </p>
                )}
              </div>

              <div className="relative flex flex-col w-full">
                <div className="w-full flex flex-row justify-center relative">
                <input
                  id="confirm"
                  name="confirm"
                  type={showConfirmPassword ? "text" : "password"}
                  required
                  className={`w-full px-3 py-4 md:py-6 border ${!passwordsMatch && inputValues.confirm ? 'border-red-500' : 'border-gray-300'} rounded-lg pr-10 text-sm md:text-base focus:border-[#2C3E50] focus:outline-none ${
                    focusedField === 'confirm' || inputValues.confirm ? 'pt-6' : ''
                  }`}
                  value={inputValues.confirm}
                  onChange={handleInputChange}
                  onFocus={() => setFocusedField('confirm')}
                  onBlur={() => setFocusedField(null)}
                  onKeyDown={handleKeyDown}
                />
                <label htmlFor="confirm" className={`absolute left-3 transition-all duration-200 ${
                    focusedField === 'confirm' || inputValues.confirm
                      ? '-top-1 text-xs px-2 text-gray-500 bg-white'
                      : 'top-1/2 -translate-y-1/2 text-gray-400'
                  }`}>
                  Confirm New Password *
                </label>
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute top-1/2 -translate-y-1/2 right-0 pr-3 flex items-center"
                >
                  <img
                    src={showConfirmPassword ? "/icons/eye-off.svg" : "/icons/eye.svg"}
                    alt={showConfirmPassword ? "Hide password" : "Show password"}
                    className="w-4 h-4 md:w-5 md:h-5"
                  />
                </button>
                </div>
                {inputValues.confirm && !passwordsMatch && (
                  <p className="text-red-500 text-xs mt-1">
                    Passwords do not match
                  </p>
                )}
              </div>
            </form>
          </div>
        </div>

        {/* Bottom section - 10% */}
        <div className="h-[10%] p-4 md:p-8 flex flex-col justify-center">
          <div className="btn flex justify-center items-center flex-col gap-3 md:gap-4 max-w-xs mx-auto w-full">
            <AuthBtn
              disabled={isLoading || !isValidPassword || !passwordsMatch || !inputValues.current}
              onClick={handleSubmit}
              style="default"
              isLoading={isLoading}
              text={isLoading ? 'Changing Password...' : 'Change Password'}
            />
          </div>
        </div>
      </div>

      {/* Keep the existing sidebar */}
      <div className="md:block hidden sidebar w-2/5 bg-linear-to-b from-[#A3C1DA] via-[#68AFDF] to-[#BDCBA1] h-full">
        <div className="flex flex-col items-center justify-center h-full p-8">
          <div className="relative">
            <img
              src="/icons/sandClock.svg"
              alt="Password Change"
              className="w-full h-full"
            />
          </div>
          <h2 className="text-2xl font-bold text-black mb-4 text-center">
            Secure Password Change
          </h2>
          <p className="text-black text-center max-w-md">
            Change your password securely to protect your account. Make sure to use a strong password that you haven't used before.
          </p>
        </div>
      </div>
      {/* <ToastWrapper containerId="changepassword-toast"/> */}
    </div>
  )
}
