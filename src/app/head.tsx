export default function Head() {
  const title = "Business Insight — AI Social Media Management, Inbox, Analytics, Post Creation & Scheduling";
  const description =
    "Business Insight is an AI-powered social media management platform for Instagram and Facebook. Manage inbox, analyze performance, create posts, and schedule content — all in one place.";
  const keywords = [
    "Business Insight",
    "social media management",
    "social media analytics",
    "social media scheduler",
    "Instagram",
    "Facebook",
    "inbox",
    "analytics",
    "post creation",
    "post scheduling",
    "scheduler",
    "content calendar",
    "unified inbox",
    "AI marketing tools",
  ].join(", ");
  const image = "/Performance/hero-parallax-bg.webp";

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: "Business Insight",
    applicationCategory: "SocialMediaManagement",
    operatingSystem: "Web",
    image,
    description,
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
    },
    featureList: [
      "Instagram management",
      "Facebook management",
      "Unified inbox",
      "Real-time analytics",
      "Post creation",
      "Post scheduling",
    ],
  };

  const faqJsonLd = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: [
      {
        "@type": "Question",
        name: "How does Business Insight's unified dashboard work?",
        acceptedAnswer: {
          "@type": "Answer",
          text:
            "Our unified dashboard combines all your data sources into one intuitive interface. It provides real-time analytics, customizable views, and seamless integration with your existing tools.",
        },
      },
      {
        "@type": "Question",
        name: "What kind of reports can I create with Business Insight?",
        acceptedAnswer: {
          "@type": "Answer",
          text:
            "You can create customized reports including performance analytics, trend analysis, comparative studies, and more. Our platform supports various data visualization options and export formats.",
        },
      },
      {
        "@type": "Question",
        name: "How secure is my data on Business Insight?",
        acceptedAnswer: {
          "@type": "Answer",
          text:
            "We implement enterprise-grade security measures including end-to-end encryption, regular security audits, and compliance with international data protection standards.",
        },
      },
      {
        "@type": "Question",
        name: "What kind of support does Business Insight offer?",
        acceptedAnswer: {
          "@type": "Answer",
          text:
            "We provide 24/7 customer support through multiple channels including live chat, email, and phone. Premium plans include dedicated support representatives.",
        },
      },
      {
        "@type": "Question",
        name: "Is there a free trial available?",
        acceptedAnswer: {
          "@type": "Answer",
          text:
            "Yes, we offer a 30-day free trial with full access to all features. No credit card required to start your trial.",
        },
      },
    ],
  };

  return (
    <>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content="index, follow" />

      {/* Open Graph */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />

      <script
        type="application/ld+json"
        // Avoid double-escaping by serializing explicitly
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqJsonLd) }}
      />
    </>
  );
}
