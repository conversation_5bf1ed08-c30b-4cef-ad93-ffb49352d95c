import "~/styles/globals.css";

import { GeistSans } from "geist/font/sans";
import { Inter } from 'next/font/google'
import ClientProviders from "./ClientProviders";

const SITE_NAME = "Business Insight";
const SITE_TITLE = "Business Insight AI";
const SITE_DESCRIPTION =
  "Business Insight is an AI-powered social media management platform for Instagram and Facebook. Manage inbox, analyze performance, create posts, and schedule content — all in one place.";

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || "";

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
})

const geist = GeistSans

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${inter.className} ${geist.className}`}>
      <head>
        <title>{SITE_TITLE}</title>
        <meta name="application-name" content={SITE_NAME} />
        <meta name="description" content={SITE_DESCRIPTION} />
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover, maximum-scale=5" />
        <meta name="theme-color" content="#2c3e50" />
        <meta name="color-scheme" content="light" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="referrer" content="origin-when-cross-origin" />
        <meta name="robots" content="index, follow" />
        <meta
          name="keywords"
          content={[
            "Business Insight",
            "social media management",
            "social media analytics",
            "social media scheduler",
            "Instagram",
            "Facebook",
            "inbox",
            "analytics",
            "post creation",
            "post scheduling",
            "scheduler",
            "content calendar",
            "unified inbox",
            "AI marketing tools",
          ].join(", ")}
        />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content={SITE_NAME} />

        {/* Open Graph base */}
        <meta property="og:type" content="website" />
        <meta property="og:site_name" content={SITE_NAME} />
        <meta property="og:title" content={SITE_TITLE} />
        <meta property="og:description" content={SITE_DESCRIPTION} />
        <meta property="og:locale" content="en_US" />
        {SITE_URL && <meta property="og:url" content={SITE_URL} />}

        {/* Canonical + hreflang (base) */}
        {SITE_URL && <link rel="canonical" href={SITE_URL} />}
        {SITE_URL && <link rel="alternate" hrefLang="en" href={SITE_URL} />}

        {/* Service worker registration disabled */}
        
        {/* iOS Splash Screen Images */}
        <link
          rel="apple-touch-startup-image"
          href="/splash/iphone5.png"
          media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)"
        />
        <link
          rel="apple-touch-startup-image"
          href="/splash/iphone6.png"
          media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)"
        />
        <link
          rel="apple-touch-startup-image"
          href="/splash/iphonex.png"
          media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)"
        />
        <link
          rel="apple-touch-startup-image"
          href="/splash/iphoneplus.png"
          media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)"
        />
        <link
          rel="apple-touch-startup-image"
          href="/splash/ipad.png"
          media="(min-device-width: 768px) and (max-device-width: 1024px)"
        />
        
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/icons/logo.svg" />
        <link rel="apple-touch-icon" href="/pwa/ios/192.png" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"/>
        {/* Allow Next.js to manage CSS chunking; avoid manual preload for app CSS */}
        <link 
          rel="preconnect" 
          href="https://fonts.googleapis.com" 
        />
        <link 
          rel="preconnect" 
          href="https://fonts.gstatic.com" 
          crossOrigin="anonymous"
        />
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      </head>
      <body className="antialiased">
        <ClientProviders>{children}</ClientProviders>
      </body>
    </html>
  );
}
