"use client";

import Header from "~/components/header";
import Footer from "~/components/footer";
import Image from "next/image";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { motion, useInView } from "framer-motion";
import Link from "next/link";
import { ChevronDown } from "lucide-react";
import { useRef, memo, useMemo, useCallback } from "react";
import MostActiveChart from "~/components/MostActiveChart";

// Animation variants for reusability
const fadeInUp = {
  hidden: { opacity: 0, y: 50 },
  visible: { opacity: 1, y: 0 },
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: { opacity: 1, scale: 1 },
};

const staggerContainer = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const Hero = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <>
      <motion.section
        ref={ref}
        className="relative w-full overflow-hidden mb-16 md:mb-24"
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        variants={staggerContainer}
      >
        {/* Background Pattern */}
        <div
          className="absolute inset-0 w-full h-full"
          style={{
            backgroundImage: "url(/Performance/hero-bg.webp)",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            backgroundSize: "cover",
          }}
        />

        {/* Content Container */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 pt-40 md:pt-28">
          {/* Main Heading */}
          <motion.h1
            className="text-center text-slate-950 text-[2rem] sm:text-[2.5rem] md:text-[3rem] lg:text-[3.5rem] font-bold leading-tight tracking-wide max-w-204 mx-auto mb-3 md:mb-4"
            variants={fadeInUp}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            Bringing your Data to Life
          </motion.h1>

          <div>
            {/* Subheading */}
            <motion.p
              className="text-center text-slate-500 text-xs md:text-sm max-w-240 mx-auto mb-6 md:mb-8 px-2"
              variants={fadeInUp}
              transition={{ duration: 0.6, ease: "easeOut", delay: 0.1 }}
            >
              Business Insight is your ultimate AI-powered social media
              management solution. Simplify management, create engaging content,
              and optimize marketing strategies. Elevate your brand with
              cutting-edge AI tools designed for influencers, businesses, and
              agencies.
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row justify-center items-center gap-3 md:gap-4 mb-12 md:mb-16"
              variants={fadeInUp}
              transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
            >
              <motion.button
                onClick={() => window.location.assign("/login")}
                className="w-full sm:w-auto px-6 md:px-8 py-3 md:py-4 bg-[#2c3e50] text-white rounded-lg shadow text-base md:text-lg font-medium capitalize transition-all duration-300 ease-in-out hover:bg-slate-300 hover:text-slate-700"
                whileHover={{ scale: 0.95 }}
                whileTap={{ scale: 0.9 }}
              >
                Get Started Now
              </motion.button>
            </motion.div>

            {/* Hero Image */}
            <motion.div
              className="relative w-full max-w-342 mx-auto"
              variants={scaleIn}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
            >
              <motion.div
                variants={scaleIn}
                transition={{ duration: 0.6, ease: "easeOut" }}
              >
                <Image
                  src="/Performance/hero-parallax-bg.webp"
                  alt="Dashboard Background"
                  className="w-full h-auto"

                  quality={100}
                  priority
                  width={2350}
                  height={1892}
                />
              </motion.div>

              <motion.div
                variants={scaleIn}
                transition={{ duration: 0.4, ease: "easeOut", delay: 0.1 }}
              >
                <Image
                  src="/Performance/parallax-1.webp"
                  alt="Parallax Element 1"
                  className="absolute top-[10%] right-[20%] w-[28%] h-auto"
                  loading="eager"
                  width={1000}
                  quality={100}
                  height={1000}


                />
              </motion.div>

              <motion.div
                variants={scaleIn}
                transition={{ duration: 0.4, ease: "easeOut", delay: 0.15 }}
              >
                <Image
                  src="/Performance/parallax-2.webp"
                  alt="Parallax Element 2"
                  className="absolute top-[28%] right-[1%] w-[25%] h-auto"
                  loading="eager"
                  width={1000}
                  height={1000}
                  quality={100}
                />
              </motion.div>

              <motion.div
                variants={scaleIn}
                transition={{ duration: 0.4, ease: "easeOut", delay: 0.2 }}
              >
                <Image
                  src="/Performance/parallax-3.webp"
                  alt="Parallax Element 3"
                  className="absolute top-[40%] right-[22%] w-[25%] h-auto"
                  loading="eager"
                  width={1000}
                  quality={100}
                  height={1000}
                />
              </motion.div>

              <motion.div
                variants={scaleIn}
                transition={{ duration: 0.4, ease: "easeOut", delay: 0.25 }}
              >
                <Image
                  src="/Performance/parallax-4.webp"
                  alt="Parallax Element 4"
                  className="absolute top-[75%] left-[65%] w-[35%] h-auto"
                  loading="eager"
                  width={1000}
                  quality={100}
                  height={1000}
                />
              </motion.div>

              <motion.div
                variants={scaleIn}
                transition={{ duration: 0.4, ease: "easeOut", delay: 0.3 }}
              >
                <Image
                  src="/Performance/right2.webp"
                  alt="Parallax Element Right"
                  className="absolute top-[1%] left-[5%] w-[50%] h-auto"
                  loading="eager"
                  width={1000}
                  quality={100}
                  height={1000}
                />
              </motion.div>
            </motion.div>
          </div>
        </div>
      </motion.section>
    </>
  );
});

// Memoized feature data to prevent unnecessary re-renders
const featuresData = [
  {
    icon: "/svg/performance/Unified.webp",
    title: "Unified Dashboard",
    description:
      "Centralize all your data sources into a single, integrated platform, enabling efficient management and comprehensive analysis of information across multiple systems",
  },
  {
    icon: "/svg/performance/Real-time.webp",
    title: "Real-Time Analytics",
    description:
      "Access up-to-the-minute analytics to monitor performance, identify trends, and make data-driven decisions without delay. Stay competitive with insights delivered in real time.",
  },
  {
    icon: "/svg/performance/Customize.webp",
    title: "Customizable Reporting",
    description:
      "Generate tailored reports to meet specific requirements, offering actionable insights and dynamic visuals to refine strategies and drive results.",
  },
  {
    icon: "/svg/performance/Integration.webp",
    title: "Integration Compatibility",
    description:
      "Seamlessly integrate with your existing tools and platforms to ensure consistent data flow and eliminate inefficiencies in your workflows.",
  },
  {
    icon: "/svg/performance/User-Friendly.webp",
    title: "Optimized User Interface",
    description:
      "Leverage an intuitive, well-designed interface built to enhance usability and minimize complexity, making navigation and functionality seamless.",
  },
  {
    icon: "/svg/performance/Data-sec.webp",
    title: "Enterprise-Grade Data Security",
    description:
      "Safeguard your data with state-of-the-art encryption protocols and compliance with industry standards, ensuring privacy and protection at all levels.",
  },
];

// Memoized FeatureCard component
const FeatureCard = memo(
  ({
    feature,
    index,
  }: {
    feature: (typeof featuresData)[0];
    index: number;
  }) => (
    <motion.div
      className="w-full max-w-[24rem] min-h-32 md:h-38 bg-white rounded-xl md:rounded-2xl shadow border border-slate-100 p-4 md:p-6 flex flex-col"
      variants={fadeInUp}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
    >
      <div className="flex flex-row items-center gap-3 md:gap-4 mb-3 md:mb-4">
        <div className="w-7 h-7 md:w-8 md:h-8 bg-[#f5f7f1] rounded-lg flex items-center justify-center">
          <img
            src={feature.icon}
            alt={feature.title}
            className="w-5 h-5 md:w-full md:h-full"
          />
        </div>
        <div className="text-slate-950 text-sm md:text-[0.938rem] font-bold leading-tight tracking-wide">
          {feature.title}
        </div>
      </div>
      <div className="text-slate-950 text-xs font-normal leading-normal md:leading-[1.15rem] tracking-wide">
        {feature.description}
      </div>
    </motion.div>
  )
);

FeatureCard.displayName = "FeatureCard";

const Features = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const brandsRef = useRef(null);
  const brandsInView = useInView(brandsRef, { once: true, margin: "-100px" });
  return (
    <section
      className="w-full max-w-7xl mx-auto px-4 py-8 md:py-16"
      id="features"
    >
      <motion.div
        ref={brandsRef}
        className="flex flex-col lg:flex-row items-center justify-between gap-4 lg:gap-16 mb-12"
        initial="hidden"
        animate={brandsInView ? "visible" : "hidden"}
        variants={staggerContainer}
      >
        {/* Left content */}
        <motion.div
          className="w-full lg:w-1/3 space-y-4 lg:space-y-6"
          variants={fadeInUp}
        >
          <h2 className="text-slate-950 text-lg lg:text-md font-bold leading-tight lg:leading-15 tracking-wide">
            Brands that trust us
          </h2>
        </motion.div>

        {/* Right image with infinite scroll animation */}
        <motion.div
          className="w-full lg:w-2/3 flex justify-center items-center"
          variants={fadeInUp}
        >
          <div className="relative w-full overflow-hidden flex justify-center items-center">
            <motion.div
              className="flex"
              animate={{ x: [0, -100] }}
              transition={{
                x: {
                  repeat: Infinity,
                  repeatType: "loop",
                  duration: 20,
                  ease: "linear",
                },
              }}
              style={{ width: "200%" }}
            >
              <img
                src="/svg/ui/logo-container.webp"
                alt="Trusted brands logos"
                className="w-full h-full object-contain"
              />
              <img
                src="/svg/ui/logo-container.webp"
                alt="Trusted brands logos"
                className="w-full h-full object-contain"
              />
            </motion.div>
          </div>
        </motion.div>
      </motion.div>

      <motion.div
        ref={ref}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        variants={staggerContainer}
      >
        <motion.div className="text-center mb-8 md:mb-12" variants={fadeInUp}>
          <div className="inline-block px-3 py-1.5 md:px-4 md:py-2 bg-[#f5f7f1] rounded-lg border border-[#717a61] mb-3 md:mb-4">
            <span className="text-[#555b48] text-xs md:text-sm font-medium uppercase leading-normal md:leading-[1.138rem] tracking-wide">
              Features
            </span>
          </div>
          <h2 className="text-slate-950 text-2xl md:text-[2.673rem] font-bold leading-tight md:leading-[4.063rem] tracking-wide mb-3 md:mb-4">
            Simplify Your Data Management
          </h2>
          <p className="text-slate-950 text-xs md:text-sm leading-relaxed md:leading-tight tracking-wide max-w-[43.645rem] mx-auto px-2">
            Access up-to-the-minute analytics to monitor performance, identify
            trends, and make data-driven decisions without delay. Stay
            competitive with insights delivered in real time.
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6"
          variants={staggerContainer}
        >
          {featuresData.map((feature, index) => (
            <FeatureCard key={index} feature={feature} index={index} />
          ))}
        </motion.div>
      </motion.div>
    </section>
  );
});

const TrustedBy = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.section
      ref={ref}
      className="w-full bg-[#f8faf6] py-8 md:py-32 px-4 lg:px-[15%] my-40"
      id="trust-us"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={staggerContainer}
    >
      <motion.div
        className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12 w-full"
        variants={scaleIn}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        {/* Left side - Image */}
        <motion.div
          className="w-full"
          variants={fadeInUp}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <div className="inline-block md:hidden px-3 py-2 mb-10 lg:px-4 lg:py-[0.563rem] bg-[#f5f7f1] rounded-lg border border-[#717a61]">
            <span className="text-center text-[#555b48] text-xs lg:text-sm font-medium uppercase leading-normal lg:leading-[1.138rem] tracking-wide">
              GLOBAL TRUST
            </span>
          </div>
          <div className="relative w-full aspect-square rounded-2xl border border-slate-100 overflow-hidden">
            <img
              src="/svg/ui/trusted-img.webp"
              alt="Trusted companies"
              className="w-full h-full object-cover"
            />
          </div>
        </motion.div>

        {/* Right side - Content */}
        <motion.div
          className="w-full space-y-6 lg:space-y-8"
          variants={fadeInUp}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
        >
          {/* Badge */}
          <div className="hidden md:inline-block px-3 py-2 lg:px-4 lg:py-[0.563rem] bg-[#f5f7f1] rounded-lg border border-[#717a61]">
            <span className="text-center text-[#555b48] text-xs lg:text-sm font-medium uppercase leading-normal lg:leading-[1.138rem] tracking-wide">
              GLOBAL TRUST
            </span>
          </div>

          {/* Heading */}
          <h2 className="text-slate-950 text-2xl lg:text-[2.344rem] font-bold leading-tight lg:leading-15 tracking-wide">
            Trusted by Industry Leaders
          </h2>

          {/* Description */}
          <p className="text-slate-950 text-sm leading-relaxed lg:leading-tight tracking-wide max-w-[36.473rem]">
            At Business Insight, we're committed to empowering marketers,
            startups, and entrepreneurs with robust data analysis solutions. Our
            platform is trusted by top industry leaders who rely on us to
            streamline their data management and drive business growth.
          </p>

          {/* Stats */}
          <div className="flex flex-row gap-6 lg:gap-8">
            <motion.div
              className="text-center md:text-left"
              variants={fadeInUp}
              transition={{ duration: 0.4, delay: 0.4 }}
            >
              <p className="text-xl lg:text-[1.934rem] font-bold leading-normal lg:leading-10 tracking-wide">
                <span className="text-slate-950">2000</span>
                <span className="text-[#555b48]">+</span>
              </p>
              <p className="text-slate-950 text-sm lg:text-[0.938rem] font-bold leading-tight tracking-wide">
                Active Users
              </p>
            </motion.div>
            <motion.div
              className="text-center sm:text-left"
              variants={fadeInUp}
              transition={{ duration: 0.4, delay: 0.5 }}
            >
              <p className="text-xl lg:text-[1.983rem] font-bold leading-normal lg:leading-10 tracking-wide">
                <span className="text-slate-950">250</span>
                <span className="text-[#555b48]">+</span>
              </p>
              <p className="text-slate-950 text-sm lg:text-[0.938rem] font-bold leading-tight tracking-wide">
                Active Users
              </p>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
    </motion.section>
  );
});

const WhySection = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.section
      ref={ref}
      className="w-full min-h-240 bg-[#f8faf6] my-40 py-8 md:py-16 sm:py-28 px-4 sm:px-[5%] lg:px-[10%]"
      id="why-business-insight"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={staggerContainer}
    >
      <motion.div
        variants={scaleIn}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="flex flex-col items-center">
          {/* Header */}
          <motion.div
            className="text-center mb-8 sm:mb-16"
            variants={fadeInUp}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <div className="inline-block px-3 sm:px-4 py-2 sm:py-[0.563rem] bg-[#f5f7f1] rounded-lg border border-[#717a61] md:mb-6 mb-6">
              <span className="text-center text-[#555b48] text-xs sm:text-sm font-medium uppercase leading-normal sm:leading-[1.138rem] tracking-wide">
                WHY CHOOSE US
              </span>
            </div>
            <h2 className="text-slate-950 text-2xl sm:text-[2.57rem] font-bold leading-tight sm:leading-[4.063rem] tracking-wide">
              Why Business Insight?
            </h2>
          </motion.div>

          {/* Content Container */}
          <div className="flex flex-col lg:flex-row gap-8 sm:gap-12 items-center lg:items-start">
            {/* Left Content */}
            <motion.div
              className="w-full lg:w-[45%] space-y-6 sm:space-y-8"
              variants={fadeInUp}
              transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
            >
              <div className="space-y-3 sm:space-y-4">
                <h3 className="text-slate-950 text-[1rem] sm:text-4xl font-bold leading-tight tracking-wide">
                  Simplify Data Analysis and Revolutionize Your Marketing
                  Strategy
                </h3>
                <p className="text-slate-950 text-xs sm:text-sm leading-relaxed sm:leading-tight tracking-wide">
                  Empower your business with Business Insight's cutting-edge
                  features, designed to simplify workflows, enhance data-driven
                  decisions, and maximize marketing success.
                </p>
              </div>

              {/* Bullet Points */}
              <motion.div
                className="space-y-4 sm:space-y-3"
                variants={staggerContainer}
                transition={{ delay: 0.4 }}
              >
                {[
                  "AI-Powered Content Creation: Effortlessly generate captions, hashtags, images, and videos with the power of AI to produce engaging, high-quality content that resonates with your audience.",
                  "Real-Time Analytics: Access instant insights to monitor performance, adapt quickly to market trends, and make smarter decisions in real time.",
                  "Dynamic Reporting: Create customizable, visually engaging reports tailored to your business needs, providing actionable insights for strategic growth.",
                  "Smart Content Scheduling: Plan and schedule your content calendar using AI-driven insights, optimizing posting times and boosting audience engagement.",
                  "Advanced Data Security: Safeguard sensitive information with enterprise-grade encryption and compliance with the latest data protection standards.",
                  "AI-Driven Recommendations: Leverage artificial intelligence to identify patterns, predict outcomes, and make data-backed decisions that drive measurable success.",
                ].map((text, index) => (
                  <motion.div
                    key={index}
                    className="flex items-start gap-3 sm:gap-4"
                    variants={fadeInUp}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                  >
                    <div className="w-5 h-5 aspect-square bg-slate-950 rounded flex items-center justify-center">
                      <svg
                        width="10"
                        height="8"
                        viewBox="0 0 10 8"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M1 4L3.5 6.5L9 1"
                          stroke="white"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                    <p className="text-slate-950 text-xs sm:text-sm leading-relaxed sm:leading-tight tracking-wide">
                      {text}
                    </p>
                  </motion.div>
                ))}
              </motion.div>

              {/* CTA Button */}
              <motion.div
                variants={fadeInUp}
                transition={{ duration: 0.4, delay: 0.8 }}
              >
                <motion.button
                  onClick={() => window.location.assign("/login")}
                  className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-[#2c3e50] text-white rounded-lg shadow text-base sm:text-lg font-medium capitalize transition-all duration-300 ease-in-out hover:bg-slate-300 hover:text-slate-700"
                  whileHover={{ scale: 0.95 }}
                  whileTap={{ scale: 0.9 }}
                >
                  Get Started Now
                </motion.button>
              </motion.div>
            </motion.div>

            {/* Right Image Container */}
            <motion.div
              className="w-full lg:w-[55%] relative mt-8 sm:mt-0"
              variants={fadeInUp}
              transition={{ duration: 0.6, ease: "easeOut", delay: 0.4 }}
            >
              <div className="relative w-full aspect-566/541 rounded-xl sm:rounded-2xl shadow-lg overflow-hidden">
                <img
                  src="/Performance/Group 1127.webp"
                  alt="Business Insight Platform"
                  className="w-full h-full object-cover"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </motion.section>
  );
});

// Memoized discover features data
const discoverFeaturesData = [
  {
    title: "Advanced Data Analysis and Insights",
    description:
      "Harness the power of AI to access comprehensive performance metrics and actionable recommendations",
    bullets: [
      "Visualized Performance Metrics",
      "AI-Driven Recommendation",
      "Social Signal and Sentiment Analysis",
    ],
    hasChart: true,
    icon: "/svg/performance/icon-1.webp",
  },
  {
    title: "Advertising Campaign Management",
    description:
      "Optimize your advertising efforts across platforms like Instagram, LinkedIn, and Google Ads with advanced tools",
    bullets: ["Campaign Tracking", "AI Optimization"],
    icon: "/svg/performance/icon-8.webp",
  },
  {
    title: "Goal Setting and Intelligent Targeting",
    description:
      "Achieve business objectives faster with AI-powered targeting and goal-setting tools",
    bullets: ["Data-Driven Insight", "Data-Driven decision making"],
    icon: "/svg/performance/icon-9.webp",
  },
  {
    title: "Smart Scheduling",
    description:
      "Save time and maximize engagement with intelligent scheduling capabilities",
    hasGetStarted: true,
    icon: "/svg/performance/icon-3.webp",
  },
  {
    isSmallGroup: true,
    items: [
      {
        title: "Artificial Intelligence-Driven Optimization",
        icon: "/svg/performance/icon-2.webp",
      },
      {
        title: "Customized Management Dashboard",
        icon: "/svg/performance/icon-4.webp",
      },
    ],
  },
  {
    title: "Access and Team Management",
    description:
      "Simplify team collaboration with flexible access and task assignment tools",
    bullets: ["User-Level Controls", "Team Management"],
    icon: "/svg/performance/icon-6.webp",
  },
  {
    title: "Customer Sentiment and Satisfaction Analysis",
    description: "Monitor and analyze customer feedback for improved service",
    bullets: [
      "Behavioral Insights",
      "Early Problem Detection",
      "Enhanced Customer Loyalty",
    ],
    icon: "/svg/performance/icon-7.webp",
  },
  {
    title: "AI-Powered Content Creation",
    description:
      "Generate high-quality, engaging content tailored to your audience. Save time and elevate your creative strategy with AI-driven precision",
    hasGetStarted: true,
    icon: "/svg/performance/icon-5.webp",
  },
];

const DiscoverSection = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.section
      ref={ref}
      className="w-full max-w-7xl mx-auto px-4 py-8 md:py-16"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={staggerContainer}
    >
      <motion.div
        variants={scaleIn}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        {/* Header */}
        <motion.div
          className="text-center mb-8 md:mb-16"
          variants={fadeInUp}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <div className="inline-block px-3 py-1.5 md:px-4 md:py-2 bg-[#f5f7f1] rounded-lg border border-[#717a61] mb-3 md:mb-4">
            <span className="text-[#555b48] text-xs md:text-sm font-medium uppercase leading-[1.138rem] tracking-wide">
              BENEFITS
            </span>
          </div>
          <h2 className="text-slate-950 text-[1.8rem] md:text-[2.609rem] font-bold leading-tight md:leading-[4.063rem] tracking-wide mb-3 md:mb-4">
            Discover Our Essential Data
            <br className="hidden md:block" /> Analysis Features
          </h2>
          <p className="text-slate-950 text-xs md:text-sm leading-relaxed md:leading-tight tracking-wide max-w-[42.27rem] mx-auto px-2">
            Business Insight provides powerful tools to simplify data
            management, offering real-time insights and customizable reports for
            informed decision-making.
          </p>
        </motion.div>

        {/* Grid container with auto-rows */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 auto-rows-min gap-4 md:gap-6"
          variants={staggerContainer}
          transition={{ delay: 0.3 }}
        >
          {discoverFeaturesData.map((feature, index) => (
            <motion.div
              key={index}
              className={`flex flex-row ${
                feature.hasChart ? "row-span-2" : "row-span-1"
              }`}
              variants={fadeInUp}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              {feature.isSmallGroup ? (
                <div className="w-full grid grid-cols-2 gap-4 md:gap-6">
                  {feature.items.map((item, i) => (
                    <motion.div
                      key={i}
                      className="bg-white rounded-xl md:rounded-2xl shadow border border-slate-100 p-4 md:p-6 flex flex-col items-center justify-center min-h-48 md:min-h-60"
                      whileHover={{ y: -5, transition: { duration: 0.2 } }}
                    >
                      {/* Icon */}
                      <div className="w-6 h-6 md:w-8 md:h-8 bg-[#f5f7f1] rounded-lg mb-3 md:mb-4">
                        <div className="w-full h-full flex items-center justify-center">
                          <img
                            src={item.icon}
                            alt={item.title}
                            className="w-full h-full"
                          />
                        </div>
                      </div>

                      {/* Content */}
                      <div>
                        <h3 className="text-slate-950 text-base md:text-lg font-bold text-center leading-snug md:leading-7.5 tracking-wide">
                          {item.title}
                        </h3>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <motion.div
                  className={`w-full bg-white rounded-xl md:rounded-2xl flex flex-col shadow border border-slate-100 p-4 md:p-6
                    ${feature.hasChart ? "h-full justify-between" : ""}`}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                >
                  {/* Top Content Section */}
                  <div className="flex flex-col">
                    <div className="w-6 h-6 md:w-8 md:h-8 bg-[#f5f7f1] rounded-lg mb-3 md:mb-4">
                      <div className="w-full h-full flex items-center justify-center">
                        <img
                          src={feature.icon}
                          alt={feature.title}
                          className="w-full h-full"
                        />
                      </div>
                    </div>
                    <h3 className="text-slate-950 text-base md:text-lg font-bold leading-snug md:leading-7.5 tracking-wide mb-2">
                      {feature.title}
                    </h3>
                    {feature.description && (
                      <p className="text-slate-950 text-xs md:text-sm leading-relaxed md:leading-tight tracking-wide mb-3 md:mb-4">
                        {feature.description}
                      </p>
                    )}

                    {/* Bullets */}
                    {feature.bullets && (
                      <ul className="space-y-2 mb-4">
                        {feature.bullets.map((bullet, i) => (
                          <li key={i} className="flex items-center gap-2">
                            <span className="w-1 h-1 bg-slate-400 rounded-full"></span>
                            <span className="text-slate-950 text-xs md:text-sm leading-relaxed">
                              {bullet}
                            </span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>

                  {/* Bottom Content Section */}
                  <div className="flex flex-col">
                    {/* Chart */}
                    {feature.hasChart && (
                      <div className="mt-auto">
                        <MostActiveChart />
                      </div>
                    )}

                    {/* Get Started Button */}
                    {feature.hasGetStarted && (
                      <motion.button
                        onClick={() => window.location.assign("/login")}
                        className="text-slate-950 text-xs md:text-sm leading-none tracking-wide flex items-center gap-2 hover:text-slate-700 transition-colors mt-4"
                        whileHover={{ x: 5 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Get Started
                        <span>→</span>
                      </motion.button>
                    )}
                  </div>
                </motion.div>
              )}
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </motion.section>
  );
});

const UseCaseSection = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const useCases = useMemo(
    () => [
      {
        id: "case-1",
        title: "Streamlining Marketing Campaigns",
        content:
          "Many marketing teams struggle to manage multiple campaigns across different platforms. With Business Insight's unified dashboard and real-time analytics, marketers can easily combine all their data into one place. They gain instant insights and create customized reports tailored to each campaign's needs, leading to better performance and time savings.",
      },
      {
        id: "case-2",
        title: "Enhancing Startup Growth",
        content:
          "Startups face unique challenges in managing and analyzing their data effectively. Our platform provides scalable solutions that grow with your business, helping you make data-driven decisions from day one.",
      },
    ],
    []
  );

  return (
    <motion.section
      ref={ref}
      className="w-full max-w-7xl mx-auto my-28 px-4 py-8 md:py-16"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={staggerContainer}
    >
      <motion.div
        className="relative flex flex-col lg:flex-row gap-8 md:gap-12"
        variants={scaleIn}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        {/* Badge - Positioned absolutely on mobile */}
        <div className="md:hidden inline-block -top-3 left-4 z-10">
          <div className="inline-block px-3 py-1.5 md:px-4 md:py-2 bg-[#f5f7f1] rounded-lg border border-[#717a61]">
            <span className="text-[#555b48] text-xs md:text-sm font-medium uppercase leading-normal md:leading-[1.138rem] tracking-wide">
              use case
            </span>
          </div>
        </div>

        {/* Left Image */}
        <motion.div
          className="w-full lg:w-[45%]"
          variants={fadeInUp}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <div className="relative w-full aspect-592/396 rounded-xl md:rounded-2xl border border-slate-100 overflow-hidden">
            <img
              src="/svg/ui/use-case.webp"
              alt="Use cases visualization"
              className="w-full h-full object-cover"
            />
          </div>
        </motion.div>

        {/* Right Content */}
        <motion.div
          className="w-full lg:w-[55%] space-y-6 md:space-y-8"
          variants={fadeInUp}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
        >
          {/* Badge - Hidden on mobile, shown on desktop */}
          <div className="hidden md:inline-block px-3 py-1.5 md:px-4 md:py-2 bg-[#f5f7f1] rounded-lg border border-[#717a61]">
            <span className="text-[#555b48] text-xs md:text-sm font-medium uppercase leading-normal md:leading-[1.138rem] tracking-wide">
              use case
            </span>
          </div>

          {/* Heading */}
          <h2 className="text-slate-950 text-[1.8rem] md:text-[2.52rem] font-bold leading-tight md:leading-[4.063rem] tracking-wide">
            Real-World Use Cases
          </h2>

          {/* Description */}
          <p className="text-slate-950 text-xs md:text-sm leading-relaxed md:leading-tight tracking-wide">
            At Business Insight, we empower organizations—whether small startups
            or large enterprises—to harness their data's full potential. Our
            platform provides cutting-edge tools designed to simplify complex
            data workflows and enable precise, data-driven decisions.
          </p>

          {/* Use Cases Accordion */}
          <motion.div
            variants={fadeInUp}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.4 }}
          >
            <Accordion
              type="single"
              collapsible
              className="w-full"
              defaultValue="marketing-campaigns"
            >
              <AccordionItem
                value="marketing-campaigns"
                className="border-b border-slate-100"
              >
                <AccordionTrigger className="hover:no-underline py-3 md:py-4">
                  <h3 className="text-[#222222] text-sm md:text-base font-bold leading-snug md:leading-[1.563rem] tracking-wide text-left">
                    Streamlining Marketing Campaigns
                  </h3>
                </AccordionTrigger>
                <AccordionContent>
                  <p className="text-[#222222] text-xs md:text-sm leading-relaxed md:leading-tight tracking-wide pb-3">
                    Managing campaigns across multiple channels can be
                    overwhelming for marketing teams. Business Insight's
                    centralized dashboard and real-time analytics simplify this
                    process by consolidating campaign data into a unified view.
                    Marketing teams can quickly generate actionable insights,
                    customize reports to meet individual campaign requirements,
                    and drive measurable improvements in performance—all while
                    significantly reducing operational inefficiencies.
                  </p>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem
                value="startup-scalability"
                className="border-b border-slate-100"
              >
                <AccordionTrigger className="hover:no-underline py-3 md:py-4">
                  <h3 className="text-[#222222] text-sm md:text-base font-bold leading-snug md:leading-[1.563rem] tracking-wide text-left">
                    Accelerating Startup Scalability
                  </h3>
                </AccordionTrigger>
                <AccordionContent>
                  <p className="text-[#222222] text-xs md:text-sm leading-relaxed md:leading-tight tracking-wide pb-3">
                    For startups, agility is key to growth. Business Insight
                    delivers AI-driven insights and automated solutions that
                    help emerging businesses uncover growth opportunities,
                    fine-tune strategies, and streamline operational efficiency.
                    By turning raw data into actionable insights, startups can
                    focus on innovation and market competitiveness while scaling
                    sustainably in fast-paced industries.
                  </p>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </motion.div>

          {/* CTA Button */}
          <motion.div
            variants={fadeInUp}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.6 }}
          >
            <motion.button
              onClick={() => window.location.assign("/login")}
              className="w-full md:w-auto px-6 md:px-8 py-3 md:py-4 bg-[#2c3e50] text-white rounded-lg shadow text-sm md:text-lg font-medium capitalize transition-all duration-300 ease-in-out hover:bg-slate-300 hover:text-slate-700"
              whileHover={{ scale: 0.95 }}
              whileTap={{ scale: 0.9 }}
            >
              Start your growth
            </motion.button>
          </motion.div>
        </motion.div>
      </motion.div>
    </motion.section>
  );
});

// Memoized testimonials data
const testimonialsData = [
  {
    review:
      "Business Insight has completely redefined how we manage our data. The unified dashboard is incredibly intuitive, allowing us to centralize all our information seamlessly. The real-time analytics provide actionable insights that have been pivotal in improving our campaigns and decision-making processes.",
    name: "Sarah Lee",
    position: "Marketing Manager at Tech Innovators",
    image: "/svg/avatars/avatar-1.webp",
  },
  {
    review:
      "The integration capabilities of Business Insight are a game-changer. It eliminates manual data transfers between systems, saving hours of work. The advanced filtering tools give us the precision we need to extract meaningful insights and drive impactful strategies.",
    name: "Mark Johnson",
    position: "Data Analyst at Start-up Solutions",
    image: "/svg/avatars/avatar-2.webp",
  },
  {
    review:
      "The ability to manage data while on the move has been transformative. Mobile accessibility ensures we never miss critical opportunities. The platform's real-time insights have helped us remain proactive in responding to trends and customer feedback, boosting engagement.",
    name: "Sophia Nguyen",
    position: "Marketing Coordinator",
    image: "/svg/avatars/Container-1.webp",
  },
  {
    review:
      "Receiving automated alerts about critical data updates has been incredibly valuable for our team. It keeps us agile, allowing us to adapt our strategies in response to emerging trends. This feature alone has helped us stay competitive in dynamic markets.",
    name: "Emma Taylor",
    position: "Analytics Lead at GrowthHub",
    image: "/svg/avatars/Container-3.webp",
  },
  {
    review:
      "The support team at Business Insight deserves special mention. Their quick, round-the-clock assistance ensures that any technical challenges are addressed promptly. It allows us to focus on innovation and growth without being bogged down by operational issues.",
    name: "Michael Tran",
    position: "Tech Lead",
    image: "/svg/avatars/Container-2.webp",
  },
  {
    review:
      "The customizable reporting options have been a game-changer for our team. We can tailor reports to suit each client's unique needs, ensuring the data is visually engaging and actionable. It's been instrumental in presenting key insights to stakeholders.",
    name: "Lisa Chen",
    position: "Senior Marketing Specialist",
    image: "/svg/avatars/Container-5.webp",
  },
  {
    review:
      "Business Insight's advanced filtering tools have significantly improved our data analysis process. We can extract precisely the information we need and analyze it efficiently. The platform's scalability ensures that as our business grows, the tools evolve alongside us.",
    name: "Ryan Davis",
    position: "Data Scientist at Rapid Innovations",
    image: "/svg/avatars/Container-4.webp",
  },
  {
    review:
      "The flexible pricing plans have been a perfect fit for our business's growth. We can scale our capabilities without worry, ensuring we always have access to the tools we need for financial planning and strategic decision-making.",
    name: "Rachel Lee",
    position: "CFO at Bright Future Inc.",
    image: "/svg/avatars/container-6.webp",
  },
  {
    review:
      "BusinessInsight's unified dashboard has streamlined workflows across all departments in our company. Real-time analytics keep us updatedon critical data changes, enabling rapidresponse times and informed decision-making.The mobile accessibility feature is also a hugeplus for staying ahead of the competition.",
    name: "David Kim",
    position: "IT Director at Digital Dynamics",
    image: "/svg/avatars/container-7.webp",
  },
];

// Memoized TestimonialCard component
const TestimonialCard = memo(
  ({
    testimonial,
    index,
  }: {
    testimonial: (typeof testimonialsData)[0];
    index: number;
  }) => (
    <motion.div
      className="bg-white rounded-2xl shadow border border-slate-100 p-6 flex flex-col transition-all duration-300"
      variants={fadeInUp}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
    >
      {/* Review Text */}
      <p className="text-slate-950 text-base leading-relaxed tracking-wide mb-8 grow">
        {testimonial.review}
      </p>

      {/* Author Info */}
      <div className="flex justify-between items-end">
        <div>
          <h3 className="text-slate-950 text-lg font-semibold leading-tight tracking-wide mb-1">
            {testimonial.name}
          </h3>
          <p className="text-slate-500 text-base leading-normal tracking-wide">
            {testimonial.position}
          </p>
        </div>

        <div className="w-16 h-16 rounded-[0.875rem] overflow-hidden">
          <img
            src={testimonial.image}
            alt={testimonial.name}
            className="w-full h-full object-cover"
          />
        </div>
      </div>
    </motion.div>
  )
);

TestimonialCard.displayName = "TestimonialCard";

const TestimonialSection = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.section
      ref={ref}
      className="w-full max-w-7xl mx-auto mb-28 px-4 py-8 sm:py-16"
      id="testimonials"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={staggerContainer}
    >
      {/* Header */}
      <motion.div
        className="text-center mb-8 sm:mb-16"
        variants={fadeInUp}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="inline-block px-3 py-1.5 sm:px-4 sm:py-2 bg-[#f5f7f1] rounded-lg border border-[#717a61] mb-4 sm:mb-8">
          <span className="text-[#555b48] text-xs sm:text-sm font-medium uppercase leading-normal sm:leading-[1.138rem] tracking-wide">
            testimonials
          </span>
        </div>

        <h2 className="text-slate-950 text-2xl sm:text-[2.55rem] font-bold leading-tight sm:leading-[4.063rem] tracking-wide mb-3 sm:mb-4">
          Trusted Reviews from Our Users
        </h2>

        <p className="text-slate-950 text-xs sm:text-sm leading-relaxed sm:leading-tight tracking-wide max-w-176 mx-auto">
          Hear directly from our satisfied users about how Business Insight has
          transformed their data analysis and decision-making processes.
        </p>
      </motion.div>

      {/* Testimonials Grid */}
      <motion.div
        className="hidden sm:grid sm:grid-cols-3 gap-6"
        variants={staggerContainer}
        transition={{ delay: 0.3 }}
      >
        {testimonialsData.map((testimonial, index) => (
          <TestimonialCard
            key={index}
            testimonial={testimonial}
            index={index}
          />
        ))}
      </motion.div>

      {/* Mobile Carousel */}
      <motion.div
        className="relative sm:hidden no-scrollbar"
        variants={fadeInUp}
        transition={{ duration: 0.6, ease: "easeOut", delay: 0.3 }}
      >
        <div className="overflow-x-auto snap-x snap-mandatory flex gap-4 pb-6">
          {testimonialsData.map((testimonial, index) => (
            <motion.div
              key={index}
              className="snap-center shrink-0 w-[85vw] first:ml-4 last:mr-4 bg-white rounded-xl shadow border border-slate-100 p-4 flex flex-col transition-all duration-300"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              {/* Review Text */}
              <p className="text-slate-950 text-sm leading-relaxed tracking-wide mb-4 grow">
                {testimonial.review}
              </p>

              {/* Author Info */}
              <div className="flex justify-between items-end">
                <div>
                  <h3 className="text-slate-950 text-base font-semibold leading-tight tracking-wide mb-0.5">
                    {testimonial.name}
                  </h3>
                  <p className="text-slate-500 text-sm leading-normal tracking-wide">
                    {testimonial.position}
                  </p>
                </div>

                <div className="w-12 h-12 rounded-lg overflow-hidden">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </motion.section>
  );
});

// Memoized pricing plans data
const pricingPlansData = [
  {
    name: "Start Free",
    price: "Free",
    audience: "Best for Beginners",
    features: [
      "30 Days free access",
      "2 Active workspace",
      "2 Team Members",
      "Free usage of ai",
      "2 social media Connection",
    ],
    ctaText: "Start free",
    featured: false,
  },
  {
    name: "Basic",
    price: "$33",
    audience: "Perfect for Startups",
    features: [
      "2 Active workspace",
      "4 Team Members",
      "Pay-as-you-go AI usage",
      "All social media Connection",
      "Priority Support",
    ],
    ctaText: "Start free Trial",
    featured: true,
  },
  {
    name: "Enterprise",
    price: "$249",
    audience: "Ideal for Enterprises and Digital Agencies",
    features: [
      "Unlimited number of Workspaces",
      "Unlimited Team Members",
      "Unlimited Cloud Storage",
      "Unlimited AI Usage",
      "24/7 Dedicated Support",
    ],
    ctaText: "Contact support",
    featured: false,
  },
];

// Memoized PricingCard component
const PricingCard = memo(
  ({ plan, index }: { plan: (typeof pricingPlansData)[0]; index: number }) => (
    <motion.div
      className={`relative bg-white rounded-2xl shadow border p-6 md:p-8 flex flex-col h-full ${
        plan.featured
          ? "border-[#2c3e50] ring-2 ring-[#2c3e50] ring-opacity-20"
          : "border-slate-100"
      }`}
      variants={fadeInUp}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{
        y: -5,
        boxShadow:
          "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        transition: { duration: 0.2 },
      }}
    >
      {plan.featured && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-[#2c3e50] text-white px-4 py-1 rounded-full text-sm font-medium">
            Most Popular
          </span>
        </div>
      )}

      <div className="text-center mb-6">
        <h3 className="text-slate-950 text-xl md:text-2xl font-bold mb-2">
          {plan.name}
        </h3>
        <div className="text-slate-950 text-3xl md:text-4xl font-bold mb-2">
          {plan.price}
        </div>
        <p className="text-slate-500 text-sm">{plan.audience}</p>
      </div>

      <ul className="space-y-3 mb-8 grow">
        {plan.features.map((feature, i) => (
          <li key={i} className="flex items-center gap-3">
            <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
              <svg
                className="w-3 h-3 text-green-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <span className="text-slate-950 text-sm">{feature}</span>
          </li>
        ))}
      </ul>

      <motion.button
        onClick={() => window.location.assign("/login")}
        className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-300 ${
          plan.featured
            ? "bg-[#2c3e50] text-white hover:bg-slate-700"
            : "border border-[#2c3e50] text-[#2c3e50] hover:bg-[#2c3e50] hover:text-white"
        }`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {plan.ctaText}
      </motion.button>
    </motion.div>
  )
);

PricingCard.displayName = "PricingCard";

const PricingSection = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.section
      ref={ref}
      className="w-full max-w-7xl mx-auto px-4 py-8 md:py-16"
      id="plans"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={staggerContainer}
    >
      {/* Header */}
      <motion.div
        className="text-center mb-8 sm:mb-16"
        variants={fadeInUp}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="inline-block px-3 py-1.5 sm:px-4 sm:py-2 bg-[#f5f7f1] rounded-lg border border-[#717a61] mb-4 sm:mb-8">
          <span className="text-[#555b48] text-xs sm:text-sm font-medium uppercase leading-normal sm:leading-[1.138rem] tracking-wide">
            pricing plans
          </span>
        </div>

        <h2 className="text-slate-950 text-2xl sm:text-3xl lg:text-[2.461rem] font-bold leading-tight lg:leading-15 tracking-wide mb-3 sm:mb-4">
          Choose the Right Plan for You
        </h2>

        <p className="text-slate-950 text-xs sm:text-sm leading-relaxed sm:leading-tight tracking-wide max-w-138 mx-auto">
          At Business Insight, we offer flexible pricing plans to fit your
          business needs. Whether you're a startup or an established enterprise,
          our plans provide the tools and features you need to succeed.
        </p>
      </motion.div>

      {/* Pricing Cards */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 max-w-280 mx-auto"
        variants={staggerContainer}
        transition={{ delay: 0.3 }}
      >
        {pricingPlansData.map((plan, index) => (
          <PricingCard key={index} plan={plan} index={index} />
        ))}
      </motion.div>
    </motion.section>
  );
});

const FAQSection = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const faqs = useMemo(
    () => [
      {
        question: "How does Business Insights's unified dashboard work?",
        answer:
          "Our unified dashboard combines all your data sources into one intuitive interface. It provides real-time analytics, customizable views, and seamless integration with your existing tools.",
      },
      {
        question: "What kind of reports can I create with Business Insight?",
        answer:
          "You can create customized reports including performance analytics, trend analysis, comparative studies, and more. Our platform supports various data visualization options and export formats.",
      },
      {
        question: "How secure is my data on Business Insight?",
        answer:
          "We implement enterprise-grade security measures including end-to-end encryption, regular security audits, and compliance with international data protection standards.",
      },
      {
        question: "What kind of support does Business Insight offer?",
        answer:
          "We provide 24/7 customer support through multiple channels including live chat, email, and phone. Premium plans include dedicated support representatives.",
      },
      {
        question: "Is there a free trial available?",
        answer:
          "Yes, we offer a 30-day free trial with full access to all features. No credit card required to start your trial.",
      },
    ],
    []
  );

  return (
    <motion.section
      ref={ref}
      className="w-full max-w-7xl mx-auto my-28 px-4 py-8 sm:py-16"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={staggerContainer}
    >
      <motion.div
        className="flex flex-col lg:flex-row gap-6 sm:gap-8"
        variants={scaleIn}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        {/* Left Content */}
        <motion.div
          className="w-full lg:w-[40%] space-y-4 sm:space-y-6"
          variants={fadeInUp}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          {/* Badge */}
          <div className="inline-block px-3 py-1.5 sm:px-4 sm:py-2 bg-[#f5f7f1] rounded-lg border border-[#717a61]">
            <span className="text-[#555b48] text-xs sm:text-sm font-medium uppercase leading-normal sm:leading-[1.138rem] tracking-wide">
              FAQ
            </span>
          </div>

          {/* Heading */}
          <h2 className="text-slate-950 text-2xl sm:text-[2.538rem] font-bold leading-tight sm:leading-[4.063rem] tracking-wide">
            Frequently Asked
            <br className="hidden sm:block" />
            Questions
          </h2>

          {/* Description */}
          <p className="text-slate-950 text-xs sm:text-sm leading-relaxed sm:leading-tight tracking-wide">
            Find answers to your questions about Business Insight and how our
            platform can help streamline your data analysis.
          </p>

          {/* CTA Button */}
          <motion.button
            onClick={() => window.location.assign("/login")}
            className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-[#2c3e50] text-white rounded-lg shadow text-sm sm:text-lg font-medium capitalize transition-all duration-300 ease-in-out hover:bg-slate-300 hover:text-slate-700"
            whileHover={{ scale: 0.95 }}
            whileTap={{ scale: 0.9 }}
          >
            Get Started
          </motion.button>
        </motion.div>

        {/* Right Content - FAQ Accordion */}
        <motion.div
          className="w-full lg:w-[60%]"
          variants={fadeInUp}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
        >
          <Accordion
            type="single"
            collapsible
            className="space-y-3 sm:space-y-4"
          >
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <AccordionItem
                  value={`item-${index}`}
                  className="bg-white rounded-xl sm:rounded-2xl border border-[#f0eff8] px-4 sm:px-6 py-3 sm:py-5"
                >
                  <AccordionTrigger className="hover:no-underline">
                    <h3 className="text-slate-950 text-base sm:text-[1.182rem] font-bold leading-snug sm:leading-relaxed tracking-wide text-left">
                      {faq.question}
                    </h3>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-6 w-6 shrink-0 text-slate-700 transition-transform duration-200"
                    >
                      <path d="M12 5v14M5 12h14" />
                    </svg>
                  </AccordionTrigger>
                  <AccordionContent className="text-slate-600 text-sm sm:text-base">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              </motion.div>
            ))}
          </Accordion>
        </motion.div>
      </motion.div>
    </motion.section>
  );
});

const CTASection = memo(() => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.section
      ref={ref}
      className="w-full max-w-7xl mx-auto my-28 px-4 py-8 sm:py-16"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={staggerContainer}
    >
      <motion.div
        className="bg-white rounded-2xl shadow border border-slate-100 overflow-hidden relative"
        variants={scaleIn}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="absolute inset-0 z-0">
          <img
            src="/Performance/get-started-bg.webp"
            alt="Background pattern"
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex flex-col lg:flex-row items-center gap-6 lg:gap-12 p-4 lg:p-8 relative z-10">
          {/* Left Content */}
          <motion.div
            className="w-full lg:w-1/2 space-y-4 lg:space-y-6"
            variants={fadeInUp}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            {/* Badge */}
            <div className="inline-block px-3 py-1.5 sm:px-4 sm:py-2 bg-[#f5f7f1] rounded-lg border border-[#717a61]">
              <span className="text-[#555b48] text-xs sm:text-sm font-medium uppercase leading-normal tracking-wide">
                Get Started for Free
              </span>
            </div>

            {/* Heading */}
            <h2 className="text-slate-950 text-2xl sm:text-3xl lg:text-[2.461rem] font-bold leading-tight lg:leading-15 tracking-wide">
              Level up your Data Game Today!
            </h2>

            {/* Description */}
            <p className="text-slate-950 text-xs sm:text-sm leading-relaxed tracking-wide max-w-136">
              Try Business Insight risk-free with our free trial and experience
              the power of unified data analysis.
            </p>

            {/* CTA Button */}
            <motion.button
              onClick={() => window.location.assign("/login")}
              className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-[#2c3e50] text-white rounded-lg shadow text-sm font-medium capitalize transition-all duration-300 ease-in-out hover:bg-slate-300 hover:text-slate-700"
              whileHover={{ scale: 0.95 }}
              whileTap={{ scale: 0.9 }}
            >
              Get Started
            </motion.button>
          </motion.div>

          {/* Right Image */}
          <motion.div
            className="w-full lg:w-1/2 mt-6 lg:mt-0"
            variants={fadeInUp}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          >
            <div className="relative w-full aspect-589/368 rounded-2xl overflow-hidden">
              <img
                src="/Performance/img.webp"
                alt="Data analysis visualization"
                className="w-full h-full object-cover"
              />
            </div>
          </motion.div>
        </div>
      </motion.div>
    </motion.section>
  );
});

const HomePage = memo(() => {
  return (
    <main className="p-4 overflow-x-hidden">
      <Header />
      <Hero />
      <Features />
      <TrustedBy />
      <WhySection />
      <DiscoverSection />
      <UseCaseSection />
      <TestimonialSection />
      <PricingSection />
      <FAQSection />
      <CTASection />
      <Footer />
    </main>
  );
});

HomePage.displayName = "HomePage";

export default HomePage;
