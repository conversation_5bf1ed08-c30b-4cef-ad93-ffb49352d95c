"use client"

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import useNetworkStatus from '~/hooks/useNetworkStatus';
import Image from 'next/image';

const version = 'v1.0.0';

export default function OfflinePage() {
  const isOnline = useNetworkStatus();
  const router = useRouter();

  // Redirect to dashboard when back online (client-side only)
  useEffect(() => {
    if (isOnline) {
      router.push('/dashboard');
    }
  }, [isOnline, router]);

  // Avoid rendering content briefly if online to prevent flicker
  if (isOnline) return null;

  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-between bg-[#2c3e50] text-white">
      {/* Logo Container */}
      <div className="flex-1 flex items-center justify-center">
        <div className="w-32 h-32 bg-white rounded-full flex items-center justify-center p-4 shadow-lg">
          <Image
            src="/icons/logo.svg"
            alt="Business Insight Logo"
            width={80}
            height={80}
            priority
          />
        </div>
      </div>
      
      {/* Version Text */}
      <div className="text-center space-y-2 pb-8">
        <p className="text-lg font-medium">Business Insight</p>
        <p className="text-sm text-gray-300">{version}</p>
      </div>

      {/* Offline Message */}
      <div className="fixed bottom-0 left-0 right-0 bg-red-500 text-white text-center py-2 text-sm">
        You're offline. Some features may be limited.
      </div>
    </div>
  );
} 