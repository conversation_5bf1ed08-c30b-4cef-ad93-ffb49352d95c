"use client"
import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import VerifyForm from "../../components/verifyform";
import Loadingscreen from "~/components/loadingscreen";
import { Suspense } from "react";
import SplashScreen from "~/components/SplashScreen";

function VerifyContent() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    const checkAuth = () => {
      const status = sessionStorage.getItem('status');
      const accessToken = sessionStorage.getItem('accessToken');
      const refreshToken = sessionStorage.getItem('refreshToken');
    };

    checkAuth();
  }, [router]);

  return (
    <>
      <div className="w-screen h-screen flex flex-row ">
        <div className="form w-full h-full bg-white md:p-2 p-2 flex justify-center items-center">
          <VerifyForm />
        </div>
        <div className=" md:block hidden sidebar w-2/5 bg-linear-to-b from-[#A3C1DA] via-[#68AFDF] to-[#BDCBA1] h-full">
          <div className="flex flex-col items-center justify-center h-full p-8">
            <div className="relative">
              <img
                src="/icons/archer.svg"
                alt="AI-Powered Insights"
                className="w-full h-full"
              />
            </div>
            <h2 className="text-2xl font-bold text-black mb-4 text-center">
              Advanced Analytics & Reporting
            </h2>
            <p className="text-black text-center max-w-md">
              Track your KPIs and hit your goals with customisable analytics and
              detailed reports. Stay aware of real-time and AI-driven data.
            </p>
          </div>
        </div>
      </div>
    </>
  );
}

export default function VerifyPage() {
  const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;
  return (
      <Suspense fallback={isPWA ? <SplashScreen /> : <Loadingscreen />}>
        <VerifyContent />
      </Suspense>
  );
}
