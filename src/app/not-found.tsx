"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";

export default function NotFound() {
  const router = useRouter();

  return (
    <main className="min-h-screen w-full flex items-center justify-center bg-background px-4">
      <div className="max-w-2xl text-center">
        <p className="text-sm md:text-base xl:text-3xl text-muted-foreground tracking-wide mb-3">Error 404</p>
        <h1 className="text-4xl sm:text-5xl md:text-6xl xl:text-8xl font-extrabold tracking-tight text-primary mb-3">
          Not Found
        </h1>
        <p className="text-sm sm:text-base md:text-lg text-foreground/80 mb-8">
          The page you’re looking for doesn’t exist or has been moved.
        </p>

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
          <button
            type="button"
            onClick={() => router.back()}
            className="inline-flex items-center justify-center rounded-lg border border-dark-blue-normal px-5 py-3 text-sm md:text-base font-medium text-blue-800 hover:bg-dark-blue-light transition-colors"
            aria-label="Go back"
          >
            Go Back
          </button>

          <Link
            href="/"
            className="inline-flex items-center justify-center rounded-lg border border-dark-blue-normal px-5 py-3 text-sm md:text-base font-medium text-blue-800  transition-colors"
            aria-label="Go to homepage"
          >
            Go Home
          </Link>
        </div>
      </div>
    </main>
  );
}
