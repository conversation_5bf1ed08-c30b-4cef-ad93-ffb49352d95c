"use client";

const downloadAndConvertToBase64 = async (url: string): Promise<string> => {
  try {
    // Fetch the file
    const response = await fetch(url);
    const blob = await response.blob();

    // Convert blob to base64
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result;
        const str = typeof result === "string" ? result : "";
        const base64String = str.replace("data:", "").replace(/^.+,/, "");
        resolve(base64String);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error("Error downloading and converting file:", error);
    throw error;
  }
};

// Example usage in a component
const FileDownloader = () => {
  const handleDownload = async () => {
    try {
      const fileUrl =
        "https://assets.predis.ai/calendar_user_upl_img/671e7fc8b0fb85b422266fe6/672707d65b7a1c5181efa259_1627a271-2619-416d-8e58-bb7fd35a3457_0_lc3Gl.mp4"; // Replace with your file URL
      const base64 = await downloadAndConvertToBase64(fileUrl);
      console.log("Base64 string:", base64);
    } catch (error) {
      console.error("Error:", error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-8">
      <div className="max-w-md w-full rounded-xl border border-gray-300 bg-white shadow-md p-6 space-y-4">
        <h1 className="text-2xl font-bold text-gray-900">
          Tailwind Visual Check
        </h1>
        <p className="text-gray-600">
          If you see colors, spacing, rounded corners and shadows, Tailwind is
          working.
        </p>
        <div className="flex items-center gap-3">
          <span className="inline-block w-6 h-6 rounded-full bg-red-500" />
          <span className="inline-block w-6 h-6 rounded-full bg-green-500" />
          <span className="inline-block w-6 h-6 rounded-full bg-blue-500" />
          <span className="text-[#2c3e50] bg-gray-100 ">Blue text</span>
        </div>
        <button
          onClick={handleDownload}
          className="px-4 py-2 rounded-lg bg-blue-600 hover:bg-blue-700 text-white font-medium transition-colors"
        >
          Download and Convert to Base64
        </button>
      </div>
    </div>
  );
};

export default FileDownloader;
