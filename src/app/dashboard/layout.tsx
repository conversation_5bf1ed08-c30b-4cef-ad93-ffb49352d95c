"use client";

import { ReactNode, useEffect } from "react";
import { useWebSocket } from "../../hooks/useWebSocket";
import { useUserStore } from "~/store/userStore";
import DashboardLayoutComponent from "./(components)/DashboardLayout";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useLocation } from "react-router-dom";
import { usePathname } from "next/navigation";
import "react-big-calendar/lib/css/react-big-calendar.css";
import axios from "axios";
import LoadingScreen from "~/components/loadingscreen";

export default function DashboardLayout({ children }: { children: ReactNode }) {
  const {
    initializeWebSocket,
    getProfile,
    getWorkspaces,
    getWebSocketData,
    getWalletValue,
    getTokenRate,
  } = useWebSocket();
  const {
    setUser,
    workspaces,
    workspacesDetails,
    isLoading,
    csrfToken,
    selectedWorkspaceDetails,
    selectedWorkspace,
    getWorkspaceDone,
    walletValue,
  } = useUserStore();

  const router = useRouter();
  const pathname = usePathname();
  const { data: session, status } = useSession();

  useEffect(() => {
    // Only run this effect when session data is available
    if (session && status === "authenticated") {
      // const fetchCsrfToken = async () => {
      //   try {
      //     const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/login`);
      //     const data = await response.json();
      //     if (data.csrf) {
      //       setUser({ csrfToken: data.csrf });
      //       sessionStorage.setItem('csrfToken', data.csrf);
      //     }
      //   } catch (error) {
      //     console.error('Failed to fetch CSRF token:', error);
      //   }
      // };

      // // Save user data to sessionStorage
      // sessionStorage.setItem('email', session.user?.email ?? '');
      // sessionStorage.setItem('name', session.user?.name ?? '');
      // sessionStorage.setItem('image', session.user?.image ?? '');
      sessionStorage.setItem("status", "authenticated");
      localStorage.setItem("status", "authenticated");
      setUser({
        status: "authenticated",
      });

      // Fetch CSRF token
      // fetchCsrfToken();

      // Save tokens if they exist in the session
      if ("accessToken" in session) {
        sessionStorage.setItem("accessToken", session.accessToken as string);
        localStorage.setItem("accessToken", session.accessToken as string);
        setUser({
          accessToken: session.accessToken as string,
        });
      }
      if ("refreshToken" in session) {
        sessionStorage.setItem("refreshToken", session.refreshToken as string);
        localStorage.setItem("refreshToken", session.refreshToken as string);
        setUser({
          refreshToken: session.refreshToken as string,
        });
      }
    }
  }, [session, status]);

  useEffect(() => {
    let isInitialized = false;

    const handleRouteChange = async () => {
      try {
        // Only initialize WebSocket if it hasn't been initialized yet
        if (!isInitialized) {
          isInitialized = true;
          try {
            await initializeWebSocket();
            setTimeout(async () => {
              await getProfile();
            }, 500);
          } catch (error) {
            if (error.message === "MAX_RETRIES_REACHED") {
              // Clear all storage
              localStorage.clear();
              sessionStorage.clear();

              // Redirect to login
              router.push("/login");
            }
          }
        }
      } catch (error) {
        console.error("Error during route change handling:", error);
      }
    };

    // Set up route change handling
    window.addEventListener("popstate", handleRouteChange);
    handleRouteChange();

    // Clean up function
    return () => {
      window.removeEventListener("popstate", handleRouteChange);
      isInitialized = false;
    };
  }, [initializeWebSocket, router]); // Add router to dependencies

  useEffect(() => {
    if (getWorkspaceDone) {
      const handleDashboardRouteCheck = () => {
        const isTeamRoute =
          window.location.pathname.startsWith("/dashboard/team/");
        const isDashboardRoute =
          window.location.pathname.startsWith("/dashboard");
        const isGoalsRoute =
          window.location.pathname.startsWith("/dashboard/goals");
        const isProfileRoute = window.location.pathname.startsWith(
          "/dashboard/user-profile"
        );
        const isOnboardingRoute = window.location.pathname.startsWith(
          "/dashboard/onboarding"
        );

        // Check for new user conditions

        const isNewUser = !workspacesDetails || workspacesDetails.length === 0;
        const hasNoTokens = walletValue === "0" || !walletValue;

        if (
          isDashboardRoute &&
          !isTeamRoute &&
          !isGoalsRoute &&
          !isProfileRoute &&
          !isOnboardingRoute &&
          isNewUser &&
          hasNoTokens
        ) {
          router.push("/dashboard/onboarding");
          return;
        }

        // Check if there are no workspaces
        if (
          !isTeamRoute &&
          isDashboardRoute &&
          !isGoalsRoute &&
          !isProfileRoute &&
          !isOnboardingRoute &&
          (!workspacesDetails || workspacesDetails.length === 0)
        ) {
          router.push("/dashboard/no-workspaces");
        }

        // if (selectedWorkspace === null || selectedWorkspace === undefined || selectedWorkspace === "") {
        //     setUser({
        //         selectedWorkspaceDetails: [] ,
        //         workspacesDetails: [],
        //         workspaces: [],
        //     });
        // }
      };

      // Check on initial load
      handleDashboardRouteCheck();
    }
  }, [pathname, getWorkspaceDone]);

  useEffect(() => {
    const fetchInvoiceHistory = async () => {
      try {
        const accessToken =
          sessionStorage.getItem("accessToken") ||
          localStorage.getItem("accessToken");

        if (!accessToken) {
          throw new Error("Access token not available");
        }

        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}/api/invoice-history/`,
          {
            headers: {
              csrf_token: csrfToken,
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        if (response.status !== 200) {
          throw new Error(
            `Failed to fetch invoice history: ${response.status}`
          );
        }

        const data = response.data;
        setUser({ invoiceHistory: data });
        sessionStorage.setItem("invoiceHistory", JSON.stringify(data));
        localStorage.setItem("invoiceHistory", JSON.stringify(data));
      } catch (error) {
        const result = error.response.data;
        console.error("Error fetching invoice history:", result);
        // Consider showing a user-friendly error message here
      }
    };

    // Handle invoice history
    if (window.location.pathname === "/dashboard/user-profile") {
      fetchInvoiceHistory();
    }
  }, [pathname]);

  const shouldShowLoading = () => {
    const isAuthRoute =
      pathname?.includes("/login") ||
      pathname?.includes("/signup") ||
      pathname?.includes("/password-recovery");
    return isLoading && !isAuthRoute && pathname?.startsWith("/dashboard");
  };

  return (
    <div className="flex no-scrollbar">
      {/* {shouldShowLoading() && <LoadingScreen />} */}
      <DashboardLayoutComponent>{children}</DashboardLayoutComponent>
    </div>
  );
}
