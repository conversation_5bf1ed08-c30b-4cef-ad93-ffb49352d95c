import { rest } from 'msw'
import { setupServer } from 'msw/node'
import { describe, expect, it, beforeAll, afterEach, afterAll, beforeEach } from '@jest/globals'

// Create handlers for MSW
const handlers = [
  rest.post('/api/auth/login', async (req, res, ctx) => {
    const { email, password } = await req.json()
    
    if (email === '<EMAIL>' && password === 'password123') {
      return res(
        ctx.status(200),
        ctx.json({
          success: true,
          access_token: 'mock_access_token',
          refresh_token: 'mock_refresh_token'
        })
      )
    }
    
    return res(
      ctx.status(401),
      ctx.json({
        success: false,
        message: 'Invalid credentials'
      })
    )
  }),

  rest.post('/api/auth/signup', async (req, res, ctx) => {
    const { email, password, firstName, lastName } = await req.json()
    
    if (email && password && firstName && lastName) {
      return res(
        ctx.status(200),
        ctx.json({
          success: true,
          accessToken: 'mock_access_token',
          refreshToken: 'mock_refresh_token'
        })
      )
    }
    
    return res(
      ctx.status(400),
      ctx.json({
        success: false,
        message: 'Missing required fields'
      })
    )
  }),

  rest.post('/api/auth/verify', async (req, res, ctx) => {
    const { code } = await req.json()
    const auth = req.headers.get('Authorization')
    
    if (auth?.includes('mock_access_token') && code === '12345') {
      return res(
        ctx.status(200),
        ctx.json({
          success: true,
          message: 'Email verified successfully'
        })
      )
    }
    
    return res(
      ctx.status(400),
      ctx.json({
        success: false,
        message: 'Invalid verification code'
      })
    )
  }),

  rest.get('/api/reset-password', (req, res, ctx) => {
    const email = req.url.searchParams.get('email')
    
    if (email) {
      return res(
        ctx.status(200),
        ctx.json({
          process_id: 'mock_process_id',
          csrf: 'mock_csrf_token'
        })
      )
    }
    
    return res(
      ctx.status(400),
      ctx.json({
        success: false,
        message: 'Email is required'
      })
    )
  }),

  rest.put('/api/reset-password', async (req, res, ctx) => {
    const { process_id, code } = await req.json()
    
    if (process_id === 'mock_process_id' && code === '12345') {
      return res(
        ctx.status(200),
        ctx.json({
          success: true,
          message: 'OTP verified successfully'
        })
      )
    }
    
    return res(
      ctx.status(400),
      ctx.json({
        success: false,
        message: 'Invalid OTP'
      })
    )
  }),

  rest.post('/api/change-password', async (req, res, ctx) => {
    const { new_password, new_password_confirm, process_id } = await req.json()
    
    if (process_id === 'mock_process_id' && new_password === new_password_confirm) {
      return res(
        ctx.status(200),
        ctx.json({
          success: true,
          message: 'Password changed successfully'
        })
      )
    }
    
    return res(
      ctx.status(400),
      ctx.json({
        success: false,
        message: 'Passwords do not match'
      })
    )
  })
]

// Setup MSW Server
const server = setupServer(...handlers)

// Setup and teardown
beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())

describe('Authentication API Tests', () => {
  beforeEach(() => {
    // Clear storage before each test
    window.sessionStorage.clear()
  })

  describe('Login', () => {
    it('should successfully login user', async () => {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      })

      const data = await response.json()
      expect(data.success).toBe(true)
      expect(data.access_token).toBeDefined()
      expect(data.refresh_token).toBeDefined()
    })

    it('should fail with invalid credentials', async () => {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
      })

      const data = await response.json()
      expect(data.success).toBe(false)
      expect(data.message).toBe('Invalid credentials')
    })
  })

  describe('Signup', () => {
    it('should successfully register new user', async () => {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'Test',
          lastName: 'User'
        })
      })

      const data = await response.json()
      expect(data.success).toBe(true)
      expect(data.accessToken).toBeDefined()
      expect(data.refreshToken).toBeDefined()
    })
  })

  describe('Email Verification', () => {
    it('should successfully verify email', async () => {
      window.sessionStorage.setItem('accessToken', 'mock_access_token')

      const response = await fetch('/api/auth/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer mock_access_token'
        },
        body: JSON.stringify({
          code: '12345'
        })
      })

      const data = await response.json()
      expect(data.success).toBe(true)
      expect(data.message).toBe('Email verified successfully')
    })
  })

  describe('Password Recovery', () => {
    it('should initiate password reset', async () => {
      const response = await fetch('/api/reset-password?email=<EMAIL>', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const data = await response.json()
      expect(data.process_id).toBeDefined()
      expect(data.csrf).toBeDefined()
    })

    it('should verify OTP', async () => {
      const response = await fetch('/api/reset-password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          process_id: 'mock_process_id',
          code: '12345'
        })
      })

      const data = await response.json()
      expect(data.success).toBe(true)
      expect(data.message).toBe('OTP verified successfully')
    })

    it('should change password', async () => {
      const response = await fetch('/api/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          new_password: 'newpassword123',
          new_password_confirm: 'newpassword123',
          process_id: 'mock_process_id'
        })
      })

      const data = await response.json()
      expect(data.success).toBe(true)
      expect(data.message).toBe('Password changed successfully')
    })
  })
}) 