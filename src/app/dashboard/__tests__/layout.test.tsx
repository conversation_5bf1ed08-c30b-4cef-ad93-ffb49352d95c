import { render, screen, waitFor, act } from '~/utils/test-utils'
import DashboardLayout from '../layout'
import { useWebSocket } from '~/hooks/useWebSocket'

// Mock the useWebSocket hook
jest.mock('~/hooks/useWebSocket', () => ({
  useWebSocket: jest.fn(),
}))

describe('DashboardLayout', () => {
  const mockWebSocket = {
    initializeWebSocket: jest.fn(),
    getProfile: jest.fn(),
    getWorkspaces: jest.fn().mockResolvedValue([]),
    getWebSocketData: jest.fn().mockReturnValue([]),
    getWalletValue: jest.fn(),
  }

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks()
    ;(useWebSocket as jest.Mock).mockReturnValue(mockWebSocket)
    
    // Mock sessionStorage
    const mockSessionStorage = {
      getItem: jest.fn(),
      setItem: jest.fn(),
    }
    Object.defineProperty(window, 'sessionStorage', {
      value: mockSessionStorage,
    })
  })

  it('renders children correctly', () => {
    render(
      <DashboardLayout>
        <div data-testid="child">Test Child</div>
      </DashboardLayout>
    )

    expect(screen.getByTestId('child')).toBeInTheDocument()
  })

  it('initializes websocket and fetches data on mount', async () => {
    render(
      <DashboardLayout>
        <div>Test Child</div>
      </DashboardLayout>
    )

    await waitFor(() => {
      expect(mockWebSocket.initializeWebSocket).toHaveBeenCalled()
      expect(mockWebSocket.getProfile).toHaveBeenCalled()
      expect(mockWebSocket.getWorkspaces).toHaveBeenCalled()
      expect(mockWebSocket.getWalletValue).toHaveBeenCalled()
    })
  })

  // Add more test cases for error scenarios
  it('handles API errors gracefully', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation()
    mockWebSocket.getWorkspaces.mockRejectedValueOnce(new Error('API Error'))

    render(
      <DashboardLayout>
        <div>Test Child</div>
      </DashboardLayout>
    )

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalled()
    })

    consoleErrorSpy.mockRestore()
  })
}) 