"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "~/store/userStore";
import AddSocialAccountModal from "~/components/addsocialaccountmodal";
import AuthBtn from "~/components/authbtn";
import { showSuccessToast, showErrorToast } from "~/components/toasts";
import { useWebSocket } from "~/hooks/useWebSocket";
import { useWindowSize } from "react-use";
import Confetti from "react-confetti";
import axios from "axios";
import { signOut } from "next-auth/react";
import { motion, AnimatePresence } from "framer-motion";

interface WorkspaceDetails {
  workspace_name: string;
  industry: string;
  website: string;
  workspace_members: string;
  logo: string;
}

interface SocialAccount {
  platform: string;
  social_id: string;
  social_name: string;
  username: string;
}

interface Workspace {
  workspace_name: string;
  id: string;
  logo: string;
  social_media_count: number;
  social_accounts?: SocialAccount[];
  industry: string;
  website: string;
  workspace_members: string;
  created_at: string;
  updated_at: string;
}

interface WorkspaceResponse {
  success: boolean;
  message?: string;
}

interface UserState {
  selectedWorkspace: string;
  selectedWorkspaceDetails: Workspace;
  workspaceLogo: string;
}

const OnboardingPage = () => {
  const router = useRouter();
  const { width, height } = useWindowSize();

  const { createWorkspace, sendSelectedWorkspace } = useWebSocket();
  const { setUser, workspacesDetails, workspaces, walletValue } =
    useUserStore();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [logo, setLogo] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [companyName, setCompanyName] = useState("");
  const [companyMembers, setCompanyMembers] = useState("");
  const [industry, setIndustry] = useState("");
  const [website, setWebsite] = useState("");
  const [showAddSocialModal, setShowAddSocialModal] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Check if user should be on this page
  useEffect(() => {
    const checkUserStatus = async () => {
      try {
        console.log("Checking user status...");
        console.log("Workspaces:", workspaces);
        console.log("Workspace details:", workspacesDetails);

        const hasWorkspaces = workspaces && workspaces.length > 0;
        console.log("Has workspaces:", hasWorkspaces);

        const hasWorkspaceWithSocial = workspacesDetails?.some(
          (workspace) =>
            workspace.social_accounts && workspace.social_accounts.length > 0
        );
        console.log("Has workspace with social:", hasWorkspaceWithSocial);

        if (hasWorkspaces && hasWorkspaceWithSocial) {
          setCurrentStep(3);
        } else if (hasWorkspaces && !hasWorkspaceWithSocial) {
          setCurrentStep(2);
          setShowAddSocialModal(true);
        }
      } catch (error) {
        console.error("Error checking user status:", error);
      } finally {
        console.log("Finished checking user status");
        setIsInitialLoading(false);
      }
    };

    checkUserStatus();
  }, [workspacesDetails]);

  // Show loading state while checking user status
  if (isInitialLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setLogo(file);
      setLogoPreview(URL.createObjectURL(file));
    }
  };

  const removeLogo = () => {
    setLogo(null);
    setLogoPreview(null);
  };

  const isFormValid = () => {
    return (
      companyName.trim() !== "" &&
      companyMembers.trim() !== "" &&
      industry.trim() !== "" &&
      website.trim() !== ""
    );
  };

  const convertToBase64 = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        const base64 = result.replace("data:image/jpeg;base64,", "");
        resolve(base64);
      };
      reader.readAsDataURL(file);
    });
  };

  const handleCreateWorkspace = async () => {
    if (!isFormValid()) {
      showErrorToast("Please fill in all required fields", "onboarding-toast");
      return;
    }

    setIsLoading(true);
    try {
      const logoBase64 = logo ? await convertToBase64(logo) : null;
      const workspaceData = {
        name: companyName,
        industry: industry,
        members: companyMembers,
        website: website,
        logo: logoBase64 || "",
      };

      const response = (await createWorkspace(
        workspaceData
      )) as WorkspaceResponse;
      if (response?.success) {
        showSuccessToast("Workspace created successfully!", "onboarding-toast");
        const workspaceDetails: WorkspaceDetails = {
          workspace_name: companyName,
          industry: industry,
          website: website,
          workspace_members: companyMembers,
          logo: logoBase64 || "",
        };

        await sendSelectedWorkspace(companyName);

        const newWorkspace: Workspace = {
          ...workspaceDetails,
          id: "",
          social_media_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          social_accounts: [],
        };

        setUser({
          selectedWorkspace: workspaceDetails.workspace_name,
          selectedWorkspaceDetails: newWorkspace,
          workspaceLogo: logoBase64
            ? `data:image/jpeg;base64,${logoBase64}`
            : "/default-workspace-logo.svg",
        });

        setCurrentStep(2);
        setShowAddSocialModal(true);
      } else {
        showErrorToast("Failed to create workspace", "onboarding-toast");
      }
    } catch (error) {
      showErrorToast(
        "Failed to create workspace. Please try again.",
        "onboarding-toast"
      );
      console.error("Error creating workspace:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialAccountAdded = () => {
    window.location.href = "/dashboard";
  };

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  const stepVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 },
  };

  const renderProgressBar = () => (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={fadeIn}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="w-full max-w-2xl mx-auto mb-8"
    >
      <div className="relative pt-1">
        <div className="flex mb-2 items-center justify-between">
          {[1, 2, 3].map((step) => (
            <motion.div
              key={step}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{
                type: "spring",
                stiffness: 260,
                damping: 20,
                delay: 0.1 * step,
              }}
              className="flex items-center"
            >
              <span
                className={`w-8 h-8 flex items-center justify-center rounded-full ${
                  currentStep >= step ? "bg-blue-500 text-white" : "bg-gray-200"
                }`}
              >
                {step}
              </span>
              <span className="ml-2 text-sm font-medium">
                {step === 1
                  ? "Create Workspace"
                  : step === 2
                  ? "Add Social Account"
                  : "Complete"}
              </span>
            </motion.div>
          ))}
        </div>
        <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
          <motion.div
            initial={{ width: "0%" }}
            animate={{ width: `${(currentStep - 1) * 50}%` }}
            transition={{ duration: 0.5 }}
            className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500"
          />
        </div>
      </div>
    </motion.div>
  );

  const handleGetStarted = () => {
    router.push("/dashboard");
  };

  const handleLogout = async () => {
    try {
      await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/logout/`, {
        headers: {
          Authorization: `Bearer ${sessionStorage.getItem("accessToken")}`,
        },
      });

      await localStorage.clear();
      await sessionStorage.clear();
      await signOut();
      router.push("/login");
    } catch (error) {
      const result = error.response.data;
      console.error("Error during logout:", result);
      showErrorToast(
        result.message || "An unexpected error occurred. Please try again.",
        "verify-toast"
      );
      router.push("/login");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && currentStep === 1 && !isLoading) {
      handleCreateWorkspace();
    }
  };

  return (
    <div className="h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="absolute top-4 left-4"
      >
        <button
          onClick={handleLogout}
          className="flex items-center space-x-4 px-12 py-6 text-xl font-medium text-gray-700 hover:text-gray-900"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-10 w-10"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M19 12H5M12 19l-7-7 7-7" />
          </svg>
          <span>Logout</span>
        </button>
      </motion.div>

      <div className="max-w-4xl mx-auto h-full flex flex-col justify-center items-center">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          transition={{ duration: 0.5 }}
          className="text-center mb-8"
        >
          {currentStep === 1 && (
            <>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Your Journey Begins Here
              </h1>
            </>
          )}
          {currentStep === 2 && (
            <>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Take Control of Your Growth
              </h1>
            </>
          )}
          {currentStep === 3 && (
            <>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Welcome to Your Journey
              </h1>
            </>
          )}
        </motion.div>

        {renderProgressBar()}

        <div className="w-full overflow-y-auto max-h-[calc(100vh-300px)] px-4">
          <AnimatePresence mode="wait">
            {currentStep === 1 && (
              <motion.div
                key="step1"
                initial="hidden"
                animate="visible"
                exit="exit"
                variants={stepVariants}
                transition={{ duration: 0.4 }}
                className="bg-white rounded-lg shadow-lg p-6 md:p-8"
                onKeyPress={handleKeyPress}
              >
                <div className="mb-6">
                  <label className="flex items-center justify-center w-full space-x-2 text-gray-600 hover:text-gray-800 cursor-pointer">
                    {logoPreview ? (
                      <div className="relative group w-fit">
                        <img
                          src={logoPreview}
                          alt="Logo preview"
                          className="h-20 w-20 object-cover rounded-lg"
                        />
                        <div
                          className="absolute inset-0 bg-black/50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center cursor-pointer"
                          onClick={removeLogo}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6 text-white"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </div>
                      </div>
                    ) : (
                      <label className="flex items-center justify-center w-full space-x-2 text-gray-600 hover:text-gray-800 cursor-pointer">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-10 w-10 sm:h-12 sm:w-12 bg-gray-200 rounded-full p-3"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                          />
                        </svg>
                        <span>Upload Logo</span>
                        <input
                          type="file"
                          className="hidden"
                          onChange={handleLogoUpload}
                          accept="image/jpeg"
                        />
                      </label>
                    )}
                  </label>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      value={companyName}
                      onChange={(e) => setCompanyName(e.target.value)}
                      placeholder="Enter company name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Members <span className="text-red-500">*</span>
                    </label>
                    <select
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                      value={companyMembers}
                      onChange={(e) => setCompanyMembers(e.target.value)}
                    >
                      <option value="">Select number of members</option>
                      <option value="5-10">5 - 10</option>
                      <option value="10-20">10 - 20</option>
                      <option value="20-50">20 - 50</option>
                      <option value="50-100">50 - 100</option>
                      <option value="100+">100+</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Industry <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      value={industry}
                      onChange={(e) => setIndustry(e.target.value)}
                      placeholder="Enter industry"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Website <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      value={website}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (!value) {
                          setWebsite("");
                        } else if (
                          !value.startsWith("http://") &&
                          !value.startsWith("https://")
                        ) {
                          setWebsite(`https://${value}`);
                        } else {
                          setWebsite(value);
                        }
                      }}
                      placeholder="https://"
                    />
                  </div>
                </div>

                <div className="mt-8 flex justify-center">
                  <AuthBtn
                    text={isLoading ? "Creating Workspace..." : "Next"}
                    style="default"
                    onClick={handleCreateWorkspace}
                    disabled={isLoading}
                    isLoading={isLoading}
                  />
                </div>
              </motion.div>
            )}

            {showAddSocialModal && (
              <AddSocialAccountModal
                onClose={() => setShowAddSocialModal(false)}
                onAddAccount={handleSocialAccountAdded}
                page="onboarding"
              />
            )}

            {currentStep === 3 && <Confetti width={width} height={height} />}

            {currentStep === 3 && (
              <motion.div
                key="step3"
                initial="hidden"
                animate="visible"
                exit="exit"
                variants={stepVariants}
                transition={{ duration: 0.4 }}
                className="bg-white rounded-lg shadow-lg p-6 md:p-8 text-center  w-full mx-auto"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 260,
                    damping: 20,
                  }}
                  className="mb-6 flex flex-col items-center justify-center"
                >
                  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg
                      className="w-12 h-12 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={3}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-semibold mb-4">
                    Congratulations!
                  </h2>
                  <p className="text-gray-600 mb-8">
                    Everything is set. Start managing your business smarter and
                    take control like never before.
                  </p>
                  <AuthBtn
                    text="Let's get started"
                    style="default"
                    onClick={handleGetStarted}
                  />
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage;
