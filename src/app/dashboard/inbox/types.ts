// Types for the Inbox page components

export interface Message {
  id: string;
  name: string;
  message: string;
  originalMessage?: string; // Store original message text when showing unread count
  avatar: string;
  conversationId?: string;
  timestamp?: string;
  chatid?: string;
  time?: string;
  hasUnread?: boolean; // Add hasUnread flag for tracking unread messages
  unreadCount?: number; // Add unreadCount to track number of unread messages
}

export interface ChatMessage {
  id: string;
  serverMessageId?: string; // Store the original message ID from the server
  originalId?: string; // Store the original ID for reference
  sender: string;
  time: string;
  timestamp: number;
  content: string;
  avatar: string;
  sharedImage?: string;
  voiceMessage?: {
    base64Data: string;
    duration: number;
  };
  shares?: {
    data: Array<{
      link: string;
      paging?: any;
    }>;
  };
  reactions?: MessageReactions;
  isPreview?: boolean;
  error?: boolean;
  replyTo?: {
    id: string;
    content: string;
    sender: string;
  };
  seen?: boolean; // Add seen status
  from?: {
    username: string;
    id: string;
  };
  isSpecialMessage?: boolean; // Flag for special messages
  messageType?: string; // Type of message (text, voice, etc.)
  platform?: string; // Platform the message is from
}

export interface Participant {
  username: string;
  profile_picture: string;
  id: string; // Add id field to fix the error
}

export interface InstagramConversation {
  conversation_id: string;
  participants: Participant[];
}

// Add interface for API response at the top with other interfaces
export interface BaseApiResponse {
  success: boolean;
  message?: string;
  message_id?: string; // Add message_id field for responses that include it
}

export interface DirectMessagesResponse extends BaseApiResponse {
  success: true;
  messages: {
    data: any[];
    previous?: string;
    next?: string;
  };
}

export interface DirectConversationsResponse extends BaseApiResponse {
  success: true;
  job: string;
  conversations: {
    after?: boolean;
    before?: boolean;
    instagram?: {
      has_access: boolean;
      data: InstagramConversation[];
    };
    facebook?: {
      has_access: boolean;
      data: Array<{
        id: string;
        participants: {
          data: Array<{
            name: string;
            email: string;
            id: string;
            picture: {
              data?: {
                height: number;
                is_silhouette: boolean;
                url: string;
                width: number;
              };
            } | null;
          }>;
        };
        messages?: {
          data: Array<{
            id: string;
            message: string;
            from: {
              name: string;
              email: string;
              id: string;
            };
            created_time: string;
          }>;
          paging?: {
            cursors: {
              before: string;
              after: string;
            };
            next?: string;
          };
        };
      }>;
    };
    next?: string;
    previous?: string;
  };
}

export interface CommentSuccessResponse extends BaseApiResponse {
  success: true;
  comments: Array<{
    id: string;
    text: string;
    timestamp: string;
    from: {
      username: string;
      profile_picture: string | null;
    };
    replies?: {
      data: Reply[];
    };
  }>;
  next?: string;
}

export interface SimpleSuccessResponse extends BaseApiResponse {
  success: true;
}

export interface ApiErrorResponse extends BaseApiResponse {
  success: false;
  error: {
    message: string;
  };
}

export type ApiResponse =
  | DirectMessagesResponse
  | DirectConversationsResponse
  | CommentSuccessResponse
  | SimpleSuccessResponse
  | ApiErrorResponse;

export interface Reply {
  id: string;
  text: string;
  timestamp: string;
  from: {
    id: string;
    username: string;
    profile_picture: string | null;
  };
}

export interface Comment {
  id: string;
  username: string;
  text: string;
  image: string;
  avatar: string;
  timestamp: string;
  likes: number;
  from: {
    id: string;
    username: string;
    profile_picture: string | null;
  };
  commentsCount: number;
  user?: string;
  caption?: string;
  date?: string;
  replies?: {
    data: Reply[];
    paging?: {
      cursors: {
        before: string;
        after: string;
      };
    };
  };
  postId?: string;
  isUnread?: boolean; // Add isUnread flag for tracking unread comments
  hasUnread?: boolean; // Add hasUnread flag for tracking if a post has unread comments
  platform?: string; // Add platform property to identify the source platform (instagram, facebook, etc.)
  lastComment?: {
    id: string;
    text: string;
    username: string;
    timestamp: string;
    avatar: string;
  };
}

export interface Post {
  id: string;
  caption: string;
  media_type: string;
  media_url: string;
  permalink: string;
  thumbnail_url?: string;
  timestamp: string;
  username: string;
  comments_count: number;
  like_count: number;
  last_comment: {
    from: {
      id: string;
      username: string;
      profile_picture: string;
    };
    id: string;
    text: string;
    timestamp: string;
  }[];
}

export interface FacebookPost {
  id: string;
  message: string;
  created_time: string;
  attachments?: {
    data: {
      media: {
        image: {
          src: string;
          height?: number;
          width?: number;
        };
      };
      target?: {
        url: string;
      };
      title?: string;
      type?: string;
      url?: string;
    }[];
  };
  last_comment?: {
    data: {
      id: string;
      text: string;
      timestamp: string;
      from: {
        id: string;
        username: string;
        profile_picture: string;
      };
    }[];
  }[];
}

export interface CommentsResponse {
  success: boolean;
  job: string;
  data: {
    after: boolean;
    before: boolean;
    instagram?: {
      posts_count: number;
      posts: Post[];
    };
    facebook?: {
      posts_count: number;
      posts: FacebookPost[];
    };
  };
}

export interface PostCommentsResponse {
  success: boolean;
  next: string | null;
  data: {
    comments: Array<{
      id: string;
      username: string;
      text: string;
      timestamp: string;
      avatar: string;
      like_count: number;
      replies?: Array<{
        id: string;
        username: string;
        text: string;
        timestamp: string;
        avatar: string;
        like_count: number;
      }>;
    }>;
  };
}

// Add reaction types
export interface MessageReactionUser {
  username: string;
  id: string;
}

export interface MessageReaction {
  users: MessageReactionUser[];
  emoji: string;
}

export interface MessageReactions {
  data: MessageReaction[];
  paging?: {
    cursors: {
      before: string;
      after: string;
    };
  };
}

export type DayType = keyof typeof workHours;

export interface AutoAnswer {
  id: string;
  day: string;
  start_time: string;
  end_time: string;
  is_closed: boolean;
}

export interface ContentTypeState {
  [url: string]: {
    type: string;
    loading: boolean;
    error?: string;
  };
}

export interface ContextMenuPosition {
  x: number;
  y: number;
  messageId: string;
}

// Add new interfaces for voice recording
export interface AudioVisualizerState {
  wavesurfer: any | null;
  audioContext: AudioContext | null;
  analyser: AnalyserNode | null;
  dataArray: Uint8Array | null;
  animationFrame: number | null;
}

export interface AudioRecorderState {
  isRecording: boolean;
  mediaRecorder: MediaRecorder | null;
  audioChunks: Blob[];
  audioUrl: string | null;
  recordingTime: number;
  hasPermission: boolean | null;
  visualizer: AudioVisualizerState;
  isReviewing: boolean;
  base64Data: string | null;
  timerInterval?: NodeJS.Timeout | null;
}

// Placeholder for workHours type
const workHours = {
  Monday: { isOpen: true, hours: "09:00 - 17:00" },
  Tuesday: { isOpen: true, hours: "09:00 - 17:00" },
  Wednesday: { isOpen: true, hours: "09:00 - 17:00" },
  Thursday: { isOpen: true, hours: "09:00 - 17:00" },
  Friday: { isOpen: true, hours: "09:00 - 17:00" },
  Saturday: { isOpen: false, hours: "09:00 - 17:00" },
  Sunday: { isOpen: false, hours: "09:00 - 17:00" },
};

export type WorkHoursType = typeof workHours;
