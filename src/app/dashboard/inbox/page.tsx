"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useWebSocket } from "~/hooks/useWebSocket";
import { useUserStore } from "~/store/userStore";
import { useRouter } from "next/navigation";
import {
  showErrorToast,
  showSuccessToast,
  showWarningToast,
} from "~/components/toasts";
import Skeleton from "~/components/skeleton";

// Import components
import {
  InboxLayout,
  InboxTabs,
  DirectMessagesList,
  CommentsList,
  ChatSection,
  ChatInput,
  ChatHeader,
  CommentDetails,
  ManageFAQModal,
  EditFAQModal,
  FilterButton,
  AutoAnswerModal,
} from "./components";

// Import types and utils
import { Message, Comment, ChatMessage, WorkHoursType } from "./types";
import { logger } from "./utils";

const Inbox = () => {
  // Tab state
  const [activeTab, setActiveTab] = useState("direct");
  const [activeFilter, setActiveFilter] = useState("newest");
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);

  // Messages state
  const [selectedChat, setSelectedChat] = useState<Message | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isTransitioningChat, setIsTransitioningChat] = useState(false);
  const [isLoadingChatMessages, setIsLoadingChatMessages] = useState(false);
  const [isLoadingMoreMessages, setIsLoadingMoreMessages] = useState(false);
  const [nextPageToken, setNextPageToken] = useState<string | null>(null);
  const [, setPrevPageToken] = useState<string | null>(null);
  const [, setIsReceivingNewMessages] = useState(false);
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true);
  const [isLoadingMoreDirects] = useState(false);
  const [, setNextDirectsToken] = useState<string | null>(null);

  // Comments state
  const [comments, setComments] = useState<Comment[]>([]);
  const [selectedComment, setSelectedComment] = useState<Comment | null>(null);
  const [isLoadingComments, setIsLoadingComments] = useState(false);
  const [isLoadingCommentsList, setIsLoadingCommentsList] = useState(false);
  const [isLoadingPostComments, setIsLoadingPostComments] = useState(false);
  const [isLoadingMorePostComments, setIsLoadingMorePostComments] =
    useState(false);
  const [nextPostCommentsToken, setNextPostCommentsToken] = useState<
    string | null
  >(null);
  const [isLoadingMorePosts, setIsLoadingMorePosts] = useState(false);
  const [nextPostsToken, setNextPostsToken] = useState<string | null>(null);
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState("");
  const [isReplying, setIsReplying] = useState(false);

  // Unread tracking
  const [unreadDirects, setUnreadDirects] = useState<string[]>([]);
  const [unreadComments, setUnreadComments] = useState<string[]>([]);
  const [unreadDirectsCount, setUnreadDirectsCount] = useState(0);
  const [unreadCommentsCount, setUnreadCommentsCount] = useState(0);

  // NEW: Track unread message objects by their IDs
  // This is the SINGLE SOURCE OF TRUTH for unread messages
  const [unreadMessageObjects, setUnreadMessageObjects] = useState<string[]>(
    []
  );

  // FAQ state
  const [showFAQModal, setShowFAQModal] = useState(false);
  const [showManageFAQModal, setShowManageFAQModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const faqsRef = useRef<{ title: string; text: string }[]>([]);
  const editingFAQRef = useRef<{ title: string; text: string }>({
    title: "",
    text: "",
  });
  const createFAQTitleRef = useRef<HTMLInputElement>(null);
  const createFAQMessageRef = useRef<HTMLTextAreaElement>(null);

  // Auto-answer state
  const [showAutoAnswerModal, setShowAutoAnswerModal] = useState(false);
  const [autoAnswerEnabled, setAutoAnswerEnabled] = useState(false);
  const [isLoadingAutoAnswer, setIsLoadingAutoAnswer] = useState(false);
  const [autoAnswerData, setAutoAnswerData] = useState<any[]>([]);

  // Debug autoAnswerEnabled state changes
  useEffect(() => {
    console.log("🔧 AUTO ANSWER ENABLED STATE CHANGED:", autoAnswerEnabled);
  }, [autoAnswerEnabled]);
  const [workHours, setWorkHours] = useState<WorkHoursType>({
    Monday: { isOpen: true, hours: "09:00 - 17:00" },
    Tuesday: { isOpen: true, hours: "09:00 - 17:00" },
    Wednesday: { isOpen: true, hours: "09:00 - 17:00" },
    Thursday: { isOpen: true, hours: "09:00 - 17:00" },
    Friday: { isOpen: true, hours: "09:00 - 17:00" },
    Saturday: { isOpen: false, hours: "09:00 - 17:00" },
    Sunday: { isOpen: false, hours: "09:00 - 17:00" },
  });
  const [appliedChannels, setAppliedChannels] = useState<
    Record<string, boolean>
  >({});
  const [autoAnswerChanges, setAutoAnswerChanges] = useState<{
    [key: string]: any;
  }>({});

  // Refs
  const isViewingPostRef = useRef<boolean>(false);
  const postCommentsRef = useRef<Comment[]>([]);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const topLoaderRef = useRef<HTMLDivElement>(null);
  const bottomLoaderRef = useRef<HTMLDivElement>(null);
  const fetchAbortController = useRef<AbortController | null>(null);
  const currentRequestId = useRef<string>("");
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Ref to prevent duplicate auto-answer requests
  const autoAnswerRequestRef = useRef<Promise<any> | null>(null);

  // Ref to track processed message IDs to prevent double counting
  const processedMessageIdsRef = useRef<Set<string>>(new Set());

  // Ref to track the current selected chat - always has the most up-to-date value
  const selectedChatRef = useRef<Message | null>(null);

  // Ref to track if we're currently processing a hot reload update
  const isProcessingHotReloadRef = useRef<boolean>(false);

  // Replying state
  const [replyingToMessage, setReplyingToMessage] =
    useState<ChatMessage | null>(null);

  // Mobile state
  const [isMobile, setIsMobile] = useState(false);
  const [isNavigatingBack, setIsNavigatingBack] = useState(false);
  const [listKey, setListKey] = useState(0);

  // Cache state
  const [isInitialFetch, setIsInitialFetch] = useState(true);

  // Comment to post mapping - maps comment object_id to post/media ID
  const [commentToPostMapping, setCommentToPostMapping] = useState<
    Record<string, string>
  >({});

  // Track sender message counts for accurate unread counts
  const [senderMessageCounts, setSenderMessageCounts] = useState<
    Record<string, number>
  >({});

  // Ref to track previous sender message counts for detecting new unread messages
  const previousSenderMessageCountsRef = useRef<Record<string, number>>({});

  // Animation state for hot reload messages
  const [animatingMessageIds, setAnimatingMessageIds] = useState<Set<string>>(
    new Set()
  );

  // State for tracking recently moved posts for animation
  const [recentlyMovedPosts, setRecentlyMovedPosts] = useState<Set<string>>(
    new Set()
  );

  // NEW: Track UI loading state for websocket hot-reload processing
  const [isHotReloadProcessing, setIsHotReloadProcessing] = useState(false);

  // Guard to prevent rapid re-entry when opening Auto Answer modal
  const isOpeningAutoAnswerModalRef = useRef<boolean>(false);

  // Guard to prevent repeated auto-answer fetches when not explicitly forcing
  const hasRequestedAutoAnswersRef = useRef<boolean>(false);

  // Get WebSocket and user store
  const {
    sendSelectedWorkspace,
    getProfile,
    socket,
    getComments,
    getDirect,
    getDirectMessages,
    getPostComments,
    replyToComment,
    deleteComment,
    postComment,
    sendDirectMessage,
    deleteSavedReply,
    getAllSavedReply,
    createSavedReply,
    editTitleSavedReply,
    editTextSavedReply,
    getAutoAnswer,
    sendReact,
    editAutoAnswer,
    deleteAutoAnswer,
    enableAutoAnswers,
    disableAutoAnswers,
    getCache,
    seenCache,
  } = useWebSocket();

  const { selectedWorkspace, selectedSocial, selectedWorkspaceDetails } =
    useUserStore() as unknown as {
      selectedWorkspace: string;
      selectedSocial: {
        platform: string;
        username: string;
        profile_photo?: string;
        social_id: string;
      };
      selectedWorkspaceDetails: {
        social_accounts: {
          social_id: string;
          platform: string;
          username: string;
          profile_photo?: string;
        }[];
      };
    };

  const router = useRouter();

  // Filter functions
  const getFilteredMessages = useCallback(
    (messages: Message[]) => {
      switch (activeFilter) {
        case "unread":
          return messages.filter(
            (msg) => msg.hasUnread || (msg.unreadCount && msg.unreadCount > 0)
          );
        case "newest":
        default:
          // For newest, we maintain the current order (most recent activity first)
          // This is already handled by our hot reload logic that moves conversations to top
          return messages;
      }
    },
    [activeFilter]
  );

  const getFilteredComments = useCallback(
    (comments: Comment[]) => {
      switch (activeFilter) {
        case "unread":
          // Filter comments that have unread status
          // This uses the enhanced unread detection with comment to post mapping
          return comments.filter((comment) => {
            // Check if this post has any unread comments using the mapping
            const hasUnreadComments = unreadComments.some((commentId) => {
              const mappedPostId = commentToPostMapping[commentId];
              return (
                mappedPostId === comment.id || commentId === comment.id
                // Removed commentId.includes(comment.id) to prevent false positives
              );
            });
            return hasUnreadComments || comment.isUnread === true;
          });
        case "most liked":
          // For most liked, sort by likes count (highest first)
          return [...comments].sort((a, b) => {
            const likesA = a.likes || 0;
            const likesB = b.likes || 0;
            // If likes are equal, sort by timestamp (most recent first) as secondary sort
            if (likesA === likesB) {
              const timeA = new Date(a.timestamp || a.date || 0).getTime();
              const timeB = new Date(b.timestamp || b.date || 0).getTime();
              return timeB - timeA;
            }
            return likesB - likesA;
          });
        case "newest":
        default:
          // For newest, sort by timestamp (most recent first)
          return [...comments].sort((a, b) => {
            const timeA = new Date(a.timestamp || a.date || 0).getTime();
            const timeB = new Date(b.timestamp || b.date || 0).getTime();
            return timeB - timeA;
          });
      }
    },
    [activeFilter, unreadComments, commentToPostMapping]
  );

  // Keep selectedChatRef in sync with selectedChat state
  useEffect(() => {
    selectedChatRef.current = selectedChat;
  }, [selectedChat]);

  // Function to trigger animation for new hot reload messages
  const triggerMessageAnimation = useCallback((messageId: string) => {
    setAnimatingMessageIds((prev) => new Set(prev).add(messageId));

    // Remove from animating set after animation completes
    setTimeout(() => {
      setAnimatingMessageIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(messageId);
        return newSet;
      });
    }, 400); // Animation duration + buffer - reduced from 800ms to 400ms
  }, []);

  // Function to scroll to bottom smoothly
  const scrollToBottom = useCallback(() => {
    if (chatContainerRef.current) {
      const isMobile = window.innerWidth <= 768;
      const mobileOffset = isMobile ? 80 : 0; // Adjusted mobile offset

      chatContainerRef.current.scrollTo({
        top:
          chatContainerRef.current.scrollHeight -
          chatContainerRef.current.clientHeight +
          mobileOffset,
        behavior: "smooth",
      });
    }

    // Force scroll to the end reference
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, []);

  // Fetch post comments
  const fetchPostComments = useCallback(
    async (postId: string, platform: string) => {
      if (!selectedWorkspace || !postId) return;

      setIsLoadingPostComments(true);
      try {
        const response: any = await getPostComments({
          workspace_name: selectedWorkspace,
          post_id: postId,
          platform: platform,
        });

        if (response.success && response.data?.comments) {
          const formattedComments = response.data.comments.map(
            (comment: any) => ({
              id: comment.id,
              text: comment.text,
              timestamp: comment.timestamp,
              username: comment.username,
              image: "", // Required by Comment type
              avatar: comment.avatar || "", // Keep avatar for backward compatibility
              likes: comment.like_count || 0,
              commentsCount: 0,
              from: {
                id: comment.from?.id || comment.id,
                username: comment.from?.username || comment.username,
                profile_picture:
                  comment.from?.profile_picture || comment.avatar,
              },
              replies: comment.replies
                ? {
                    data: comment.replies.map((reply: any) => ({
                      id: reply.id,
                      text: reply.text,
                      timestamp: reply.timestamp,
                      from: {
                        id: reply.from?.id || reply.id,
                        username: reply.from?.username || reply.username,
                        profile_picture:
                          reply.from?.profile_picture || reply.avatar,
                      },
                    })),
                  }
                : undefined,
            })
          );

          postCommentsRef.current = formattedComments;
          setNextPostCommentsToken(response.next || null);
        } else {
          postCommentsRef.current = [];
          setNextPostCommentsToken(null);
        }
      } catch (error) {
        postCommentsRef.current = [];
      } finally {
        setIsLoadingPostComments(false);
      }
    },
    [selectedWorkspace, getPostComments]
  );

  // Handle reply submission
  const handleReplySubmit = useCallback(async () => {
    if (!replyText.trim() || !replyingTo || !selectedComment || isReplying)
      return;

    setIsReplying(true);
    try {
      const response: any = await replyToComment({
        workspace_name: selectedWorkspace,
        post_id: selectedComment.id,
        comment_id: replyingTo,
        text: replyText,
        platform: selectedComment.platform || "instagram",
      });

      if (response.success) {
        showSuccessToast("Reply posted successfully");
        setReplyText("");
        setShowReplyInput(false);
        setReplyingTo(null);

        // Refresh post comments
        if (selectedComment.platform) {
          fetchPostComments(selectedComment.id, selectedComment.platform);
        }
      } else {
        showErrorToast("Failed to post reply");
      }
    } catch (error) {
      showErrorToast("Error posting reply");
    } finally {
      setIsReplying(false);
    }
  }, [
    replyText,
    replyingTo,
    selectedComment,
    isReplying,
    selectedWorkspace,
    replyToComment,
    fetchPostComments,
  ]);

  // Handle comment submission
  const handleCommentSubmit = useCallback(
    async (commentText: string) => {
      if (!commentText.trim() || !selectedComment) return;

      try {
        const response: any = await postComment({
          workspace_name: selectedWorkspace,
          post_id: selectedComment.id,
          text: commentText,
          social_id: selectedSocial.social_id,
          platform: selectedComment.platform || "instagram",
        });

        if (response.success) {
          showSuccessToast("Comment posted successfully");

          // Refresh post comments
          if (selectedComment.platform) {
            fetchPostComments(selectedComment.id, selectedComment.platform);
          }
        } else {
          showErrorToast("Failed to post comment");
        }
      } catch (error) {
        showErrorToast("Error posting comment");
      }
    },
    [
      selectedComment,
      selectedWorkspace,
      selectedSocial.social_id,
      postComment,
      fetchPostComments,
    ]
  );

  // Handle sending a message - now receives message text as parameter
  const handleSend = useCallback(
    (messageText: string) => {
      // Use the ref to get the most current selected chat to prevent race conditions
      const currentSelectedChat = selectedChatRef.current;

      if (
        messageText.trim() &&
        currentSelectedChat?.chatid &&
        !isSendingMessage
      ) {
        setIsSendingMessage(true);
        const trimmedMessage = messageText.trim();

        // Store the current chat context to prevent race conditions
        const currentChatContext = {
          chatid: currentSelectedChat.chatid,
          conversationId:
            currentSelectedChat.conversationId || currentSelectedChat.id,
          id: currentSelectedChat.id,
        };

        // Create a preview message with our username as sender
        const previewMsg: ChatMessage = {
          id: "preview-" + Date.now().toString(),
          sender: selectedSocial.username, // Set sender to our username for proper alignment
          content: trimmedMessage,
          time: new Date().toISOString(),
          timestamp: Date.now(),
          avatar:
            selectedSocial.profile_photo ||
            `https://ui-avatars.com/api/?name=${selectedSocial.username}&background=random`,
          isPreview: true,
          replyTo: replyingToMessage
            ? {
                id: replyingToMessage.id,
                content: replyingToMessage.content,
                sender: replyingToMessage.sender,
              }
            : undefined,
          // Add from property to ensure consistency
          from: {
            id: selectedSocial.social_id,
            username: selectedSocial.username,
          },
        };

        setChatMessages((prev) => [previewMsg, ...prev]);
        // No need to clear input here - ChatInput handles it internally
        setReplyingToMessage(null);

        setTimeout(scrollToBottom, 100);
        setTimeout(() => {
          sendDirectMessage({
            workspace_name: selectedWorkspace,
            target_id: currentChatContext.chatid!,
            conversation_id: currentChatContext.conversationId, // Add conversation_id parameter
            text: trimmedMessage,
            platform: selectedSocial.platform,
            social_id: selectedSocial.social_id,
          })
            .then((response: any) => {
              if (response.success) {
                // Extract the message ID from the response if available
                const messageId =
                  response.data?.message_id ||
                  response.message_id ||
                  `local-${Date.now().toString()}`;

                // Set flag to indicate we're receiving new messages (not loading a new chat)
                setIsReceivingNewMessages(true);

                // Only add the message to chatMessages if we're still in the same chat
                // This prevents messages from appearing in the wrong conversation
                setChatMessages((prev) => {
                  // Get the most current selected chat for validation
                  const latestSelectedChat = selectedChatRef.current;

                  // Verify we're still in the same chat context
                  if (
                    !latestSelectedChat ||
                    latestSelectedChat.chatid !== currentChatContext.chatid ||
                    (latestSelectedChat.conversationId ||
                      latestSelectedChat.id) !==
                      currentChatContext.conversationId
                  ) {
                    // Still remove preview message even if context changed
                    return prev.filter((msg) => !msg.id.includes("preview"));
                  }

                  const withoutPreview = prev.filter(
                    (msg) => !msg.id.includes("preview")
                  );

                  // Validate message data before creating new message
                  if (
                    !messageText.trim() ||
                    !selectedSocial.username ||
                    !selectedSocial.social_id
                  ) {
                    return withoutPreview;
                  }

                  // Create a new message with our username as sender
                  const newMessage: ChatMessage = {
                    id: `local-${Date.now().toString()}`, // Use a local ID for the UI
                    serverMessageId: messageId, // Store the actual message ID from the API response
                    sender: selectedSocial.username, // Set sender to our username for proper alignment
                    content: messageText,
                    time: new Date().toISOString(),
                    timestamp: Date.now(),
                    avatar:
                      selectedSocial.profile_photo ||
                      `https://ui-avatars.com/api/?name=${selectedSocial.username}&background=random`,
                    // Initialize empty reactions array to ensure it's ready for reactions
                    reactions: { data: [] },
                    // Add from property to ensure consistency
                    from: {
                      id: selectedSocial.social_id,
                      username: selectedSocial.username,
                    },
                  };

                  return [...withoutPreview, newMessage];
                });

                // Move this conversation to the top of the messages list
                // Only update if we're still in the same chat context
                setMessages((prevMessages) => {
                  // Get the most current selected chat for validation
                  const latestSelectedChat = selectedChatRef.current;

                  // Verify we're still in the same chat context
                  if (
                    !latestSelectedChat ||
                    latestSelectedChat.chatid !== currentChatContext.chatid
                  ) {
                    return prevMessages;
                  }

                  // Find the current conversation in the list
                  const currentConversation = prevMessages.find(
                    (msg) => msg.chatid === currentChatContext.chatid
                  );
                  if (!currentConversation) return prevMessages;

                  // Create updated conversation object with the new message
                  const updatedMsg = {
                    ...currentConversation,
                    message: messageText,
                    timestamp: new Date().toISOString(),
                  };

                  // ALWAYS move the conversation to the top for sent messages
                  // This ensures the most recent activity is always visible first
                  // Remove the conversation from its current position
                  const filteredMessages = prevMessages.filter(
                    (msg) => msg.chatid !== currentChatContext.chatid
                  );

                  // Add the updated conversation to the top of the list
                  return [updatedMsg, ...filteredMessages];
                });
              } else {
                setChatMessages((prev) =>
                  prev.map((msg) =>
                    msg.id === previewMsg.id
                      ? { ...msg, error: true, isPreview: false }
                      : msg
                  )
                );
                showErrorToast(
                  response.error?.message || "Failed to send message",
                  "inbox-toast"
                );
              }
            })
            .catch((error) => {
              setChatMessages((prev) =>
                prev.map((msg) =>
                  msg.id === previewMsg.id
                    ? { ...msg, error: true, isPreview: false }
                    : msg
                )
              );
              const errorMessage =
                error?.error?.message || "Failed to send message";
              showErrorToast(errorMessage, "inbox-toast");
            })
            .finally(() => {
              setIsSendingMessage(false);
            });
        }, 500);
      }
    },
    [
      // Removed selectedChat from dependencies since we use selectedChatRef.current
      isSendingMessage,
      selectedSocial,
      replyingToMessage,
      scrollToBottom,
      sendDirectMessage,
      selectedWorkspace,
    ]
  );

  // Handle editing FAQ
  const handleEditFAQ = useCallback((faq: { title: string; text: string }) => {
    editingFAQRef.current = { title: faq.title, text: faq.text };
    setShowEditModal(true);
  }, []);

  // Handle deleting saved reply
  const handleDeleteSaveReply = useCallback(
    (title: string) => {
      deleteSavedReply({
        workspace_name: selectedWorkspace,
        title: title,
      }).then((res: any) => {
        if (res.success) {
          showSuccessToast(res.message, "inbox-toast");
        } else {
          showErrorToast(res.message, "inbox-toast");
        }
      });
    },
    [deleteSavedReply, selectedWorkspace]
  );

  // Fetch auto-answers with request deduplication
  const fetchAutoAnswers = useCallback(async () => {
    console.log("🔧 FETCH AUTO ANSWERS CALLED:", { selectedWorkspace });
    if (!selectedWorkspace) return;

    // If there's already a request in progress, return that promise
    if (autoAnswerRequestRef.current) {
      console.log("🔧 AUTO ANSWER REQUEST ALREADY IN PROGRESS");
      return autoAnswerRequestRef.current;
    }

    setIsLoadingAutoAnswer(true);

    // Create and store the request promise
    const requestPromise = getAutoAnswer({
      workspace_name: selectedWorkspace,
    });

    autoAnswerRequestRef.current = requestPromise;

    try {
      const response: any = await requestPromise;

      if (response.success) {
        // Set the autoAnswerEnabled state based on the autoanswer_enabled flag
        const enabledValue = Boolean(response.autoanswer_enabled);
        console.log("🔧 AUTO ANSWER DEBUG:", {
          rawResponse: response.autoanswer_enabled,
          enabledValue,
          autoAnswersLength: response.auto_answers?.length || 0,
        });
        setAutoAnswerEnabled(enabledValue);

        if (response.auto_answers) {
          setAutoAnswerData(response.auto_answers);

          // Populate autoAnswerChanges with the fetched data
          const autoAnswerChangesData: { [key: string]: any } = {};
          response.auto_answers.forEach((answer: any) => {
            autoAnswerChangesData[answer.day] = answer;
          });
          setAutoAnswerChanges(autoAnswerChangesData);

          // Initialize workHours based on auto-answer data
          const initialWorkHours = {
            Monday: { isOpen: true, hours: "09:00 - 17:00" },
            Tuesday: { isOpen: true, hours: "09:00 - 17:00" },
            Wednesday: { isOpen: true, hours: "09:00 - 17:00" },
            Thursday: { isOpen: true, hours: "09:00 - 17:00" },
            Friday: { isOpen: true, hours: "09:00 - 17:00" },
            Saturday: { isOpen: false, hours: "09:00 - 17:00" },
            Sunday: { isOpen: false, hours: "09:00 - 17:00" },
          };

          // Update workHours based on auto-answer data
          response.auto_answers.forEach((answer: any) => {
            const day =
              answer.day.charAt(0).toUpperCase() + answer.day.slice(1);
            if (initialWorkHours[day as keyof typeof initialWorkHours]) {
              initialWorkHours[day as keyof typeof initialWorkHours] = {
                isOpen: !answer.is_closed,
                hours: `${answer.start_time.slice(
                  0,
                  5
                )} - ${answer.end_time.slice(0, 5)}`,
              };
            }
          });

          setWorkHours(initialWorkHours as WorkHoursType);

          // Initialize applied channels
          const channelState: Record<string, boolean> = {};

          // Initialize all channels to false first
          selectedWorkspaceDetails?.social_accounts?.forEach((account) => {
            channelState[account.social_id] = false;
          });

          // Check if any auto-answer has social_media_ids
          let hasChannelData = false;
          response.auto_answers.forEach((answer: any) => {
            if (
              answer.social_media_ids &&
              Array.isArray(answer.social_media_ids)
            ) {
              hasChannelData = true;
              answer.social_media_ids.forEach((id: string) => {
                if (id) channelState[id] = true;
              });
            }
          });

          // If no channel data found, default to Instagram
          if (!hasChannelData) {
            selectedWorkspaceDetails?.social_accounts?.forEach((account) => {
              if (account.platform.toLowerCase() === "instagram") {
                channelState[account.social_id] = true;
              }
            });
          }

          setAppliedChannels(channelState);
        }
      }
    } catch (error) {
      showErrorToast("Failed to fetch auto-answer settings");
    } finally {
      setIsLoadingAutoAnswer(false);
      // Clear the request reference to allow future requests
      autoAnswerRequestRef.current = null;
    }
  }, [selectedWorkspace, getAutoAnswer, selectedWorkspaceDetails]);

  // Wrapper to ensure we only fetch once unless force is requested
  const fetchAutoAnswersSafe = useCallback(
    async (force: boolean = false) => {
      if (!force) {
        if (hasRequestedAutoAnswersRef.current) return;
        hasRequestedAutoAnswersRef.current = true;
      }
      await fetchAutoAnswers();
    },
    [fetchAutoAnswers]
  );

  // Separate effect to ensure auto-answer data is fetched when workspace is available
  useEffect(() => {
    if (selectedWorkspace && !isLoadingAutoAnswer) {
      console.log(
        "🔧 WORKSPACE AVAILABLE, ENSURING AUTO ANSWER DATA IS FETCHED"
      );
      // Only fetch if we don't have any auto-answer data yet
      if (autoAnswerData.length === 0 && !autoAnswerRequestRef.current) {
        console.log("🔧 NO AUTO ANSWER DATA, FETCHING ONCE SAFELY...");
        fetchAutoAnswersSafe(false);
      }
    }
  }, [
    selectedWorkspace,
    isLoadingAutoAnswer,
    autoAnswerData.length,
    fetchAutoAnswersSafe,
  ]);

  // Toggle auto-answer feature
  const toggleAutoAnswerFeature = useCallback(
    async (enabled: boolean) => {
      try {
        // Call the appropriate API function
        const response: any = enabled
          ? await enableAutoAnswers({ workspace_name: selectedWorkspace })
          : await disableAutoAnswers({ workspace_name: selectedWorkspace });

        if (response && response.success) {
          // Update UI state
          setAutoAnswerEnabled(enabled);

          // Update workHours state if disabling
          if (!enabled) {
            setWorkHours((prev) => {
              const updatedHours = { ...prev };
              Object.keys(updatedHours).forEach((day) => {
                updatedHours[day as keyof typeof workHours].isOpen = false;
              });
              return updatedHours;
            });
          }

          // Show appropriate toast based on action
          if (enabled) {
            showSuccessToast("Auto-answer feature enabled");
          } else {
            showWarningToast("Auto-answer feature disabled");
          }

          // Refresh auto-answers data to ensure UI is in sync with backend
          await fetchAutoAnswersSafe(true);
        } else {
          // Show error toast
          showErrorToast(
            `Failed to ${enabled ? "enable" : "disable"} auto-answer feature`
          );

          // Revert UI state
          setAutoAnswerEnabled(!enabled);

          // Refresh data to ensure UI state is correct
          await fetchAutoAnswersSafe(true);
        }
      } catch (error) {
        // Show error toast
        showErrorToast(
          `Error ${enabled ? "enabling" : "disabling"} auto-answer feature`
        );

        // Revert UI state
        setAutoAnswerEnabled(!enabled);

        // Refresh data to ensure UI state is correct
        await fetchAutoAnswersSafe(true);
      }
    },
    [
      selectedWorkspace,
      enableAutoAnswers,
      disableAutoAnswers,
      fetchAutoAnswersSafe,
    ]
  );

  // Centralized handler to open Auto Answer modal without spamming fetches
  const handleOpenAutoAnswerModal = useCallback(async () => {
    if (isOpeningAutoAnswerModalRef.current) return;
    isOpeningAutoAnswerModalRef.current = true;

    try {
      // If a request is already in flight, await it
      if (autoAnswerRequestRef.current) {
        await autoAnswerRequestRef.current;
      } else if (!isLoadingAutoAnswer && autoAnswerData.length === 0) {
        // If no data and not loading, fetch once and await
        await fetchAutoAnswersSafe(false);
      }
      setShowAutoAnswerModal(true);
    } finally {
      // Small delay to avoid immediate re-entry during state churn
      setTimeout(() => {
        isOpeningAutoAnswerModalRef.current = false;
      }, 50);
    }
  }, [autoAnswerData.length, isLoadingAutoAnswer, fetchAutoAnswersSafe]);

  // Effect to fetch initial data - only runs once when workspace is available
  useEffect(() => {
    console.log("🔧 INITIAL EFFECT TRIGGERED:", {
      selectedWorkspace,
      isInitialFetch,
    });
    if (selectedWorkspace && isInitialFetch) {
      console.log("🔧 STARTING INITIAL DATA FETCH");

      // Fetch direct messages inline
      setIsLoadingMessages(true);
      getDirect({
        workspace_name: selectedWorkspace,
      })
        .then((response: any) => {
          if (
            response.success &&
            response.conversations?.instagram?.has_access
          ) {
            if (
              response.conversations?.instagram?.data &&
              response.conversations.instagram.data.length > 0
            ) {
              const formattedConversations =
                response.conversations.instagram.data.map(
                  (conversation: any) => {
                    const chatId = conversation.participants[1]?.id;
                    const participant = conversation.participants[1];

                    let profilePicture = participant?.profile_picture;
                    if (!profilePicture) {
                      const username = participant?.username || "Unknown";
                      profilePicture = `https://ui-avatars.com/api/?name=${encodeURIComponent(
                        username
                      )}&background=random&color=fff`;
                    }

                    return {
                      id: conversation.conversation_id,
                      name: participant?.username || "Unknown User",
                      message: "Instagram Direct Message",
                      originalMessage: "Instagram Direct Message",
                      avatar: profilePicture,
                      conversationId: conversation.conversation_id,
                      chatid: chatId,
                      timestamp: new Date().toISOString(),
                      hasUnread: false,
                      unreadCount: 0,
                    };
                  }
                );
              setMessages(formattedConversations);
              setNextDirectsToken(response.conversations?.next || null);
            } else {
              setMessages([]);
            }
          } else if (
            response.conversations?.instagram &&
            !response.conversations.instagram.has_access
          ) {
            if (selectedWorkspaceDetails?.social_accounts?.length > 0) {
              showErrorToast(
                "You don't have access to Instagram messages",
                "inbox-toast"
              );
              router.push("/dashboard");
            }
          } else {
            setMessages([]);
          }
        })
        .catch(() => {
          setMessages([]);
        })
        .finally(() => {
          setIsLoadingMessages(false);
        });

      // Fetch comments inline
      setIsLoadingCommentsList(true);
      getComments({
        workspace_name: selectedWorkspace,
      })
        .then((response: any) => {
          let allPosts: any[] = [];
          if (response?.data) {
            if (response.data.instagram?.posts) {
              allPosts = [...allPosts, ...response.data.instagram.posts];
            }
            if (response.data.facebook?.posts) {
              const facebookPosts = response.data.facebook.posts.map(
                (fbPost: any) => ({
                  id: fbPost.id,
                  caption: fbPost.message || "",
                  media_type: "IMAGE",
                  media_url:
                    fbPost.attachments?.data?.[0]?.media?.image?.src || "",
                  permalink: fbPost.attachments?.data?.[0]?.url || "",
                  timestamp: fbPost.created_time,
                  username: "Facebook User",
                  comments_count: fbPost.last_comment?.[0]?.data?.length || 0,
                  like_count: 0,
                  last_comment: fbPost.last_comment?.[0]?.data?.[0]
                    ? [
                        {
                          from: {
                            id: fbPost.last_comment[0].data[0].from?.id || "",
                            username:
                              fbPost.last_comment[0].data[0].from?.username ||
                              "",
                            profile_picture:
                              fbPost.last_comment[0].data[0].from
                                ?.profile_picture || "",
                          },
                          id: fbPost.last_comment[0].data[0].id,
                          text: fbPost.last_comment[0].data[0].text,
                          timestamp: fbPost.last_comment[0].data[0].timestamp,
                        },
                      ]
                    : [],
                })
              );
              allPosts = [...allPosts, ...facebookPosts];
            }

            if (allPosts.length > 0) {
              const formattedComments = allPosts.map((post: any) => {
                const lastComment =
                  post.last_comment && post.last_comment.length > 0
                    ? post.last_comment[0]
                    : null;
                const platform = post.username ? "instagram" : "facebook";

                return {
                  id: post.id,
                  username: post.username,
                  text: post.caption,
                  image: post.media_url?.includes(".mp4")
                    ? post.thumbnail_url || ""
                    : post.media_url,
                  avatar: `https://ui-avatars.com/api/?name=${
                    post.username || "Unknown"
                  }&background=random`,
                  timestamp: new Date(
                    post.timestamp || post.created_time
                  ).toLocaleString(),
                  likes: post.like_count,
                  commentsCount: post.comments_count,
                  user: post.username,
                  caption: post.caption,
                  date: new Date(
                    post.timestamp || post.created_time
                  ).toLocaleString(),
                  from: {
                    id: post.id,
                    username: post.username,
                    profile_picture: null,
                  },
                  platform: platform,
                  lastComment: lastComment
                    ? {
                        id: lastComment.id,
                        text: lastComment.text,
                        username: lastComment.from?.username || "",
                        timestamp: lastComment.timestamp,
                        avatar: lastComment.from?.profile_picture || "",
                      }
                    : undefined,
                  isUnread: false,
                };
              });
              setComments(formattedComments);
              setNextPostsToken(response.data.after ? "next" : null);
            }
          }
        })
        .catch(() => {
          // Error handled silently
        })
        .finally(() => {
          setIsLoadingCommentsList(false);
        });

      // Fetch saved replies
      getAllSavedReply({ workspace_name: selectedWorkspace })
        .then((response: any) => {
          if (response.success && response.replies) {
            faqsRef.current = response.replies.map((reply: any) => ({
              title: reply.title,
              text: reply.text,
            }));
          }
        })
        .catch(() => {
          // Error handled silently
        });

      // Fetch cache data after initial data is loaded
      getCache({ workspace_name: selectedWorkspace })
        .then((response: any) => {
          if (response.success && response.caches) {
            // Process cache data to update unread counts
            const instagramNewMessages =
              response.caches.instagram?.newMessage || [];
            const facebookNewMessages =
              response.caches.facebook?.newMessage || [];

            // Process messages for unread counts
            const messageObjectIds: string[] = [];
            const senderCounts: Record<string, number> = {};

            [...instagramNewMessages, ...facebookNewMessages].forEach(
              (item: any) => {
                const objectId = item.object_id;
                const senderId =
                  item.data?.entry?.[0]?.messaging?.[0]?.sender?.id;

                if (objectId && senderId) {
                  messageObjectIds.push(objectId);
                  senderCounts[senderId] = (senderCounts[senderId] || 0) + 1;
                }
              }
            );

            // Update unread states - ONLY set absolute values on initial fetch
            // For subsequent workspace changes, we should preserve existing counts
            if (isInitialFetch) {
              setUnreadMessageObjects(messageObjectIds);
              setUnreadDirects([
                ...messageObjectIds,
                ...Object.keys(senderCounts),
              ]);
              setUnreadDirectsCount(messageObjectIds.length);
              setSenderMessageCounts(senderCounts);

              // Update the ref to track initial counts
              previousSenderMessageCountsRef.current = { ...senderCounts };

              console.log("🔧 INITIAL FETCH: Set absolute unread counts:", {
                messageCount: messageObjectIds.length,
                senderCounts,
              });

              // IMMEDIATELY update messages with unread counts after setting senderMessageCounts
              // This ensures the UI shows the correct counts right after cache data is loaded
              setMessages((prevMessages) => {
                console.log(
                  "🔧 IMMEDIATE UPDATE: Applying unread counts to messages:",
                  {
                    messagesCount: prevMessages.length,
                    senderCounts,
                  }
                );

                return prevMessages.map((msg) => {
                  const chatId = msg.chatid;
                  if (chatId && senderMessageCounts[chatId] !== undefined) {
                    const unreadCount = senderMessageCounts[chatId];
                    const updatedMsg = {
                      ...msg,
                      hasUnread: unreadCount > 0,
                      unreadCount: unreadCount,
                      message:
                        unreadCount > 0
                          ? `${unreadCount} new message${
                              unreadCount > 1 ? "s" : ""
                            }`
                          : msg.originalMessage || "Instagram Direct Message",
                    };

                    console.log("🔧 IMMEDIATE UPDATE: Updated message:", {
                      chatId,
                      name: msg.name,
                      oldMessage: msg.message,
                      newMessage: updatedMsg.message,
                      unreadCount,
                    });

                    return updatedMsg;
                  }
                  return msg;
                });
              });
            } else {
              // For non-initial fetches, we should merge with existing data
              // This prevents resetting counts when workspace changes
              console.log(
                "🔧 NON-INITIAL FETCH: Skipping absolute count reset to preserve existing unread counts"
              );
            }

            // Process comments
            const instagramNewComments =
              response.caches.instagram?.newComment || [];
            const facebookNewComments =
              response.caches.facebook?.newComment || [];
            const commentObjectIds: string[] = [];
            const newCommentToPostMapping: Record<string, string> = {};

            [...instagramNewComments, ...facebookNewComments].forEach(
              (item: any) => {
                const objectId = item.object_id;
                if (objectId) {
                  commentObjectIds.push(objectId);
                  const mediaId =
                    item.data?.entry?.[0]?.changes?.[0]?.value?.media?.id;
                  if (mediaId) {
                    newCommentToPostMapping[objectId] = mediaId;
                  }
                }
              }
            );

            // Update comment unread states - ONLY set absolute values on initial fetch
            if (isInitialFetch) {
              setUnreadComments(commentObjectIds);
              setUnreadCommentsCount(commentObjectIds.length);
              setCommentToPostMapping(newCommentToPostMapping);

              console.log("🔧 INITIAL FETCH: Set absolute comment counts:", {
                commentCount: commentObjectIds.length,
              });

              // Set the initial fetch flag to false after all cache data is processed
              setIsInitialFetch(false);
            } else {
              // For non-initial fetches, preserve existing comment counts
              console.log(
                "🔧 NON-INITIAL FETCH: Skipping comment count reset to preserve existing unread counts"
              );
            }
          }
        })
        .catch(() => {
          // Error handled silently
          // Set the initial fetch flag to false even if cache fetch fails
          if (isInitialFetch) {
            setIsInitialFetch(false);
          }
        });
    }
  }, [selectedWorkspace, isInitialFetch]);

  // Effect to update messages with unread counts when cache data changes
  // This effect handles both initial cache data and WebSocket updates
  useEffect(() => {
    console.log("🔧 EFFECT TRIGGERED (Message unread count update):", {
      messagesLength: messages.length,
      senderMessageCounts,
      senderMessageCountsKeys: Object.keys(senderMessageCounts || {}),
      previousCounts: previousSenderMessageCountsRef.current,
    });

    if (
      messages.length > 0 &&
      senderMessageCounts &&
      Object.keys(senderMessageCounts).length > 0
    ) {
      // Check if this is a direct WebSocket update (not from hot reload)
      // Hot reload updates handle their own message updates
      const isDirectWebSocketUpdate =
        !isProcessingHotReloadRef.current &&
        Object.keys(senderMessageCounts).some((chatId) => {
          const currentCount = senderMessageCounts[chatId] || 0;
          const previousCount =
            previousSenderMessageCountsRef.current[chatId] || 0;
          return currentCount !== previousCount;
        });

      // Check if this is the initial load with cache data
      const isInitialCacheLoad =
        Object.keys(previousSenderMessageCountsRef.current).length === 0 &&
        Object.keys(senderMessageCounts).length > 0;

      // Update messages if this is either a WebSocket update or initial cache load
      if (isDirectWebSocketUpdate || isInitialCacheLoad) {
        console.log("🔧 UPDATING MESSAGES WITH UNREAD COUNTS:", {
          isDirectWebSocketUpdate,
          isInitialCacheLoad,
          senderMessageCounts,
        });

        setMessages((prev) => {
          // Update all messages with their unread counts
          return prev.map((msg) => {
            const chatId = msg.chatid;
            if (chatId && senderMessageCounts[chatId] !== undefined) {
              const unreadCount = senderMessageCounts[chatId];
              return {
                ...msg,
                hasUnread: unreadCount > 0,
                unreadCount: unreadCount,
                message:
                  unreadCount > 0
                    ? `${unreadCount} new message${unreadCount > 1 ? "s" : ""}`
                    : msg.originalMessage || "Instagram Direct Message",
              };
            }
            return msg;
          });
        });

        // Update the ref with current counts for hot reload comparison
        previousSenderMessageCountsRef.current = { ...senderMessageCounts };
      }
    }
  }, [senderMessageCounts, messages.length]);

  // Effect to update comments with unread status when cache data changes
  useEffect(() => {
    if (comments.length > 0 && unreadComments.length > 0) {
      setComments((prev) =>
        prev.map((comment) => {
          // Check for unread status using the comment to post mapping
          const hasUnread = unreadComments.some((commentId) => {
            // Check if this comment ID maps to our post ID
            const mappedPostId = commentToPostMapping[commentId];
            return (
              mappedPostId === comment.id || commentId === comment.id
              // Removed commentId.includes(comment.id) to prevent false positives
            );
          });
          return {
            ...comment,
            isUnread: hasUnread,
          };
        })
      );
    }
  }, [unreadComments, comments.length, commentToPostMapping, setComments]);

  // Function to handle seen status from WebSocket
  const handleSeenStatus = useCallback(
    (seenStatusData: any) => {
      if (!selectedChat || !seenStatusData) return;

      // Extract the messaging data
      const messaging = seenStatusData.entry?.[0]?.messaging?.[0];
      if (!messaging || !messaging.read) {
        return;
      }

      // Extract the message ID from the read object
      const messageId = messaging.read.mid;
      const senderId = messaging.sender?.id; // Who sent the read receipt

      if (!messageId) {
        return;
      }

      // Determine if this read receipt is for a message from our user
      // If the sender of the read receipt matches the current chat ID, they read our message
      // If the recipient of the read receipt matches the current chat ID, we read their message
      const isReadReceiptForOurMessage = senderId === selectedChat?.chatid;

      // Also check if the sender is the person we're chatting with
      const isFromCurrentChatPartner = senderId === selectedChat.chatid;

      // Only process if this is a read receipt for our message from the current chat partner
      if (!isReadReceiptForOurMessage || !isFromCurrentChatPartner) {
        return;
      }

      // Only mark the last message from the current user as seen if it's also the last message in the chat
      setChatMessages((prev) => {
        // Find the last message from the current user
        // Messages from our user are those where the sender is NOT the current chat partner
        const userMessages = prev.filter(
          (msg) =>
            msg.from?.id !== selectedChat?.chatid ||
            msg.sender === selectedSocial.username
        );

        // Sort by timestamp in descending order (newest first)
        const sortedUserMessages = [...userMessages].sort((a, b) => {
          const timeA = a.timestamp || new Date(a.time).getTime();
          const timeB = b.timestamp || new Date(b.time).getTime();
          return timeB - timeA;
        });

        // Get the last message
        const lastUserMessage =
          sortedUserMessages.length > 0 ? sortedUserMessages[0] : null;

        if (!lastUserMessage) return prev;

        // Check if the last message in the chat is from the current user
        const allMessages = [...prev].sort((a, b) => {
          const timeA = a.timestamp || new Date(a.time).getTime();
          const timeB = b.timestamp || new Date(b.time).getTime();
          return timeA - timeB; // Sort in chronological order
        });

        const lastMessage =
          allMessages.length > 0 ? allMessages[allMessages.length - 1] : null;

        // Check if the last message is from our user (not from the current chat partner)
        const isLastMessageFromUser = lastMessage
          ? lastMessage.from?.id !== selectedChat?.chatid ||
            lastMessage.sender === selectedSocial.username
          : false;

        // Only mark the last message as seen if it's from the current user and it's the last message in the chat
        if (!isLastMessageFromUser) {
          // If the last message is not from the user, clear all seen statuses
          return prev.map((msg) => ({
            ...msg,
            seen: false,
          }));
        }

        // Only mark the last user message as seen
        return prev.map((msg) => {
          if (msg.id === lastUserMessage.id) {
            return { ...msg, seen: true };
          }
          return { ...msg, seen: false }; // Clear seen status for all other messages
        });
      });
    },
    [
      selectedChat,
      selectedSocial.social_id,
      selectedSocial.username,
      chatMessages,
    ]
  );

  // WebSocket event handling for new messages, reactions, and seen status
  useEffect(() => {
    if (!socket) return;

    const handleWebSocketMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);

        // Handle get_cache job response for hot reload messages
        if (data.job === "get_cache" && data.caches) {
          // Skip processing comments from WebSocket if this is the initial fetch
          // The initial useEffect will handle the initial load
          if (isInitialFetch) {
            return;
          }

          // Process Instagram comments (only for real-time updates, not initial load)
          if (
            data.caches.instagram?.newComment &&
            Array.isArray(data.caches.instagram.newComment) &&
            data.caches.instagram.newComment.length > 0
          ) {
            // Track new comment IDs
            const newCommentIds: string[] = [];

            // Process each new comment
            const newMappingUpdates: Record<string, string> = {};

            data.caches.instagram.newComment.forEach((commentItem: any) => {
              if (!commentItem || !commentItem.object_id || !commentItem.data)
                return;

              const commentId = commentItem.object_id;
              const mediaId =
                commentItem.data?.entry?.[0]?.changes?.[0]?.value?.media?.id;

              // Check if we already have this comment in our unread list
              const isAlreadyTracked = unreadComments.includes(commentId);

              if (!isAlreadyTracked) {
                // Build mapping for this comment
                if (mediaId) {
                  newMappingUpdates[commentId] = mediaId;
                }

                // Check if this comment belongs to the currently selected post
                // and if we're currently viewing that post
                const belongsToCurrentPost =
                  selectedComment &&
                  isViewingPostRef.current &&
                  mediaId &&
                  selectedComment.id === mediaId;

                // Only add to unread list if it doesn't belong to the currently selected post
                if (!belongsToCurrentPost) {
                  newCommentIds.push(commentId);
                  logger.ws("Found new comment via WebSocket", {
                    commentId,
                    mediaId,
                    commentData: commentItem.data,
                    mapping: `${commentId} -> ${mediaId}`,
                    addedToUnread: true,
                  });
                } else {
                  logger.ws(
                    "Found new comment via WebSocket for current post",
                    {
                      commentId,
                      mediaId,
                      commentData: commentItem.data,
                      mapping: `${commentId} -> ${mediaId}`,
                      addedToUnread: false,
                      reason: "belongs to currently selected post",
                    }
                  );
                }

                // Send seen_cache request if this comment belongs to the current post
                if (belongsToCurrentPost) {
                  // Send seen_cache request for this comment after a short delay
                  // to ensure the comment is rendered first
                  setTimeout(async () => {
                    try {
                      await seenCache({
                        workspace_name: selectedWorkspace,
                        cache_type: "newComment",
                        platform: selectedSocial.platform,
                        object_id: commentId,
                      });
                    } catch (error) {
                      // Error handled silently
                    }
                  }, 100); // Small delay to ensure comment is rendered
                }
              }
            });

            // Update the mapping with new comments
            if (Object.keys(newMappingUpdates).length > 0) {
              setCommentToPostMapping((prev) => ({
                ...prev,
                ...newMappingUpdates,
              }));
            }

            // Update unread comments if we have new ones
            if (newCommentIds.length > 0) {
              let actuallyNewCommentsCount = 0;

              // Update unread comments list and count new ones
              setUnreadComments((prev) => {
                const newUnreadList = [...prev];
                newCommentIds.forEach((commentId) => {
                  if (!newUnreadList.includes(commentId)) {
                    newUnreadList.push(commentId);
                    actuallyNewCommentsCount++;
                  }
                });
                return newUnreadList;
              });

              // Update unread comments count - add the number of actually new comments
              if (actuallyNewCommentsCount > 0) {
                setUnreadCommentsCount(
                  (prev) => prev + actuallyNewCommentsCount
                );
              }

              // Move posts with new comments to the top of the comments list
              // Extract media IDs from the new comments to identify which posts to move
              const affectedPostIds = new Set<string>();
              newCommentIds.forEach((commentId) => {
                const mappedPostId = newMappingUpdates[commentId];
                if (mappedPostId) {
                  affectedPostIds.add(mappedPostId);
                }
              });

              if (affectedPostIds.size > 0) {
                // Set recently moved posts for animation
                setRecentlyMovedPosts(new Set(affectedPostIds));

                setComments((prevComments) => {
                  const postsToMove: Comment[] = [];
                  const remainingPosts: Comment[] = [];

                  // Separate posts that received new comments from the rest
                  prevComments.forEach((comment) => {
                    if (affectedPostIds.has(comment.id)) {
                      // Update the post with unread status and move to top
                      postsToMove.push({
                        ...comment,
                        isUnread: true,
                        timestamp: new Date().toISOString(), // Update timestamp for proper sorting
                      });
                    } else {
                      remainingPosts.push(comment);
                    }
                  });

                  // Return posts with new comments at the top, followed by the rest
                  return [...postsToMove, ...remainingPosts];
                });

                // Clear recently moved posts after animation duration
                setTimeout(() => {
                  setRecentlyMovedPosts(new Set());
                }, 3000);

                logger.ws("Moved posts with new comments to top", {
                  affectedPostIds: Array.from(affectedPostIds),
                  newCommentIds,
                });
              }
            }
          }

          // Process Facebook comments (only for real-time updates, not initial load)
          if (
            data.caches.facebook?.newComment &&
            Array.isArray(data.caches.facebook.newComment) &&
            data.caches.facebook.newComment.length > 0
          ) {
            // Track new comment IDs
            const newCommentIds: string[] = [];
            const newMappingUpdates: Record<string, string> = {};

            // Process each new comment
            data.caches.facebook.newComment.forEach((commentItem: any) => {
              if (!commentItem || !commentItem.object_id || !commentItem.data)
                return;

              const commentId = commentItem.object_id;
              // For Facebook, we might need to extract the post ID differently
              // This will depend on the Facebook comment structure
              const facebookPostId =
                commentItem.data?.entry?.[0]?.changes?.[0]?.value?.post_id;

              // Check if we already have this comment in our unread list
              const isAlreadyTracked = unreadComments.includes(commentId);

              if (!isAlreadyTracked) {
                // Add Facebook post ID mapping when structure is available
                if (facebookPostId) {
                  newMappingUpdates[commentId] = facebookPostId;
                }

                // Check if this comment belongs to the currently selected post
                // and if we're currently viewing that post
                const belongsToCurrentPost =
                  selectedComment &&
                  isViewingPostRef.current &&
                  facebookPostId &&
                  selectedComment.id === facebookPostId;

                // Only add to unread list if it doesn't belong to the currently selected post
                if (!belongsToCurrentPost) {
                  newCommentIds.push(commentId);
                }

                // Send seen_cache request if this comment belongs to the current post
                if (belongsToCurrentPost) {
                  // Send seen_cache request for this comment after a short delay
                  // to ensure the comment is rendered first
                  setTimeout(async () => {
                    try {
                      await seenCache({
                        workspace_name: selectedWorkspace,
                        cache_type: "newComment",
                        platform: selectedSocial.platform,
                        object_id: commentId,
                      });
                    } catch (error) {
                      // Error handled silently
                    }
                  }, 100); // Small delay to ensure comment is rendered
                }
              }
            });

            // Update the mapping with new Facebook comments
            if (Object.keys(newMappingUpdates).length > 0) {
              setCommentToPostMapping((prev) => ({
                ...prev,
                ...newMappingUpdates,
              }));
            }

            // Update unread comments if we have new ones
            if (newCommentIds.length > 0) {
              let actuallyNewCommentsCount = 0;

              // Update unread comments list and count new ones
              setUnreadComments((prev) => {
                const newUnreadList = [...prev];
                newCommentIds.forEach((commentId) => {
                  if (!newUnreadList.includes(commentId)) {
                    newUnreadList.push(commentId);
                    actuallyNewCommentsCount++;
                  }
                });
                return newUnreadList;
              });

              // Update unread comments count - add the number of actually new comments
              if (actuallyNewCommentsCount > 0) {
                setUnreadCommentsCount(
                  (prev) => prev + actuallyNewCommentsCount
                );
              }

              // Move posts with new comments to the top of the comments list (Facebook)
              // Extract post IDs from the new comments to identify which posts to move
              const affectedPostIds = new Set<string>();
              newCommentIds.forEach((commentId) => {
                const mappedPostId = newMappingUpdates[commentId];
                if (mappedPostId) {
                  affectedPostIds.add(mappedPostId);
                }
              });

              if (affectedPostIds.size > 0) {
                // Set recently moved posts for animation (Facebook)
                setRecentlyMovedPosts(new Set(affectedPostIds));

                setComments((prevComments) => {
                  const postsToMove: Comment[] = [];
                  const remainingPosts: Comment[] = [];

                  // Separate posts that received new comments from the rest
                  prevComments.forEach((comment) => {
                    if (affectedPostIds.has(comment.id)) {
                      // Update the post with unread status and move to top
                      postsToMove.push({
                        ...comment,
                        isUnread: true,
                        timestamp: new Date().toISOString(), // Update timestamp for proper sorting
                      });
                    } else {
                      remainingPosts.push(comment);
                    }
                  });

                  // Return posts with new comments at the top, followed by the rest
                  return [...postsToMove, ...remainingPosts];
                });

                // Clear recently moved posts after animation duration
                setTimeout(() => {
                  setRecentlyMovedPosts(new Set());
                }, 3000);

                logger.ws("Moved Facebook posts with new comments to top", {
                  affectedPostIds: Array.from(affectedPostIds),
                  newCommentIds,
                });
              }
            }
          }

          // Process Instagram messages
          if (
            data.caches.instagram?.newMessage &&
            Array.isArray(data.caches.instagram.newMessage) &&
            data.caches.instagram.newMessage.length > 0
          ) {
            // Track new messages to process
            const newMessages: any[] = [];
            // Track message IDs and sender IDs separately
            const newMessageIds: string[] = [];
            const newSenderIds: string[] = [];
            // Track sender message counts
            const senderMessageCounts: Record<string, number> = {};

            // Process each new message
            data.caches.instagram.newMessage.forEach((messageItem: any) => {
              if (!messageItem || !messageItem.object_id || !messageItem.data)
                return;

              // Extract messaging data
              const messaging = messageItem.data.entry?.[0]?.messaging?.[0];
              if (!messaging || !messaging.message) return;

              const senderId = messaging.sender?.id;
              const recipientId = messaging.recipient?.id;
              const messageId = messageItem.object_id;
              const messageData = messaging.message;

              // Check if we already have this message in our unread list
              const isAlreadyTracked = unreadDirects.includes(messageId);

              // Determine if this message is from our user using chat-based identification
              // We need to check against all possible chats, not just the selected one
              const isFromOurUser = !messages.some(
                (msg) => msg.chatid === senderId
              );

              // IMPORTANT: Check if this message belongs to the currently selected chat
              // This prevents unread count increments for messages in the active chat
              const belongsToCurrentChat =
                selectedChat &&
                (selectedChat.chatid === senderId ||
                  selectedChat.chatid === recipientId);

              // If it's not already tracked, NOT from our user, AND doesn't belong to current chat, add it to our processing list
              if (
                !isAlreadyTracked &&
                !isFromOurUser &&
                !belongsToCurrentChat
              ) {
                // Add to new messages array for processing
                newMessages.push({
                  messageId,
                  senderId,
                  recipientId,
                  messageData,
                  messageItem,
                });

                // Add to message IDs array if not already there
                if (!newMessageIds.includes(messageId)) {
                  newMessageIds.push(messageId);
                }

                // Add to sender IDs array if not already there
                if (senderId && !newSenderIds.includes(senderId)) {
                  newSenderIds.push(senderId);
                }

                // Track sender message counts
                if (senderId) {
                  senderMessageCounts[senderId] =
                    (senderMessageCounts[senderId] || 0) + 1;
                }
              }

              // Check if this message belongs to the current chat using chat-based identification
              if (selectedChat && selectedChat.chatid) {
                // Check if this message is related to the current chat
                const isRelatedToCurrentChat =
                  selectedChat.chatid === senderId ||
                  selectedChat.chatid === recipientId;

                // Determine if this is from our user or the other person
                const isFromOtherUser = senderId === selectedChat.chatid;
                const isFromOurUser = !isFromOtherUser;

                if (isRelatedToCurrentChat) {
                  // Send seen_cache request for this message if it's not from our user
                  if (!isFromOurUser) {
                    try {
                      // Use setTimeout to avoid blocking the UI
                      setTimeout(async () => {
                        try {
                          await seenCache({
                            workspace_name: selectedWorkspace,
                            cache_type: "newMessage",
                            platform: selectedSocial.platform,
                            object_id: messageItem.object_id,
                          });
                        } catch (error) {
                          // Error handled silently
                        }
                      }, 0);
                    } catch (error) {
                      // Error handled silently
                    }
                  }

                  // If this is a message from our user, add it to the chat
                  if (isFromOurUser) {
                    const timestamp =
                      messaging.timestamp > 1000000000000
                        ? messaging.timestamp
                        : messaging.timestamp * 1000;

                    const senderName = isFromOurUser
                      ? selectedSocial.username
                      : selectedChat?.name || "Unknown";

                    const senderAvatar = isFromOurUser
                      ? selectedSocial.profile_photo || `/default-avatar.png`
                      : selectedChat?.avatar ||
                        `https://ui-avatars.com/api/?name=${senderName}&background=random`;

                    const newMessage: ChatMessage = {
                      id: messageData.mid,
                      sender: senderName,
                      time: new Date(timestamp).toISOString(),
                      timestamp: timestamp,
                      content: messageData.text || "",
                      avatar: senderAvatar,
                      seen: false,
                      reactions: { data: [] },
                      from: {
                        username: senderName,
                        id: isFromOurUser ? selectedSocial.social_id : senderId,
                      },
                    };

                    // Check if this message already exists in the chat
                    const messageExists = chatMessages.some(
                      (msg: ChatMessage) => msg.id === messageData.mid
                    );

                    if (!messageExists) {
                      // Trigger animation for this hot reload message
                      triggerMessageAnimation(newMessage.id);

                      setChatMessages((prev) => [...prev, newMessage]);
                      setTimeout(scrollToBottom, 100);
                    }
                  }
                }
              }
            });

            // Process new messages that aren't already tracked
            if (newMessages.length > 0) {
              // Group messages by sender
              const messagesBySender: Record<string, any[]> = {};

              newMessages.forEach((msg) => {
                // Make sure senderId exists
                if (msg.senderId) {
                  const senderId = msg.senderId;
                  if (!messagesBySender[senderId]) {
                    messagesBySender[senderId] = [];
                  }
                  messagesBySender[senderId].push(msg);
                }
              });

              // Update unread counts and messages
              setUnreadDirects((prev) => {
                const newUnreadList = [...prev];

                // Add each new message ID
                newMessageIds.forEach((messageId) => {
                  if (!newUnreadList.includes(messageId)) {
                    newUnreadList.push(messageId);
                  }
                });

                // Add sender IDs if not already present
                newSenderIds.forEach((senderId) => {
                  if (!newUnreadList.includes(senderId)) {
                    newUnreadList.push(senderId);
                  }
                });

                return newUnreadList;
              });

              // INCREMENT unread count based on the number of new messages
              // Don't set absolute value, add to existing count
              const newMessagesCount = newMessageIds.length;
              setUnreadDirectsCount((prev) => {
                const newTotal = prev + newMessagesCount;
                console.log("🔧 HOT RELOAD: Updating unread count:", {
                  previousCount: prev,
                  newMessages: newMessagesCount,
                  newTotal: newTotal,
                  newMessageIds: newMessageIds,
                });
                return newTotal;
              });

              // ADD new unread message objects to existing ones, don't overwrite
              // Use Set to prevent duplicates
              setUnreadMessageObjects((prev) => {
                const combined = [...prev, ...newMessageIds];
                return [...new Set(combined)]; // Remove duplicates
              });

              // Update sender message counts and move conversations with new unread messages to top
              console.log(
                "🔧 HOT RELOAD: Setting sender message counts:",
                senderMessageCounts
              );

              // Set flag to indicate we're processing hot reload
              isProcessingHotReloadRef.current = true;
              setIsHotReloadProcessing(true);

              // Merge new counts with existing counts instead of replacing them
              const previousCounts = previousSenderMessageCountsRef.current;
              const currentCounts = { ...previousCounts };

              // Add the new message counts to existing counts
              Object.keys(senderMessageCounts).forEach((senderId) => {
                const newCount = senderMessageCounts[senderId];
                if (newCount !== undefined) {
                  currentCounts[senderId] =
                    (currentCounts[senderId] || 0) + newCount;
                }
              });

              console.log("🔧 HOT RELOAD: Merging sender message counts:", {
                previousCounts,
                newCounts: senderMessageCounts,
                mergedCounts: currentCounts,
              });

              // Find conversations that have new unread messages
              const conversationsWithNewUnread: string[] = [];

              // Check both new message senders and existing counts that changed
              const allAffectedChatIds = new Set([
                ...Object.keys(senderMessageCounts), // New messages
                ...Object.keys(currentCounts).filter((chatId) => {
                  const currentCount = currentCounts[chatId] || 0;
                  const previousCount = previousCounts[chatId] || 0;
                  return currentCount > previousCount;
                }),
              ]);

              allAffectedChatIds.forEach((chatId) => {
                const currentCount = currentCounts[chatId] || 0;
                const previousCount = previousCounts[chatId] || 0;

                // If the current count is greater than 0 and greater than previous, this conversation has new unread messages
                if (currentCount > 0 && currentCount > previousCount) {
                  conversationsWithNewUnread.push(chatId);
                  console.log("🔧 HOT RELOAD: Detected new unread for chat:", {
                    chatId,
                    currentCount,
                    previousCount,
                    newMessagesInThisBatch: senderMessageCounts[chatId] || 0,
                  });
                }
              });

              // Update the ref with current counts
              previousSenderMessageCountsRef.current = { ...currentCounts };

              // Set the sender message counts
              setSenderMessageCounts(currentCounts);

              // ALWAYS update messages with current counts, regardless of reordering
              // This ensures unread counts are always in sync
              setMessages((prevMessages) => {
                // First, update ALL messages with their current unread counts
                const updatedMessages = prevMessages.map((msg) => {
                  const chatId = msg.chatid;
                  if (chatId && currentCounts[chatId] !== undefined) {
                    const unreadCount = currentCounts[chatId];
                    return {
                      ...msg,
                      hasUnread: unreadCount > 0,
                      unreadCount: unreadCount,
                      message:
                        unreadCount > 0
                          ? `${unreadCount} new message${
                              unreadCount > 1 ? "s" : ""
                            }`
                          : msg.originalMessage || "Instagram Direct Message",
                    };
                  }
                  return msg;
                });

                // If we have conversations with new unread messages, move them to top
                if (conversationsWithNewUnread.length > 0) {
                  console.log(
                    "🔧 HOT RELOAD: Moving conversations to top:",
                    conversationsWithNewUnread
                  );

                  const conversationsToMoveToTop: any[] = [];
                  const remainingConversations: any[] = [];

                  // Separate conversations to move to top vs remaining
                  updatedMessages.forEach((msg) => {
                    if (
                      msg.chatid &&
                      conversationsWithNewUnread.includes(msg.chatid)
                    ) {
                      conversationsToMoveToTop.push(msg);
                    } else {
                      remainingConversations.push(msg);
                    }
                  });

                  // Sort conversations to move to top by timestamp (most recent first)
                  conversationsToMoveToTop.sort((a, b) => {
                    const timeA = new Date(a.timestamp || 0).getTime();
                    const timeB = new Date(b.timestamp || 0).getTime();
                    return timeB - timeA;
                  });

                  console.log("🔧 HOT RELOAD: Reordered conversations:", {
                    movedToTop: conversationsToMoveToTop.map((m) => ({
                      chatid: m.chatid,
                      name: m.name,
                      unreadCount: m.unreadCount,
                    })),
                    remaining: remainingConversations.length,
                  });

                  // Return reordered list with new unread conversations at the top
                  return [
                    ...conversationsToMoveToTop,
                    ...remainingConversations,
                  ];
                }

                // If no reordering needed, just return the updated messages
                return updatedMessages;
              });

              // Reset the hot reload processing flag
              setTimeout(() => {
                isProcessingHotReloadRef.current = false;
                setIsHotReloadProcessing(false);
              }, 100);
            }
          }
        }

        // Handle the specific message format from the example
        if (
          data.object === "instagram" &&
          data.entry &&
          Array.isArray(data.entry)
        ) {
          // Extract messaging data from the entry
          const messaging = data.entry[0]?.messaging?.[0];
          if (!messaging) {
            return;
          }

          // Handle direct message
          if (messaging.message) {
            const messageData = messaging.message;
            const senderId = messaging.sender.id;
            const recipientId = messaging.recipient?.id;
            // Convert timestamp to milliseconds if it's in seconds
            const timestamp =
              messaging.timestamp > 1000000000000
                ? messaging.timestamp
                : messaging.timestamp * 1000;

            // Check if this message is from our user or from the other person in the chat
            // If sender matches the current chat ID, it's from the other person
            // If sender doesn't match the current chat ID, it's from our user
            const isFromOtherUser = senderId === selectedChat?.chatid;
            const isFromOurUser = !isFromOtherUser;

            // Find the conversation from the messages list
            const conversation = isFromOurUser
              ? messages.find((msg: Message) => msg.chatid === recipientId)
              : messages.find((msg: Message) => msg.chatid === senderId);

            // Check if we're in the current chat
            // For messages from our user: check if we're chatting with the recipient
            // For messages from others: check if we're chatting with the sender
            const isInCurrentChat = isFromOurUser
              ? selectedChat?.chatid === recipientId
              : selectedChat?.chatid === senderId;

            // Also check if this message is related to the current chat even if isInCurrentChat is false
            // This is a fallback to ensure messages don't get missed
            const isRelatedToCurrentChat =
              selectedChat &&
              (selectedChat.chatid === senderId ||
                selectedChat.chatid === recipientId);

            // IMPORTANT: Determine if this message belongs to the currently opened/selected chat
            // This prevents unread count increments for messages in the active chat
            const belongsToCurrentChat =
              isInCurrentChat || isRelatedToCurrentChat;

            if (belongsToCurrentChat) {
              // We are in the current chat, add the message to the chat
              setIsReceivingNewMessages(true);

              // Determine if this is our own message by checking if sender matches the chat
              // If sender matches the current chat ID, it's from the other person
              // If sender doesn't match the current chat ID, it's from our user
              const isLikelyOurMessage = senderId !== selectedChat?.chatid;

              // For messages from our user, we might not have the conversation in the messages list yet
              // So we need to get the recipient's name from selectedChat if available
              const senderName = isLikelyOurMessage
                ? selectedSocial.username
                : conversation?.name || selectedChat?.name || "Unknown";

              const senderAvatar = isLikelyOurMessage
                ? selectedSocial.profile_photo || `/default-avatar.png`
                : selectedChat?.avatar ||
                  conversation?.avatar ||
                  `https://ui-avatars.com/api/?name=${senderName}&background=random`;

              const newMessage: ChatMessage = {
                id: messageData.mid,
                // Use username for sender identification to match our ChatSection component
                sender: senderName,
                time: new Date(timestamp).toISOString(),
                timestamp: timestamp,
                content: messageData.text || "",
                avatar: senderAvatar,
                seen: false,
                reactions: { data: [] }, // Initialize empty reactions
                from: {
                  username: senderName,
                  id: isLikelyOurMessage ? selectedSocial.social_id : senderId,
                },
              };

              // Check if this message already exists in the chat
              const messageExists = chatMessages.some(
                (msg: ChatMessage) => msg.id === messageData.mid
              );

              if (!messageExists) {
                // Trigger animation for this hot reload message
                triggerMessageAnimation(newMessage.id);

                setChatMessages((prev) => [...prev, newMessage]);

                // Scroll to bottom for new messages
                setTimeout(scrollToBottom, 100);

                // If the message is not from our user, mark it as seen
                if (
                  !isLikelyOurMessage &&
                  selectedWorkspace &&
                  messageData.mid
                ) {
                  // Send seen_cache request for this message

                  try {
                    // Use setTimeout to avoid blocking the UI
                    setTimeout(async () => {
                      try {
                        await seenCache({
                          workspace_name: selectedWorkspace,
                          cache_type: "newMessage",
                          platform: selectedSocial.platform,
                          object_id: messageData.mid,
                        });
                      } catch (error) {
                        // Error handled silently
                      }
                    }, 0);
                  } catch (error) {
                    // Error handled silently
                  }
                }
              }
            } else {
              // Not in current chat, handle as unread ONLY if it's not from our user
              const messageId = messageData.mid;

              // Check if this message is from our user
              // If sender matches the current chat ID, it's from the other person
              // If sender doesn't match the current chat ID, it's from our user
              const isLikelyOurMessage = senderId !== selectedChat?.chatid;

              // ADDITIONAL SAFETY CHECK: Double-check that this message doesn't belong to current chat
              // This prevents edge cases where a message might slip through and get counted as unread
              const messageRelatedToCurrentChat =
                selectedChat &&
                (selectedChat.chatid === senderId ||
                  selectedChat.chatid === recipientId);

              // Only process as unread if:
              // 1. This is NOT from our user
              // 2. We haven't seen it before
              // 3. It doesn't belong to the currently selected chat (additional safety check)
              if (
                !isLikelyOurMessage &&
                !unreadMessageObjects.includes(messageId) &&
                !messageRelatedToCurrentChat
              ) {
                // STEP 1: Add the message to our unread message objects array
                // This is the SINGLE SOURCE OF TRUTH for unread messages
                setUnreadMessageObjects((prev) => [...prev, messageId]);

                // STEP 2: Update the unread directs array for UI matching and seenCache
                setUnreadDirects((prev) => {
                  const newList = [...prev, messageId];

                  // Also add the sender ID if it's not already in the list
                  if (!prev.includes(senderId)) {
                    newList.push(senderId);
                  }

                  return newList;
                });

                // STEP 3: Update the sender message counts for UI display
                // Only update if not currently processing hot reload to prevent conflicts
                if (!isProcessingHotReloadRef.current) {
                  setSenderMessageCounts((prev) => {
                    const newCounts = { ...prev };
                    newCounts[senderId] = (newCounts[senderId] || 0) + 1;
                    console.log(
                      "🔧 DIRECT WEBSOCKET: Updating sender message counts:",
                      {
                        senderId,
                        previousCount: prev[senderId] || 0,
                        newCount: newCounts[senderId],
                        allCounts: newCounts,
                      }
                    );

                    // Update the ref immediately to prevent conflicts with hot reload
                    previousSenderMessageCountsRef.current = { ...newCounts };

                    return newCounts;
                  });
                }

                // STEP 4: Increment the total unread count
                // Only update if not currently processing hot reload to prevent conflicts
                if (!isProcessingHotReloadRef.current) {
                  setUnreadDirectsCount((prev) => prev + 1);
                }
              }

              // Update the messages list to move this conversation to the top
              // But only mark as unread if it's not from our user
              setMessages((prevMessages) => {
                // For messages from our user, we need to find the conversation by recipient ID
                // For messages from others, we find it by sender ID
                const conversationId = isLikelyOurMessage
                  ? recipientId
                  : senderId;

                // Check if this conversation exists in the list
                const conversationExists = prevMessages.some(
                  (msg) => msg.chatid === conversationId
                );

                if (conversationExists) {
                  // Find the conversation that needs to be updated
                  const updatedConversation = prevMessages.find(
                    (msg) => msg.chatid === conversationId
                  );
                  if (!updatedConversation) return prevMessages;

                  // Get the current unread count from senderMessageCounts for accuracy
                  const currentUnreadCount =
                    !isLikelyOurMessage && !messageRelatedToCurrentChat
                      ? senderMessageCounts[senderId] ||
                        (updatedConversation.unreadCount || 0) + 1
                      : 0;

                  // Create updated conversation object with the new message
                  const updatedMsg = {
                    ...updatedConversation,
                    message:
                      currentUnreadCount > 0
                        ? `${currentUnreadCount} new message${
                            currentUnreadCount > 1 ? "s" : ""
                          }`
                        : messageData.text || "New message", // Show unread count or actual message text
                    originalMessage:
                      updatedConversation.originalMessage ||
                      "Instagram Direct Message",
                    timestamp: new Date(timestamp).toISOString(),
                    // Only mark as unread if it's not from our user AND doesn't belong to current chat
                    hasUnread: currentUnreadCount > 0,
                    unreadCount: currentUnreadCount,
                  };

                  console.log("🔧 DIRECT WEBSOCKET: Updated conversation:", {
                    chatId: conversationId,
                    name: updatedMsg.name,
                    unreadCount: updatedMsg.unreadCount,
                    hasUnread: updatedMsg.hasUnread,
                    message: updatedMsg.message,
                    isFromOurUser: isLikelyOurMessage,
                    belongsToCurrentChat: messageRelatedToCurrentChat,
                  });

                  // ALWAYS move the conversation to the top for hot reload messages
                  // This ensures the most recent activity is always visible first
                  // Remove the conversation from its current position
                  const filteredMessages = prevMessages.filter(
                    (msg) => msg.chatid !== conversationId
                  );

                  // Add the updated conversation to the top of the list
                  return [updatedMsg, ...filteredMessages];
                } else {
                  // Conversation doesn't exist, return unchanged
                  return prevMessages;
                }
              });
            }
          }

          // Handle reaction
          if (messaging.reaction) {
            const reaction = messaging.reaction;
            const messageId = reaction.mid;
            const emoji =
              reaction.emoji ||
              (reaction.reaction === "love" ? "❤" : reaction.reaction);
            const action = reaction.action || "react"; // Default to 'react' if not specified
            const sender = messaging.sender;

            // Extract a unique identifier from the message ID
            let messageIdentifier = "";
            if (messageId && messageId.includes(":")) {
              const parts = messageId.split(":");
              messageIdentifier = parts[parts.length - 1]
                .split("ZAZD")[0]
                .split("ZD")[0];
            } else {
              messageIdentifier = messageId;
            }

            // Update the message with the new reaction
            setChatMessages((prev) =>
              prev.map((msg) => {
                // Check for message match
                const isExactMatch = msg.id === messageId;
                const hasServerMessageId = msg.serverMessageId === messageId;
                const serverIdMatchesMid =
                  msg.serverMessageId &&
                  messageId &&
                  msg.serverMessageId.trim() === messageId.trim();
                const containsIdentifier =
                  messageIdentifier &&
                  (msg.id?.includes(messageIdentifier) ||
                    msg.serverMessageId?.includes(messageIdentifier));

                if (
                  isExactMatch ||
                  hasServerMessageId ||
                  serverIdMatchesMid ||
                  containsIdentifier
                ) {
                  // Apply reaction to message
                  const existingReactions = msg.reactions?.data || [];

                  // If unreacting, remove the reaction
                  if (action === "unreact") {
                    const updatedReactions = [...existingReactions];

                    // Find the reaction that contains this user
                    const userReactionIndex = updatedReactions.findIndex((r) =>
                      r.users.some((u) => u.id === sender.id)
                    );

                    if (userReactionIndex !== -1) {
                      const currentReaction =
                        updatedReactions[userReactionIndex];

                      if (
                        currentReaction &&
                        Array.isArray(currentReaction.users)
                      ) {
                        // Remove the user from the reaction
                        const updatedUsers = currentReaction.users.filter(
                          (u) => u.id !== sender.id
                        );

                        // Update the users array
                        currentReaction.users = updatedUsers;

                        // If no users left, remove the reaction entirely
                        if (updatedUsers.length === 0) {
                          updatedReactions.splice(userReactionIndex, 1);
                        }
                      }
                    }

                    return {
                      ...msg,
                      reactions: {
                        data: updatedReactions,
                      },
                    };
                  }

                  // Check if user already has a reaction
                  const userReactionIndex = existingReactions.findIndex((r) =>
                    r.users.some((u) => u.id === sender.id)
                  );

                  if (userReactionIndex !== -1) {
                    // Update existing reaction
                    const updatedReactions = [...existingReactions];
                    const currentReaction = updatedReactions[userReactionIndex];
                    if (currentReaction) {
                      updatedReactions[userReactionIndex] = {
                        ...currentReaction,
                        emoji: emoji,
                        users: currentReaction.users || [],
                      };
                    }
                    return {
                      ...msg,
                      reactions: {
                        data: updatedReactions,
                      },
                    };
                  } else {
                    // Add new reaction
                    return {
                      ...msg,
                      reactions: {
                        data: [
                          ...existingReactions,
                          {
                            emoji: emoji,
                            users: [
                              {
                                username: sender.username || sender.id,
                                id: sender.id,
                              },
                            ],
                          },
                        ],
                      },
                    };
                  }
                }
                return msg;
              })
            );
          }

          // Handle seen status
          if (messaging.read) {
            // Pass the entire original data object to handleSeenStatus
            // This ensures we're working with the exact format received from the WebSocket
            handleSeenStatus(data);
          }
        }
      } catch (error) {
        // Error handled silently
      }
    };

    socket.addEventListener("message", handleWebSocketMessage);
    return () => socket.removeEventListener("message", handleWebSocketMessage);
  }, [
    socket,
    selectedChat,
    selectedSocial,
    messages,
    chatMessages,
    scrollToBottom,
    setUnreadDirects,
    setUnreadDirectsCount,
    setMessages,
    setChatMessages,
    handleSeenStatus,
    selectedWorkspace,
    seenCache,
    setSenderMessageCounts,
    unreadMessageObjects,
    setUnreadMessageObjects,
  ]);

  // Add a function to send reactions
  const handleReaction = useCallback(
    (messageId: string, emoji: string, unreact: boolean = false) => {
      if (!selectedChat || !messageId) return;

      // Check if the message already has a reaction from this user
      const message = chatMessages.find(
        (msg) => msg.id === messageId || msg.serverMessageId === messageId
      );

      if (!message) {
        return;
      }

      // Determine the correct message ID to use for the API call
      // Priority: serverMessageId (real server ID) > id (local ID)
      const apiMessageId = message.serverMessageId || message.id;

      // Validate that we have a proper server message ID for API calls
      if (!message.serverMessageId && message.id.startsWith("local-")) {
        showErrorToast(
          "Cannot react to message: Message not yet synchronized",
          "inbox-toast"
        );
        return;
      }

      // Send reaction or unreaction using the server message ID
      sendReact({
        workspace_name: selectedWorkspace,
        message_id: apiMessageId, // Use server message ID for API call
        reaction: "love", // API only supports "love" reaction type
        target_id: selectedChat.chatid!,
        social_id: selectedSocial.social_id,
        unreact: unreact, // Add unreact parameter
      })
        .then((response: any) => {
          if (response.success) {
            // Update the message with the reaction or remove it
            setChatMessages((prev) => {
              return prev.map((msg) => {
                // Check if this is the message we're reacting to
                // We need to match against both the original messageId passed to the function
                // and the apiMessageId that was actually sent to the server
                const isTargetMessage =
                  msg.id === messageId ||
                  msg.serverMessageId === messageId ||
                  msg.id === apiMessageId ||
                  msg.serverMessageId === apiMessageId;

                if (isTargetMessage) {
                  // Get existing reactions
                  const existingReactions = msg.reactions?.data || [];

                  // Check if the user already has a reaction
                  const userReactionIndex = existingReactions.findIndex((r) =>
                    r.users.some((u) => u.id === selectedSocial.social_id)
                  );

                  // If unreacting, remove the reaction
                  if (unreact) {
                    // If user has a reaction, remove it
                    if (userReactionIndex !== -1) {
                      const updatedReactions = [...existingReactions];
                      const currentReaction =
                        updatedReactions[userReactionIndex];

                      if (currentReaction && currentReaction.users) {
                        // Remove the user from the reaction users
                        const updatedUsers = currentReaction.users.filter(
                          (u) => u.id !== selectedSocial.social_id
                        );

                        // Update the users array
                        currentReaction.users = updatedUsers;

                        // If no users left for this reaction, remove the reaction
                        if (updatedUsers.length === 0) {
                          updatedReactions.splice(userReactionIndex, 1);
                        }
                      }

                      return {
                        ...msg,
                        reactions: {
                          data: updatedReactions,
                        },
                      };
                    }
                  } else {
                    // Adding a reaction
                    if (userReactionIndex !== -1) {
                      // User already has a reaction, update it
                      const updatedReactions = [...existingReactions];
                      const currentReaction =
                        updatedReactions[userReactionIndex];

                      if (currentReaction) {
                        // Update the emoji while preserving the users array
                        currentReaction.emoji = emoji;
                      }
                      return {
                        ...msg,
                        reactions: {
                          data: updatedReactions,
                        },
                      };
                    } else {
                      // Add new reaction
                      return {
                        ...msg,
                        reactions: {
                          data: [
                            ...existingReactions,
                            {
                              emoji: emoji,
                              users: [
                                {
                                  id: selectedSocial.social_id,
                                  username: selectedSocial.username,
                                },
                              ],
                            },
                          ],
                        },
                      };
                    }
                  }
                }
                return msg;
              });
            });
          } else {
            showErrorToast("Failed to send reaction", "inbox-toast");
          }
        })
        .catch(() => {
          showErrorToast("Error sending reaction", "inbox-toast");
        });
    },
    [
      selectedChat,
      selectedWorkspace,
      selectedSocial,
      sendReact,
      chatMessages,
      logger,
      showErrorToast,
    ]
  );

  // Effect to handle mobile view
  useEffect(() => {
    const handleResize = () => {
      const mobileWidth = window.innerWidth < 768;
      setIsMobile(mobileWidth);

      // If switching to mobile view and a chat or comment is selected, we need to ensure proper layout
      if (mobileWidth && (selectedChat || selectedComment)) {
        // Ensure the chat/comment view is visible on mobile when selected
        document.body.style.overflow = "hidden";
      } else {
        document.body.style.overflow = "";
      }
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => {
      window.removeEventListener("resize", handleResize);
      document.body.style.overflow = "";
    };
  }, [selectedChat, selectedComment]);

  // Effect to handle chat container scroll events
  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;

      // Check if scrolled to bottom (with a small threshold)
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50;
      setIsScrolledToBottom(isAtBottom);

      // Show/hide scroll to bottom button
      setShowScrollToBottom(!isAtBottom && scrollHeight > clientHeight + 100);

      // Check if scrolled to top for loading older messages
      if (scrollTop < 50 && nextPageToken && !isLoadingMoreMessages) {
        // Load older messages
        setIsLoadingMoreMessages(true);

        // Implementation for loading older messages would go here
        // This is a placeholder for now
        setTimeout(() => {
          setIsLoadingMoreMessages(false);
        }, 1000);
      }
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [chatContainerRef, nextPageToken, isLoadingMoreMessages]);

  // Function to mark messages as seen
  const markMessagesAsSeen = useCallback(
    async (msg: Message) => {
      if (!msg || !msg.chatid || !msg.hasUnread || !selectedWorkspace) return;

      try {
        // Get the unread messages for this chat
        const chatId = msg.chatid;

        // We need to get the actual object_ids from the cache, not just the sender IDs
        // The unreadDirects array should contain the full object_ids from the cache
        // Filter to find the object_ids that are related to this chat
        const unreadMessagesForChat = unreadDirects.filter((id) => {
          // Check if this is a full object_id (which would be a long string)
          // or if it's a sender ID that matches our chat ID
          const isFullObjectId = id.length > 30; // Assuming object_ids are long strings
          const isSenderIdMatch = id === chatId || id.includes(chatId);

          // We want to keep full object_ids that are related to this chat
          // or sender IDs that match this chat
          return isFullObjectId || isSenderIdMatch;
        });

        // Count how many messages we're marking as seen
        // Only count message IDs (which are long), not sender IDs
        const messageCount = unreadMessagesForChat.filter(
          (id) => id.length > 30
        ).length;

        // Update the UI state to reflect that messages are being marked as seen
        if (messageCount > 0) {
          // STEP 1: Find all message object IDs related to this chat
          const messageObjectsToRemove = unreadMessageObjects.filter((id) =>
            // Check if the message ID contains this chat ID
            id.includes(chatId)
          );

          // STEP 2: Remove these message objects from our tracking array
          setUnreadMessageObjects((prev) =>
            prev.filter((id) => !messageObjectsToRemove.includes(id))
          );

          // STEP 3: Remove these messages from the unread directs array
          setUnreadDirects((prev) =>
            prev.filter((id) => !(id === chatId || id.includes(chatId)))
          );

          // STEP 4: Update the sender message counts
          setSenderMessageCounts((prev) => {
            const newCounts = { ...prev };

            // Clear the count for this chat
            if (chatId in newCounts) {
              newCounts[chatId] = 0;
            }

            return newCounts;
          });

          // We only need to decrement the unread count if this wasn't triggered by openChat
          // If it was triggered by openChat, we've already decremented the count
          // We can check if the sender message count for this chat is already 0
          if (senderMessageCounts[chatId] !== 0) {
            // STEP 5: Decrement the total unread count
            setUnreadDirectsCount((prev) => Math.max(0, prev - messageCount));
          }

          // STEP 6: Update the message in the messages list
          setMessages((prev) =>
            prev.map((m) => {
              if (m.chatid === chatId) {
                return {
                  ...m,
                  hasUnread: false,
                  unreadCount: 0,
                  message: m.originalMessage || "Instagram Direct Message",
                };
              }
              return m;
            })
          );
        }

        // Mark each unread message as seen on the server
        // We only want to send seenCache for the full object_ids, not the sender IDs
        for (const messageId of unreadMessagesForChat) {
          // Only process if this looks like a full object_id (long string)
          if (messageId.length > 30) {
            try {
              await seenCache({
                workspace_name: selectedWorkspace,
                cache_type: "newMessage",
                platform: selectedSocial.platform,
                object_id: messageId,
              });
            } catch (error) {
              // Error handled silently
            }
          }
        }
      } catch (error) {
        // Error handled silently
      }
    },
    [
      selectedWorkspace,
      unreadDirects,
      unreadMessageObjects,
      seenCache,
      selectedSocial.platform,
      setUnreadDirects,
      setUnreadDirectsCount,
      setMessages,
      setSenderMessageCounts,
      setUnreadMessageObjects,
    ]
  );

  // Open chat function
  const openChat = useCallback(
    (msg: Message) => {
      if (!msg || !msg.chatid) return;

      // Cancel any pending message operations when switching chats
      setIsSendingMessage(false);

      // Clear any replying state
      setReplyingToMessage(null);

      // Set loading state
      setIsTransitioningChat(true);
      setChatMessages([]);

      // Set selected chat
      setSelectedChat(msg);

      // If the chat has unread messages, immediately update the UI and mark them as seen
      if (msg.hasUnread && msg.chatid) {
        // Get the unread count for this chat
        const chatUnreadCount = msg.unreadCount || 0;

        if (chatUnreadCount > 0) {
          // STEP 1: Immediately decrement the total unread count
          setUnreadDirectsCount((prev) => Math.max(0, prev - chatUnreadCount));

          // STEP 2: Update the sender message counts to show this chat as read
          setSenderMessageCounts((prev) => {
            const newCounts = { ...prev };

            // Clear the count for this chat
            if (msg.chatid && msg.chatid in newCounts) {
              newCounts[msg.chatid] = 0;
            }

            return newCounts;
          });

          // STEP 3: Update the message in the messages list
          setMessages((prev) =>
            prev.map((m) => {
              if (m.chatid === msg.chatid) {
                return {
                  ...m,
                  hasUnread: false,
                  unreadCount: 0,
                  message: m.originalMessage || "Instagram Direct Message",
                };
              }
              return m;
            })
          );
        }

        // STEP 4: Mark messages as seen on the server (this will handle removing from unreadMessageObjects)
        markMessagesAsSeen(msg);
      }

      // Fetch chat messages
      if (msg.chatid) {
        setIsLoadingChatMessages(true);
        getDirectMessages({
          workspace_name: selectedWorkspace,
          target_id: msg.chatid,
          conversation_id: msg.conversationId || msg.id, // Add conversation_id parameter
          platform: selectedSocial.platform,
          social_id: selectedSocial.social_id,
        })
          .then((response: any) => {
            if (response.success && response.messages?.data) {
              // Format messages
              const formattedMessages = response.messages.data.map(
                (message: any) => {
                  // Determine if this message is from the current user
                  // Messages from our user are those where the sender is NOT the current chat partner
                  const isFromCurrentUser =
                    message.from?.id !== selectedChat?.chatid;

                  // Get profile picture from the participants data
                  let profilePicture = isFromCurrentUser
                    ? selectedSocial.profile_photo
                    : null;

                  // If not our user, try to find the profile picture in the participants
                  if (!isFromCurrentUser && response.participants?.data) {
                    const participant = response.participants.data.find(
                      (p: any) => p.id === message.from?.id
                    );

                    // Handle both Instagram and Facebook profile picture formats
                    if (participant?.picture?.data?.url) {
                      // Facebook format: participant.picture.data.url
                      profilePicture = participant.picture.data.url;
                    } else if (participant?.profile_picture) {
                      // Instagram format: participant.profile_picture
                      profilePicture = participant.profile_picture;
                    }
                  }

                  // If still no profile picture for other user, try to get it from the selected chat
                  if (!isFromCurrentUser && !profilePicture && selectedChat) {
                    profilePicture = selectedChat.avatar;
                  }

                  // Calculate final avatar
                  const finalAvatar =
                    profilePicture ||
                    (isFromCurrentUser
                      ? `https://ui-avatars.com/api/?name=${selectedSocial.username}&background=random`
                      : selectedChat?.avatar ||
                        msg.avatar ||
                        `https://ui-avatars.com/api/?name=${
                          selectedChat?.name || msg.name
                        }&background=random`);

                  return {
                    id: message.id,
                    serverMessageId: message.id, // Store the server message ID for reaction handling
                    sender: isFromCurrentUser
                      ? selectedSocial.username
                      : message.from?.name || msg.name,
                    content: message.message,
                    time: message.created_time || message.timestamp,
                    timestamp: new Date(
                      message.created_time || message.timestamp
                    ).getTime(),
                    avatar: finalAvatar,
                    from: {
                      id:
                        message.from?.id ||
                        (isFromCurrentUser ? selectedSocial.social_id : ""),
                      username:
                        message.from?.name ||
                        (isFromCurrentUser
                          ? selectedSocial.username
                          : msg.name),
                    },
                    seen: message.seen,
                    reactions: message.reactions || { data: [] },
                  } as ChatMessage;
                }
              );

              // Set chat messages
              setChatMessages(formattedMessages);

              // Set next/prev page tokens
              if (response.messages.paging) {
                setNextPageToken(response.messages.paging.next || null);
                setPrevPageToken(response.messages.paging.previous || null);
              } else {
                setNextPageToken(null);
                setPrevPageToken(null);
              }
            } else {
              setChatMessages([]);
              setNextPageToken(null);
              setPrevPageToken(null);
            }
          })
          .catch(() => {
            setChatMessages([]);
          })
          .finally(() => {
            setIsLoadingChatMessages(false);
            setIsTransitioningChat(false);

            // Scroll to bottom after a short delay to ensure messages are rendered
            setTimeout(scrollToBottom, 100);
          });
      }
    },
    [
      selectedWorkspace,
      selectedSocial,
      getDirectMessages,
      scrollToBottom,
      markMessagesAsSeen,
      setUnreadDirectsCount,
      setSenderMessageCounts,
      setMessages,
    ]
  );

  return (
    <InboxLayout isMobile={isMobile}>
      {/* Mobile and Desktop shared content */}
      <div
        className={`${
          isMobile && selectedChat && activeTab === "direct"
            ? "hidden"
            : isMobile && selectedComment && activeTab === "comments"
            ? "hidden"
            : isMobile
            ? "w-full h-full"
            : "w-1/3 border-r"
        } ${isMobile ? "p-2" : "p-4"} bg-gray-100/80 ${
          isMobile ? "rounded-none" : "rounded-xl"
        } overflow-y-auto flex flex-col`}
      >
        <h1
          className={`text-2xl p-2 pt-4 font-bold ${
            isMobile ? "mb-3 " : "mb-4 "
          }`}
        >
          Inbox
        </h1>
        <InboxTabs
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          unreadDirectsCount={unreadDirectsCount}
          unreadCommentsCount={unreadCommentsCount}
          messages={messages}
          comments={comments}
          setMessages={setMessages}
          setComments={setComments}
          unreadDirects={unreadDirects}
          unreadComments={unreadComments}
        />
        <div className="flex justify-between p-1 items-center mb-4">
          <div className="flex items-center gap-3">
            <h2 className="text-lg font-semibold">
              {activeTab === "direct" ? "Messages" : "Comments"}
            </h2>
            {activeFilter === "unread" && (
              <motion.div
                className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                Unread only
              </motion.div>
            )}
            {activeFilter === "most liked" && activeTab === "comments" && (
              <motion.div
                className="flex items-center gap-1 px-2 py-1 bg-pink-100 text-pink-700 text-xs rounded-full"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <svg
                  className="w-3 h-3 text-pink-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                    clipRule="evenodd"
                  />
                </svg>
                Most liked
              </motion.div>
            )}
          </div>
          <FilterButton
            activeTab={activeTab}
            activeFilter={activeFilter}
            setActiveFilter={setActiveFilter}
            showFilterDropdown={showFilterDropdown}
            setShowFilterDropdown={setShowFilterDropdown}
          />
        </div>
        <div className="space-y-4">
          {activeTab === "direct" && (
            <>
              {getFilteredMessages(messages).length === 0 &&
              activeFilter === "unread" &&
              !isLoadingMessages ? (
                <motion.div
                  className="flex flex-col items-center justify-center py-12 text-gray-500"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <svg
                      className="w-8 h-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
                      />
                    </svg>
                  </div>
                  <p className="text-sm font-medium mb-1">No unread messages</p>
                  <p className="text-xs text-gray-400">All caught up! 🎉</p>
                </motion.div>
              ) : isLoadingMessages || isHotReloadProcessing ? (
                <div className="space-y-4">
                  {[...Array(10)].map((_, idx) => (
                    <div
                      key={`dm-skel-${idx}`}
                      className="flex items-center gap-4 py-4 px-3 bg-white rounded-lg shadow-sm"
                    >
                      <Skeleton className="w-12 h-12" variant="circular" animation="wave" />
                      <div className="flex-1 space-y-3">
                        <Skeleton className="h-4 w-1/2" animation="wave" />
                        <Skeleton className="h-4 w-3/4" animation="wave" />
                        <Skeleton className="h-4 w-2/5" animation="wave" />
                      </div>
                      <Skeleton className="h-4 w-14" animation="wave" />
                    </div>
                  ))}
                </div>
              ) : (
                <DirectMessagesList
                  key={`direct-${listKey}-${activeFilter}`}
                  messages={getFilteredMessages(messages)}
                  selectedChat={selectedChat}
                  isLoadingMessages={isLoadingMessages}
                  unreadDirects={unreadDirects}
                  unreadDirectsCount={unreadDirectsCount}
                  setUnreadDirects={setUnreadDirects}
                  setUnreadDirectsCount={setUnreadDirectsCount}
                  setMessages={setMessages}
                  openChat={openChat}
                  isLoadingMoreDirects={isLoadingMoreDirects}
                  isNavigatingBack={isNavigatingBack}
                  isMobile={isMobile}
                />
              )}
            </>
          )}
          {activeTab === "comments" && (
            <>
              {getFilteredComments(comments).length === 0 &&
              activeFilter === "unread" &&
              !isLoadingCommentsList ? (
                <motion.div
                  className="flex flex-col items-center justify-center py-12 text-gray-500"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <svg
                      className="w-8 h-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.013 8.013 0 01-2.906-.533l-3.431 1.715a.5.5 0 01-.694-.456L6 18.5c-2.69-1.789-4-4.533-4-7.5 0-4.418 3.582-8 8-8s8 3.582 8 8z"
                      />
                    </svg>
                  </div>
                  <p className="text-sm font-medium mb-1">No unread comments</p>
                  <p className="text-xs text-gray-400">All caught up! 🎉</p>
                </motion.div>
              ) : (
                <CommentsList
                  key={`comments-${listKey}-${activeFilter}`}
                  comments={getFilteredComments(comments)}
                  selectedComment={selectedComment}
                  isLoadingCommentsList={isLoadingCommentsList}
                  unreadComments={unreadComments}
                  setSelectedComment={setSelectedComment}
                  setUnreadComments={setUnreadComments}
                  setUnreadCommentsCount={setUnreadCommentsCount}
                  setComments={setComments}
                  isLoadingMorePosts={isLoadingMorePosts}
                  fetchPostComments={fetchPostComments}
                  isViewingPostRef={isViewingPostRef}
                  selectedWorkspace={selectedWorkspace}
                  selectedSocial={selectedSocial}
                  seenCache={seenCache}
                  commentToPostMapping={commentToPostMapping}
                  isNavigatingBack={isNavigatingBack}
                  isMobile={isMobile}
                  recentlyMovedPosts={recentlyMovedPosts}
                />
              )}
            </>
          )}
        </div>
      </div>

      {/* Chat/Comment section - full width on mobile with proper height */}
      {(!isMobile ||
        (isMobile && selectedChat && activeTab === "direct") ||
        (isMobile && selectedComment && activeTab === "comments")) && (
        <div
          className={`${
            isMobile
              ? "fixed inset-0 top-[8vh] w-full flex flex-col bg-white z-30"
              : "w-2/3 h-full flex flex-col bg-gray-100/80 rounded-xl"
          } overflow-hidden`}
          style={
            isMobile
              ? {
                  height: "calc(100vh - 8vh)",
                  maxHeight: "calc(100vh - 8vh)",
                  display: "flex",
                  flexDirection: "column",
                }
              : {}
          }
        >
          {activeTab === "direct" && (
            <>
              {selectedChat ? (
                <>
                  <ChatHeader
                    selectedChat={selectedChat}
                    closeChat={() => {
                      // Cancel any pending message operations when closing chat
                      setIsSendingMessage(false);

                      // Clear any replying state
                      setReplyingToMessage(null);

                      // Set navigation state for mobile animations
                      if (isMobile) {
                        setIsNavigatingBack(true);
                        setListKey((prev) => prev + 1); // Force remount
                      }
                      setSelectedChat(null);

                      // Reset navigation state after component remounts
                      if (isMobile) {
                        setTimeout(() => setIsNavigatingBack(false), 50);
                      }
                    }}
                  />
                  <ChatSection
                    selectedChat={selectedChat}
                    selectedSocial={selectedSocial}
                    chatMessages={chatMessages}
                    isLoadingChatMessages={isLoadingChatMessages}
                    isTransitioningChat={isTransitioningChat}
                    isLoadingMoreMessages={isLoadingMoreMessages}
                    showScrollToBottom={showScrollToBottom}
                    scrollToBottom={scrollToBottom}
                    setReplyingToMessage={setReplyingToMessage}
                    handleReaction={handleReaction}
                    handleSeenStatus={handleSeenStatus}
                    chatContainerRef={chatContainerRef}
                    messagesEndRef={messagesEndRef}
                    topLoaderRef={topLoaderRef}
                    bottomLoaderRef={bottomLoaderRef}
                    animatingMessageIds={animatingMessageIds}
                  />
                  {/* Render ChatInput inside flex container on desktop, outside on mobile */}
                  {!isMobile && (
                    <ChatInput
                      onSendMessage={handleSend}
                      isSendingMessage={isSendingMessage}
                      showFAQModal={showFAQModal}
                      setShowFAQModal={setShowFAQModal}
                      replyingToMessage={replyingToMessage}
                      setReplyingToMessage={setReplyingToMessage}
                      autoAnswerEnabled={autoAnswerEnabled}
                      openAutoAnswerModal={handleOpenAutoAnswerModal}
                      toggleAutoAnswerFeature={toggleAutoAnswerFeature}
                      faqsRef={faqsRef}
                      handleEditFAQ={handleEditFAQ}
                      handleDeleteSaveReply={handleDeleteSaveReply}
                      setShowManageFAQModal={setShowManageFAQModal}
                      selectedWorkspace={selectedWorkspace}
                    />
                  )}
                </>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  Select a chat to start messaging
                </div>
              )}
            </>
          )}
          {activeTab === "comments" && (
            <>
              {selectedComment ? (
                <CommentDetails
                  selectedComment={selectedComment}
                  postCommentsRef={postCommentsRef}
                  isLoadingPostComments={isLoadingPostComments}
                  isLoadingMorePostComments={isLoadingMorePostComments}
                  showReplyInput={showReplyInput}
                  setShowReplyInput={setShowReplyInput}
                  replyingTo={replyingTo}
                  setReplyingTo={setReplyingTo}
                  replyText={replyText}
                  setReplyText={setReplyText}
                  handleReplySubmit={handleReplySubmit}
                  isReplying={isReplying}
                  handleCommentSubmit={handleCommentSubmit}
                  socket={socket}
                  selectedWorkspace={selectedWorkspace}
                  closeComment={() => {
                    // Set navigation state for mobile animations
                    if (isMobile) {
                      setIsNavigatingBack(true);
                      setListKey((prev) => prev + 1); // Force remount
                    }
                    setSelectedComment(null);
                    // Reset viewing post flag when closing comment
                    isViewingPostRef.current = false;
                    // Reset navigation state after component remounts
                    if (isMobile) {
                      setTimeout(() => setIsNavigatingBack(false), 50);
                    }
                  }}
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  Select a post to view comments
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* Mobile ChatInput - Fixed at bottom when chat is selected */}
      {isMobile && selectedChat && activeTab === "direct" && (
        <ChatInput
          onSendMessage={handleSend}
          isSendingMessage={isSendingMessage}
          showFAQModal={showFAQModal}
          setShowFAQModal={setShowFAQModal}
          replyingToMessage={replyingToMessage}
          setReplyingToMessage={setReplyingToMessage}
          autoAnswerEnabled={autoAnswerEnabled}
          openAutoAnswerModal={handleOpenAutoAnswerModal}
          toggleAutoAnswerFeature={toggleAutoAnswerFeature}
          faqsRef={faqsRef}
          handleEditFAQ={handleEditFAQ}
          handleDeleteSaveReply={handleDeleteSaveReply}
          setShowManageFAQModal={setShowManageFAQModal}
          selectedWorkspace={selectedWorkspace}
        />
      )}

      {/* Modals */}

      {showManageFAQModal && (
        <ManageFAQModal
          setShowManageFAQModal={setShowManageFAQModal}
          createFAQTitleRef={createFAQTitleRef}
          createFAQMessageRef={createFAQMessageRef}
          selectedWorkspace={selectedWorkspace}
          createSavedReply={createSavedReply}
        />
      )}

      {showEditModal && (
        <EditFAQModal
          editingFAQRef={editingFAQRef}
          setShowEditModal={setShowEditModal}
          selectedWorkspace={selectedWorkspace}
          editTitleSavedReply={editTitleSavedReply}
          editTextSavedReply={editTextSavedReply}
        />
      )}

      {showAutoAnswerModal && (
        <AutoAnswerModal
          isOpen={showAutoAnswerModal}
          onClose={() => setShowAutoAnswerModal(false)}
          isLoadingAutoAnswer={isLoadingAutoAnswer}
          workHours={workHours}
          setWorkHours={setWorkHours}
          autoAnswerEnabled={autoAnswerEnabled}
          toggleAutoAnswerFeature={toggleAutoAnswerFeature}
          appliedChannels={appliedChannels}
          setAppliedChannels={setAppliedChannels}
          selectedWorkspaceDetails={selectedWorkspaceDetails}
          autoAnswerChanges={autoAnswerChanges}
          editAutoAnswer={editAutoAnswer}
          selectedWorkspace={selectedWorkspace}
        />
      )}
    </InboxLayout>
  );
};

export default Inbox;

