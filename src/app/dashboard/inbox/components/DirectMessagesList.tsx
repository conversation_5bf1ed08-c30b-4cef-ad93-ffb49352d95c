"use client";

import React, { useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { Message } from "../types";
import InstagramIcon from "../../../../../public/icons/insta.svg";

interface DirectMessagesListProps {
  messages: Message[];
  selectedChat: Message | null;
  isLoadingMessages: boolean;
  unreadDirects: string[];
  unreadDirectsCount: number;
  setUnreadDirects: React.Dispatch<React.SetStateAction<string[]>>;
  setUnreadDirectsCount: React.Dispatch<React.SetStateAction<number>>;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  openChat: (msg: Message) => void;
  isLoadingMoreDirects: boolean;
  isNavigatingBack?: boolean;
  isMobile?: boolean;
}

// Animation variants - simplified with only scale and opacity
// Different variants for mobile navigation back
const getContainerVariants = (isNavigatingBack: boolean, isMobile: boolean) => {
  // Disable all animations when navigating back on mobile
  if (isNavigatingBack && isMobile) {
    return {
      hidden: { opacity: 1 },
      visible: { opacity: 1 },
    };
  }

  return {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.05,
      },
    },
  };
};

const getItemVariants = (isNavigatingBack: boolean, isMobile: boolean) => {
  // Disable all animations when navigating back on mobile
  if (isNavigatingBack && isMobile) {
    return {
      hidden: { opacity: 1, scale: 1 },
      visible: { opacity: 1, scale: 1 },
      exit: { opacity: 1, scale: 1 },
    };
  }

  return {
    hidden: {
      opacity: 0,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      transition: {
        duration: 0.2,
        ease: "easeInOut",
      },
    },
  };
};

const getSkeletonVariants = (isNavigatingBack: boolean, isMobile: boolean) => {
  // Disable all animations when navigating back on mobile
  if (isNavigatingBack && isMobile) {
    return {
      hidden: { opacity: 1, scale: 1 },
      visible: (i: number) => ({ opacity: 1, scale: 1 }),
    };
  }

  return {
    hidden: { opacity: 0, scale: 0.95 },
    visible: (i: number) => ({
      opacity: 1,
      scale: 1,
      transition: {
        delay: i * 0.05,
        duration: 0.3,
        ease: "easeOut",
      },
    }),
  };
};

const DirectMessagesList: React.FC<DirectMessagesListProps> = ({
  messages,
  selectedChat,
  isLoadingMessages,
  unreadDirects,
  unreadDirectsCount,
  setUnreadDirects,
  setUnreadDirectsCount,
  setMessages,
  openChat,
  isLoadingMoreDirects,
  isNavigatingBack = false,
  isMobile = false,
}) => {
  const directsBottomLoaderRef = useRef<HTMLDivElement>(null);

  // Avatar fallback state
  const [failedAvatars, setFailedAvatars] = React.useState<Set<string>>(
    new Set()
  );

  // Helper function to get avatar URL with fallback logic
  const getAvatarUrl = (
    originalUrl: string | undefined,
    fallbackName: string
  ): string => {
    if (
      !originalUrl ||
      originalUrl === "/default-avatar.png" ||
      failedAvatars.has(originalUrl)
    ) {
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(
        fallbackName
      )}&background=random&color=fff`;
    }

    if (originalUrl.startsWith("http") || originalUrl.startsWith("https")) {
      return originalUrl;
    }

    return `https://ui-avatars.com/api/?name=${encodeURIComponent(
      fallbackName
    )}&background=random&color=fff`;
  };

  // Helper function to handle avatar load errors
  const handleAvatarError = (originalUrl: string | undefined) => {
    if (originalUrl) {
      setFailedAvatars((prev) => new Set([...prev, originalUrl]));
    }
  };

  // Get dynamic variants based on navigation state
  const containerVariants = getContainerVariants(isNavigatingBack, isMobile);
  const itemVariants = getItemVariants(isNavigatingBack, isMobile);
  const skeletonVariants = getSkeletonVariants(isNavigatingBack, isMobile);

  if (isLoadingMessages && messages.length === 0) {
    return (
      <motion.div
        className="space-y-4"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {[...Array(3)].map((_, index) => (
          <motion.div
            key={index}
            custom={index}
            variants={skeletonVariants}
            className="flex items-center p-3 bg-gray-50 rounded-lg"
          >
            <motion.div
              className="w-12 h-12 bg-gray-200 rounded-full mr-3"
              animate={{
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <div className="flex-1">
              <motion.div
                className="h-4 w-24 bg-gray-200 rounded mb-2"
                animate={{
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.2,
                }}
              />
              <motion.div
                className="h-3 w-32 bg-gray-200 rounded"
                animate={{
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.4,
                }}
              />
            </div>
          </motion.div>
        ))}
      </motion.div>
    );
  }

  if (messages.length === 0) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center h-64 text-gray-500"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.5 }}
          transition={{ delay: 0.1, duration: 0.2 }}
        >
          <Image
            src="/icons/empty-inbox.svg"
            alt="Empty inbox"
            width={64}
            height={64}
            className="mb-4 opacity-50"
            onError={(e) => {
              // Fallback if image doesn't exist
              (e.target as HTMLImageElement).style.display = "none";
            }}
          />
        </motion.div>
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.2 }}
        >
          No messages found
        </motion.p>
        <motion.p
          className="text-sm mt-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.2 }}
        >
          Messages from Instagram will appear here
        </motion.p>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      layout
      transition={{
        layout: {
          duration: 0.4,
          ease: "easeInOut",
        },
      }}
    >
      <AnimatePresence mode="popLayout">
        {messages.map((msg) => {
          // Check for unread status in multiple ways
          // 1. Check if the message ID is in the unreadDirects array
          // 2. Check if the conversation ID is in the unreadDirects array
          // 3. Check if the message has the hasUnread flag set directly
          // 4. Check if there's a prefixed version of the ID in unreadDirects
          // 5. Check if the chatid (sender ID) is in the unreadDirects array
          const messageId = msg.id;
          const conversationId = msg.conversationId || msg.chatid;
          const chatId = msg.chatid;
          const prefixedId = conversationId
            ? `${conversationId}:${messageId}`
            : null;

          const isUnread =
            unreadDirects.includes(messageId) ||
            (conversationId && unreadDirects.includes(conversationId)) ||
            (chatId && unreadDirects.includes(chatId)) ||
            msg.hasUnread === true ||
            (prefixedId && unreadDirects.includes(prefixedId));

          // Function to mark message as read in the UI
          // Note: This is only used for local UI updates
          // The actual server-side marking as seen happens in the openChat function
          const markAsRead = () => {
            if (!isUnread) return;

            // We don't need to update the unreadDirects array or count here
            // as that will be handled by the markMessagesAsSeen function
            // when the chat is opened

            // Just update the message in the UI to show it's being read
            setMessages((prev) =>
              prev.map((m) => {
                if (m.id === messageId) {
                  return {
                    ...m,
                    hasUnread: false,
                    message: m.originalMessage || "Instagram Direct Message",
                    unreadCount: 0,
                  };
                }
                return m;
              })
            );
          };

          return (
            <motion.div
              key={messageId}
              {...(!(isNavigatingBack && isMobile) && {
                layoutId: messageId,
                layout: true
              })}
              variants={itemVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              transition={{
                layout: {
                  duration: 0.4,
                  ease: "easeInOut",
                },
              }}
              whileHover={{
                scale: 1.005,
                transition: { duration: 0.2, ease: "easeOut" },
              }}
              whileTap={{ scale: 0.995 }}
              className={`flex items-center cursor-pointer rounded-lg p-3 mb-2 ${
                selectedChat?.id === messageId
                  ? "bg-white border-l-4 border-blue-700 shadow-lg"
                  : "bg-transparent hover:bg-white hover:shadow-md"
              }`}
              onClick={() => {
                // Mark as read in the UI first
                markAsRead();

                // Then open the chat (which will also mark messages as seen on the server)
                openChat(msg);
              }}
            >
              <div className="relative">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Image
                    src={getAvatarUrl(msg.avatar, msg.name || "User")}
                    alt={msg.name}
                    width={48}
                    height={48}
                    className="rounded-full mr-3 object-cover w-12 h-12"
                    onError={() => {
                      handleAvatarError(msg.avatar);
                    }}
                  />
                </motion.div>
                <motion.div
                  className="absolute bottom-0 right-3"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.2 }}
                >
                  <Image
                    src={InstagramIcon}
                    alt="Instagram"
                    width={16}
                    height={16}
                    className="w-4 h-4 "
                    onError={(e) => {
                      // Hide if icon fails to load
                      (e.target as HTMLImageElement).style.display = "none";
                    }}
                  />
                </motion.div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <motion.h3
                    className={`font-semibold ${
                      isUnread ? "text-blue-600 font-bold" : "text-gray-900"
                    } truncate`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.1, duration: 0.2 }}
                  >
                    {msg.name}
                  </motion.h3>
                </div>
                <div className="flex justify-between items-center">
                  <motion.p
                    className={`text-sm ${
                      isUnread ? "text-blue-600 font-bold" : "text-gray-600"
                    } truncate max-w-[80%]`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.15, duration: 0.2 }}
                  >
                    {msg.message}
                  </motion.p>
                  <AnimatePresence>
                    {isUnread && (
                      <motion.div
                        className="w-3 h-3 bg-blue-500 rounded-full shrink-0"
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: 1,
                        }}
                        exit={{ scale: 0, opacity: 0 }}
                        transition={{
                          scale: {
                            duration: 0.6,
                            repeat: Infinity,
                            repeatDelay: 2,
                          },
                          opacity: { duration: 0.2 },
                        }}
                      />
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>

      {/* Bottom loader for infinite scroll */}
      <motion.div
        ref={directsBottomLoaderRef}
        className="h-10 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <AnimatePresence>
          {isLoadingMoreDirects && (
            <motion.div
              className="rounded-full h-5 w-5 border-b-2 border-gray-900"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{
                opacity: 1,
                scale: 1,
                rotate: 360,
              }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{
                opacity: { duration: 0.2 },
                scale: { duration: 0.2 },
                rotate: {
                  duration: 1,
                  repeat: Infinity,
                  ease: "linear",
                },
              }}
            />
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
};

export default DirectMessagesList;
