"use client";

import React, { useEffect } from "react";
import { showSuccessToast, showErrorToast } from "~/components/toasts";
import { AutoAnswer, DayType, WorkHoursType } from "../types";
import { TimePicker } from "antd";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";

interface AutoAnswerModalProps {
  isOpen: boolean;
  onClose: () => void;
  isLoadingAutoAnswer: boolean;
  workHours: WorkHoursType;
  setWorkHours: React.Dispatch<React.SetStateAction<WorkHoursType>>;
  autoAnswerEnabled: boolean;
  toggleAutoAnswerFeature: (enabled: boolean) => Promise<void>;
  appliedChannels: Record<string, boolean>;
  setAppliedChannels: React.Dispatch<
    React.SetStateAction<Record<string, boolean>>
  >;
  selectedWorkspaceDetails: {
    social_accounts: {
      social_id: string;
      platform: string;
      username: string;
      profile_photo?: string;
    }[];
  } | null;
  autoAnswerChanges: {
    [key: string]: AutoAnswer;
  };
  editAutoAnswer: (params: any) => Promise<any>;
  selectedWorkspace: string;
}



const AutoAnswerModal: React.FC<AutoAnswerModalProps> = ({
  isOpen,
  onClose,
  isLoadingAutoAnswer,
  workHours,
  setWorkHours,
  autoAnswerEnabled,
  toggleAutoAnswerFeature,
  appliedChannels,
  setAppliedChannels,
  selectedWorkspaceDetails,
  autoAnswerChanges,
  editAutoAnswer,
  selectedWorkspace,
}) => {
  // Debug and ensure Instagram is selected by default when modal opens
  useEffect(() => {
    if (isOpen) {
      console.log("🔧 AUTO ANSWER MODAL DEBUG:", {
        autoAnswerEnabled,
        workHours,
        appliedChannels,
        autoAnswerChanges,
        selectedWorkspaceDetails: selectedWorkspaceDetails?.social_accounts?.length || 0
      });

      if (selectedWorkspaceDetails?.social_accounts) {
        // Check if appliedChannels is empty (no channels selected)
        const hasAnyChannelSelected =
          Object.values(appliedChannels).some(Boolean);

        if (!hasAnyChannelSelected) {
          // Initialize with Instagram selected by default
          const channelState: Record<string, boolean> = {};
          selectedWorkspaceDetails.social_accounts.forEach((account) => {
            channelState[account.social_id] =
              account.platform.toLowerCase() === "instagram";
          });
          setAppliedChannels(channelState);
        }
      }
    }
  }, [isOpen, selectedWorkspaceDetails, appliedChannels, setAppliedChannels, autoAnswerEnabled]);

  // Remove the blocking loading state - let the modal show with loading indicators instead

  const toggleDay = async (day: DayType) => {
    const currentSettings = workHours[day];
    const newIsOpen = !currentSettings.isOpen;

    // Update local state first
    setWorkHours((prev) => ({
      ...prev,
      [day]: {
        ...prev[day],
        isOpen: newIsOpen,
      },
    }));

    const existingAnswer = Object.values(autoAnswerChanges).find(
      (answer) => answer.day === day
    );

    try {
      if (existingAnswer) {
        // Update existing auto-answer
        const response = await editAutoAnswer({
          workspace_name: selectedWorkspace,
          autoanswer_id: existingAnswer.id,
          day: day.toLowerCase(),
          start_time: currentSettings.hours.split(" - ")[0],
          end_time: currentSettings.hours.split(" - ")[1],
          is_closed: !newIsOpen,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          social_media_ids: Object.entries(appliedChannels)
            .filter(([_, isApplied]) => isApplied)
            .map(([id]) => id),
        });

        if (response.success) {
          showSuccessToast(`Updated auto-answer for ${day}`);
        } else {
          showErrorToast(`Failed to update auto-answer for ${day}`);
          // Revert local state on failure
          setWorkHours((prev) => ({
            ...prev,
            [day]: {
              ...prev[day],
              isOpen: !newIsOpen,
            },
          }));
        }
      } else {
        // Create new auto-answer using editAutoAnswer
        const response = await editAutoAnswer({
          workspace_name: selectedWorkspace,
          day: day.toLowerCase(),
          start_time: currentSettings.hours.split(" - ")[0],
          end_time: currentSettings.hours.split(" - ")[1],
          is_closed: !newIsOpen,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          social_media_ids: Object.entries(appliedChannels)
            .filter(([_, isApplied]) => isApplied)
            .map(([id]) => id),
        });

        if (response.success) {
          showSuccessToast(`Created auto-answer for ${day}`);
        } else {
          showErrorToast(`Failed to create auto-answer for ${day}`);
          // Revert local state on failure
          setWorkHours((prev) => ({
            ...prev,
            [day]: {
              ...prev[day],
              isOpen: !newIsOpen,
            },
          }));
        }
      }
    } catch (error) {
      console.error(`Error updating auto-answer for ${day}:`, error);
      showErrorToast(`Error updating auto-answer for ${day}`);
      // Revert local state on error
      setWorkHours((prev) => ({
        ...prev,
        [day]: {
          ...prev[day],
          isOpen: !newIsOpen,
        },
      }));
    }
  };

  const updateHours = async (day: DayType, hours: string) => {
    // Update local state first
    setWorkHours((prev) => ({
      ...prev,
      [day]: {
        ...prev[day],
        hours,
      },
    }));

    const existingAnswer = Object.values(autoAnswerChanges).find(
      (answer) => answer.day === day
    );

    try {
      const [startTime, endTime] = hours.split(" - ");
      if (existingAnswer) {
        // Update existing auto-answer
        await editAutoAnswer({
          workspace_name: selectedWorkspace,
          autoanswer_id: existingAnswer.id,
          day: day.toLowerCase(),
          start_time: startTime,
          end_time: endTime,
          is_closed: !workHours[day].isOpen,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          social_media_ids: Object.entries(appliedChannels)
            .filter(([_, isApplied]) => isApplied)
            .map(([id]) => id),
        });
      } else if (workHours[day].isOpen) {
        // Only create if the day is enabled
        await editAutoAnswer({
          workspace_name: selectedWorkspace,
          day: day.toLowerCase(),
          start_time: startTime,
          end_time: endTime,
          is_closed: !workHours[day].isOpen,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          social_media_ids: Object.entries(appliedChannels)
            .filter(([_, isApplied]) => isApplied)
            .map(([id]) => id),
        });
      }
    } catch (error) {
      console.error(`Error updating hours for ${day}:`, error);
      // No need to revert state as this is just a UI update
    }
  };

  const toggleChannel = async (socialId: string) => {
    if (!autoAnswerEnabled) return;

    const newAppliedChannels = {
      ...appliedChannels,
      [socialId]: !appliedChannels[socialId],
    };

    setAppliedChannels(newAppliedChannels);

    // Update all auto-answers with the new channel settings
    try {
      for (const day of Object.keys(workHours) as DayType[]) {
        if (!workHours[day].isOpen) continue;

        const existingAnswer = Object.values(autoAnswerChanges).find(
          (answer) => answer.day === day
        );

        if (existingAnswer) {
          await editAutoAnswer({
            workspace_name: selectedWorkspace,
            autoanswer_id: existingAnswer.id,
            day: day.toLowerCase(),
            start_time: workHours[day].hours.split(" - ")[0],
            end_time: workHours[day].hours.split(" - ")[1],
            is_closed: !workHours[day].isOpen,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            social_media_ids: Object.entries(newAppliedChannels)
              .filter(([_, isApplied]) => isApplied)
              .map(([id]) => id),
          });
        } else {
          await editAutoAnswer({
            workspace_name: selectedWorkspace,
            day: day.toLowerCase(),
            start_time: workHours[day].hours.split(" - ")[0],
            end_time: workHours[day].hours.split(" - ")[1],
            is_closed: !workHours[day].isOpen,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            social_media_ids: Object.entries(newAppliedChannels)
              .filter(([_, isApplied]) => isApplied)
              .map(([id]) => id),
          });
        }
      }
    } catch (error) {
      console.error("Error updating channels:", error);
      // Revert on error
      setAppliedChannels(appliedChannels);
    }
  };

  // Handle time change with Ant Design TimePicker
  const handleTimeChange = async (
    day: DayType,
    times: [Dayjs, Dayjs] | null
  ) => {
    if (!times || !times[0] || !times[1]) return;

    const [startTime, endTime] = times;
    const timeString = `${startTime.format("HH:mm")} - ${endTime.format(
      "HH:mm"
    )}`;

    // Update local state
    setWorkHours((prev) => ({
      ...prev,
      [day]: {
        ...prev[day],
        hours: timeString,
      },
    }));

    // Find existing answer for this day
    const existingAnswer = Object.values(autoAnswerChanges).find(
      (answer) => answer.day === day
    );

    try {
      if (existingAnswer) {
        // Update existing auto-answer
        const response = await editAutoAnswer({
          workspace_name: selectedWorkspace,
          autoanswer_id: existingAnswer.id,
          day: day.toLowerCase(),
          start_time: startTime.format("HH:mm"),
          end_time: endTime.format("HH:mm"),
          is_closed: !workHours[day].isOpen,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          social_media_ids: Object.entries(appliedChannels)
            .filter(([_, isApplied]) => isApplied)
            .map(([id]) => id),
        });

        if (response.success) {
          showSuccessToast(`Updated hours for ${day}`);
        } else {
          showErrorToast(`Failed to update hours for ${day}`);
          // Revert local state on failure
          setWorkHours((prev) => ({
            ...prev,
            [day]: {
              ...prev[day],
              hours: `${existingAnswer.start_time} - ${existingAnswer.end_time}`,
            },
          }));
        }
      } else if (workHours[day].isOpen) {
        // Only create if the day is enabled
        const response = await editAutoAnswer({
          workspace_name: selectedWorkspace,
          day: day.toLowerCase(),
          start_time: startTime.format("HH:mm"),
          end_time: endTime.format("HH:mm"),
          is_closed: !workHours[day].isOpen,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          social_media_ids: Object.entries(appliedChannels)
            .filter(([_, isApplied]) => isApplied)
            .map(([id]) => id),
        });

        if (response.success) {
          showSuccessToast(`Created auto-answer for ${day}`);
        } else {
          showErrorToast(`Failed to create auto-answer for ${day}`);
        }
      }
    } catch (error) {
      console.error(`Error updating hours for ${day}:`, error);
      showErrorToast(`Error updating hours for ${day}`);
    }
  };

  return (
    <>
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/30 flex items-center justify-center z-60 p-4"
          style={{ backdropFilter: 'blur(2px)' }}
          onClick={onClose}
        >
          <div
            className="w-full max-w-md md:max-w-2xl bg-white rounded-2xl shadow-2xl flex flex-col h-auto max-h-[90vh] overflow-y-auto border border-gray-100"
            onClick={(e) => e.stopPropagation()}
            style={{
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
            }}
          >
            {/* Header */}
            <div className="flex justify-between items-center p-4 border-b border-gray-200 rounded-t-2xl bg-linear-to-r from-gray-50 to-white">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-linear-to-br from-purple-500 via-pink-500 to-orange-400 rounded-xl flex items-center justify-center shadow-lg">
                  <img
                    src="/icons/performance/instagram-on.svg"
                    alt="Instagram"
                    className="w-5 h-5 filter brightness-0 invert"
                  />
                </div>
                <h2 className="text-lg font-semibold text-gray-800">
                  Auto Answer
                </h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-red-500 w-10 h-10 rounded-xl border border-gray-200 hover:border-red-200 flex items-center justify-center transition-all duration-200 bg-gray-50 hover:bg-red-50"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              {/* Auto Answer Feature */}
              <div className="bg-gray-50 p-4 rounded-md mb-4">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="text-sm font-medium text-gray-700">
                    Auto Answer Feature
                  </h3>
                  {isLoadingAutoAnswer ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900"></div>
                  ) : (
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={autoAnswerEnabled}
                        onChange={() =>
                          toggleAutoAnswerFeature(!autoAnswerEnabled)
                        }
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500" />
                    </label>
                  )}
                </div>
                <p className="text-xs text-gray-600">
                  {isLoadingAutoAnswer
                    ? "Loading auto-answer settings..."
                    : "With this feature you can automate your inbox, so your saved reply will be automatically sent to unread messages without your permission."
                  }
                </p>
              </div>

              {/* Hours of work */}
              <h3 className="text-sm font-medium text-gray-800 mb-2">
                Hours of work
              </h3>
              {isLoadingAutoAnswer ? (
                <div className="space-y-3">
                  {[...Array(7)].map((_, index) => (
                    <div key={index} className="flex items-center justify-between py-1">
                      <div className="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
                      <div className="flex items-center gap-2">
                        <div className="w-9 h-5 bg-gray-200 rounded-full animate-pulse"></div>
                        <div className="w-32 h-8 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-3">
                  {Object.entries(workHours).map(([day, settings]) => (
                  <div
                    key={day}
                    className={`flex flex-col sm:flex-row sm:items-center justify-between gap-2 py-1 ${
                      !autoAnswerEnabled ? "opacity-50" : ""
                    }`}
                  >
                    <span className="text-xs font-medium text-gray-700 w-20">
                      {day}
                    </span>
                    <div className="flex flex-wrap items-center gap-2">
                      <label
                        className={`flex items-center ${
                          autoAnswerEnabled
                            ? "cursor-pointer"
                            : "cursor-not-allowed"
                        }`}
                      >
                        <div className="relative">
                          <input
                            type="checkbox"
                            className="sr-only"
                            checked={settings.isOpen}
                            onChange={() =>
                              autoAnswerEnabled && toggleDay(day as DayType)
                            }
                            disabled={!autoAnswerEnabled}
                          />
                          <div
                            className={`w-9 h-5 ${
                              settings.isOpen ? "bg-green-500" : "bg-gray-300"
                            } rounded-full shadow-inner transition-colors`}
                          ></div>
                          <div
                            className={`absolute w-4 h-4 bg-white rounded-full shadow top-0.5 transition-transform ${
                              settings.isOpen ? "translate-x-4" : "left-0.5"
                            }`}
                          ></div>
                        </div>
                        <span className="ml-2 text-xs text-gray-600">
                          {settings.isOpen ? "Open" : "Closed"}
                        </span>
                      </label>
                      <TimePicker.RangePicker
                        format="HH:mm"
                        className="w-[160px] h-8 text-xs"
                        disabled={!autoAnswerEnabled || !settings.isOpen}
                        value={
                          settings.hours
                            ? [
                                dayjs(settings.hours.split(" - ")[0], "HH:mm"),
                                dayjs(settings.hours.split(" - ")[1], "HH:mm"),
                              ]
                            : null
                        }
                        onChange={(times) =>
                          autoAnswerEnabled &&
                          settings.isOpen &&
                          handleTimeChange(
                            day as DayType,
                            times as [Dayjs, Dayjs] | null
                          )
                        }
                        popupClassName="time-picker-popup"
                      />
                    </div>
                  </div>
                ))}
                </div>
              )}

              {/* Apply to Channels */}
              <h3 className="text-sm font-medium text-gray-800 mt-6 mb-3">
                Apply to Channels
              </h3>
              <div className="flex flex-wrap gap-4">
                {selectedWorkspaceDetails?.social_accounts?.map((account) => (
                  <div key={account.social_id} className="flex items-center">
                    <button
                      onClick={() => toggleChannel(account.social_id)}
                      disabled={!autoAnswerEnabled}
                      className={`relative flex items-center justify-center w-10 h-10 rounded-full transition-all ${
                        appliedChannels[account.social_id]
                          ? "bg-linear-to-br from-purple-500 to-indigo-600 ring-2 ring-purple-300"
                          : "bg-gray-200"
                      } ${
                        !autoAnswerEnabled
                          ? "opacity-50 cursor-not-allowed"
                          : "cursor-pointer hover:shadow-md"
                      }`}
                    >
                      {account.platform.toLowerCase() === "facebook" && (
                        <div className="absolute -bottom-1 -right-1 bg-blue-600 rounded-full w-4 h-4 flex items-center justify-center">
                          <svg
                            className="w-3 h-3 text-white"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M9.19795 21.5H13.198V13.4901H16.8021L17.198 9.50977H13.198V7.5C13.198 6.94772 13.6457 6.5 14.198 6.5H17.198V2.5H14.198C11.4365 2.5 9.19795 4.73858 9.19795 7.5V9.50977H7.19795L6.80206 13.4901H9.19795V21.5Z" />
                          </svg>
                        </div>
                      )}
                      {account.platform.toLowerCase() === "instagram" && (
                        <div className="absolute -bottom-1 -right-1 bg-linear-to-br from-pink-500 to-orange-400 rounded-full w-4 h-4 flex items-center justify-center">
                          <svg
                            className="w-3 h-3 text-white"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                          </svg>
                        </div>
                      )}
                      {account.platform.toLowerCase() === "pinterest" && (
                        <div className="absolute -bottom-1 -right-1 bg-red-600 rounded-full w-4 h-4 flex items-center justify-center">
                          <svg
                            className="w-3 h-3 text-white"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 2C6.477 2 2 6.477 2 12C2 17.523 6.477 22 12 22C17.523 22 22 17.523 22 12C22 6.477 17.523 2 12 2Z" />
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M12.0001 5C8.13413 5 5.00006 8.13407 5.00006 12C5.00006 14.9117 6.85768 17.4181 9.44647 18.4046C9.35275 17.6729 9.26378 16.5144 9.44647 15.8893C9.60987 15.3328 10.3988 11.9292 10.3988 11.9292C10.3988 11.9292 10.1334 11.3992 10.1334 10.6189C10.1334 9.38778 10.7001 8.45773 11.4147 8.45773C12.0214 8.45773 12.3134 8.91272 12.3134 9.45938C12.3134 10.0727 11.9361 10.9961 11.7401 11.8461C11.5761 12.5598 12.0947 13.1431 12.7947 13.1431C14.0681 13.1431 15.0534 11.7764 15.0534 9.77139C15.0534 7.84472 13.6867 6.50806 11.9601 6.50806C9.9334 6.50806 8.76673 7.97473 8.76673 9.73139C8.76673 10.4314 9.01339 11.1114 9.36006 11.5314C9.42673 11.6114 9.43339 11.6847 9.41339 11.7647C9.34673 12.0447 9.20673 12.6114 9.18006 12.7314C9.14673 12.8914 9.06006 12.9247 8.89339 12.8447C7.98006 12.4114 7.38006 10.9847 7.38006 9.70806C7.38006 7.13139 9.06006 5.13806 12.1601 5.13806C14.6734 5.13806 16.6201 6.91139 16.6201 9.63139C16.6201 12.0847 15.0267 14.0514 12.9534 14.0514C12.2267 14.0514 11.5401 13.6714 11.2934 13.2247C11.2934 13.2247 10.9134 14.7047 10.8201 15.0247C10.6467 15.6514 10.2267 16.4581 9.92006 16.9647C10.5734 17.1647 11.2667 17.2781 12.0001 17.2781C15.8661 17.2781 19.0001 14.144 19.0001 10.2781C19.0001 6.41213 15.8661 3.27806 12.0001 3.27806C8.13413 3.27806 5.00006 6.41213 5.00006 10.2781"
                              fill="white"
                            />
                          </svg>
                        </div>
                      )}
                      {account.platform.toLowerCase() === "tiktok" && (
                        <div className="absolute -bottom-1 -right-1 bg-black rounded-full w-4 h-4 flex items-center justify-center">
                          <svg
                            className="w-3 h-3 text-white"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M16.6 5.82C15.9165 5.03962 15.5397 4.03743 15.54 3H12.45V15.4C12.4494 15.9501 12.2489 16.4797 11.8856 16.886C11.5223 17.2923 11.0197 17.5442 10.47 17.6C9.45213 17.6758 8.49955 17.1474 8 16.25C7.50045 15.3526 7.6699 14.2518 8.42 13.53C9.1701 12.8082 10.2935 12.6646 11.19 13.16V10C9.31407 9.8613 7.50403 10.6493 6.29 12.1C5.07597 13.5507 4.6027 15.5246 5.03 17.43C5.4573 19.3354 6.73893 20.9486 8.5 21.72C10.2611 22.4914 12.2919 22.3089 14 21.23C15.7081 20.1511 16.8328 18.2975 17 16.25V9.15C18.1947 10.0641 19.5979 10.6312 21.06 10.79V7.65C19.1224 7.6613 17.4045 6.98767 16.6 5.82Z" />
                          </svg>
                        </div>
                      )}

                      {/* Profile image */}
                      <img
                        src={
                          account.profile_photo ||
                          `https://ui-avatars.com/api/?name=${account.username}&background=random`
                        }
                        alt={account.username}
                        className="w-8 h-8 rounded-full object-cover"
                        onError={(e) => {
                          (
                            e.target as HTMLImageElement
                          ).src = `https://ui-avatars.com/api/?name=${account.username}&background=random`;
                        }}
                      />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Save Changes Button */}
            <div className="p-4 border-t border-gray-200">
              <button
                onClick={() => {
                  // Simply close the modal without saving
                  onClose();
                }}
                className="w-full py-2.5 bg-[#2c3e50] hover:bg-[#48637e] text-white text-sm font-medium rounded-lg transition-colors flex items-center justify-center"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AutoAnswerModal;
