"use client";

import React, { useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { showSuccessToast } from "~/components/toasts";

interface EditFAQModalProps {
  editingFAQRef: React.MutableRefObject<{ title: string; text: string }>;
  setShowEditModal: React.Dispatch<React.SetStateAction<boolean>>;
  selectedWorkspace: string;
  editTitleSavedReply: (params: {
    workspace_name: string;
    title: string;
    new_title: string;
  }) => Promise<any>;
  editTextSavedReply: (params: {
    workspace_name: string;
    title: string;
    text: string;
  }) => Promise<any>;
}

// Simple opacity-only animation variants
const modalVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { duration: 0.2, ease: "easeOut" },
  },
  exit: {
    opacity: 0,
    transition: { duration: 0.15, ease: "easeIn" },
  },
};

const backdropVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { duration: 0.2, ease: "easeOut" },
  },
  exit: {
    opacity: 0,
    transition: { duration: 0.15, ease: "easeIn" },
  },
};

const EditFAQModal: React.FC<EditFAQModalProps> = ({
  editingFAQRef,
  setShowEditModal,
  selectedWorkspace,
  editTitleSavedReply,
  editTextSavedReply,
}) => {
  const titleInputRef = useRef<HTMLInputElement>(null);
  const textInputRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    // Initialize input values when modal opens
    if (titleInputRef.current) {
      titleInputRef.current.value = editingFAQRef.current.title;
    }
    if (textInputRef.current) {
      textInputRef.current.value = editingFAQRef.current.text;
    }
  }, [editingFAQRef]);

  const handleSaveChanges = () => {
    const title = titleInputRef.current?.value.trim();
    const text = textInputRef.current?.value.trim();
    if (title && text) {
      const isTitleChanged = title !== editingFAQRef.current.title;
      const isTextChanged = text !== editingFAQRef.current.text;
      const updatePromises = [];
      if (isTitleChanged) {
        updatePromises.push(
          editTitleSavedReply({
            workspace_name: selectedWorkspace,
            title: editingFAQRef.current.title,
            new_title: title,
          }).then(() => {
            showSuccessToast("Title updated successfully");
          })
        );
      }
      if (isTextChanged) {
        updatePromises.push(
          editTextSavedReply({
            workspace_name: selectedWorkspace,
            title: title,
            text: text,
          }).then(() => {
            showSuccessToast("Text updated successfully");
          })
        );
      }
      Promise.all(updatePromises).then(() => {
        setShowEditModal(false);
      });
    }
    setShowEditModal(false);
  };

  // Add keypress handler
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSaveChanges();
    }
  };

  return (
    <motion.div
      className="fixed inset-0 bg-black/30 flex items-center justify-center z-60"
      style={{ backdropFilter: 'blur(2px)' }}
      variants={backdropVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      onClick={() => setShowEditModal(false)}
    >
      <motion.div
        className="bg-white p-6 rounded-2xl w-96 relative shadow-2xl border border-gray-100"
        variants={modalVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        onClick={(e) => e.stopPropagation()}
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
        }}
      >
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-linear-to-br from-purple-500 via-pink-500 to-orange-400 rounded-xl flex items-center justify-center shadow-lg">
              <img
                src="/icons/performance/instagram-on.svg"
                alt="Instagram"
                className="w-5 h-5 filter brightness-0 invert"
              />
            </div>
            <h2 className="text-xl font-bold">Edit Saved Reply</h2>
          </div>
          <motion.button
            onClick={() => setShowEditModal(false)}
            className="text-gray-400 hover:text-red-500 w-10 h-10 rounded-xl border border-gray-200 hover:border-red-200 flex items-center justify-center transition-all duration-200 bg-gray-50 hover:bg-red-50"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </motion.button>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Title (required)
          </label>
          <input
            type="text"
            ref={titleInputRef}
            defaultValue={editingFAQRef.current.title}
            className="w-full border rounded-md p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
            onKeyPress={handleKeyPress}
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Message (required)
          </label>
          <textarea
            ref={textInputRef}
            defaultValue={editingFAQRef.current.text}
            className="w-full border rounded-md p-2 h-32 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
            onKeyPress={handleKeyPress}
          />
        </div>
        <button
          onClick={handleSaveChanges}
          className="mt-auto h-10 bg-[#2c3e50] w-full text-white text-xl font-semibold rounded-lg shadow hover:bg-[#48637e] transition-colors"
        >
          Save Changes
        </button>
      </motion.div>
    </motion.div>
  );
};

export default EditFAQModal;
