"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChatMessage } from "../types";
import FAQModal from "./FAQModal";
import { useWebSocket } from "@/hooks/useWebSocket";

interface ChatInputProps {
  onSendMessage: (message: string) => void; // Changed from handleSend to onSendMessage with message parameter
  isSendingMessage: boolean;
  showFAQModal: boolean;
  setShowFAQModal: React.Dispatch<React.SetStateAction<boolean>>;
  replyingToMessage: ChatMessage | null;
  setReplyingToMessage: React.Dispatch<
    React.SetStateAction<ChatMessage | null>
  >;
  autoAnswerEnabled?: boolean;
  openAutoAnswerModal?: () => void;
  toggleAutoAnswerFeature?: (enabled: boolean) => Promise<void>;
  // FAQ Modal props
  faqsRef?: React.MutableRefObject<{ title: string; text: string }[]>;
  handleEditFAQ?: (faq: { title: string; text: string }) => void;
  handleDeleteSaveReply?: (title: string) => void;
  setShowManageFAQModal?: React.Dispatch<React.SetStateAction<boolean>>;
  selectedWorkspace?: string; // Add selectedWorkspace prop
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  isSendingMessage,
  showFAQModal,
  setShowFAQModal,
  replyingToMessage,
  setReplyingToMessage,
  autoAnswerEnabled = false,
  openAutoAnswerModal,
  toggleAutoAnswerFeature,
  // FAQ Modal props
  faqsRef = { current: [] } as React.MutableRefObject<
    { title: string; text: string }[]
  >,
  handleEditFAQ = () => {},
  handleDeleteSaveReply = () => {},
  setShowManageFAQModal = () => {},
  selectedWorkspace = "",
}) => {
  // Move inputMessage state to this component to prevent parent re-renders
  const [inputMessage, setInputMessage] = useState("");

  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [autoAnswerData, setAutoAnswerData] = useState<any[]>([]);
  const [isLoadingAutoAnswerData, setIsLoadingAutoAnswerData] = useState(false);
  const [isLoadingSavedReplies, setIsLoadingSavedReplies] = useState(false);
  const { getAutoAnswer, getAllSavedReply } = useWebSocket();

  // Debug autoAnswerEnabled prop
  useEffect(() => {
    console.log("🔧 CHAT INPUT AUTO ANSWER DEBUG:", {
      autoAnswerEnabled,
      autoAnswerDataLength: autoAnswerData.length,
      hasToggleFunction: !!toggleAutoAnswerFeature,
    });
  }, [autoAnswerEnabled, autoAnswerData]);

  // Note: Auto-answer data is now fetched in the main inbox page to prevent duplicate requests
  // This component receives the auto-answer state via props

  // Fetch saved replies when the modal is opened
  useEffect(() => {
    const fetchSavedReplies = async () => {
      if (!selectedWorkspace || !showFAQModal) return; // Skip if no workspace is selected or modal is not shown

      setIsLoadingSavedReplies(true);
      try {
        const response: any = await getAllSavedReply({
          workspace_name: selectedWorkspace,
        });

        console.log("Saved replies response:", response);

        if (response.success && response.replies) {
          // Update faqsRef with the saved replies
          faqsRef.current = response.replies.map((reply: any) => ({
            title: reply.title,
            text: reply.text,
          }));
        }
      } catch (error) {
        console.error("Error fetching saved replies:", error);
      } finally {
        setIsLoadingSavedReplies(false);
      }
    };

    fetchSavedReplies();
  }, [getAllSavedReply, selectedWorkspace, showFAQModal, faqsRef]);

  // Mobile detection
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Reset textarea height when message is cleared
  useEffect(() => {
    if (!inputMessage && inputRef.current) {
      inputRef.current.style.height = "40px";
      inputRef.current.style.overflowY = "hidden";
    }
  }, [inputMessage]);

  // Handle focus to ensure proper sizing
  const handleFocus = () => {
    if (inputRef.current && inputMessage) {
      // Trigger resize on focus if there's content
      const event = {
        target: inputRef.current,
      } as React.ChangeEvent<HTMLTextAreaElement>;
      handleInputChange(event);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  // Internal send handler that clears input and calls parent
  const handleSend = () => {
    if (inputMessage.trim() && !isSendingMessage) {
      const messageText = inputMessage.trim();
      setInputMessage(""); // Clear input immediately
      onSendMessage(messageText); // Send to parent
    }
  };

  // Auto-resize textarea based on content - optimized for performance
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const textarea = e.target;
      const value = textarea.value;

      // Update input message state
      setInputMessage(value);

      // Use requestAnimationFrame to defer DOM manipulation for better performance
      requestAnimationFrame(() => {
        // Reset height to auto to get the correct scrollHeight
        textarea.style.height = "auto";

        // Set height based on scrollHeight, with min and max constraints
        const minHeight = 40; // Minimum height (about 1 line)
        const maxHeight = window.innerWidth <= 768 ? 80 : 120; // Even smaller max height on mobile to ensure it stays within bounds
        const newHeight = Math.min(
          Math.max(textarea.scrollHeight, minHeight),
          maxHeight
        );

        textarea.style.height = `${newHeight}px`;

        // Enable scrolling if content exceeds max height
        if (textarea.scrollHeight > maxHeight) {
          textarea.style.overflowY = "auto";
        } else {
          textarea.style.overflowY = "hidden";
        }
      });
    },
    [setInputMessage]
  );

  return (
    <div
      className={`${
        isMobile
          ? "mobile-chat-input fixed bottom-0 left-0 right-0 w-full border-t bg-white rounded-none p-3 z-10"
          : "shrink-0 w-full border-t bg-gray-100 rounded-b-lg p-4 relative"
      }`}
      style={
        isMobile
          ? {
              maxHeight: "25vh",
              minHeight: "auto",
            }
          : {}
      }
    >
      <style jsx>{`
        /* Custom scrollbar for webkit browsers */
        textarea::-webkit-scrollbar {
          width: 6px;
        }

        textarea::-webkit-scrollbar-track {
          background: transparent;
        }

        textarea::-webkit-scrollbar-thumb {
          background: #cbd5e0;
          border-radius: 3px;
        }

        textarea::-webkit-scrollbar-thumb:hover {
          background: #a0aec0;
        }

        /* Ensure smooth transitions */
        textarea {
          transition: height 0.2s ease-in-out;
        }

        /* Mobile input container constraints */
        @media (max-width: 768px) {
          .mobile-chat-input {
            position: fixed !important;
            bottom: 0 !important;
            left: 0 !important;
            right: 0 !important;
            width: 100% !important;
            max-height: 25vh !important;
            z-index: 50 !important;
            background: white !important;
            border-top: 1px solid #e5e7eb !important;
          }
        }
      `}</style>
      <AnimatePresence>
        {showFAQModal && (
          <FAQModal
            isLoadingSavedReply={isLoadingSavedReplies}
            faqsRef={faqsRef}
            setInputMessage={setInputMessage} // Use internal setInputMessage
            handleEditFAQ={handleEditFAQ}
            handleDeleteSaveReply={handleDeleteSaveReply}
            setShowFAQModal={setShowFAQModal}
            setShowManageFAQModal={setShowManageFAQModal}
          />
        )}
      </AnimatePresence>

      {replyingToMessage && (
        <div className="mb-2 p-2 bg-gray-200 rounded-lg flex items-start">
          <div className="flex-1">
            <div className="flex items-center">
              <span className="text-sm font-medium text-gray-700">
                Replying to {replyingToMessage.sender}
              </span>
            </div>
            <p className="text-sm text-gray-600 truncate">
              {replyingToMessage.content}
            </p>
          </div>
          <button
            onClick={() => setReplyingToMessage(null)}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      )}

      {/* Quick action buttons */}
      <motion.div
        className="flex mb-3 space-x-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.3 }}
      >
        <motion.button
          onClick={() => setShowFAQModal(!showFAQModal)}
          className="flex items-center px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded-md text-sm text-gray-700"
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.95 }}
          animate={{
            backgroundColor: showFAQModal ? "#d1d5db" : "#e5e7eb",
          }}
          transition={{ duration: 0.15 }}
        >
          <motion.img
            alt="Ready text"
            width="16"
            height="16"
            className="mr-1"
            src="/icons/performance/timer.svg"
            whileHover={{ rotate: 5 }}
            transition={{ duration: 0.15 }}
          />
          Ready text
        </motion.button>

        <motion.div
          className="flex items-center px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded-md text-sm text-gray-700"
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.95 }}
        >
          {/* Calendar Icon - Created inline SVG */}
          <motion.div
            className="mr-2 flex items-center"
            whileHover={{ scale: 1.03 }}
            transition={{ duration: 0.15 }}
          >
            <svg
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-blue-600 w-5 h-5"
            >
              <path
                d="M8 2V5M16 2V5M3.5 9.09H20.5M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M15.6947 13.7002H15.7037M11.9955 13.7002H12.0045M8.29431 13.7002H8.30329M15.6947 17.0002H15.7037M11.9955 17.0002H12.0045M8.29431 17.0002H8.30329"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </motion.div>

          {/* Clickable Auto Answer Text */}
          <motion.button
            onClick={openAutoAnswerModal}
            className="flex items-center mr-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            Auto Answer
          </motion.button>

          {/* Functional Checkbox - After text */}
          <motion.button
            onClick={() => {
              console.log("🔧 CHECKBOX CLICKED:", {
                autoAnswerEnabled,
                toggleAutoAnswerFeature: !!toggleAutoAnswerFeature,
              });
              if (toggleAutoAnswerFeature) {
                toggleAutoAnswerFeature(!autoAnswerEnabled);
              } else {
                console.warn("toggleAutoAnswerFeature function not provided");
              }
            }}
            className="flex items-center"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            disabled={!toggleAutoAnswerFeature}
          >
            <motion.div
              className={`w-8 h-4 rounded-full relative cursor-pointer ${
                !toggleAutoAnswerFeature ? "opacity-50 cursor-not-allowed" : ""
              }`}
              animate={{
                backgroundColor: autoAnswerEnabled ? "#10b981" : "#d1d5db",
              }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="absolute w-3 h-3 rounded-full bg-white top-0.5"
                animate={{
                  x: autoAnswerEnabled ? 16 : 4,
                }}
                transition={{
                  type: "spring",
                  stiffness: 500,
                  damping: 30,
                }}
              />
            </motion.div>
          </motion.button>

          {/* Loading Indicator */}
          <AnimatePresence>
            {isLoadingAutoAnswerData && (
              <motion.div
                className="ml-2 w-3 h-3 rounded-full border-2 border-t-transparent border-blue-500"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{
                  opacity: 1,
                  scale: 1,
                  rotate: 360,
                }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{
                  opacity: { duration: 0.2 },
                  scale: { duration: 0.2 },
                  rotate: {
                    duration: 1,
                    repeat: Infinity,
                    ease: "linear",
                  },
                }}
              />
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>

      <div className="flex items-start gap-2">
        <div className="relative flex-1">
          <motion.textarea
            ref={inputRef}
            value={inputMessage}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={handleFocus}
            placeholder="Type a message... (Shift+Enter for new line)"
            rows={1}
            className={`w-full bg-gray-50 border border-gray-200 rounded-lg py-2 px-4 focus:outline-none focus:bg-white focus:border-blue-500 focus:ring-2 focus:ring-blue-100 resize-none overflow-hidden min-h-[40px] ${
              isMobile ? "max-h-[80px]" : "max-h-[120px]"
            } leading-6 transition-all duration-200 ease-in-out placeholder-gray-400`}
            style={{
              height: "40px",
              scrollbarWidth: "thin",
              scrollbarColor: "#cbd5e0 transparent",
            }}
            whileFocus={{ scale: 1.01 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          />
        </div>
        <motion.button
          onClick={handleSend}
          disabled={!inputMessage.trim() || isSendingMessage}
          className={`px-4 py-2 rounded-lg font-semibold transition-all duration-200 ease-in-out flex items-center justify-center shrink-0 ${
            !inputMessage.trim() || isSendingMessage
              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700 shadow-md hover:shadow-lg"
          }`}
          style={{ height: "40px", minWidth: "60px" }}
          whileHover={
            !inputMessage.trim() || isSendingMessage
              ? {}
              : { scale: 1.05, transition: { duration: 0.2, ease: "easeOut" } }
          }
          whileTap={
            !inputMessage.trim() || isSendingMessage
              ? {}
              : {
                  scale: 0.95,
                  transition: { duration: 0.1, ease: "easeInOut" },
                }
          }
        >
          {isSendingMessage ? (
            <motion.div
              className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            />
          ) : (
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              Send
            </motion.span>
          )}
        </motion.button>
      </div>
    </div>
  );
};

// Memoize ChatInput to prevent unnecessary re-renders
export default React.memo(ChatInput, (prevProps, nextProps) => {
  // Only re-render if essential props change (removed inputMessage since it's now internal)
  return (
    prevProps.isSendingMessage === nextProps.isSendingMessage &&
    prevProps.showFAQModal === nextProps.showFAQModal &&
    prevProps.replyingToMessage?.id === nextProps.replyingToMessage?.id &&
    prevProps.autoAnswerEnabled === nextProps.autoAnswerEnabled &&
    prevProps.selectedWorkspace === nextProps.selectedWorkspace &&
    prevProps.toggleAutoAnswerFeature === nextProps.toggleAutoAnswerFeature
  );
});
