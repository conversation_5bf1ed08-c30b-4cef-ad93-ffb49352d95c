"use client";

import React, { useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { Comment } from "../types";
import { formatPostDate, truncateText } from "../utils";

interface CommentsListProps {
  comments: Comment[];
  selectedComment: Comment | null;
  isLoadingCommentsList: boolean;
  unreadComments: string[];
  setSelectedComment: React.Dispatch<React.SetStateAction<Comment | null>>;
  setUnreadComments: React.Dispatch<React.SetStateAction<string[]>>;
  setUnreadCommentsCount: React.Dispatch<React.SetStateAction<number>>;
  setComments: React.Dispatch<React.SetStateAction<Comment[]>>;
  isLoadingMorePosts: boolean;
  fetchPostComments: (postId: string, platform: string) => Promise<void>;
  isViewingPostRef: React.MutableRefObject<boolean>;
  selectedWorkspace: string;
  selectedSocial: {
    platform: string;
    social_id: string;
    username?: string;
    profile_photo?: string;
  };
  seenCache: (data: {
    workspace_name: string;
    cache_type: string;
    platform?: string;
    object_id: string;
  }) => Promise<any>;
  commentToPostMapping: Record<string, string>;
  isNavigatingBack?: boolean;
  isMobile?: boolean;
  recentlyMovedPosts?: Set<string>; // Track posts that were recently moved to top
}

// Animation variants for comments - simplified with only scale and opacity
// Different variants for mobile navigation back
const getContainerVariants = (isNavigatingBack: boolean, isMobile: boolean) => {
  // Disable all animations when navigating back on mobile
  if (isNavigatingBack && isMobile) {
    return {
      hidden: { opacity: 1 },
      visible: { opacity: 1 },
    };
  }

  return {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.02,
        delayChildren: 0.02,
      },
    },
  };
};

const getItemVariants = (isNavigatingBack: boolean, isMobile: boolean) => {
  // Disable all animations when navigating back on mobile
  if (isNavigatingBack && isMobile) {
    return {
      hidden: { opacity: 1, scale: 1 },
      visible: { opacity: 1, scale: 1 },
      exit: { opacity: 1, scale: 1 },
      hotComment: { opacity: 1, scale: 1 },
    };
  }

  return {
    hidden: {
      opacity: 0,
      y: 6,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.25,
        ease: "easeOut",
      },
    },
    exit: {
      opacity: 0,
      y: 6,
      transition: {
        duration: 0.2,
        ease: "easeInOut",
      },
    },
    // Special animation for posts that just received hot comments
    hotComment: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.25,
        ease: "easeOut",
      },
    },
  };
};

const CommentsList: React.FC<CommentsListProps> = ({
  comments,
  selectedComment,
  isLoadingCommentsList,
  unreadComments,
  setSelectedComment,
  setUnreadComments,
  setUnreadCommentsCount,
  setComments,
  isLoadingMorePosts,
  fetchPostComments,
  isViewingPostRef,
  selectedWorkspace,
  selectedSocial,
  seenCache,
  commentToPostMapping,
  isNavigatingBack = false,
  isMobile = false,
  recentlyMovedPosts = new Set(),
}) => {
  const postsBottomLoaderRef = useRef<HTMLDivElement>(null);

  // Avatar fallback state
  const [failedAvatars, setFailedAvatars] = React.useState<Set<string>>(
    new Set()
  );

  // Track posts that are currently showing hot comment animation
  const [hotCommentPosts, setHotCommentPosts] = React.useState<Set<string>>(
    new Set()
  );

  // Effect to handle recently moved posts animation
  React.useEffect(() => {
    if (recentlyMovedPosts.size > 0) {
      // Add posts to hot comment animation state
      setHotCommentPosts(new Set(recentlyMovedPosts));

      // Remove the hot comment animation after 3 seconds
      const timer = setTimeout(() => {
        setHotCommentPosts(new Set());
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [recentlyMovedPosts]);

  // Helper function to get avatar URL with fallback logic
  const normalizeUrl = (path?: string | null): string => {
    if (!path) return "";
    if (/^https?:\/\//i.test(path) || path.startsWith("data:")) return path;
    const base = process.env.NEXT_PUBLIC_API_URL || "";
    if (!base) return path;
    const needsSlash = !base.endsWith("/") && !path.startsWith("/");
    const dedupSlash = base.endsWith("/") && path.startsWith("/") ? path.slice(1) : path;
    return needsSlash ? `${base}/${path}` : `${base}${dedupSlash}`;
  };

  const getAvatarUrl = (
    originalUrl: string | undefined,
    fallbackName: string,
    preferSelected: boolean = true
  ): string => {
    // Optionally prefer selected social profile image when available (workspace account image)
    const preferred = preferSelected ? selectedSocial?.profile_photo : undefined;
    const candidate = preferred || originalUrl;
    if (!candidate || failedAvatars.has(candidate)) {
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(
        fallbackName
      )}&background=random&color=fff`;
    }
    const normalized = normalizeUrl(candidate);
    if (!normalized) {
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(
        fallbackName
      )}&background=random&color=fff`;
    }
    return normalized;
  };

  // Helper function to handle avatar load errors
  const handleAvatarError = (originalUrl: string | undefined) => {
    if (originalUrl) {
      setFailedAvatars((prev) => new Set([...prev, originalUrl]));
    }
  };

  if (isLoadingCommentsList) {
    return (
      <div className="flex flex-col space-y-4">
        {[...Array(3)].map((_, index) => (
          <div
            key={index}
            className="animate-pulse flex items-center p-3 bg-gray-50 rounded-lg"
          >
            <div className="w-20 h-20 bg-gray-200 rounded-lg mr-3"></div>
            <div className="flex-1">
              <div className="flex items-center mb-2">
                <div className="w-5 h-5 bg-gray-200 rounded-full mr-2"></div>
                <div className="h-4 w-24 bg-gray-200 rounded"></div>
              </div>
              <div className="h-4 w-3/4 bg-gray-200 rounded mb-2"></div>
              <div className="flex items-center">
                <div className="h-3 w-12 bg-gray-200 rounded mr-3"></div>
                <div className="h-3 w-12 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (comments.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <Image
          src="/icons/empty-comments.svg"
          alt="No comments"
          width={64}
          height={64}
          className="mb-4 opacity-50"
          onError={(e) => {
            // Fallback if image doesn't exist
            (e.target as HTMLImageElement).style.display = "none";
          }}
        />
        <p>No comments found</p>
        <p className="text-sm mt-2">
          Comments from your posts will appear here
        </p>
      </div>
    );
  }

  // Get dynamic variants based on navigation state
  const containerVariants = getContainerVariants(isNavigatingBack, isMobile);
  const itemVariants = getItemVariants(isNavigatingBack, isMobile);

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="overflow-x-hidden"
    >
      <AnimatePresence mode="popLayout">
        {comments.map((comment) => {
          // Check for unread status and count unread comments for this post
          const postId = comment.id;

          // Find all unread comments that belong to this post
          const unreadCommentsForThisPost = unreadComments.filter(
            (commentId) => {
              // Check if this comment ID maps to our post ID
              const mappedPostId = commentToPostMapping[commentId];
              return (
                mappedPostId === postId ||
                commentId === postId ||
                commentId.includes(postId)
              );
            }
          );

          const unreadCountForThisPost = unreadCommentsForThisPost.length;
          const isUnread =
            unreadCountForThisPost > 0 || comment.isUnread === true;

          // Function to mark comment as read
          const markAsRead = async () => {
            if (!isUnread) return;

            // Find comment object IDs that belong to this post using the mapping
            // Look for comment IDs where the mapped post/media ID matches this post ID
            const relatedCommentIds = unreadComments.filter((commentId) => {
              // Check if this comment ID maps to our post ID
              const mappedPostId = commentToPostMapping[commentId];
              return (
                mappedPostId === postId ||
                commentId === postId ||
                commentId.includes(postId)
              );
            });

            console.log(
              `Marking ${relatedCommentIds.length} comments as read for post ${postId}`,
              {
                postId,
                relatedCommentIds,
                allUnreadComments: unreadComments,
                commentToPostMapping,
              }
            );

            // Remove from unreadComments array
            setUnreadComments((prev) =>
              prev.filter((commentId) => {
                const mappedPostId = commentToPostMapping[commentId];
                return !(
                  mappedPostId === postId ||
                  commentId === postId ||
                  commentId.includes(postId)
                );
              })
            );

            // Decrement the unread count by the number of comments we're marking as read
            setUnreadCommentsCount((prev) =>
              Math.max(0, prev - relatedCommentIds.length)
            );

            // Update the comment to remove the unread flag
            setComments((prev) =>
              prev.map((c) => (c.id === postId ? { ...c, isUnread: false } : c))
            );

            // Mark each related comment as seen on the server
            for (const commentObjectId of relatedCommentIds) {
              if (selectedWorkspace) {
                try {
                  const response = await seenCache({
                    workspace_name: selectedWorkspace,
                    cache_type: "newComment",
                    platform: selectedSocial.platform,
                    object_id: commentObjectId,
                  });

                  console.log(
                    `Marked comment ${commentObjectId} as seen:`,
                    response
                  );
                } catch (error) {
                  console.error(
                    `Error marking comment ${commentObjectId} as seen:`,
                    error
                  );
                }
              }
            }
          };

          // Check if this post is in the hot comment animation state
          const isHotComment = hotCommentPosts.has(postId);

          return (
            <motion.div
              key={postId}
              variants={itemVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              whileTap={{ scale: 0.995 }}
              className={`flex cursor-pointer rounded-lg p-3 mb-2 transition-all duration-300 w-full overflow-hidden ${
                selectedComment?.id === postId
                  ? "bg-white border-l-4 border-blue-700 shadow-lg"
                  : isUnread
                  ? isHotComment
                    ? "bg-linear-to-r from-blue-100 to-blue-50 hover:bg-white hover:shadow-lg border-l-2 border-blue-400"
                    : "bg-blue-50 hover:bg-white hover:shadow-md"
                  : "bg-transparent hover:bg-white hover:shadow-md"
              }`}
              onClick={() => {
                // Mark as read first
                markAsRead();

                // Set the viewing post flag to prevent comments fetch
                isViewingPostRef.current = true;

                // Set the selected comment
                setSelectedComment(comment);

                // Fetch post comments
                if (comment.platform) {
                  fetchPostComments(comment.id, comment.platform);
                }
              }}
            >
              {comment.image && (
                <Image
                  src={comment.image}
                  alt={comment.username}
                  width={80}
                  height={80}
                  className="w-28 h-20 object-cover rounded-lg mr-3"
                  onError={(e) => {
                    // Hide image if it fails to load
                    (e.target as HTMLImageElement).style.display = "none";
                  }}
                />
              )}
              {!comment.image && (
                <div className="w-20 h-20 bg-gray-200 rounded-lg mr-3 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              )}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1 min-w-0">
                  <div className="flex items-center min-w-0">
                    <Image
                      src={getAvatarUrl(
                        comment.from?.profile_picture || comment.avatar,
                        comment.username || selectedSocial?.username || "User",
                        true
                      )}
                      alt={comment.username}
                      width={20}
                      height={20}
                      className="w-5 h-5 rounded-full mr-2"
                      onError={() => {
                        handleAvatarError(
                          selectedSocial?.profile_photo ||
                            comment.from?.profile_picture ||
                            comment.avatar
                        );
                      }}
                    />
                    <span className="font-medium text-sm truncate">
                      {comment.username}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">
                    {formatPostDate(comment.timestamp)}
                  </span>
                </div>
                <p className="text-xs text-gray-700 mb-1 break-words whitespace-normal overflow-hidden">
                  {truncateText(comment.text, 100)}
                </p>
                {comment.lastComment && (
                  <div className="flex items-start text-[11px] text-gray-600 mb-1 break-words whitespace-normal overflow-hidden">
                    <Image
                      src={getAvatarUrl(
                        // Keep last commenter's own avatar only; do NOT replace with selected social
                        comment.lastComment.avatar,
                        comment.lastComment.username || "User",
                        false
                      )}
                      alt={comment.lastComment.username || "Last commenter"}
                      width={16}
                      height={16}
                      className="w-4 h-4 rounded-full mr-1 mt-px shrink-0"
                      onError={() => handleAvatarError(comment.lastComment?.avatar)}
                    />
                    <span className="font-medium mr-1 truncate max-w-[30%]">
                      {comment.lastComment.username}:
                    </span>
                    <span className="truncate">
                      {truncateText(comment.lastComment.text, 100)}
                    </span>
                  </div>
                )}
                <div className="flex items-center text-xs text-gray-500">
                  <div className="flex items-center mr-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                    {comment.likes}
                  </div>
                  <div className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                      />
                    </svg>
                    {comment.commentsCount}
                  </div>
                  <div className="flex items-center ml-auto">
                    {/* Unread indicator with count */}
                    {isUnread && (
                      <motion.div
                        initial={{ scale: 0.9, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.9, opacity: 0 }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        className={`text-white text-xs font-bold rounded-full min-w-[20px] h-[20px] flex items-center justify-center px-1 bg-blue-500`}
                      >
                        {unreadCountForThisPost > 99
                          ? "99+"
                          : unreadCountForThisPost || "•"}
                      </motion.div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>

      {/* Bottom loader for infinite scroll */}
      <motion.div
        ref={postsBottomLoaderRef}
        className="h-10 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <AnimatePresence>
          {isLoadingMorePosts && (
            <motion.div
              className="rounded-full h-5 w-5 border-b-2 border-gray-900"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{
                opacity: 1,
                scale: 1,
                rotate: 360,
              }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{
                opacity: { duration: 0.2 },
                scale: { duration: 0.2 },
                rotate: {
                  duration: 1,
                  repeat: Infinity,
                  ease: "linear",
                },
              }}
            />
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
};

export default React.memo(CommentsList);
