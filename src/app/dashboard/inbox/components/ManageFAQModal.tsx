"use client";

import React, { useRef } from "react";

interface ManageFAQModalProps {
  setShowManageFAQModal: React.Dispatch<React.SetStateAction<boolean>>;
  createFAQTitleRef: React.RefObject<HTMLInputElement>;
  createFAQMessageRef: React.RefObject<HTMLTextAreaElement>;
  selectedWorkspace: string;
  createSavedReply: (params: {
    workspace_name: string;
    title: string;
    text: string;
  }) => Promise<any>;
}



const ManageFAQModal: React.FC<ManageFAQModalProps> = ({
  setShowManageFAQModal,
  createFAQTitleRef,
  createFAQMessageRef,
  selectedWorkspace,
  createSavedReply,
}) => {
  const handleSaveChanges = () => {
    const title = createFAQTitleRef.current?.value.trim();
    const message = createFAQMessageRef.current?.value.trim();

    if (title && message) {
      createSavedReply({
        workspace_name: selectedWorkspace,
        title: title,
        text: message,
      });
      setShowManageFAQModal(false);
    }
  };

  // Add keypress handlers
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSaveChanges();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/30 flex items-center justify-center z-60"
      style={{ backdropFilter: 'blur(2px)' }}
      onClick={() => setShowManageFAQModal(false)}
    >
      <div
        className="bg-white p-6 rounded-2xl w-[400px] shadow-2xl border border-gray-100"
        onClick={(e) => e.stopPropagation()}
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
        }}
      >
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-linear-to-br from-purple-500 via-pink-500 to-orange-400 rounded-xl flex items-center justify-center shadow-lg">
              <img
                src="/icons/performance/instagram-on.svg"
                alt="Instagram"
                className="w-5 h-5 filter brightness-0 invert"
              />
            </div>
            <h2 className="text-2xl font-bold">Create A Saved Reply</h2>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowManageFAQModal(false);
            }}
            className="text-gray-400 hover:text-red-500 w-10 h-10 rounded-xl border border-gray-200 hover:border-red-200 flex items-center justify-center transition-all duration-200 bg-gray-50 hover:bg-red-50"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Title (required)
          </label>
          <input
            type="text"
            ref={createFAQTitleRef}
            defaultValue={createFAQTitleRef.current?.value}
            className="w-full border border-gray-300 rounded-md p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
            placeholder="Enter title"
            onClick={(e) => e.stopPropagation()}
            onKeyPress={handleKeyPress}
          />
        </div>
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Message (required)
          </label>
          <textarea
            ref={createFAQMessageRef}
            defaultValue={createFAQMessageRef.current?.value}
            className="w-full border border-gray-300 rounded-md p-2 h-32 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
            placeholder="Enter message"
            onClick={(e) => e.stopPropagation()}
            onKeyPress={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSaveChanges();
              }
            }}
          />
        </div>

        <button
          onClick={(e) => {
            e.stopPropagation();
            handleSaveChanges();
          }}
          className="bg-[#2C3E50] w-full text-white px-4 py-2 rounded-md hover:bg-[#4B5563] transition-colors"
        >
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default ManageFAQModal;
