"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { Comment } from "../types";

interface CommentHeaderProps {
  selectedComment: Comment;
  closeComment?: () => void;
}

const CommentHeader: React.FC<CommentHeaderProps> = ({
  selectedComment,
  closeComment,
}) => {
  const [isMobile, setIsMobile] = useState(false);

  // Avatar fallback state
  const [failedAvatars, setFailedAvatars] = useState<Set<string>>(new Set());

  // Helper function to get avatar URL with fallback logic
  const getAvatarUrl = (
    originalUrl: string | undefined,
    fallbackName: string
  ): string => {
    if (!originalUrl || failedAvatars.has(originalUrl)) {
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(
        fallbackName
      )}&background=random&color=fff`;
    }

    if (originalUrl.startsWith("http") || originalUrl.startsWith("https")) {
      return originalUrl;
    }

    return `https://ui-avatars.com/api/?name=${encodeURIComponent(
      fallbackName
    )}&background=random&color=fff`;
  };

  // Helper function to handle avatar load errors
  const handleAvatarError = (originalUrl: string | undefined) => {
    if (originalUrl) {
      setFailedAvatars((prev) => new Set([...prev, originalUrl]));
    }
  };

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Format date as YYYY/MM/DD, HH:MM
  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${year}/${month}/${day}, ${hours}:${minutes}`;
  };

  return (
    <div className="shrink-0 w-full">
      <div
        className={`${
          isMobile ? "p-3 h-[60px]" : "p-4 min-h-[72px]"
        } w-full flex bg-gray-100 ${
          isMobile ? "rounded-none" : "rounded-t-lg"
        } items-center justify-between`}
      >
        <div className="flex items-center w-full">
          {isMobile && closeComment && (
            <motion.button
              onClick={closeComment}
              className="mr-2 text-gray-500 hover:text-gray-700 active:text-gray-800 p-1 shrink-0 transition-colors duration-150"
              aria-label="Back to comments"
              whileHover={{
                scale: 1.05,
                color: "#374151",
                transition: { duration: 0.2, ease: "easeOut" },
              }}
              whileTap={{
                scale: 0.95,
                transition: { duration: 0.1, ease: "easeInOut" },
              }}
            >
              <motion.svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                viewBox="0 0 20 20"
                fill="currentColor"
                whileHover={{ x: -2 }}
                transition={{ duration: 0.2, ease: "easeOut" }}
              >
                <path
                  fillRule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </motion.svg>
            </motion.button>
          )}
          <Image
            src={getAvatarUrl(
              selectedComment.from?.profile_picture || selectedComment.avatar,
              selectedComment.username || "User"
            )}
            alt={selectedComment.username}
            width={32}
            height={32}
            className="w-8 h-8 rounded-full mr-3 shrink-0"
            onError={() => {
              handleAvatarError(
                selectedComment.from?.profile_picture || selectedComment.avatar
              );
            }}
          />
          <div className="min-w-0 flex-1">
            <h3
              className={`font-medium truncate ${
                isMobile ? "text-sm" : "text-base"
              }`}
            >
              {selectedComment.username || "Unknown User"}
            </h3>
            <p
              className={`text-gray-500 truncate ${
                isMobile ? "text-xs" : "text-sm"
              }`}
            >
              {selectedComment.platform === "facebook"
                ? "Facebook Post"
                : "Instagram Post"}
            </p>
          </div>
        </div>
      </div>

      {/* Date divider with lines on both sides */}
      <div
        className={`w-full flex items-center justify-center ${
          isMobile ? "py-2 h-[32px]" : "py-2"
        } px-4 bg-gray-100`}
      >
        <div className="grow h-px bg-gray-200"></div>
        <span className="px-3 text-xs lg:text-sm text-gray-600/90 whitespace-nowrap">
          {formatDate(new Date())}
        </span>
        <div className="grow h-px bg-gray-200"></div>
      </div>
    </div>
  );
};

export default CommentHeader;
