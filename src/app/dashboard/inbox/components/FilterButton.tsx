"use client";

import React, { useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface FilterButtonProps {
  activeTab: string;
  activeFilter: string;
  setActiveFilter: React.Dispatch<React.SetStateAction<string>>;
  showFilterDropdown: boolean;
  setShowFilterDropdown: React.Dispatch<React.SetStateAction<boolean>>;
}

const FilterButton: React.FC<FilterButtonProps> = ({
  activeTab,
  activeFilter,
  setActiveFilter,
  showFilterDropdown,
  setShowFilterDropdown,
}) => {
  const filterRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (filterRef.current && !filterRef.current.contains(target)) {
        setShowFilterDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [setShowFilterDropdown]);

  const filterOptions =
    activeTab === "direct"
      ? ["newest", "unread"]
      : ["newest", "unread", "most liked"];

  return (
    <div className="relative" ref={filterRef}>
      <motion.button
        className="flex items-center gap-2 px-3 py-1.5 rounded-[8px] md:rounded-lg bg-white border border-gray-200 text-sm text-gray-600 transition-all duration-200"
        onClick={() => setShowFilterDropdown(!showFilterDropdown)}
        whileHover={{
          scale: 1.02,
          backgroundColor: "#f9fafb",
          borderColor: "#d1d5db"
        }}
        whileTap={{ scale: 0.98 }}
        animate={{
          borderColor: showFilterDropdown ? "#3b82f6" : "#e5e7eb"
        }}
        transition={{ duration: 0.15 }}
      >
        <span className="capitalize">{activeFilter}</span>
        <motion.svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          animate={{
            rotate: showFilterDropdown ? 180 : 0,
            scale: showFilterDropdown ? 1.1 : 1
          }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
        >
          <path
            d="M2 4H14M4 8H12M6 12H10"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </motion.svg>
      </motion.button>

      <AnimatePresence>
        {showFilterDropdown && (
          <motion.div
            className="absolute right-0 mt-2 w-36 bg-white border border-gray-200 rounded-lg shadow-lg z-50 overflow-hidden"
            initial={{
              opacity: 0,
              scale: 0.95,
              y: -10,
              transformOrigin: "top right"
            }}
            animate={{
              opacity: 1,
              scale: 1,
              y: 0
            }}
            exit={{
              opacity: 0,
              scale: 0.95,
              y: -10
            }}
            transition={{
              duration: 0.15,
              ease: "easeOut"
            }}
          >
            {filterOptions.map((option, index) => (
              <motion.div
                key={option}
                className="w-full"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{
                  delay: index * 0.05,
                  duration: 0.15
                }}
              >
                <motion.button
                  className={`w-full px-4  py-2.5 text-sm text-center transition-all duration-150 ${
                    activeFilter === option
                      ? "bg-blue-50 text-blue-600 font-medium"
                      : "text-gray-700 hover:bg-gray-100/50"
                  } ${index === 0 ? "rounded-t-lg" : ""} ${
                    index === filterOptions.length - 1 ? "rounded-b-lg" : ""
                  }`}
                  onClick={() => {
                    setActiveFilter(option);
                    setShowFilterDropdown(false);
                  }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ duration: 0.1 }}
                >
                  <span className="capitalize">{option}</span>
                </motion.button>
                {index !== filterOptions.length - 1 && (
                  <motion.hr
                    className="border-gray-100"
                    initial={{ scaleX: 0 }}
                    animate={{ scaleX: 1 }}
                    transition={{ delay: index * 0.05 + 0.1, duration: 0.2 }}
                  />
                )}
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FilterButton;
