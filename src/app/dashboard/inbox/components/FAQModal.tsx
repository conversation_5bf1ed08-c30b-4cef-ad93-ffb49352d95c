"use client";

import React, { useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface FAQModalProps {
  isLoadingSavedReply: boolean;
  faqsRef: React.MutableRefObject<{ title: string; text: string }[]>;
  setInputMessage: React.Dispatch<React.SetStateAction<string>>;
  handleEditFAQ: (faq: { title: string; text: string }) => void;
  handleDeleteSaveReply: (title: string) => void;
  setShowFAQModal: React.Dispatch<React.SetStateAction<boolean>>;
  setShowManageFAQModal: React.Dispatch<React.SetStateAction<boolean>>;
}

// Animation variants for FAQ box - simplified
const modalVariants = {
  hidden: {
    opacity: 0,
    y: 10,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    }
  },
  exit: {
    opacity: 0,
    y: 10,
    transition: { duration: 0.15, ease: "easeIn" }
  },
};

const FAQModal: React.FC<FAQModalProps> = ({
  isLoadingSavedReply,
  faqsRef,
  setInputMessage,
  handleEditFAQ,
  handleDeleteSaveReply,
  setShowFAQModal,
  setShowManageFAQModal,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleModalClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (modalRef.current && !modalRef.current.contains(target)) {
        setShowFAQModal(false);
      }
    };

    document.addEventListener("mousedown", handleModalClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleModalClickOutside);
    };
  }, [setShowFAQModal]);

  return (
    <motion.div
      ref={modalRef}
      className="absolute md:bottom-16 md:shadow-none shadow-lg rounded-[20px] left-[2vh] bottom-[80px] md:left-0 bg-white border border-gray-300 md:rounded-lg p-4 w-[calc(100vw-2rem)] md:w-[300px] max-h-[300px] overflow-y-auto z-10"
      variants={modalVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      <h3 className="text-lg font-semibold mb-3">
        Select Your FAQ
      </h3>
      {isLoadingSavedReply ? (
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 w-4 bg-gray-200 rounded ml-auto"></div>
            </div>
          ))}
        </div>
      ) : (faqsRef.current || []).length > 0 ? (
        <div>
          {(faqsRef.current || []).map(
            (faq: { title: string; text: string }, index: number) => (
              <div
                key={index}
                className="flex items-center mb-3"
              >
                <label
                  onClick={() => setInputMessage(faq.text)}
                  htmlFor={`faq-${index}`}
                  className="text-sm grow pr-2 break-words cursor-pointer hover:text-blue-600 transition-colors"
                >
                  {faq.text}
                </label>
                <button
                  onClick={() => {
                    handleEditFAQ({ title: faq.title, text: faq.text });
                    setShowFAQModal(false);
                  }}
                  className="text-gray-400 hover:text-gray-600 shrink-0 transition-colors"
                >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                  />
                    </svg>
                  </button>
                  <button
                    onClick={() => {
                      handleDeleteSaveReply(faq.title);
                      setShowFAQModal(false);
                    }}
                    className="text-gray-400 hover:text-gray-600 shrink-0 transition-colors"
                  >
                    <svg
                      className="h-4 w-4 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                  </button>
                </div>
              )
            )}
        </div>
      ) : faqsRef.current?.length === 0 ? (
        <p className="text-sm text-gray-500">No FAQs found</p>
      ) : (
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 w-4 bg-gray-200 rounded ml-auto"></div>
            </div>
          ))}
        </div>
      )}
      <button
        onClick={() => {
          setShowManageFAQModal(true);
          setShowFAQModal(false);
        }}
        className="flex items-center text-blue-500 hover:text-blue-700 text-sm font-medium mt-2 transition-colors"
      >
        <svg
          className="h-4 w-4 mr-1"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 4v16m8-8H4"
          />
        </svg>
        Add
      </button>
    </motion.div>
  );
};

export default FAQModal;
