"use client";

import React, { useEffect, useMemo, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { ChatMessage, Message } from "../types";

// Type definition for the WebSocket seen status message
interface SeenStatusMessage {
  object: string;
  entry: Array<{
    time: number;
    id: string;
    messaging: Array<{
      sender: {
        id: string;
      };
      recipient: {
        id: string;
      };
      timestamp: number;
      read: {
        mid: string;
      };
    }>;
  }>;
}

interface ChatSectionProps {
  selectedChat: Message;
  selectedSocial: {
    platform: string;
    username: string;
    profile_photo?: string;
    social_id: string;
  };
  chatMessages: ChatMessage[];
  isLoadingChatMessages: boolean;
  isTransitioningChat: boolean;
  isLoadingMoreMessages: boolean;
  showScrollToBottom: boolean;
  scrollToBottom: () => void;
  setReplyingToMessage: React.Dispatch<
    React.SetStateAction<ChatMessage | null>
  >;
  handleReaction?: (
    messageId: string,
    emoji: string,
    unreact?: boolean
  ) => void;
  handleSeenStatus?: (seenStatusData: SeenStatusMessage) => void;
  chatContainerRef: React.RefObject<HTMLDivElement>;
  messagesEndRef: React.RefObject<HTMLDivElement>;
  topLoaderRef: React.RefObject<HTMLDivElement>;
  bottomLoaderRef: React.RefObject<HTMLDivElement>;
  animatingMessageIds: Set<string>;
}

const ChatSection: React.FC<ChatSectionProps> = ({
  selectedChat,
  selectedSocial,
  chatMessages,
  isLoadingChatMessages,
  isTransitioningChat,
  isLoadingMoreMessages,
  showScrollToBottom,
  scrollToBottom,
  setReplyingToMessage,
  handleReaction,
  handleSeenStatus,
  chatContainerRef,
  messagesEndRef,
  topLoaderRef,
  bottomLoaderRef,
  animatingMessageIds,
}) => {
  // Animation variants for hot reload messages - simplified with only scale and opacity
  const hotReloadMessageVariants = {
    initial: {
      opacity: 0,
      scale: 0.95,
    },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      transition: {
        duration: 0.2,
        ease: "easeInOut",
      },
    },
  };

  // State for context menu
  const [contextMenu, setContextMenu] = React.useState<{
    visible: boolean;
    x: number;
    y: number;
    messageId: string;
  }>({
    visible: false,
    x: 0,
    y: 0,
    messageId: "",
  });

  // Mobile detection
  const [isMobile, setIsMobile] = React.useState(false);

  // Long press state for mobile
  const [longPressTimer, setLongPressTimer] =
    React.useState<NodeJS.Timeout | null>(null);
  const [isLongPressing, setIsLongPressing] = React.useState(false);

  // Avatar fallback state - memoized to prevent unnecessary re-renders
  const [failedAvatars, setFailedAvatars] = React.useState<Set<string>>(
    new Set()
  );

  // Memoized avatar URL cache to prevent recalculation on every render
  const avatarUrlCache = useMemo(() => new Map<string, string>(), []);

  // Memoized function to get avatar URL with caching
  const getAvatarUrl = useCallback(
    (originalUrl: string | undefined, fallbackName: string): string => {
      const cacheKey = `${originalUrl || "undefined"}-${fallbackName}`;

      // Check cache first
      if (avatarUrlCache.has(cacheKey)) {
        return avatarUrlCache.get(cacheKey)!;
      }

      let resultUrl: string;

      if (!originalUrl || failedAvatars.has(originalUrl)) {
        resultUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(
          fallbackName
        )}&background=random&color=fff`;
      } else if (
        originalUrl.startsWith("http") ||
        originalUrl.startsWith("https")
      ) {
        resultUrl = originalUrl;
      } else {
        resultUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(
          fallbackName
        )}&background=random&color=fff`;
      }

      // Cache the result
      avatarUrlCache.set(cacheKey, resultUrl);
      return resultUrl;
    },
    [failedAvatars, avatarUrlCache]
  );

  // Memoized function to handle avatar load errors
  const handleAvatarError = useCallback(
    (originalUrl: string | undefined) => {
      if (originalUrl) {
        setFailedAvatars((prev) => {
          const newSet = new Set([...prev, originalUrl]);
          // Clear cache entries for this failed URL
          for (const [key] of avatarUrlCache.entries()) {
            if (key.startsWith(originalUrl)) {
              avatarUrlCache.delete(key);
            }
          }
          return newSet;
        });
      }
    },
    [avatarUrlCache]
  );

  React.useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // We'll use these states when we implement the full Auto Answer functionality
  // For now, we're just removing the Robot Answer button and adding the Auto Answer button

  // Handle right click on message - now only for desktop since mobile uses click
  const handleContextMenu = (e: React.MouseEvent, messageId: string) => {
    e.preventDefault();

    // Only handle right-click on desktop, mobile uses regular click
    if (!isMobile) {
      const x = e.clientX;
      const y = e.clientY;

      setContextMenu({
        visible: true,
        x,
        y,
        messageId,
      });
    }
  };

  // Handle long press start for mobile - now used as alternative to click
  const handleTouchStart = (e: React.TouchEvent, messageId: string) => {
    if (!isMobile) return;

    const timer = setTimeout(() => {
      setIsLongPressing(true);
      const touch = e.touches[0];
      const x = Math.min(touch.clientX, window.innerWidth - 150);
      const y = Math.max(touch.clientY - 50, 50);

      setContextMenu({
        visible: true,
        x,
        y,
        messageId,
      });

      // Add haptic feedback if available
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }, 300); // Reduced to 300ms for faster response

    setLongPressTimer(timer);
  };

  // Handle touch end - clear long press timer and prevent click if long press occurred
  const handleTouchEnd = (e: React.TouchEvent) => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }

    // If long press occurred, prevent the click event
    if (isLongPressing) {
      e.preventDefault();
      setIsLongPressing(false);
      return;
    }

    setIsLongPressing(false);
  };

  // Handle touch move - cancel long press if user moves finger
  const handleTouchMove = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
    setIsLongPressing(false);
  };

  // Helper function to check if a message is from our user using chat-based identification
  const isMessageFromOurUser = (message: ChatMessage): boolean => {
    // Use chat-based identification: if the message sender is NOT the current chat partner, it's from our user
    return message.from?.id !== selectedChat?.chatid;
  };

  // Helper function to check if the current user has reacted to a message
  const hasUserReactedToMessage = (message: ChatMessage): boolean => {
    if (!message.reactions?.data || !selectedSocial?.social_id) return false;

    // Check all possible ways the user might be identified in reactions
    return message.reactions.data.some((reaction) =>
      reaction.users?.some((user) => {
        // Check by social_id (primary identifier)
        if (user.id === selectedSocial.social_id) return true;

        // Check by username if IDs don't match but usernames do
        if (user.username === selectedSocial.username) return true;

        return false;
      })
    );
  };

  // Handle reaction toggle (add or remove)
  const toggleReaction = (
    messageId: string,
    emoji: string,
    element?: HTMLElement
  ) => {
    if (!handleReaction) return;

    // Find the message - check both id and serverMessageId
    const message = chatMessages.find(
      (msg) => msg.id === messageId || msg.serverMessageId === messageId
    );

    if (!message) {
      console.warn("Message not found for reaction:", messageId);
      return;
    }

    // Use our helper function to check if user has reacted
    const hasUserReacted = hasUserReactedToMessage(message);

    // Log for debugging
    console.log("Toggle reaction:", {
      messageId,
      hasUserReacted,
      userSocialId: selectedSocial?.social_id,
      reactions: message.reactions,
    });

    if (hasUserReacted && element) {
      // Apply fade-out animation before removing
      element.style.animation = "reactionFadeOut 0.3s forwards";

      // We could check if this is the last reaction from this user
      // but we don't need it for now - keeping the code simple

      // Check if this is the last reaction overall
      const totalReactions =
        message.reactions?.data?.reduce(
          (count, r) => count + (r.users?.length || 0),
          0
        ) || 0;

      const isLastReactionOverall = totalReactions === 1;

      // Wait for animation to complete before removing
      setTimeout(() => {
        // Remove the reaction
        handleReaction(messageId, emoji, true);

        // If this was the last reaction, also fade out the container
        if (isLastReactionOverall) {
          const container = document.getElementById(
            `reaction-container-${messageId}`
          );
          if (container) {
            container.style.animation = "reactionFadeOut 0.3s forwards";
          }
        }
      }, 300);
    } else {
      // Toggle the reaction (remove if exists, add if not)
      handleReaction(messageId, emoji, hasUserReacted);
    }
  };

  // The parent component directly calls handleSeenStatus when it receives WebSocket messages
  // We've removed the local processSeenStatus function since it's no longer needed

  // Handle click outside to close context menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // Don't close if clicking on a message or the context menu itself
      if (
        target.closest("[data-message-id]") ||
        target.closest(".fixed") || // Context menu has fixed positioning
        target.closest("button") // Don't close when clicking buttons
      ) {
        return;
      }

      setContextMenu((prev) => ({ ...prev, visible: false }));
    };
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  // Cleanup long press timer on unmount
  useEffect(() => {
    return () => {
      if (longPressTimer) {
        clearTimeout(longPressTimer);
      }
    };
  }, [longPressTimer]);

  // We don't need to handle WebSocket messages here anymore
  // The parent component will call handleSeenStatus when it receives a real WebSocket message
  // This removes the simulation that was showing "Seen" status after 5 seconds
  // Now the "Seen" status will only appear when a real WebSocket message is received

  // Group messages by date - using approach from old-page.tsx
  const groupedMessages = React.useMemo(() => {
    const groups: { [key: string]: ChatMessage[] } = {};

    // Make a copy of the messages array to avoid mutating the original
    const messagesCopy = [...chatMessages];

    // Sort messages by timestamp in ascending order (oldest first)
    messagesCopy.sort((a, b) => {
      const timeA = a.timestamp || new Date(a.time).getTime();
      const timeB = b.timestamp || new Date(b.time).getTime();
      return timeA - timeB; // Sort in chronological order
    });

    messagesCopy.forEach((message) => {
      const date = message.timestamp
        ? new Date(message.timestamp)
        : new Date(message.time);

      // Format date as YYYY-MM-DD for consistent grouping
      const dateKey = date.toISOString().split("T")[0] as string;

      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }

      groups[dateKey].push(message);
    });

    return groups;
  }, [chatMessages]);

  // Sort dates in chronological order (oldest to newest)
  const sortedDates = React.useMemo(() => {
    return Object.keys(groupedMessages).sort((a, b) => {
      // Simple date string comparison (YYYY-MM-DD format sorts chronologically)
      return a.localeCompare(b);
    });
  }, [groupedMessages]);

  // Find the last message from the current user
  const lastUserMessage = React.useMemo(() => {
    // Flatten all messages from all dates
    const allMessages = Object.values(groupedMessages).flat();

    // Filter messages from the current user using chat-based identification
    const userMessages = allMessages.filter((msg) => isMessageFromOurUser(msg));

    // Sort by timestamp in descending order (newest first)
    const sortedUserMessages = [...userMessages].sort((a, b) => {
      const timeA = a.timestamp || new Date(a.time).getTime();
      const timeB = b.timestamp || new Date(b.time).getTime();
      return timeB - timeA;
    });

    // Return the newest message (first in the sorted array)
    return sortedUserMessages.length > 0 ? sortedUserMessages[0] : null;
  }, [groupedMessages, selectedSocial]);

  // Check if the last message in the chat is from the current user
  const isLastMessageFromUser = React.useMemo(() => {
    // Flatten all messages from all dates
    const allMessages = Object.values(groupedMessages).flat();

    // Sort all messages by timestamp in ascending order (oldest first)
    const sortedMessages = [...allMessages].sort((a, b) => {
      const timeA = a.timestamp || new Date(a.time).getTime();
      const timeB = b.timestamp || new Date(b.time).getTime();
      return timeA - timeB;
    });

    // Get the last message in the chat
    const lastMessage =
      sortedMessages.length > 0
        ? sortedMessages[sortedMessages.length - 1]
        : null;

    // Check if the last message is from the current user using chat-based identification
    return lastMessage ? isMessageFromOurUser(lastMessage) : false;
  }, [groupedMessages, selectedSocial]);

  if (isLoadingChatMessages && !isTransitioningChat) {
    return (
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {[...Array(5)].map((_, index) => (
          <div key={index} className="flex items-start animate-pulse">
            <div className="w-8 h-8 bg-gray-200 rounded-full mr-2"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
              <div className="h-10 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div
      ref={chatContainerRef}
      className="flex-1 w-full overflow-y-auto overflow-x-hidden p-4 relative min-h-0 "
      style={{
        paddingBottom: isMobile ? "160px" : "0px", // Add more bottom padding on mobile for fixed input
        height: "100%",
      }}
    >
      {/* Add animation styles */}
      <style jsx global>{`
        /* Animation for reactions */
        @keyframes reactionPop {
          0% {
            transform: scale(0);
            opacity: 0;
          }
          70% {
            transform: scale(1.2);
            opacity: 1;
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @keyframes reactionFadeOut {
          0% {
            transform: scale(1);
            opacity: 1;
          }
          100% {
            transform: scale(0);
            opacity: 0;
          }
        }

        /* Reaction container styles */
        .reactions-container {
          position: absolute;
          bottom: -14px;
          right: 0;
          background-color: white;
          border-radius: 16px;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          padding: 2px 6px;
          display: flex;
          align-items: center;
          border: 1px solid #f0f0f0;
          z-index: 5;
          min-height: 18px;
          min-width: 22px;
          justify-content: center;
        }

        /* Individual reaction styles */
        .reaction-item {
          position: relative;
          margin: 0 1px;
          animation: reactionPop 0.3s ease-out;
          font-size: 0.9em;
        }

        /* User's own reaction */
        .reaction-item[data-user-reacted="true"] {
          transform: scale(1.05);
        }

        /* Media queries for responsive design */
        @media (max-width: 640px) {
          .reactions-container {
            padding: 1px 4px;
            min-height: 16px;
            min-width: 18px;
            bottom: -5px;
          }

          .reaction-item {
            font-size: 0.8em;
            margin: 0;
          }
        }
      `}</style>
      {/* Top loader for loading older messages */}
      <div ref={topLoaderRef} className="h-10 flex items-center justify-center">
        {isLoadingMoreMessages && (
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900"></div>
        )}
      </div>

      {/* Show transition state */}
      {isTransitioningChat && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-20">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900"></div>
        </div>
      )}

      {/* Render message groups by date */}
      {sortedDates.map((dateKey) => (
        <div key={dateKey} data-message-group data-date={dateKey}>
          <div className="flex justify-center my-4">
            <div className="bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">
              {new Date(dateKey).toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </div>
          </div>

          {/* Render messages for this date */}
          {groupedMessages[dateKey]?.map((message) => {
            const isAnimating = animatingMessageIds.has(message.id);

            return (
              <motion.div
                key={message.id}
                data-message-id={message.id}
                data-is-our-message={
                  isMessageFromOurUser(message) ? "true" : "false"
                }
                className={`flex mb-4 ${
                  isMessageFromOurUser(message)
                    ? "justify-end"
                    : "justify-start"
                }`}
                variants={isAnimating ? hotReloadMessageVariants : undefined}
                initial={isAnimating ? "initial" : undefined}
                animate={isAnimating ? "animate" : undefined}
                layout
              >
                {/* Only show avatar for messages from other users */}
                {!isMessageFromOurUser(message) && (
                  <div className="shrink-0 mr-2">
                    <Image
                      src={getAvatarUrl(
                        selectedChat.avatar,
                        message.sender || "User"
                      )}
                      alt={message.sender}
                      width={32}
                      height={32}
                      className="w-8 h-8 rounded-full"
                      onError={() => {
                        handleAvatarError(selectedChat.avatar);
                      }}
                      priority={false}
                      loading="lazy"
                      unoptimized={getAvatarUrl(
                        selectedChat.avatar,
                        message.sender || "User"
                      ).includes("ui-avatars.com")}
                    />
                  </div>
                )}

                {/* Show profile picture for our own messages */}
                {isMessageFromOurUser(message) && (
                  <div className="shrink-0 order-last ml-2">
                    <Image
                      src={getAvatarUrl(
                        selectedSocial?.profile_photo,
                        selectedSocial?.username || "User"
                      )}
                      alt="Me"
                      width={32}
                      height={32}
                      className="w-8 h-8 rounded-full"
                      onError={() => {
                        handleAvatarError(selectedSocial?.profile_photo);
                      }}
                      priority={false}
                      loading="lazy"
                      unoptimized={getAvatarUrl(
                        selectedSocial?.profile_photo,
                        selectedSocial?.username || "User"
                      ).includes("ui-avatars.com")}
                    />
                  </div>
                )}

                <div
                  className={`max-w-[70%] ${
                    isMessageFromOurUser(message)
                      ? "relative bg-blue-500 text-white rounded-tr-none rounded-2xl p-3 snap-always snap-center scroll-auto"
                      : "relative bg-gray-200 rounded-tl-none rounded-2xl p-3 snap-always snap-center scroll-auto"
                  } p-3 relative ${message.isPreview ? "opacity-70" : ""} ${
                    isLongPressing ? "scale-95 transition-transform" : ""
                  }`}
                  onClick={(e) => {
                    // Show context menu on click for both mobile and desktop (if not from long press)
                    if (!isLongPressing) {
                      e.stopPropagation(); // Use stopPropagation instead of preventDefault
                      const x = Math.min(e.clientX, window.innerWidth - 150);
                      const y = Math.max(e.clientY - 50, 50);

                      setContextMenu({
                        visible: true,
                        x,
                        y,
                        messageId: message.id,
                      });
                    }
                  }}
                  onContextMenu={(e) => handleContextMenu(e, message.id)}
                  onTouchStart={(e) => handleTouchStart(e, message.id)}
                  onTouchEnd={handleTouchEnd}
                  onTouchMove={handleTouchMove}
                >
                  {/* Reply reference if this is a reply */}
                  {message.replyTo && (
                    <div
                      className={`text-xs mb-1 p-1 rounded ${
                        isMessageFromOurUser(message)
                          ? "bg-blue-600 text-blue-100"
                          : "bg-gray-300 text-gray-700"
                      }`}
                    >
                      <div className="font-medium">
                        ↩️ {message.replyTo.sender}
                      </div>
                      <div className="truncate">{message.replyTo.content}</div>
                    </div>
                  )}

                  {/* Message content */}
                  <p className="whitespace-pre-wrap break-words text-sm lg:text-base">
                    {message.content}
                  </p>

                  {/* Shared image if any */}
                  {message.sharedImage && (
                    <div className="mt-2">
                      <Image
                        src={message.sharedImage}
                        alt="Shared content"
                        width={300}
                        height={200}
                        className="max-w-full rounded-lg"
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = "none";
                        }}
                        priority={false}
                        loading="lazy"
                      />
                    </div>
                  )}

                  {/* Time and seen status */}
                  <div
                    className={`flex items-center justify-between text-xs mt-1 ${
                      isMessageFromOurUser(message)
                        ? "opacity-60"
                        : "text-gray-500"
                    }`}
                  >
                    <span className="flex items-center">
                      {/* Only show sender name for messages from other users */}
                      {!isMessageFromOurUser(message) && (
                        <span className="font-medium mr-1"></span>
                      )}
                      {(message.timestamp
                        ? new Date(message.timestamp)
                        : new Date(message.time)
                      ).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>

                    {/* We'll remove the seen status from inside the message bubble */}
                  </div>

                  {/* Reactions */}
                  {message.reactions?.data?.some(
                    (r) => r.users && r.users.length > 0
                  ) && (
                    <div
                      className="reactions-container"
                      id={`reaction-container-${message.id}`}
                    >
                      {message.reactions.data
                        .filter(
                          (reaction) =>
                            reaction.users && reaction.users.length > 0
                        )
                        .map((reaction, index) => {
                          // Check if current user has reacted using our helper function
                          const hasUserReacted = reaction.users?.some(
                            (user) => {
                              // Check by social_id (primary identifier)
                              if (user.id === selectedSocial?.social_id)
                                return true;

                              // Check by username if IDs don't match but usernames do
                              if (user.username === selectedSocial?.username)
                                return true;

                              return false;
                            }
                          );

                          return (
                            <div
                              key={index}
                              className="reaction-item"
                              data-user-reacted={
                                hasUserReacted ? "true" : "false"
                              }
                            >
                              {/* Emoji */}
                              <span className="text-base text-red-600 sm:text-sm">
                                {reaction.emoji}
                              </span>
                            </div>
                          );
                        })}
                    </div>
                  )}

                  {/* Error indicator */}
                  {message.error && (
                    <div className="absolute -bottom-6 left-0 text-red-500 text-xs">
                      Failed to send. Tap to retry.
                    </div>
                  )}

                  {/* Loading indicator for preview */}
                  {message.isPreview && (
                    <div className="absolute bottom-1 right-1">
                      <div className="animate-pulse w-2 h-2 bg-current rounded-full"></div>
                    </div>
                  )}
                </div>

                {/* Reply button */}
                {!isMessageFromOurUser(message) && (
                  <button
                    onClick={() => setReplyingToMessage(message)}
                    className="opacity-0 group-hover:opacity-100 ml-2 text-gray-500 hover:text-gray-700"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.707 3.293a1 1 0 010 1.414L5.414 7H11a7 7 0 017 7v2a1 1 0 11-2 0v-2a5 5 0 00-5-5H5.414l2.293 2.293a1 1 0 11-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                )}
              </motion.div>
            );
          })}

          {/* Add "Seen" status indicator after the last message from the current user */}
          {/* The seen status will only appear when we receive a WebSocket message with read status */}
          {/* Only show the seen status if the last message in the chat is from the current user */}
          {lastUserMessage &&
            lastUserMessage.seen &&
            isLastMessageFromUser &&
            dateKey ===
              new Date(lastUserMessage.timestamp || lastUserMessage.time)
                .toISOString()
                .split("T")[0] && (
              <div className="flex justify-end mb-4 mr-10">
                <div className="flex items-center text-xs text-gray-500">
                  <span className="mr-1">Seen</span>
                </div>
              </div>
            )}
        </div>
      ))}

      {/* Context Menu */}
      <AnimatePresence>
        {contextMenu.visible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.15, ease: "easeOut" }}
            className={`fixed bg-white shadow-lg border border-gray-200 ${
              isMobile
                ? "rounded-xl p-2 min-w-[100px]"
                : "rounded-full p-1 w-10 h-10 flex items-center justify-center"
            } text-sm backdrop-blur-sm bg-white/95`}
            style={{
              top: `${contextMenu.y}px`,
              left: `${contextMenu.x}px`,
              maxWidth: "calc(100vw - 20px)",
              zIndex: 30,
              position: "fixed",
            }}
          >
            <div
              className={`${
                isMobile ? "flex flex-col" : "flex items-center justify-center"
              }`}
            >
              {/* Find the message to check if user already reacted */}
              {(() => {
                // Find the message by ID
                const message = chatMessages.find(
                  (msg) =>
                    msg.id === contextMenu.messageId ||
                    msg.serverMessageId === contextMenu.messageId
                );

                // Check if user already reacted using our helper function
                const hasUserReaction = message
                  ? hasUserReactedToMessage(message)
                  : false;

                return hasUserReaction ? (
                  // Show remove reaction button if user has already reacted
                  <motion.button
                    whileHover={{ scale: isMobile ? 1 : 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className={`${
                      isMobile
                        ? "px-3 py-2 text-left hover:bg-red-50 flex items-center text-red-600 rounded-lg transition-colors w-full"
                        : "w-8 h-8 rounded-full hover:bg-red-50 flex items-center justify-center text-red-600 transition-colors"
                    }`}
                    onClick={() => {
                      toggleReaction(contextMenu.messageId, "❤️");
                      setContextMenu((prev) => ({ ...prev, visible: false }));
                    }}
                  >
                    <span
                      className={`${isMobile ? "mr-2 text-base" : "text-sm"}`}
                    >
                      ❤️
                    </span>
                    {isMobile && (
                      <span className="flex items-center text-xs font-medium">
                        Remove{" "}
                        <span className="ml-1 text-xs font-bold bg-red-500 text-white rounded-full w-3 h-3 flex items-center justify-center">
                          ×
                        </span>
                      </span>
                    )}
                  </motion.button>
                ) : (
                  // Show add reaction button if user hasn't reacted
                  <motion.button
                    whileHover={{ scale: isMobile ? 1 : 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className={`${
                      isMobile
                        ? "px-3 py-2 text-left hover:bg-gray-50 flex items-center rounded-lg transition-colors w-full"
                        : "w-8 h-8 rounded-full hover:bg-gray-50 flex items-center justify-center transition-colors"
                    }`}
                    onClick={() => {
                      toggleReaction(contextMenu.messageId, "❤️");
                      setContextMenu((prev) => ({ ...prev, visible: false }));
                    }}
                  >
                    <span
                      className={`${isMobile ? "mr-2 text-base" : "text-sm"}`}
                    >
                      ❤️
                    </span>
                    {isMobile && (
                      <span className="text-xs font-medium text-gray-700">
                        Add
                      </span>
                    )}
                  </motion.button>
                );
              })()}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Bottom loader for loading newer messages */}
      <div
        ref={bottomLoaderRef}
        className="h-10 flex items-center justify-center"
      >
        {isLoadingMoreMessages && (
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900"></div>
        )}
      </div>

      {/* Scroll to bottom button */}
      {showScrollToBottom && (
        <button
          onClick={scrollToBottom}
          className="absolute bottom-4 right-4 bg-gray-800 text-white rounded-full p-2 shadow-lg"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      )}

      {/* End of messages marker for scrolling */}
      <div ref={messagesEndRef} />

      {/* We don't need to render the Ready Text Modal here as it's handled by the FAQModal component */}
    </div>
  );
};

// Memoize the entire ChatSection component to prevent unnecessary re-renders
export default React.memo(ChatSection, (prevProps, nextProps) => {
  // Custom comparison function to optimize re-renders
  return (
    prevProps.selectedChat?.id === nextProps.selectedChat?.id &&
    prevProps.chatMessages.length === nextProps.chatMessages.length &&
    prevProps.isLoadingChatMessages === nextProps.isLoadingChatMessages &&
    prevProps.isTransitioningChat === nextProps.isTransitioningChat &&
    prevProps.isLoadingMoreMessages === nextProps.isLoadingMoreMessages &&
    prevProps.showScrollToBottom === nextProps.showScrollToBottom &&
    prevProps.selectedSocial?.social_id ===
      nextProps.selectedSocial?.social_id &&
    prevProps.selectedSocial?.profile_photo ===
      nextProps.selectedSocial?.profile_photo &&
    prevProps.animatingMessageIds.size === nextProps.animatingMessageIds.size &&
    // Check if the last few messages are the same (most likely to change)
    JSON.stringify(prevProps.chatMessages.slice(-5)) ===
      JSON.stringify(nextProps.chatMessages.slice(-5))
  );
});
