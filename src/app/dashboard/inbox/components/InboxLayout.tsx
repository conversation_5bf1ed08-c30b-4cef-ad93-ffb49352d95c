"use client";

import React, { ReactNode, useState, useEffect } from "react";

interface InboxLayoutProps {
  isMobile: boolean;
  children: ReactNode;
  renderMobileView?: () => ReactNode;
}

const InboxLayout: React.FC<InboxLayoutProps> = ({
  isMobile,
  children,
  renderMobileView,
}) => {
  return (
    <div
      className={`${
        isMobile ? "h-[92vh] mt-[8vh]" : "h-full"
      } overflow-hidden bg-white w-full overflow-x-hidden ${
        isMobile ? "p-0" : "p-4 xl:px-8"
      }`}
    >
      {isMobile ? (
        <div className="h-full w-full flex flex-col relative">{children}</div>
      ) : (
        <div className="h-full w-full flex flex-row gap-4 xl:gap-6">{children}</div>
      )}
    </div>
  );
};

export default InboxLayout;
