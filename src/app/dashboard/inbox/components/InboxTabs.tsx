"use client";

import React from "react";
import { Message, Comment } from "../types";

interface InboxTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  unreadDirectsCount: number;
  unreadCommentsCount: number;
  messages: Message[];
  comments: Comment[];
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  setComments: React.Dispatch<React.SetStateAction<Comment[]>>;
  unreadDirects: string[];
  unreadComments: string[];
}

const InboxTabs: React.FC<InboxTabsProps> = ({
  activeTab,
  setActiveTab,
  unreadDirectsCount,
  unreadCommentsCount,
  messages,
  comments,
  setMessages,
  setComments,
  unreadDirects,
  unreadComments,
}) => {
  return (
    <div className="w-full px-2 mb-2">
      <div className="flex mb-3 w-full max-w-[280px] mx-auto md:mx-0">
        <button
          className={`mr-2 px-3 py-1.5 rounded-[8px] flex items-center flex-1 justify-center ${
            activeTab === "direct"
              ? "bg-white text-gray-900 shadow-sm border border-[#0D3E4F]"
              : "bg-gray-100 text-gray-600"
          }`}
          onClick={() => {
            // Update active tab
            setActiveTab("direct");

            // Apply unread states to messages
            if (messages.length > 0) {
              setMessages((prevMessages) => {
                return prevMessages.map((msg) => {
                  const chatId = msg.chatid;
                  // Check if this conversation has unread messages
                  const hasUnread = unreadDirects.some((id) => {
                    return (
                      id.includes(chatId || "") || id === msg.conversationId
                    );
                  });

                  if (hasUnread) {
                    return { ...msg, hasUnread: true };
                  }
                  return msg;
                });
              });
            }
          }}
        >
          <span>direct</span>
          {unreadDirectsCount > 0 && (
            <span className="ml-2 bg-blue-500 text-white text-xs rounded-full px-2 py-0.5 flex items-center justify-center min-w-[20px] h-[20px]">
              {unreadDirectsCount}
            </span>
          )}
        </button>
        <button
          className={`px-3 py-1.5 rounded-[8px] flex items-center flex-1 justify-center ${
            activeTab === "comments"
              ? "bg-white text-gray-950 shadow-sm border border-[#0D3E4F]"
              : "bg-gray-100 text-gray-600"
          }`}
          onClick={() => {
            // Update active tab
            setActiveTab("comments");

            // Apply unread states to comments
            if (comments.length > 0) {
              setComments((prevComments) => {
                return prevComments.map((comment) => {
                  const postId = comment.id;
                  // Check if this post has unread comments
                  const hasUnread = unreadComments.some((id) => {
                    return id.includes(postId) || id === postId;
                  });

                  if (hasUnread) {
                    return { ...comment, isUnread: true };
                  }
                  return comment;
                });
              });
            }
          }}
        >
          <span>comments</span>
          {unreadCommentsCount > 0 && (
            <span className="ml-2 bg-blue-500 text-white text-xs rounded-full px-2 py-0.5">
              {unreadCommentsCount}
            </span>
          )}
        </button>
      </div>
      <div className="w-full border-b border-gray-200" />
    </div>
  );
};

export default InboxTabs;
