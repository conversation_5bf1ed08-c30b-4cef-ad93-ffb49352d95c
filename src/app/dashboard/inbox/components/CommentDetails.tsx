"use client";

import React, { useRef, useState, useEffect, useCallback, memo } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { Comment } from "../types";
import { formatPostDate } from "../utils";
import { logger } from "../utils";
import { FaHeart, FaRegComment } from "react-icons/fa6";

// Memoized comment item component to prevent unnecessary re-renders
const CommentItem = memo(
  ({
    comment,
    getAvatarUrl,
    handleAvatarError,
    showReplyInput,
    replyingTo,
    setReplyingTo,
    setShowReplyInput,
    replyInputRef,
    replyText,
    setReplyText,
    handleKeyPress,
    handleReplySubmit,
    isReplying,
  }: {
    comment: Comment;
    getAvatarUrl: (
      originalUrl: string | undefined,
      fallbackName: string
    ) => string;
    handleAvatarError: (originalUrl: string | undefined) => void;
    showReplyInput: boolean;
    replyingTo: string | null;
    setReplyingTo: React.Dispatch<React.SetStateAction<string | null>>;
    setShowReplyInput: React.Dispatch<React.SetStateAction<boolean>>;
    replyInputRef: React.RefObject<HTMLInputElement>;
    replyText: string;
    setReplyText: React.Dispatch<React.SetStateAction<string>>;
    handleKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    handleReplySubmit: () => void;
    isReplying: boolean;
  }) => (
    <div
      key={comment.id}
      className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"
    >
      <Image
        src={getAvatarUrl(
          comment.from?.profile_picture,
          comment.from?.username || "User"
        )}
        alt={comment.from?.username}
        width={32}
        height={32}
        className="w-8 h-8 rounded-full"
        onError={() => {
          handleAvatarError(comment.from?.profile_picture);
        }}
      />
      <div className="flex-1">
        <div className="flex justify-between items-start">
          <span className="font-medium text-sm">{comment.from?.username}</span>
          <span className="text-xs text-gray-500">
            {new Date(comment.timestamp).toLocaleString()}
          </span>
        </div>
        <p className="text-sm mt-1">{comment.text}</p>
        <div className="flex items-center mt-2">
          <button
            onClick={() => {
              setReplyingTo(comment.id);
              setShowReplyInput(true);
              setTimeout(() => {
                replyInputRef.current?.focus();
              }, 100);
            }}
            className="text-xs text-blue-500 hover:text-blue-700"
          >
            Reply
          </button>
        </div>

        {/* Show replies if any */}
        {comment.replies &&
          comment.replies.data &&
          comment.replies.data.length > 0 && (
            <div className="mt-3 pl-4 border-l-2 border-gray-200 space-y-3">
              {comment.replies.data.map((reply) => (
                <div key={reply.id} className="flex items-start space-x-2">
                  <Image
                    src={getAvatarUrl(
                      reply.from?.profile_picture,
                      reply.from?.username || "User"
                    )}
                    alt={reply.from?.username}
                    width={24}
                    height={24}
                    className="w-6 h-6 rounded-full"
                    onError={() => {
                      handleAvatarError(reply.from?.profile_picture);
                    }}
                  />
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <span className="font-medium text-xs">
                        {reply.from?.username}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(reply.timestamp).toLocaleString()}
                      </span>
                    </div>
                    <p className="text-xs mt-1">{reply.text}</p>
                  </div>
                </div>
              ))}
            </div>
          )}

        {/* Reply input */}
        {showReplyInput && replyingTo === comment.id && (
          <motion.div
            className="mt-3 flex items-center gap-2"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            <motion.input
              ref={replyInputRef}
              type="text"
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder={`Reply to ${comment.from?.username}...`}
              className="flex-1 bg-gray-50 border border-gray-200 rounded-lg py-2.5 px-3 text-sm focus:outline-none focus:bg-white focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all duration-200 ease-in-out placeholder-gray-400"
              whileFocus={{ scale: 1.01 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
            />
            <motion.button
              onClick={handleReplySubmit}
              disabled={!replyText.trim() || isReplying}
              className={`py-2.5 px-4 rounded-lg text-sm font-medium transition-all duration-200 ease-in-out ${
                !replyText.trim() || isReplying
                  ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                  : "bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700 shadow-sm hover:shadow-md"
              }`}
              whileHover={
                !replyText.trim() || isReplying
                  ? {}
                  : {
                      scale: 1.05,
                      transition: { duration: 0.2, ease: "easeOut" },
                    }
              }
              whileTap={
                !replyText.trim() || isReplying
                  ? {}
                  : {
                      scale: 0.95,
                      transition: {
                        duration: 0.1,
                        ease: "easeInOut",
                      },
                    }
              }
            >
              {isReplying ? (
                <motion.div
                  className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                />
              ) : (
                <motion.span
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  Send
                </motion.span>
              )}
            </motion.button>
          </motion.div>
        )}
      </div>
    </div>
  )
);

CommentItem.displayName = "CommentItem";

// Memoized comment input component to isolate it from the comments list
const CommentInputSection = memo(
  ({
    commentInputRef,
    commentText,
    setCommentText,
    handleCommentKeyPress,
    handleCommentSend,
    isSubmittingComment,
  }: {
    commentInputRef: React.RefObject<HTMLInputElement>;
    commentText: string;
    setCommentText: React.Dispatch<React.SetStateAction<string>>;
    handleCommentKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    handleCommentSend: () => void;
    isSubmittingComment: boolean;
  }) => (
    <motion.div
      className="w-full p-4 border-t bg-gray-100 shadow-lg"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <div className="flex items-center gap-x-3">
        <motion.input
          ref={commentInputRef}
          type="text"
          value={commentText}
          placeholder="Add a comment..."
          className="grow bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-sm focus:outline-none focus:bg-white focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all duration-200 ease-in-out placeholder-gray-400"
          onChange={(e) => setCommentText(e.target.value)}
          onKeyDown={handleCommentKeyPress}
          whileFocus={{ scale: 1.01 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
        />
        <motion.button
          onClick={handleCommentSend}
          disabled={!commentText.trim() || isSubmittingComment}
          className={`rounded-lg px-6 py-3 font-semibold text-sm transition-all duration-200 ease-in-out ${
            !commentText.trim() || isSubmittingComment
              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
              : "bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700 shadow-md hover:shadow-lg"
          }`}
          whileHover={
            !commentText.trim() || isSubmittingComment
              ? {}
              : { scale: 1.05, transition: { duration: 0.2, ease: "easeOut" } }
          }
          whileTap={
            !commentText.trim() || isSubmittingComment
              ? {}
              : {
                  scale: 0.95,
                  transition: { duration: 0.1, ease: "easeInOut" },
                }
          }
        >
          {isSubmittingComment ? (
            <motion.div
              className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            />
          ) : (
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              Post
            </motion.span>
          )}
        </motion.button>
      </div>
    </motion.div>
  )
);

CommentInputSection.displayName = "CommentInputSection";

interface CommentDetailsProps {
  selectedComment: Comment;
  postCommentsRef: React.MutableRefObject<Comment[]>;
  isLoadingPostComments: boolean;
  isLoadingMorePostComments: boolean;
  showReplyInput: boolean;
  setShowReplyInput: React.Dispatch<React.SetStateAction<boolean>>;
  replyingTo: string | null;
  setReplyingTo: React.Dispatch<React.SetStateAction<string | null>>;
  replyText: string;
  setReplyText: React.Dispatch<React.SetStateAction<string>>;
  handleReplySubmit: () => void;
  isReplying: boolean;
  handleCommentSubmit?: (text: string) => Promise<void>;
  socket?: WebSocket | null;
  selectedWorkspace?: string;
  closeComment?: () => void;
}

const CommentDetails: React.FC<CommentDetailsProps> = ({
  selectedComment,
  postCommentsRef,
  isLoadingPostComments,
  isLoadingMorePostComments,
  showReplyInput,
  setShowReplyInput,
  replyingTo,
  setReplyingTo,
  replyText,
  setReplyText,
  handleReplySubmit,
  isReplying,
  handleCommentSubmit,
  socket,
  selectedWorkspace,
  closeComment,
}) => {
  const postCommentsBottomLoaderRef = useRef<HTMLDivElement>(null);
  const replyInputRef = useRef<HTMLInputElement>(null);
  const commentInputRef = useRef<HTMLInputElement>(null);
  const [commentText, setCommentText] = useState("");
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Avatar fallback state
  const [failedAvatars, setFailedAvatars] = useState<Set<string>>(new Set());
  const [forceUpdate, setForceUpdate] = useState(0);

  // Mobile detection
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Helper function to get avatar URL with fallback logic - memoized to prevent re-creation
  const getAvatarUrl = useCallback(
    (originalUrl: string | undefined, fallbackName: string): string => {
      if (!originalUrl || failedAvatars.has(originalUrl)) {
        return `https://ui-avatars.com/api/?name=${encodeURIComponent(
          fallbackName
        )}&background=random&color=fff`;
      }

      if (originalUrl.startsWith("http") || originalUrl.startsWith("https")) {
        return originalUrl;
      }

      return `https://ui-avatars.com/api/?name=${encodeURIComponent(
        fallbackName
      )}&background=random&color=fff`;
    },
    [failedAvatars]
  );

  // Helper function to handle avatar load errors - optimized to avoid creating new Set on every call
  const handleAvatarError = useCallback((originalUrl: string | undefined) => {
    if (originalUrl) {
      setFailedAvatars((prev) => {
        if (prev.has(originalUrl)) return prev;
        const newSet = new Set(prev);
        newSet.add(originalUrl);
        return newSet;
      });
    }
  }, []);

  // Handle WebSocket events for hot reload comments - optimized dependencies
  useEffect(() => {
    if (!socket) return;

    const handleWebSocketMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);

        // Process comments from both Instagram and Facebook
        if (
          (data.object === "instagram" || data.object === "facebook") &&
          data.entry &&
          data.entry.length > 0
        ) {
          // Check if it's a comment change
          const changes = data.entry[0]?.changes;
          if (
            changes &&
            changes.length > 0 &&
            changes[0]?.field === "comments"
          ) {
            const commentValue = changes[0]?.value;

            // Make sure we have the necessary data
            if (commentValue && commentValue.from && commentValue.media) {
              logger.ws("Received hot reload comment", commentValue);

              // Check if this comment belongs to the current post
              const postId = commentValue.media.id;

              if (
                postId === selectedComment.id ||
                postId === selectedComment.postId
              ) {
                logger.ws("Comment belongs to current post", {
                  postId,
                  selectedCommentId: selectedComment.id,
                  platform: data.object,
                });

                // Create a new comment object
                const newComment: Comment = {
                  id: commentValue.id,
                  text: commentValue.text,
                  timestamp: new Date().toISOString(), // Use current time as the timestamp
                  username: commentValue.from.username,
                  image: "", // Required by Comment type
                  avatar: "", // Will be replaced by profile picture if available
                  likes: 0,
                  commentsCount: 0,
                  from: {
                    id: commentValue.from.id,
                    username: commentValue.from.username,
                    profile_picture: null, // Profile picture might not be available in the webhook
                  },
                  platform: data.object, // Add platform information
                };

                // Check if this comment already exists in the comments list
                const commentExists = postCommentsRef.current.some(
                  (comment) => comment.id === newComment.id
                );

                if (!commentExists) {
                  logger.ws("Adding new comment to post comments", newComment);

                  // Add the new comment to the beginning of the list
                  postCommentsRef.current = [
                    newComment,
                    ...postCommentsRef.current,
                  ];

                  // Trigger a re-render using proper state management
                  setForceUpdate((prev) => prev + 1);

                  // Play notification sound if available
                  try {
                    const notificationSound = new Audio(
                      "/sounds/notification.mp3"
                    );
                    notificationSound.volume = 0.5;
                    notificationSound.play().catch((e) => {
                      // Ignore errors - browser might block autoplay
                      console.log("Could not play notification sound", e);
                    });
                  } catch (error) {
                    // Ignore errors
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    socket.addEventListener("message", handleWebSocketMessage);
    return () => socket.removeEventListener("message", handleWebSocketMessage);
  }, [socket, selectedComment.id]);

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleReplySubmit();
      }
    },
    [handleReplySubmit]
  );

  const handleCommentSend = useCallback(async () => {
    if (!commentText.trim() || !handleCommentSubmit || isSubmittingComment)
      return;

    setIsSubmittingComment(true);
    try {
      await handleCommentSubmit(commentText);
      setCommentText("");
    } finally {
      setIsSubmittingComment(false);
    }
  }, [commentText, handleCommentSubmit, isSubmittingComment]);

  const handleCommentKeyPress = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleCommentSend();
      }
    },
    [handleCommentSend]
  );

  return (
    <div className="h-full flex flex-col bg-gray-100">
      {/* Back button header row - fixed at top */}
      {isMobile && closeComment && (
        <div className="flex items-center p-3 border-b border-gray-200 bg-white shrink-0">
          <motion.button
            onClick={closeComment}
            className="text-gray-500 hover:text-gray-700 active:text-gray-800 p-1 shrink-0 transition-colors duration-150"
            aria-label="Back to comments"
            whileHover={{
              scale: 1.05,
              transition: { duration: 0.2, ease: "easeOut" },
            }}
            whileTap={{
              scale: 0.95,
              transition: { duration: 0.1, ease: "easeInOut" },
            }}
          >
            <motion.svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              viewBox="0 0 20 20"
              fill="currentColor"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.2, ease: "easeOut" }}
            >
              <path
                fillRule="evenodd"
                d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </motion.svg>
          </motion.button>
          <span className="ml-3 text-lg font-medium text-gray-800">Post</span>
        </div>
      )}

      {/* Post content section - fixed at top */}
      <div className="p-4 border-b bg-gray-100 shrink-0">
        <div className="flex items-center flex-col">
          <div className="flex flex-row w-full justify-start items-start gap-4">
            <Image
              src={selectedComment.image}
              alt="Post"
              width={800}
              height={200}
              className="rounded-lg w-3/6 h-52 object-cover aspect-square"
            />
            <div className="flex flex-col flex-1 w-full text-wrap overflow-hidden">
              <div className="flex flex-row items-center justify-between">
                <div
                  className={`${
                    isMobile ? "text-sm" : "text-xl"
                  } text-gray-700`}
                >
                  <span>
                    posted on {formatPostDate(selectedComment.timestamp)}
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center">
                    <span className="text-gray-900 mr-1">
                      {selectedComment.likes}
                    </span>
                    <FaHeart size={16} color="#ED4956" />
                  </div>
                  <div className="flex items-center">
                    <span className="text-gray-900 mr-1">
                      {selectedComment.commentsCount}
                    </span>
                    <FaRegComment size={16} className="text-gray-700" />
                  </div>
                </div>
              </div>
              <p className="mt-4 text-xs lg:text-sm text-gray-700 leading-relaxed whitespace-pre-wrap text-sm ">
                {selectedComment.text || "No caption found for this post"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable comments section - takes remaining space */}
      {/* Add bottom padding on small screens so last items aren't hidden behind the fixed input */}
      <div className="flex-1 overflow-y-auto p-4 min-h-0 pb-28 md:pb-4">
        {isLoadingPostComments ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-start space-x-3 animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full" />
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2" />
                  <div className="h-3 bg-gray-200 rounded w-3/4" />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-4" key={forceUpdate}>
            {postCommentsRef.current?.map((comment) => (
              <CommentItem
                key={comment.id}
                comment={comment}
                getAvatarUrl={getAvatarUrl}
                handleAvatarError={handleAvatarError}
                showReplyInput={showReplyInput}
                replyingTo={replyingTo}
                setReplyingTo={setReplyingTo}
                setShowReplyInput={setShowReplyInput}
                replyInputRef={replyInputRef}
                replyText={replyText}
                setReplyText={setReplyText}
                handleKeyPress={handleKeyPress}
                handleReplySubmit={handleReplySubmit}
                isReplying={isReplying}
              />
            ))}

            {/* Bottom loader for infinite scroll */}
            <div
              ref={postCommentsBottomLoaderRef}
              className="h-10 flex items-center justify-center"
            >
              {isLoadingMorePostComments && (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900"></div>
              )}
            </div>
            {/* Add some bottom padding to ensure last comment is visible above the input */}
            <div className="h-4"></div>
          </div>
        )}
      </div>

      {/* Comment input - fixed at bottom on mobile, relative on md+ */}
      <div className="shrink-0 md:static fixed inset-x-0 bottom-0 z-20">
        <CommentInputSection
          commentInputRef={commentInputRef}
          commentText={commentText}
          setCommentText={setCommentText}
          handleCommentKeyPress={handleCommentKeyPress}
          handleCommentSend={handleCommentSend}
          isSubmittingComment={isSubmittingComment}
        />
      </div>
    </div>
  );
};

export default CommentDetails;
