import { Comment } from '../types';

// Mock comment data for testing
const mockComments: Comment[] = [
  {
    id: '1',
    username: 'user1',
    text: 'First comment',
    image: '',
    avatar: '',
    timestamp: '2024-01-01T10:00:00Z',
    likes: 5,
    from: {
      id: '1',
      username: 'user1',
      profile_picture: null,
    },
    commentsCount: 0,
    platform: 'instagram',
  },
  {
    id: '2',
    username: 'user2',
    text: 'Second comment',
    image: '',
    avatar: '',
    timestamp: '2024-01-02T10:00:00Z',
    likes: 15,
    from: {
      id: '2',
      username: 'user2',
      profile_picture: null,
    },
    commentsCount: 0,
    platform: 'instagram',
  },
  {
    id: '3',
    username: 'user3',
    text: 'Third comment',
    image: '',
    avatar: '',
    timestamp: '2024-01-03T10:00:00Z',
    likes: 10,
    from: {
      id: '3',
      username: 'user3',
      profile_picture: null,
    },
    commentsCount: 0,
    platform: 'instagram',
    isUnread: true,
  },
  {
    id: '4',
    username: 'user4',
    text: 'Fourth comment',
    image: '',
    avatar: '',
    timestamp: '2024-01-04T10:00:00Z',
    likes: 15, // Same likes as comment 2, should be sorted by timestamp
    from: {
      id: '4',
      username: 'user4',
      profile_picture: null,
    },
    commentsCount: 0,
    platform: 'instagram',
  },
];

// Function to simulate the getFilteredComments logic
function getFilteredComments(comments: Comment[], activeFilter: string): Comment[] {
  switch (activeFilter) {
    case "unread":
      return comments.filter((comment) => comment.isUnread === true);
    case "most liked":
      // For most liked, sort by likes count (highest first)
      return [...comments].sort((a, b) => {
        const likesA = a.likes || 0;
        const likesB = b.likes || 0;
        // If likes are equal, sort by timestamp (most recent first) as secondary sort
        if (likesA === likesB) {
          const timeA = new Date(a.timestamp || a.date || 0).getTime();
          const timeB = new Date(b.timestamp || b.date || 0).getTime();
          return timeB - timeA;
        }
        return likesB - likesA;
      });
    case "newest":
    default:
      // For newest, sort by timestamp (most recent first)
      return [...comments].sort((a, b) => {
        const timeA = new Date(a.timestamp || a.date || 0).getTime();
        const timeB = new Date(b.timestamp || b.date || 0).getTime();
        return timeB - timeA;
      });
  }
}

describe('Comment Filtering', () => {
  test('should filter unread comments correctly', () => {
    const filtered = getFilteredComments(mockComments, 'unread');
    expect(filtered).toHaveLength(1);
    expect(filtered[0].id).toBe('3');
    expect(filtered[0].isUnread).toBe(true);
  });

  test('should sort comments by most liked correctly', () => {
    const filtered = getFilteredComments(mockComments, 'most liked');
    expect(filtered).toHaveLength(4);

    // Should be sorted by likes (highest first)
    // Comments 2 and 4 both have 15 likes, comment 4 should come first (more recent)
    expect(filtered[0].id).toBe('4'); // 15 likes, most recent
    expect(filtered[1].id).toBe('2'); // 15 likes, older
    expect(filtered[2].id).toBe('3'); // 10 likes
    expect(filtered[3].id).toBe('1'); // 5 likes

    // Verify likes are in descending order
    expect(filtered[0].likes).toBe(15);
    expect(filtered[1].likes).toBe(15);
    expect(filtered[2].likes).toBe(10);
    expect(filtered[3].likes).toBe(5);
  });

  test('should sort comments by newest correctly', () => {
    const filtered = getFilteredComments(mockComments, 'newest');
    expect(filtered).toHaveLength(4);

    // Should be sorted by timestamp (most recent first)
    expect(filtered[0].id).toBe('4'); // 2024-01-04
    expect(filtered[1].id).toBe('3'); // 2024-01-03
    expect(filtered[2].id).toBe('2'); // 2024-01-02
    expect(filtered[3].id).toBe('1'); // 2024-01-01
  });

  test('should handle comments with zero likes', () => {
    const commentsWithZeroLikes: Comment[] = [
      ...mockComments,
      {
        id: '5',
        username: 'user5',
        text: 'Fifth comment',
        image: '',
        avatar: '',
        timestamp: '2024-01-05T10:00:00Z',
        likes: 0,
        from: {
          id: '5',
          username: 'user5',
          profile_picture: null,
        },
        commentsCount: 0,
        platform: 'instagram',
      },
    ];

    const filtered = getFilteredComments(commentsWithZeroLikes, 'most liked');
    expect(filtered).toHaveLength(5);

    // Comment with 0 likes should be last
    expect(filtered[filtered.length - 1].id).toBe('5');
    expect(filtered[filtered.length - 1].likes).toBe(0);
  });

  test('should handle comments with undefined likes', () => {
    const commentsWithUndefinedLikes: Comment[] = [
      {
        id: '6',
        username: 'user6',
        text: 'Sixth comment',
        image: '',
        avatar: '',
        timestamp: '2024-01-06T10:00:00Z',
        likes: undefined as any,
        from: {
          id: '6',
          username: 'user6',
          profile_picture: null,
        },
        commentsCount: 0,
        platform: 'instagram',
      },
    ];

    const filtered = getFilteredComments(commentsWithUndefinedLikes, 'most liked');
    expect(filtered).toHaveLength(1);

    // Should treat undefined likes as 0
    expect(filtered[0].id).toBe('6');
  });

  test('should filter unread comments using comment to post mapping', () => {
    // Mock unread comments and mapping
    const unreadComments = ['comment_123', 'comment_456'];
    const commentToPostMapping = {
      'comment_123': '1', // Maps to post 1
      'comment_456': '2', // Maps to post 2
    };

    // Function to simulate enhanced unread filtering
    function getFilteredCommentsWithMapping(comments: Comment[], activeFilter: string, unreadComments: string[], commentToPostMapping: Record<string, string>): Comment[] {
      switch (activeFilter) {
        case "unread":
          return comments.filter((comment) => {
            // Check if this post has any unread comments using the mapping
            const hasUnreadComments = unreadComments.some((commentId) => {
              const mappedPostId = commentToPostMapping[commentId];
              return (
                mappedPostId === comment.id ||
                commentId === comment.id
                // Removed commentId.includes(comment.id) as it causes false positives
              );
            });
            // Return true if the post has unread comments OR if it's explicitly marked as unread
            return hasUnreadComments || comment.isUnread === true;
          });
        default:
          return comments;
      }
    }

    const filtered = getFilteredCommentsWithMapping(mockComments, 'unread', unreadComments, commentToPostMapping);

    // Should include posts 1 and 2 (from mapping) and post 3 (from isUnread flag)
    // Post 4 should NOT be included (no unread comments and isUnread is not true)
    expect(filtered).toHaveLength(3);

    const filteredIds = filtered.map(c => c.id);
    expect(filteredIds).toContain('1'); // Has unread comment via mapping
    expect(filteredIds).toContain('2'); // Has unread comment via mapping
    expect(filteredIds).toContain('3'); // Has isUnread: true flag
    expect(filteredIds).not.toContain('4'); // Should not be included
  });

  test('should move posts with new comments to top', () => {
    // Simulate moving posts to top when they receive new comments
    function movePostsToTop(comments: Comment[], affectedPostIds: Set<string>): Comment[] {
      const postsToMove: Comment[] = [];
      const remainingPosts: Comment[] = [];

      comments.forEach((comment) => {
        if (affectedPostIds.has(comment.id)) {
          postsToMove.push({
            ...comment,
            isUnread: true,
            timestamp: new Date().toISOString(),
          });
        } else {
          remainingPosts.push(comment);
        }
      });

      return [...postsToMove, ...remainingPosts];
    }

    const affectedPostIds = new Set(['2', '4']); // Posts 2 and 4 received new comments
    const reordered = movePostsToTop(mockComments, affectedPostIds);

    expect(reordered).toHaveLength(4);

    // Posts 2 and 4 should be at the top
    expect(reordered[0].id).toBe('2');
    expect(reordered[1].id).toBe('4');

    // Both should be marked as unread
    expect(reordered[0].isUnread).toBe(true);
    expect(reordered[1].isUnread).toBe(true);

    // Remaining posts should follow
    expect(reordered[2].id).toBe('1');
    expect(reordered[3].id).toBe('3');
  });
});
