import axios from "axios";

// Logger functions with console output for debugging
export const logger = {
  api: (action: string, data: any) => {
    console.log(`[API] ${action}:`, data);
  },
  ws: (action: string, data: any) => {
    console.log(`[WS] ${action}:`, data);
  },
  state: (action: string, data: any) => {
    console.log(`[STATE] ${action}:`, data);
  },
  error: (action: string, error: any) => {
    console.error(`[ERROR] ${action}:`, error);
  },
};

// Format post date to "MMM DD" format
export const formatPostDate = (dateStr: string) => {
  const date = new Date(dateStr);
  return date
    .toLocaleDateString("en-US", {
      month: "short",
      day: "2-digit",
    })
    .toLowerCase();
};

// Format date to relative time (e.g., "2h ago", "Yesterday", etc.)
export const formatDate = (dateStr: string) => {
  const date = new Date(dateStr);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffDay > 6) {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  } else if (diffDay > 1) {
    return `${diffDay}d ago`;
  } else if (diffDay === 1) {
    return "Yesterday";
  } else if (diffHour >= 1) {
    return `${diffHour}h ago`;
  } else if (diffMin >= 1) {
    return `${diffMin}m ago`;
  } else {
    return "Just now";
  }
};

// Truncate text to a specified length
export const truncateText = (
  text: string | null | undefined,
  maxLength: number = 50
): string => {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + "...";
};

// Detect content type (image, video, etc.) from URL
export const detectContentType = async (url: string): Promise<string> => {
  if (!url) return "";

  try {
    // First try to detect using HEAD request to avoid downloading full content
    const response = await axios({
      method: "head",
      url: url,
      timeout: 5000,
      headers: {
        Accept: "*/*",
      },
      validateStatus: (status) => status < 500, // Accept all status codes except 500+
    });

    const contentType = response.headers["content-type"]?.toLowerCase() || "";

    // Check content type from headers
    if (contentType) {
      if (contentType.includes("video") || contentType.includes("mp4"))
        return "video";
      if (contentType.includes("image")) return "image";
    }

    // If headers don't help, check URL patterns
    const urlPattern = url.toLowerCase();

    // Instagram-specific patterns
    if (urlPattern.includes("instagram.com")) {
      if (urlPattern.includes("/video/")) return "video";
      if (urlPattern.includes("/p/")) return "image";
    }

    // Check file extensions as fallback
    const extension = url.split(".").pop()?.toLowerCase();
    if (extension) {
      if (["mp4", "mov", "webm", "avi", "m4v"].includes(extension))
        return "video";
      if (["jpg", "jpeg", "png", "gif", "webp", "bmp"].includes(extension))
        return "image";
    }

    return "";
  } catch (error) {
    console.error("Error detecting content type:", error);

    // Fallback to extension checking if request fails
    const extension = url.split(".").pop()?.toLowerCase();
    if (extension) {
      if (["mp4", "mov", "webm", "avi", "m4v"].includes(extension))
        return "video";
      if (["jpg", "jpeg", "png", "gif", "webp", "bmp"].includes(extension))
        return "image";
    }

    return "";
  }
};

// Type guards for API responses
import {
  ApiResponse,
  DirectConversationsResponse,
  DirectMessagesResponse,
  CommentSuccessResponse,
} from "./types";

export const isDirectConversationsResponse = (
  response: ApiResponse
): response is DirectConversationsResponse => {
  return response.success && "conversations" in response;
};

export const isDirectMessagesResponse = (
  response: ApiResponse
): response is DirectMessagesResponse => {
  return response.success && "messages" in response;
};

export const isCommentSuccessResponse = (
  response: ApiResponse
): response is CommentSuccessResponse => {
  return response.success && "comments" in response;
};

// Format date for message grouping with proper chronological order
export const formatMessageDate = (
  timestamp: number | string | Date
): string => {
  const date =
    timestamp instanceof Date
      ? timestamp
      : typeof timestamp === "number"
      ? new Date(timestamp)
      : new Date(timestamp);

  const now = new Date();

  // Check if date is today
  if (date.toDateString() === now.toDateString()) {
    return "Today";
  }

  // Check if date is yesterday
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  if (date.toDateString() === yesterday.toDateString()) {
    return "Yesterday";
  }

  // Check if date is within the last 7 days
  const oneWeekAgo = new Date(now);
  oneWeekAgo.setDate(now.getDate() - 7);
  if (date > oneWeekAgo) {
    return date.toLocaleDateString("en-US", { weekday: "long" });
  }

  // For older dates, show the full date
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Compare dates for sorting (ignoring time)
export const isSameDay = (date1: Date, date2: Date): boolean => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

// Get chronological order of dates (for proper sorting)
export const getDateOrder = (date1: string, date2: string): number => {
  const d1 = new Date(date1);
  const d2 = new Date(date2);

  // First compare years
  if (d1.getFullYear() !== d2.getFullYear()) {
    return d1.getFullYear() - d2.getFullYear();
  }

  // Then compare months
  if (d1.getMonth() !== d2.getMonth()) {
    return d1.getMonth() - d2.getMonth();
  }

  // Finally compare days
  return d1.getDate() - d2.getDate();
};
