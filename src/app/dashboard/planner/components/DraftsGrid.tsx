"use client";
import React from "react";
import DraftCard from "./DraftCard";

type PostShape = any;

type Props = {
  drafted: PostShape[];
  onEdit: (post: PostShape) => void;
  onDelete: (post: PostShape) => void;
};

export default function DraftsGrid({ drafted, onEdit, onDelete }: Props) {
  if (!drafted || drafted.length === 0) return null;

  return (
    <div className="grid md:grid-cols-3 grid-cols-1 gap-4">
      {drafted.map((post: PostShape, index: number) => (
        <DraftCard
          key={post.id || index}
          post={post}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      ))}
    </div>
  );
}
