"use client";
import React from "react";

// Minimal Post shape used by DraftCard to avoid cross-file type coupling
interface Post {
  id?: string;
  caption?: string;
  content?: string;
  ai_generated?: boolean;
  platform?: string;
  social_media?: Array<{
    platform: string;
    social_id: string;
    username?: string;
    name?: string;
    profile_photo?: string;
  }>;
  created_at?: number | string;
}

const AiBadge = () => (
  <div className="inline-flex items-center gap-1 bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full text-xs">
    <img src="/icons/ai-generated.svg" alt="AI" className="w-3 h-3" />
  </div>
);

type Props = {
  post: Post;
  onEdit: (post: Post) => void;
  onDelete: (post: Post) => void;
};

export default function DraftCard({ post, onEdit, onDelete }: Props) {
  const platform =
    post.social_media?.[0]?.platform || post.platform || "instagram";

  const profileName =
    post.social_media?.[0]?.username ||
    post.social_media?.[0]?.name ||
    post.social_media?.[0]?.social_id ||
    "Unknown";

  const formatDate = (v?: number | string) => {
    if (!v) return "";
    const n = typeof v === "string" ? Number(v) : v;
    // created_at may be seconds or milliseconds
    const ts = n > 1e12 ? n : n * 1000;
    try {
      return new Date(ts).toLocaleString();
    } catch (_e) {
      return "";
    }
  };

  // Extract first media URL if present
  const getFirstMedia = (post: Post) => {
    if (!post || !post.hasOwnProperty("media")) return null as string | null;
    // @ts-ignore - media can be string or object in other parts of repo
    const media = post.media;
    if (!media) return null;
    if (typeof media === "string") {
      try {
        const parsed = JSON.parse(media);
        if (parsed && typeof parsed === "object") {
          const vals = Object.values(parsed);
          return vals.length ? String(vals[0]) : null;
        }
      } catch (e) {
        // if it's a plain url string
        return String(media);
      }
    }
    if (typeof media === "object") {
      const vals = Object.values(media as Record<string, string>);
      return vals.length ? String(vals[0]) : null;
    }
    return null;
  };

  const thumbnail = getFirstMedia(post);

  return (
    <div className="group relative h-full">
      <div
        className={`bg-gray-50 p-4 rounded-lg shadow-sm group-hover:blur-xs transition-all flex flex-col h-full`}
      >
        <div className="flex items-start justify-between mb-2 gap-3 flex-shrink-0">
          <div className="flex items-center gap-2">
            {post.ai_generated && <AiBadge />}
            <div
              className={`w-6 h-6 rounded-full flex items-center justify-center ${
                platform?.includes("facebook")
                  ? "bg-[#1877F2]/10"
                  : platform?.includes("instagram")
                  ? "bg-[#E4405F]/10"
                  : "bg-gray-200"
              }`}
            >
              <img
                src={`/icons/performance/${
                  platform?.includes("facebook")
                    ? "facebook"
                    : platform?.includes("instagram")
                    ? "instagram"
                    : "default"
                }-on.svg`}
                alt={platform || "platform"}
                className="w-4 h-4"
              />
            </div>
            <div className="flex flex-col">
              <div className="text-sm font-medium truncate max-w-[10rem]">
                {profileName}
              </div>
              <div className="text-xs text-gray-400">
                {formatDate(post.created_at)}
              </div>
            </div>
          </div>
        </div>

        {/* Post image centered (mini Instagram style) */}
        <div className="w-full flex items-center justify-center mb-3 flex-1">
          {thumbnail ? (
            <img
              src={
                thumbnail.startsWith("/")
                  ? `${process.env.NEXT_PUBLIC_API_URL || ""}${thumbnail}`
                  : thumbnail
              }
              alt={post.caption || "post image"}
              className="w-full max-w-[520px] h-full object-cover rounded-md"
            />
          ) : (
            <div className="w-full max-w-[520px] h-full bg-gray-100 rounded-md flex items-center justify-center">
              <svg
                className="w-8 h-8 text-gray-300"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <rect x="3" y="3" width="18" height="14" rx="2" ry="2" />
                <circle cx="8.5" cy="8.5" r="1.5" />
                <path d="M21 21l-5.2-5.2" />
              </svg>
            </div>
          )}
        </div>

        {/* Caption - show up to two lines */}
        <p className="text-sm text-gray-600 mb-1 line-clamp-2">
          {post.caption}
        </p>
        {/* Optional extra content (kept but less prominent) */}
        <p className="text-xs text-gray-400 hidden">{post.content}</p>
      </div>
      <div className="absolute inset-0 hidden group-hover:flex items-center justify-center gap-4">
        <button
          className="p-2 bg-white rounded-full hover:bg-gray-100"
          onClick={() => onEdit(post)}
        >
          <svg
            className="w-4 h-4 text-gray-600"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
          </svg>
        </button>
        <button
          className="p-2 bg-white rounded-full hover:bg-gray-100"
          onClick={() => onDelete(post)}
        >
          <svg
            className="w-4 h-4 text-red-500"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path d="M3 6h18" />
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" />
            <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
          </svg>
        </button>
      </div>
    </div>
  );
}
