"use client";
import React, { useEffect, useState } from "react";
import { showErrorToast, showSuccessToast } from "~/components/toasts";
import { useWebSocket } from "~/hooks/useWebSocket";
import { useUserStore } from "~/store/userStore";
import { useRouter } from "next/navigation";
import { Calendar, dateFnsLocalizer } from "react-big-calendar";
import { format, parse, startOfWeek, getDay } from "date-fns";
import { enUS } from "date-fns/locale";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { AnimatePresence, motion } from "framer-motion";
import { DraftsGrid } from "./components";

interface Post {
  id: string;
  caption: string;
  content?: string;
  created_at?: string;
  publish_date?: string;
  typ3?: string;
  media?: string | Record<string, string>;
  social_media?: Array<{
    platform: string;
    social_id: string;
  }>;
  ai_generated?: boolean;
}

interface Event extends Post {
  time?: string;
  displayContent?: string;
  color?: string;
  icon?: string;
}

// First, add this shared AI badge component
const AiBadge = () => (
  <div className="inline-flex items-center gap-1 bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full text-xs shrink-0">
    <img
      src="/icons/ai-generated.svg"
      alt="AI"
      className="w-4 h-4 sm:w-3 sm:h-3 flex-shrink-0"
    />
  </div>
);

// Add localizer setup for react-big-calendar
const locales = {
  "en-US": enUS,
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek: (date: Date) => startOfWeek(date, { weekStartsOn: 0 }), // Ensure Sunday start
  getDay,
  locales,
});

// Helper to convert API timestamps (seconds or milliseconds) or date strings into a JS Date
const toDate = (value: any) => {
  if (!value) return null;
  // if already a Date
  if (value instanceof Date) return value;
  // numeric strings or numbers
  if (typeof value === "number" || typeof value === "string") {
    const n = Number(value);
    if (!Number.isNaN(n)) {
      // if looks like seconds (10 digits) convert to ms
      if (String(Math.trunc(n)).length === 10) {
        const date = new Date(n * 1000);
        // Ensure we get the local date without timezone offset issues
        return new Date(date.getFullYear(), date.getMonth(), date.getDate());
      }
      const date = new Date(n);
      // Ensure we get the local date without timezone offset issues
      return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    }
    // fallback to Date constructor for ISO strings
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      // Ensure we get the local date without timezone offset issues
      return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    }
  }
  return new Date(value);
};

// const WeekView = ({
//   scheduled,
//   selectedDate,
//   handleEditPost
// }: {
//   scheduled: any,
//   selectedDate: Date,
//   handleEditPost: (post: any) => void
// }) => {
//   const {drafted, setUser} = useUserStore();

//   const today = new Date();
//   // Use selectedDate instead of creating a new state
//   const [currentWeek, setCurrentWeek] = useState(selectedDate);

//   // Update useEffect to sync currentWeek with selectedDate
//   useEffect(() => {
//     setCurrentWeek(selectedDate);
//   }, [selectedDate]);

//   const days = Array.from({length: 7}, (_, i) => {
//     const date = new Date(currentWeek);
//     date.setDate(currentWeek.getDate() - currentWeek.getDay() + i); // Start from Sunday
//     return date;
//   });

//   const events = {
//     [days[0].getDate()]: [{ platform: 'mizegerd.agency', content: 'he only way of get...', color: 'bg-gray-300' }],
//     [days[2].getDate()]: [
//       { platform: 'Mizegerd Digital Age...', content: 'Shine ✨ your Team ...', color: 'bg-green-100' },
//       { platform: 'mizegerd.agency', content: 'Say Hi to our new pr...', color: 'bg-pink-100' }
//     ]
//   };

//   const handlePrevWeek = () => {
//     setCurrentWeek(prev => {
//       const newDate = new Date(prev);
//       newDate.setDate(prev.getDate() - 7); // Change from 5 to 7 days
//       return newDate;
//     });
//   };

//   const handleNextWeek = () => {
//     setCurrentWeek(prev => {
//       const newDate = new Date(prev);
//       newDate.setDate(prev.getDate() + 7); // Change from 5 to 7 days
//       return newDate;
//     });
//   };

//   return (
//     <div className="mt-4">
//       <div className="flex justify-between items-center mb-4">
//         <button className="text-2xl hover:bg-gray-100 p-2 rounded-full" onClick={handlePrevWeek}>&lt;</button>
//         <div className="grid grid-cols-7 gap-1 sm:gap-4 w-full px-1 sm:px-4">
//           {days.map((date, index) => (
//             <div key={date.getDate()} className="flex justify-center">
//               <div className={`flex flex-col items-center justify-center
//                 ${date.getDate() === today.getDate() ? 'bg-blue-500 text-white rounded-full w-8 sm:w-12 h-8 sm:h-12' :
//                   date.getDate() === selectedDate.getDate() ? 'bg-emerald-500 text-white rounded-full w-8 sm:w-12 h-8 sm:h-12' : ''}`}>
//                 <div className="text-sm sm:text-xl font-bold">
//                   {date.getDate()}
//                 </div>
//                 <div className={`text-[10px] sm:text-xs ${date.getDate() === currentWeek.getDate() || date.getDate() === today.getDate() ? 'text-white' : 'text-gray-500'}`}>
//                   {date.toLocaleString('default', { month: 'short' })}
//                 </div>
//               </div>
//             </div>
//           ))}
//         </div>
//         <button className="text-2xl hover:bg-gray-100 p-2 rounded-full" onClick={handleNextWeek}>&gt;</button>
//       </div>
//       <div className="grid grid-cols-7 gap-0 border rounded-lg">
//         {days.map((day) => (
//           <div key={day.toString()} className="h-[calc(100vh-250px)] border-r last:border-r-0 p-1 sm:p-2">
//             {scheduled?.filter(post => {
//                 const postDate = new Date(post.publish_date);
//                 return postDate.getDate() === day.getDate();
//               }).map((event, index) => (
//               <div key={index} className="group relative">
//                  {event.ai_generated && <AiBadge />}
//                 <div className={`${event.color} rounded-lg p-1 sm:p-2 mb-2 group-hover:blur-sm transition-all`}>
//                   <div className="text-xs sm:text-sm font-medium flex items-center justify-between">
//                     <span className="truncate">"No workspace name available"</span>

//                   </div>
//                   <div className="text-xs sm:text-sm truncate">{event.caption}</div>
//                 </div>
//                 <div className="absolute inset-0 hidden group-hover:flex items-center justify-center gap-2 sm:gap-4 z-10">
//                   <button
//                     className="p-1 sm:p-2 bg-white rounded-full hover:bg-gray-100 shadow-md"
//                     onClick={() => handleEditPost(event)}
//                   >
//                     <svg className="w-3 h-3 sm:w-4 sm:h-4 text-gray-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
//                       <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
//                       <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
//                     </svg>
//                   </button>
//                   <button className="p-1 sm:p-2 bg-white rounded-full hover:bg-gray-100 shadow-md">
//                     <svg className="w-3 h-3 sm:w-4 sm:h-4 text-red-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
//                       <path d="M3 6h18" />
//                       <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" />
//                       <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
//                     </svg>
//                   </button>
//                 </div>
//               </div>
//             ))}
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

const WeekView = ({
  selectedDate,
  handleEditPost,
  setSelectedDate,
}: {
  scheduled: any;
  selectedDate: Date;
  handleEditPost: (post: any) => void;
  setSelectedDate: (date: Date) => void;
}) => {
  const { drafted, scheduled, published, setUser } = useUserStore();
  const { deletePost } = useWebSocket();
  const router = useRouter();

  // Navigation functions for week
  const handlePrevWeek = () => {
    const prevWeek = new Date(selectedDate);
    prevWeek.setDate(selectedDate.getDate() - 7);
    setSelectedDate(prevWeek);
  };

  const handleNextWeek = () => {
    const nextWeek = new Date(selectedDate);
    nextWeek.setDate(selectedDate.getDate() + 7);
    setSelectedDate(nextWeek);
  };

  // Fix the weekDays calculation
  const weekDays = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(selectedDate);
    date.setDate(selectedDate.getDate() - selectedDate.getDay() + i);
    return date;
  });

  const handleDeletePost = async (post: any) => {
    deletePost({
      post_id: post.id,
    }).then((data: any) => {
      if (data?.posts) {
        // Separate draft and scheduled posts
        const draftPosts = data.posts.filter(
          (post: any) => post.typ3 === "draft"
        );

        const scheduledPosts = data.posts.filter(
          (post: any) => post.typ3 === "schedule"
        );
        const publishedPosts = data.posts.filter(
          (post: any) => post.typ3 === "publish"
        );
        // Update state with separated posts
        setUser({
          drafted: draftPosts as any,
          scheduled: scheduledPosts as any,
          published: publishedPosts as any,
        });

        showSuccessToast(data.message);
      } else {
        setUser({
          drafted: [],
          scheduled: [],
          published: [],
        });
        showErrorToast(data.message);
      }
    });
  };

  const isPostEditable = (post: any) => {
    const postDate = toDate(
      post.typ3 === "publish" ? (post as any).created_at : post.publish_date
    );
    const now = new Date();
    return post.typ3 !== "publish" && postDate !== null && postDate > now;
  };

  return (
    <div className="overflow-y-auto">
      {/* Week Navigation Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={handlePrevWeek}
          className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          aria-label="Previous week"
        >
          <svg
            className="w-5 h-5 text-gray-600"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path d="M15 18l-6-6 6-6" />
          </svg>
        </button>

        <div className="text-center">
          <h2 className="text-lg font-semibold">
            {selectedDate.toLocaleDateString("en-US", {
              month: "long",
              day: "numeric",
              year: "numeric",
            })}
          </h2>
          <p className="text-sm text-gray-500">Week View</p>
        </div>

        <button
          onClick={handleNextWeek}
          className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          aria-label="Next week"
        >
          <svg
            className="w-5 h-5 text-gray-600"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path d="M9 18l6-6-6-6" />
          </svg>
        </button>
      </div>

      <div className="space-y-4">
        {weekDays.map((date, idx) => {
          const postsForDay = [
            ...(scheduled || []),
            ...(published || []),
          ]?.filter((post) => {
            const postDate = toDate(
              post.typ3 === "publish"
                ? (post as any).created_at
                : post.publish_date
            );
            return postDate && postDate.toDateString() === date.toDateString();
          });

          return (
            <div
              key={date.toISOString()}
              className="group relative flex items-start border-b border-gray-200 pb-4 last:border-b-0 flex-col gap-2"
            >
              <div className="flex flex-row gap-2 items-center w-full justify-between">
                <div className="flex items-center gap-2">
                  <h3 className="font-medium text-sm sm:text-base">
                    {date.toLocaleDateString("en-US", { weekday: "long" })}
                  </h3>
                  <p className="text-xs sm:text-sm text-gray-500">
                    {date.toLocaleDateString("en-US", { month: "short" })}{" "}
                    {date.getDate()}
                  </p>
                </div>
              </div>
              <div className="grow w-full">
                {postsForDay.map((post, index) => {
                  const canEdit = isPostEditable(post);

                  return (
                    <div
                      key={index}
                      className={`group relative my-2 ${
                        !canEdit ? "cursor-default" : ""
                      }`}
                    >
                      <div
                        className={`${
                          post.social_media?.[0]?.platform?.includes("facebook")
                            ? "bg-[#1877F2]/10"
                            : post.social_media?.[0]?.platform?.includes(
                                "instagram"
                              )
                            ? "bg-[#E4405F]/10"
                            : "bg-gray-100"
                        } rounded-lg p-2 sm:p-3 ${
                          canEdit ? "group-hover:blur-sm" : ""
                        } transition-all`}
                      >
                        {/* Post content */}
                        <div className="flex items-start justify-between min-w-0">
                          <div className="flex items-center gap-2 shrink-0">
                            {post.ai_generated && <AiBadge />}
                            <div
                              className={`w-7 h-7 sm:w-6 sm:h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                                post.social_media?.[0]?.platform?.includes(
                                  "facebook"
                                )
                                  ? "bg-[#1877F2]/10"
                                  : post.social_media?.[0]?.platform?.includes(
                                      "instagram"
                                    )
                                  ? "bg-[#E4405F]/10"
                                  : "bg-gray-100"
                              }`}
                            >
                              <img
                                src={`/icons/performance/${
                                  post.social_media?.[0]?.platform?.includes(
                                    "facebook"
                                  )
                                    ? "facebook"
                                    : post.social_media?.[0]?.platform?.includes(
                                        "instagram"
                                      )
                                    ? "instagram"
                                    : "default"
                                }-on.svg`}
                                alt={
                                  post.social_media?.[0]?.platform || "default"
                                }
                                className="w-5 h-5 sm:w-4 sm:h-4 flex-shrink-0"
                              />
                            </div>
                          </div>
                          <div className="grow px-2 sm:px-3 min-w-0 flex flex-col gap-0.5 sm:gap-1">
                            {/* Caption: single-line on mobile, 2-line clamp on sm+ */}
                            <p className="text-xs font-medium truncate max-w-full sm:hidden">
                              {post.caption}
                            </p>
                            <p
                              className="hidden sm:block text-xs lg:text-sm font-medium text-slate-800 overflow-hidden"
                              style={{
                                display: "-webkit-box",
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: "vertical",
                              }}
                            >
                              {post.caption}
                            </p>

                            {/* Optional description on mobile, 3 lines with text-xs */}
                            {post.content && (
                              <p
                                className="block sm:hidden text-xs text-gray-600 overflow-hidden max-w-full"
                                style={{
                                  display: "-webkit-box",
                                  WebkitLineClamp: 3,
                                  WebkitBoxOrient: "vertical",
                                }}
                              >
                                {post.content}
                              </p>
                            )}

                            {/* Time: extra compact on mobile */}
                            <p className="text-[10px] sm:text-xs text-gray-500 mt-0.5">
                              {toDate(
                                post.typ3 === "publish"
                                  ? (post as any).created_at
                                  : post.publish_date
                              )?.toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </p>
                          </div>
                          {post.typ3 === "publish" && (
                            <span className="inline-flex items-center px-1.5 sm:px-2 py-0.5 rounded-full text-[10px] sm:text-xs font-medium bg-green-100 text-green-800 shrink-0">
                              Published
                            </span>
                          )}
                        </div>
                      </div>
                      {canEdit && (
                        <div className="absolute inset-0 hidden group-hover:flex items-center justify-center gap-4">
                          <button
                            className="p-2 bg-white rounded-full hover:bg-gray-100 shadow-md"
                            onClick={() => handleEditPost(post)}
                          >
                            <svg
                              className="w-4 h-4 text-gray-600"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                            >
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                            </svg>
                          </button>
                          <button
                            className="p-2 bg-white rounded-full hover:bg-gray-100 shadow-md"
                            onClick={() => handleDeletePost(post)}
                          >
                            <svg
                              className="w-4 h-4 text-red-500"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                            >
                              <path d="M3 6h18" />
                              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" />
                              <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                            </svg>
                          </button>
                        </div>
                      )}
                    </div>
                  );
                })}
                {/* Always show a small add (+) button in the corner for this day (visible on hover) */}
                <button
                  aria-label={`Add a post on ${date.toDateString()}`}
                  onClick={() =>
                    router.push(`/dashboard/new-post?date=${date.getTime()}`)
                  }
                  className="absolute top-2 right-2 hidden group-hover:flex items-center justify-center w-8 h-8 bg-[#0b57d0] hover:bg-[#084bb0] focus:outline-none focus:ring-2 focus:ring-[#0b57d0] text-white rounded-full shadow-md"
                >
                  <svg
                    className="w-4 h-4 text-white"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="M12 5v14M5 12h14" stroke="white" />
                  </svg>
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Update MonthView component
const MonthView = ({
  setSelectedDate,
  scheduled,
  handleEditPost,
  selectedDate,
}: {
  setSelectedDate: (date: Date) => void;
  scheduled: any;
  handleEditPost: (post: any) => void;
  selectedDate: Date;
}) => {
  const [currentMonth, setCurrentMonth] = useState(selectedDate);
  const { published } = useUserStore();
  const router = useRouter();

  // Track navigation direction for smooth month slide
  const [monthDirection, setMonthDirection] = useState<1 | -1>(1);
  // Navigation animation state when going to new-post
  const [navigatingTo, setNavigatingTo] = useState<string | null>(null);

  const handlePrevMonth = () => {
    const newDate = new Date(currentMonth);
    newDate.setMonth(currentMonth.getMonth() - 1);
    setCurrentMonth(newDate);
    setMonthDirection(-1);
  };

  const handleNextMonth = () => {
    const newDate = new Date(currentMonth);
    newDate.setMonth(currentMonth.getMonth() + 1);
    setCurrentMonth(newDate);
    setMonthDirection(1);
  };

  // Convert both scheduled and published posts to react-big-calendar events format
  const events = [...(scheduled || []), ...(published || [])]
    .map((post: any) => {
      const startDate = toDate(
        post.typ3 === "publish" ? (post as any).created_at : post.publish_date
      );
      if (!startDate) return null;

      // Create a clean date without time to prevent timezone/positioning issues
      const cleanDate = new Date(
        startDate.getFullYear(),
        startDate.getMonth(),
        startDate.getDate(),
        12,
        0,
        0,
        0
      );

      return {
        id: post.id,
        title: post.caption,
        start: cleanDate,
        end: cleanDate,
        resource: post,
        allDay: true, // Force as all-day to prevent time-based positioning issues
      };
    })
    .filter(Boolean) as any[];

  return (
    <div className="mt-4 relative">
      {/* Month Navigation */}
      <div className="w-full flex justify-center items-center mb-4">
        <div className="flex items-center justify-between gap-8 w-full h-12">
          <button
            onClick={handlePrevMonth}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>
          <div className="text-center">
            <span className="text-lg font-medium">
              {format(currentMonth, "MMMM yyyy")}
            </span>
          </div>
          <button
            onClick={handleNextMonth}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
        </div>
      </div>

      {/* Calendar */}
      <div className="h-[calc(100vh-350px)] min-h-[520px] text-xs lg:text-sm">
        <AnimatePresence initial={false} mode="popLayout">
          <motion.div
            key={format(currentMonth, "yyyy-MM")}
            initial={{ x: 40 * monthDirection, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -40 * monthDirection, opacity: 0 }}
            transition={{
              type: "spring",
              stiffness: 320,
              damping: 28,
              mass: 0.5,
            }}
            className="h-full"
          >
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.25 }}
              className="h-full"
            >
              <Calendar
                localizer={localizer}
                events={[]} // Pass empty array since we render events manually
                view="month"
                views={["month"]}
                date={currentMonth}
                getNow={() => new Date()} // Ensure consistent "now" reference
                onNavigate={(date: Date) => {
                  setCurrentMonth(date);
                }}
                onSelectSlot={({ start }: { start: Date }) => {
                  const url = `/dashboard/new-post?date=${start.getTime()}`;
                  setNavigatingTo(url);
                }}
                selectable
                className="rounded-xl shadow-sm bg-white overflow-hidden border border-black/10 month-calendar"
                style={{ height: "100%" }}
                components={{
                  dateCellWrapper: (props) => {
                    // Get events for this specific date only
                    const cellDate = toDate(props.value);
                    const cellEvents = events.filter((event) => {
                      const eventDate = toDate(event.start);
                      return (
                        eventDate &&
                        cellDate &&
                        eventDate.getFullYear() === cellDate.getFullYear() &&
                        eventDate.getMonth() === cellDate.getMonth() &&
                        eventDate.getDate() === cellDate.getDate()
                      );
                    });

                    const isToday =
                      new Date().toDateString() === cellDate?.toDateString();
                    const isCurrentMonth =
                      cellDate?.getMonth() === currentMonth.getMonth();

                    return (
                      <motion.div
                        className="w-full relative h-full"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <motion.div
                          className={`group w-full h-full relative min-h-[100px] ${
                            isToday
                              ? "bg-blue-50 border-2 border-blue-500 rounded-lg"
                              : ""
                          } ${!isCurrentMonth ? "bg-gray-100" : ""}`}
                          whileTap={{ scale: 0.98 }}
                        >
                          {/* Render original calendar cell content */}
                          <div className="absolute inset-0">
                            {props.children}
                          </div>

                          {/* Custom events rendering for this cell only */}
                          {cellEvents.length > 0 && (
                            <div className="absolute inset-0 top-6 p-1 space-y-1 z-5 pointer-events-none">
                              {cellEvents.slice(0, 3).map((event, idx) => {
                                const platform =
                                  event?.resource?.social_media?.[0]
                                    ?.platform || "";
                                const bg = platform.includes("facebook")
                                  ? "bg-[#1877F2]/10"
                                  : platform.includes("instagram")
                                  ? "bg-[#E4405F]/10"
                                  : "bg-gray-100";

                                const isInsta = platform.includes("instagram");
                                const isFacebook =
                                  platform.includes("facebook");

                                return (
                                  <div
                                    key={event.id || idx}
                                    className={`flex items-center gap-1.5 px-2 py-1 rounded-md w-full cursor-pointer pointer-events-auto ${bg}`}
                                    onClick={() =>
                                      handleEditPost(event.resource)
                                    }
                                  >
                                    <div className="flex items-center justify-center flex-shrink-0">
                                      <div
                                        className={`flex items-center justify-center rounded-full ${
                                          isFacebook
                                            ? "bg-[#1877F2]/20"
                                            : isInsta
                                            ? "bg-[#E4405F]/20"
                                            : "bg-gray-200"
                                        } p-1`}
                                      >
                                        <img
                                          src={`/icons/performance/${
                                            isFacebook
                                              ? "facebook"
                                              : isInsta
                                              ? "instagram"
                                              : "default"
                                          }-on.svg`}
                                          alt={platform || "default"}
                                          className={`${
                                            isInsta ? "w-4 h-4" : "w-3 h-3"
                                          }`}
                                        />
                                      </div>
                                    </div>
                                    <span
                                      className={`text-xs text-gray-700 truncate ${
                                        isInsta
                                          ? "max-w-[80px] sm:max-w-[100px]"
                                          : "max-w-[100px] sm:max-w-[120px]"
                                      }`}
                                      title={event.title}
                                    >
                                      {event.title}
                                    </span>
                                  </div>
                                );
                              })}
                              {cellEvents.length > 3 && (
                                <div className="text-xs text-gray-500 px-2">
                                  +{cellEvents.length - 3} more
                                </div>
                              )}
                            </div>
                          )}

                          {/* Hover overlay to ensure hover detection works even with events */}
                          <div
                            className="absolute inset-0 z-10 pointer-events-none group-hover:pointer-events-auto"
                            onMouseEnter={() => {}}
                          />
                          {/* Background overlay for button visibility */}
                          <motion.div
                            className="hidden group-hover:block absolute inset-0 bg-black/5 pointer-events-none z-20"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.2 }}
                          />
                          {/* Always show add button centered on hover, positioned to not interfere with events */}
                          <motion.button
                            className="hidden group-hover:flex absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 items-center justify-center w-12 h-12 bg-[#0b57d0] hover:bg-[#084bb0] focus:outline-none focus:ring-2 focus:ring-[#0b57d0] text-white rounded-full shadow-xl z-30 pointer-events-auto"
                            initial={{ scale: 0, rotate: -90 }}
                            animate={{ scale: 1, rotate: 0 }}
                            whileHover={{ scale: 1.15 }}
                            whileTap={{ scale: 0.9 }}
                            transition={{
                              type: "spring",
                              stiffness: 260,
                              damping: 20,
                            }}
                            onClick={(e: React.MouseEvent) => {
                              e.stopPropagation(); // Prevent event bubbling
                              const url = `/dashboard/new-post?date=${cellDate?.getTime()}`;
                              setNavigatingTo(url);
                            }}
                            aria-label={`Add a post on ${cellDate?.toDateString()}`}
                          >
                            <motion.svg
                              className="w-6 h-6 text-white"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2.5"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.1 }}
                            >
                              <path d="M12 5v14M5 12h14" stroke="white" />
                            </motion.svg>
                          </motion.button>
                        </motion.div>
                      </motion.div>
                    );
                  },
                  header: ({ label }: { label: string }) => (
                    <div className="px-4 py-3">
                      <span className="font-semibold text-gray-700">
                        {label}
                      </span>
                    </div>
                  ),
                  event: ({ event }: { event: any }) => {
                    // Return null to prevent default event rendering since we handle it in dateCellWrapper
                    return null;
                  },
                }}
              />
            </motion.div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Page transition overlay when navigating to new-post */}
      <style jsx>{`
        @media (max-width: 640px) {
          :global(.month-calendar) {
            zoom: 0.8;
          }
        }
      `}</style>
      <AnimatePresence>
        {navigatingTo && (
          <motion.div
            className="fixed inset-0 z-60 bg-white"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            onAnimationComplete={() => {
              if (navigatingTo) {
                const url = navigatingTo;
                setNavigatingTo(null);
                router.push(url);
              }
            }}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

// Add this new component for the sidebar
const Sidebar = ({
  scheduled,
  selectedDate,
  handleEditPost,
  setSelectedDate,
}: {
  scheduled: Post[];
  selectedDate: Date;
  handleEditPost: (post: Post) => void;
  setSelectedDate: React.Dispatch<React.SetStateAction<Date>>;
}) => {
  const { drafted, published, setUser } = useUserStore();
  const { deletePost } = useWebSocket();

  const handleDeletePost = async (post: Post) => {
    deletePost({
      post_id: post.id,
    })
      .then((data: any) => {
        if (data?.posts) {
          const normalize = (p: any) => ({
            ...p,
            social_media: p.social_media || [],
            created_at: p.created_at || p.createdAt || p.created || undefined,
          });

          // Separate draft/scheduled/published posts and normalize
          const draftPosts = (data.posts || [])
            .map(normalize)
            .filter((p: any) => p.typ3 === "draft");
          const publishedPosts = (data.posts || [])
            .map(normalize)
            .filter((p: any) => p.typ3 === "publish");
          const scheduledPosts = (data.posts || [])
            .map(normalize)
            .filter((p: any) => p.typ3 === "schedule");

          // Update both drafted and scheduled posts
          setUser({
            drafted: draftPosts,
            scheduled: scheduledPosts,
            published: publishedPosts,
          });

          // Show success toast
          showSuccessToast("Post deleted successfully");
        }
      })
      .catch((error) => {
        console.error("Error deleting post:", error);
      });
  };

  const handlePrevDay = () => {
    setSelectedDate((prev: Date) => {
      const newDate = new Date(prev);
      if (newDate.getDate() > 1) {
        newDate.setDate(prev.getDate() - 1);
        return newDate;
      }
      return prev;
    });
  };

  const handleNextDay = () => {
    setSelectedDate((prev: Date) => {
      const newDate = new Date(prev);
      if (
        newDate.getDate() <
        new Date(prev.getFullYear(), prev.getMonth() + 1, 0).getDate()
      ) {
        newDate.setDate(prev.getDate() + 1);
        return newDate;
      }
      return prev;
    });
  };

  const isPostEditable = (post: any) => {
    const postDate = toDate(
      post.typ3 === "publish" ? (post as any).created_at : post.publish_date
    );
    const now = new Date();
    return post.typ3 !== "publish" && postDate !== null && postDate > now;
  };

  const getDayEvents = (selectedDate: Date): Event[] => {
    const matchingPosts =
      ([...scheduled, ...(published || [])] as Post[]).filter((post: Post) => {
        const postDate = toDate(
          post.typ3 === "publish" ? (post as any).created_at : post.publish_date
        );
        if (!postDate) return false;
        return (
          postDate.getDate() === selectedDate.getDate() &&
          postDate.getMonth() === selectedDate.getMonth()
        );
      }) || [];

    return matchingPosts.map((post) => ({
      ...post,
      time: toDate(
        post.typ3 === "publish" ? (post as any).created_at : post.publish_date
      )
        ? toDate(
            post.typ3 === "publish"
              ? (post as any).created_at
              : post.publish_date
          )!.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })
        : undefined,
      displayContent: post.caption,
      color: "bg-green-50",
      icon: "bg-blue-500",
      canEdit: isPostEditable(post),
    }));
  };

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg shadow-md p-3 sm:p-6">
        <p className="text-sm text-gray-500 mb-3 text-center">Daily View</p>
        <div className="flex items-center justify-between mb-4 px-2">
          <button
            onClick={handlePrevDay}
            className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>

          <div className="flex flex-col items-center">
            <div className="text-xs text-gray-500 font-medium">
              {selectedDate.toLocaleString("default", { month: "short" })}
            </div>
            <div className="text-xl sm:text-2xl font-semibold text-gray-900">
              {selectedDate.getDate()}
            </div>
          </div>

          <button
            onClick={handleNextDay}
            className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
        </div>

        <div className="space-y-3">
          {getDayEvents(selectedDate).map(
            (event: Event & { canEdit?: boolean }, index: number) => (
              <div
                key={index}
                className={`group relative ${
                  !event.canEdit ? "cursor-default" : ""
                }`}
              >
                <div
                  className={`${
                    event.social_media?.[0]?.platform?.includes("facebook")
                      ? "bg-[#1877F2]/10"
                      : event.social_media?.[0]?.platform?.includes("instagram")
                      ? "bg-[#E4405F]/10"
                      : "bg-gray-50"
                  } rounded-lg p-4 ${
                    event.canEdit ? "group-hover:blur-sm" : ""
                  } transition-all duration-200`}
                >
                  <div className="flex flex-col space-y-3">
                    {/* Header row with AI badge and platform info */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {event.ai_generated && <AiBadge />}
                        <div className="flex items-center space-x-3">
                          <div
                            className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              event.social_media?.[0]?.platform?.includes(
                                "facebook"
                              )
                                ? "bg-[#1877F2]/15"
                                : event.social_media?.[0]?.platform?.includes(
                                    "instagram"
                                  )
                                ? "bg-[#E4405F]/15"
                                : "bg-gray-200"
                            }`}
                          >
                            <img
                              src={`/icons/performance/${
                                event.social_media?.[0]?.platform?.includes(
                                  "facebook"
                                )
                                  ? "facebook"
                                  : event.social_media?.[0]?.platform?.includes(
                                      "instagram"
                                    )
                                  ? "instagram"
                                  : "default"
                              }-on.svg`}
                              alt={
                                event.social_media?.[0]?.platform || "default"
                              }
                              className="w-5 h-5"
                            />
                          </div>
                          <span className="text-sm font-medium text-gray-700 truncate flex items-center">
                            {event.social_media?.[0]?.platform ||
                              "Unknown Platform"}
                          </span>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 font-medium shrink-0 flex items-center">
                        {event.time}
                      </div>
                    </div>

                    {/* Content row */}
                    <div className="flex flex-col space-y-2 pl-11">
                      <p
                        className="text-sm text-gray-600 leading-relaxed overflow-hidden"
                        style={{
                          display: "-webkit-box",
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: "vertical",
                        }}
                      >
                        {event.displayContent}
                      </p>
                      {event.typ3 === "publish" && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 self-start">
                          Published
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                {event.canEdit && (
                  <div className="absolute inset-0 hidden group-hover:flex items-center justify-center gap-3 bg-black/5 rounded-lg backdrop-blur-sm">
                    <button
                      className="p-2 bg-white rounded-full hover:bg-gray-50 shadow-lg transition-all duration-200 transform hover:scale-105"
                      onClick={() => handleEditPost(event)}
                    >
                      <svg
                        className="w-4 h-4 text-gray-600"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                      </svg>
                    </button>
                    <button
                      className="p-2 bg-white rounded-full hover:bg-gray-50 shadow-lg transition-all duration-200 transform hover:scale-105"
                      onClick={() => handleDeletePost(event)}
                    >
                      <svg
                        className="w-4 h-4 text-red-500"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <path d="M3 6h18" />
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" />
                        <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                      </svg>
                    </button>
                  </div>
                )}
              </div>
            )
          )}
          {getDayEvents(selectedDate).length === 0 && (
            <div className="text-center text-gray-500 py-8">
              <svg
                className="w-12 h-12 text-gray-300 mx-auto mb-3"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1"
              >
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                <line x1="16" y1="2" x2="16" y2="6" />
                <line x1="8" y1="2" x2="8" y2="6" />
                <line x1="3" y1="10" x2="21" y2="10" />
              </svg>
              <p className="text-sm">No events scheduled for this day</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Update the PlannerPage component to use the new Sidebar
const PlannerPage = () => {
  const router = useRouter();

  const [view, setView] = useState("week");
  const [activeTab, setActiveTab] = useState("scheduled");
  const [selectedDate, setSelectedDate] = useState(new Date());
  const { selectedWorkspaceDetails } = useUserStore();
  const { getPosts, deletePost } = useWebSocket();
  const { drafted, scheduled, published, setUser } = useUserStore();

  console.log("these are published posts", published);
  console.log("these are scheduled posts", scheduled);

  console.log(selectedDate);

  console.log(drafted);

  useEffect(() => {
    getPosts({
      workspace_name: selectedWorkspaceDetails.workspace_name,
    })
      .then((data: any) => {
        if (data?.posts) {
          const normalize = (p: any) => ({
            ...p,
            social_media: p.social_media || [],
            created_at: p.created_at || p.createdAt || p.created || undefined,
          });

          // Separate posts based on typ3 and normalize
          const draftPosts = (data.posts || [])
            .map(normalize)
            .filter((p: any) => p.typ3 === "draft");
          const scheduledPosts = (data.posts || [])
            .map(normalize)
            .filter((p: any) => p.typ3 === "schedule");
          const publishedPosts = (data.posts || [])
            .map(normalize)
            .filter((p: any) => p.typ3 === "publish");

          // Debug logs
          console.log("Fetched scheduled posts:", scheduledPosts);
          console.log("Fetched published posts:", publishedPosts);

          // Update state with all posts
          setUser({
            drafted: draftPosts,
            scheduled: scheduledPosts,
            published: publishedPosts,
          });
        }
      })
      .catch((error) => {
        console.error("Error fetching posts:", error);
      });
  }, [selectedWorkspaceDetails.workspace_name]);

  // Add debug logs
  useEffect(() => {
    console.log("Current scheduled posts in state:", scheduled);
    console.log("Current published posts in state:", published);
  }, [scheduled, published]);

  const handleDeletePost = async (post_id: string) => {
    deletePost({
      post_id,
    }).then((data: any) => {
      console.log("this is the data", data);

      if (data?.posts) {
        data.posts.map((post: any) => {
          const draftPosts = data.posts.filter(
            (post: Post) => post.typ3 === "draft"
          );

          const scheduledPosts = data.posts.filter(
            (post: Post) => post.typ3 === "schedule"
          );

          const publishedPosts = data.posts.filter(
            (post: Post) => post.typ3 === "publish"
          );

          setUser({
            drafted: draftPosts,
            scheduled: scheduledPosts,
            published: publishedPosts,
          });
        });
      } else {
        setUser({ scheduled: [], drafted: [], published: [] });
      }
    });
  };

  const handleEditPost = (post: any) => {
    // Log the post data to debug
    console.log("Post data:", post);

    // Handle media object format
    let mediaUrls: string[] = [];
    if (post.media) {
      try {
        // Parse media if it's a string
        const mediaObj =
          typeof post.media === "string" ? JSON.parse(post.media) : post.media;

        // Convert object format to array of URLs
        if (typeof mediaObj === "object") {
          mediaUrls = Object.values(mediaObj).map((url) => {
            if (typeof url === "string") {
              return url.startsWith("/")
                ? `${process.env.NEXT_PUBLIC_API_URL}${url}`
                : url;
            }
            return String(url);
          });
        }
      } catch (e) {
        console.error("Error parsing media:", e);
      }
    }

    // Navigate to new-post page with query params
    router.push(
      `/dashboard/new-post?` +
        `postId=${encodeURIComponent(post.id)}` +
        `&caption=${encodeURIComponent(post.caption)}` +
        `&media=${encodeURIComponent(JSON.stringify(mediaUrls))}` +
        `&social=${encodeURIComponent(
          post.social_media?.[0]?.social_id || ""
        )}` +
        `&ai_generated=${encodeURIComponent(post.ai_generated || false)}`
    );

    // Refresh data after navigation
    setTimeout(() => {
      getPosts({
        workspace_name: selectedWorkspaceDetails.workspace_name,
      }).then((data: any) => {
        if (data?.posts) {
          const draftPosts = data.posts.filter(
            (post: Post) => post.typ3 === "draft"
          );

          const scheduledPosts = data.posts.filter(
            (post: Post) => post.typ3 === "schedule"
          );

          const publishedPosts = data.posts.filter(
            (post: Post) => post.typ3 === "publish"
          );

          setUser({
            drafted: draftPosts,
            scheduled: scheduledPosts,
            published: publishedPosts,
          });
        }
      });
    }, 300);
  };

  // Add sample draft posts

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.25 }}
      className="p-6 bg-gray-100 flex flex-col lg:flex-row gap-4 items-start justify-between md:mt-0 mt-[8vh]"
    >
      <div className="bg-white rounded-lg shadow-md md:p-6 p-3 w-full lg:w-3/4">
        <div className="flex flex-row justify-between items-center mb-6">
          <div className="flex space-x-0.5 sm:space-x-2">
            <button
              className={`px-3 sm:px-4 py-2 sm:py-2 text-xs sm:text-base ${
                activeTab === "scheduled" ? "bg-gray-200" : "bg-white"
              } rounded-full font-medium`}
              onClick={() => setActiveTab("scheduled")}
            >
              Scheduled
            </button>
            <button
              className={`px-3 sm:px-4 py-2 sm:py-2 flex-row  items-center flex text-xs sm:text-base ${
                activeTab === "drafts" ? "bg-gray-200" : "bg-white"
              } rounded-full font-medium`}
              onClick={() => setActiveTab("drafts")}
            >
              Drafts{" "}
              <span className="ml-1 px-1.5 py-0.5 bg-blue-100 text-blue-600 rounded-full text-xs">
                {drafted?.length}
              </span>
            </button>
          </div>
          {activeTab === "scheduled" && (
            <>
              <div className="hidden sm:flex bg-gray-100 rounded-full text-sm sm:text-base p-1">
                <button
                  className={`px-2 sm:px-4 py-1.5 sm:py-2 rounded-full transition-all duration-200 ${
                    view === "week"
                      ? "bg-blue-500 text-white shadow-sm"
                      : "text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                  }`}
                  onClick={() => setView("week")}
                >
                  Week
                </button>
                <button
                  className={`px-2 sm:px-4 py-1.5 sm:py-2 rounded-full transition-all duration-200 ${
                    view === "month"
                      ? "bg-blue-500 text-white shadow-sm"
                      : "text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                  }`}
                  onClick={() => setView("month")}
                >
                  Month
                </button>
              </div>

              {/* Mobile View */}
              <div className="sm:hidden flex bg-gray-100 rounded-full text-xs p-0.5">
                <button
                  className={`px-3 py-1.5 rounded-full transition-all duration-200 ${
                    view === "week"
                      ? "bg-blue-500 text-white shadow-sm font-medium"
                      : "text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                  }`}
                  onClick={() => setView("week")}
                >
                  Week
                </button>
                <button
                  className={`px-3 py-1.5 rounded-full transition-all duration-200 ${
                    view === "month"
                      ? "bg-blue-500 text-white shadow-sm font-medium"
                      : "text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                  }`}
                  onClick={() => setView("month")}
                >
                  Month
                </button>
              </div>
            </>
          )}
        </div>

        {activeTab === "scheduled" ? (
          <div className="grow w-full">
            {view === "week" ? (
              <WeekView
                scheduled={scheduled}
                selectedDate={selectedDate}
                handleEditPost={handleEditPost}
                setSelectedDate={setSelectedDate}
              />
            ) : (
              <MonthView
                setSelectedDate={setSelectedDate}
                scheduled={scheduled}
                handleEditPost={handleEditPost}
                selectedDate={selectedDate}
              />
            )}
          </div>
        ) : (
          <>
            {drafted.length === 0 ? (
              <div className="grid md:grid-cols-3 grid-cols-1 gap-4">
                <div className="col-span-full flex flex-col items-center justify-center py-12">
                  <svg
                    className="w-16 h-16 text-gray-300 mb-4"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1"
                  >
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                  </svg>
                  <p className="text-gray-500 text-lg">No drafts yet</p>
                  <p className="text-gray-400 text-sm mt-1">
                    Create a new post to get started
                  </p>
                </div>
              </div>
            ) : (
              <DraftsGrid
                drafted={drafted}
                onEdit={handleEditPost}
                onDelete={handleDeletePost}
              />
            )}
          </>
        )}
      </div>
      <div className="w-full lg:w-1/3">
        <Sidebar
          scheduled={scheduled}
          selectedDate={selectedDate}
          handleEditPost={handleEditPost}
          setSelectedDate={setSelectedDate}
        />
      </div>
      {/* <ToastWrapper containerId="planner-toast"/> */}
    </motion.div>
  );
};

export default PlannerPage;
