"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import Dashboardbtn from "~/components/dashboardbtn";
import FallbackImage from "~/components/ui/FallbackImage";
import AnimatedActionsDropdown from "~/components/ui/AnimatedActionsDropdown";
import RadixDialog from "~/components/ui/RadixDialog";
import WorkspaceCard from "./components/WorkspaceCard";
import WorkspaceRow from "./components/WorkspaceRow";
import { useWebSocket } from "../../../hooks/useWebSocket";
import { useUserStore } from "~/store/userStore"; // Add this import
import { showErrorToast, showSuccessToast } from "../../../components/toasts";
import AddSocialAccountModal from "../../../components/addsocialaccountmodal"; // Add this import
import CreateWorkspaceModal from "./components/CreateWorkspaceModal";
import UpgradeModal from "./components/UpgradeModal";
import { useRouter, useSearchParams } from "next/navigation";
import { formatDateDisplay } from "../../../utils/date";

interface Workspace {
  id: string;
  workspace_name: string;
  logo: string;
  social_media_count: string | number;
  workspace_members: string;
  created_at: string;
  last_activity: string;
  industry: string;
  website: string;
}

const WorkspacesPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const { getProfile } = useWebSocket();

  // Add handleWorkspaceSelect function
  const handleWorkspaceSelect = (workspace: any) => {
    sendSelectedWorkspace(workspace.workspace_name);
    getProfile();
    setUser({
      selectedWorkspace: workspace.workspace_name,
      selectedWorkspaceDetails: workspace,
      workspaceLogo: workspace.logo
        ? workspace.logo
        : "/default-workspace-logo.svg",
    });
    window.location.reload();
  };

  console.log(activeDropdown, "this is activeDropdown");
  const [modalType, setModalType] = useState<"edit" | "new">("edit");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showAddSocialModal, setShowAddSocialModal] = useState(false);
  const [socialTargetWorkspace, setSocialTargetWorkspace] = useState<any>(null);

  const {
    workspaces,
    workspacesDetails,
    setUser,
    selectedWorkspaceDetails,
    userId,
  } = useUserStore();
  const workspacesDetailsList = Array.isArray(workspacesDetails)
    ? (workspacesDetails as any[])
    : [];

  console.log(workspacesDetails, "this is workspacesDetails");

  const typedWorkspacesDetails = workspacesDetails;

  const toggleDropdown = (id: string) => {
    setActiveDropdown(activeDropdown === id ? null : id);
  };

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const openAddSocialForWorkspace = (workspace: any) => {
    setSocialTargetWorkspace(workspace);
    setShowAddSocialModal(true);
  };

  const closeAddSocialModal = () => {
    setShowAddSocialModal(false);
    setSocialTargetWorkspace(null);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest("[data-dropdown-root]")) setActiveDropdown(null);
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const {
    initializeWebSocket,
    deleteWorkspace,
    updateWorkspace,
    sendSelectedWorkspace,
  } = useWebSocket();
  const [editingWorkspace, setEditingWorkspace] = useState<any>();

  useEffect(() => {
    if (!searchParams) return;
    const mode = searchParams.get("mode");
    if (mode === "new") {
      setIsModalOpen(true);
      setModalType("new");
      // Optionally, you can clean up the URL
      router.replace("/dashboard/workspaces");
    }
  }, [searchParams, router]);

  const handleDeleteWorkspace = async (
    workspace_name: string,
    type: string
  ) => {
    console.log(workspace_name, "this is workspace_name");
    try {
      await new Promise((resolve) => setTimeout(resolve, 500));
      await deleteWorkspace({ workspace_name: workspace_name });
      setActiveDropdown(null);

      if (type === "delete") {
        showSuccessToast("Workspace deleted successfully", "workspaces-toast");
      } else if (type === "leave") {
        showSuccessToast("You have left the workspace", "workspaces-toast");
        window.location.reload();
      }
      // Optionally reload the page or update the workspace list
    } catch (error) {
      console.error("Error deleting workspace:", error);
    }
  };

  const handleUpgradeClick = () => {
    router.push("/dashboard/user-profile?modal=changePlan");
  };

  return (
    <div className="bg-gray-100 min-h-screen md:mt-0 mt-[8vh]">
      {/* Desktop Header - Only visible on medium screens and up */}
      <div className="hidden md:block p-6">
        <h1 className="text-2xl font-bold  mb-2 mt-8">Manage Workspaces</h1>
      </div>

      <div className="p-4 md:p-6">
        {/* Mobile Workspace List */}
        <div className="md:hidden space-y-4">
          {workspacesDetailsList.length > 0 ? (
            workspacesDetailsList.map((workspace: any) => (
              <WorkspaceCard
                key={workspace.id}
                workspace={workspace}
                userId={userId}
                onSelect={handleWorkspaceSelect}
                onEdit={(w) => {
                  setEditingWorkspace(w);
                  setModalType("edit");
                  openModal();
                }}
                onDeleteOrLeave={(name, type) =>
                  handleDeleteWorkspace(name, type)
                }
                isDropdownOpen={activeDropdown === workspace.id}
                onToggleDropdown={() => toggleDropdown(workspace.id)}
                onManageSocial={openAddSocialForWorkspace}
                formatDate={(date) =>
                  formatDateDisplay(date, { fallback: "N/A", locale: "en-CA" })
                }
              />
            ))
          ) : (
            <p className="text-center text-gray-500">
              No workspaces available.
            </p>
          )}
        </div>

        {/* Desktop Workspace List */}
        <div className="hidden md:block">
          {workspacesDetailsList.length > 0 ? (
            workspacesDetailsList.map((workspace: any) => (
              <WorkspaceRow
                key={workspace.id}
                workspace={workspace}
                userId={userId}
                onSelect={handleWorkspaceSelect}
                onEdit={(w) => {
                  setEditingWorkspace(w);
                  setModalType("edit");
                  openModal();
                }}
                onDeleteOrLeave={(name, type) =>
                  handleDeleteWorkspace(name, type)
                }
                isDropdownOpen={activeDropdown === workspace.id}
                onToggleDropdown={() => toggleDropdown(workspace.id)}
                onManageSocial={openAddSocialForWorkspace}
                formatDate={(date) =>
                  formatDateDisplay(date, { fallback: "N/A", locale: "en-CA" })
                }
              />
            ))
          ) : (
            <p className="text-center text-gray-500">
              No workspaces available.
            </p>
          )}
        </div>

        {/* Add New Workspace Button */}
        <div className="mt-6 border-2 border-dashed border-[#1565C0] rounded-2xl px-4 py-8 cursor-pointer!">
          <button
            className="w-full text-gray-500 flex items-center justify-center space-x-2 cursor-pointer!"
            onClick={() => {
              openModal();
              setModalType("new");
            }}
          >
            <span>+</span>
            <span>Add New Workspace</span>
          </button>
        </div>
      </div>

      <RadixDialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        {workspaces.length < 99 || modalType === "edit" ? (
          <CreateWorkspaceModal
            onClose={closeModal}
            workspace={editingWorkspace}
            mode={modalType}
            isModalOpen={isModalOpen}
            setIsModalOpen={setIsModalOpen}
          />
        ) : (
          <UpgradeModal onClose={closeModal} />
        )}
      </RadixDialog>
      {showAddSocialModal && (
        <AddSocialAccountModal
          onClose={closeAddSocialModal}
          dim={true}
          onAddAccount={async (platform: string) => {
            // after redirect flow the backend should update workspace socials;
            // call getProfile to refresh data and update UI
            setShowAddSocialModal(false);
            try {
              await getProfile();
              // Optionally update the selected workspace details in store
              if (socialTargetWorkspace) {
                const updated = (workspacesDetails as any[])?.find(
                  (w) =>
                    w.workspace_name === socialTargetWorkspace.workspace_name
                );
                if (updated) {
                  setUser({ selectedWorkspaceDetails: updated });
                }
              }
            } catch (err) {
              console.error(
                "Error refreshing profile after adding social:",
                err
              );
            }
          }}
          page={"workspaces"}
        />
      )}
      {/* Floating Upgrade Button - Desktop Only */}
      <div className="absolute bottom-5 right-5 p-4">
        <div className="hidden md:flex lg:left-8 flex-col items-center">
          <div className="mb-[-7vh] z-10">
            <img
              src="/icons/coin-up.svg"
              alt="Upgrade icon"
              className="w-24 h-32 scale-150"
            />
          </div>
          <div className="bg-white rounded-3xl shadow-lg p-4 w-64 text-center flex flex-col justify-center items-center pt-16">
            <h3 className="text-lg font-semibold mb-2">
              Upgrade To Unlock Premium Feature
            </h3>
            <button
              onClick={handleUpgradeClick}
              className="newpost w-3/4 text-white font-semibold py-2 px-4 rounded-full transition-all duration-500 ease-in-out flex items-center justify-center"
            >
              <span>Upgrade</span>
            </button>
          </div>
        </div>
      </div>
      {/* <ToastWrapper containerId="workspaces-toast"/> */}
    </div>
  );
};

export default WorkspacesPage;
