"use client";

import React from "react";
import FallbackImage from "~/components/ui/FallbackImage";
import AnimatedActionsDropdown from "~/components/ui/AnimatedActionsDropdown";

interface WorkspaceRowProps {
  workspace: any;
  userId: string | number | undefined;
  onSelect: (workspace: any) => void;
  onEdit: (workspace: any) => void;
  onDeleteOrLeave: (workspaceName: string, type: "delete" | "leave") => void;
  isDropdownOpen: boolean;
  onToggleDropdown: () => void;
  formatDate: (date: string) => string;
  onManageSocial?: (workspace: any) => void;
}

const WorkspaceRow: React.FC<WorkspaceRowProps> = ({
  workspace,
  userId,
  onSelect,
  onEdit,
  onDeleteOrLeave,
  isDropdownOpen,
  onToggleDropdown,
  formatDate,
  onManageSocial,
}) => {
  return (
    <div className="flex flex-col md:flex-row items-center justify-between border-b py-4">
      <div
        className="flex items-center space-x-4 w-full md:w-1/4 cursor-pointer"
        onClick={() => onSelect(workspace)}
      >
        <FallbackImage
          src={workspace?.logo ? workspace.logo : undefined}
          alt={workspace?.workspace_name ?? "Workspace"}
          width={48}
          height={48}
          className="w-12 h-12 object-cover"
        />
        <div className="flex flex-col">
          <span className="font-semibold">{workspace?.workspace_name}</span>
          {/* Show primary social (if any) and action button */}
          {workspace?.social_accounts &&
          workspace.social_accounts.length > 0 ? (
            <div className="flex items-center space-x-2 mt-1">
              <img
                src={`/icons/${workspace.social_accounts[0].platform}.svg`}
                alt={workspace.social_accounts[0].platform}
                className="w-5 h-5"
              />
              <span className="text-xs text-gray-600 truncate max-w-[100px]">
                {workspace.social_accounts[0].username ||
                  workspace.social_accounts[0].social_name}
              </span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onManageSocial && onManageSocial(workspace);
                }}
                className="text-xs text-blue-600 hover:underline ml-1"
              >
                Change
              </button>
            </div>
          ) : (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onManageSocial && onManageSocial(workspace);
              }}
              className="text-sm text-blue-600 hover:underline mt-1"
            >
              Add Social
            </button>
          )}
        </div>
      </div>
      <div className="flex flex-col md:flex-row items-center justify-between space-y-2 md:space-y-0 md:space-x-8 w-full md:w-3/4">
        <div className="text-center">
          <div className="font-semibold">
            {workspace?.social_accounts?.length ?? 0}
          </div>
          <div className="text-sm text-gray-500">Social Media Accounts</div>
        </div>
        <div className="text-center">
          <div className="font-semibold">{workspace?.members?.length ?? 0}</div>
          <div className="text-sm text-gray-500">Team Members</div>
        </div>
        <div className="text-center">
          <div className="font-semibold">
            {formatDate(workspace?.created_at)}
          </div>
          <div className="text-sm text-gray-500">Date Created</div>
        </div>
        <div className="text-center">
          <div className="font-semibold">{workspace?.last_activity}</div>
          <div className="text-sm text-gray-500">Last activity</div>
        </div>

        <div className="relative ml-auto" data-dropdown-root>
          <button
            className="text-gray-600 hover:bg-gray-100 rounded-full p-2"
            aria-label="Workspace actions"
            title="Actions"
            onClick={onToggleDropdown}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              width="22"
              height="22"
              fill="currentColor"
            >
              <circle cx="5" cy="12" r="2" />
              <circle cx="12" cy="12" r="2" />
              <circle cx="19" cy="12" r="2" />
            </svg>
          </button>
          <AnimatedActionsDropdown
            isOpen={isDropdownOpen}
            onClose={onToggleDropdown}
          >
            {workspace?.owner == userId ? (
              <>
                <button
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex flex-row items-center gap-2"
                  onClick={() => onEdit(workspace)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z" />
                  </svg>
                  Edit
                </button>
                <hr className="w-full" />
                <button
                  className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex flex-row items-center gap-2"
                  onClick={() =>
                    onDeleteOrLeave(workspace.workspace_name, "delete")
                  }
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M3 6h18M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2M10 11v6M14 11v6" />
                  </svg>
                  Delete
                </button>
              </>
            ) : (
              <button
                className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex flex-row items-center gap-2"
                onClick={() =>
                  onDeleteOrLeave(workspace.workspace_name, "leave")
                }
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 6h18M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2M10 11v6M14 11v6" />
                </svg>
                Leave
              </button>
            )}
          </AnimatedActionsDropdown>
        </div>
      </div>
    </div>
  );
};

export default WorkspaceRow;
