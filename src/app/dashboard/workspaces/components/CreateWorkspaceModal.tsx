"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "../../../../store/userStore";
import { useWebSocket } from "../../../../hooks/useWebSocket";
import {
  showErrorToast,
  showSuccessToast,
} from "../../../../components/toasts";
import Dashboardbtn from "~/components/dashboardbtn";
import AddSocialAccountModal from "../../../../components/addsocialaccountmodal";
import FallbackImage from "~/components/ui/FallbackImage";
import * as Select from "@radix-ui/react-select";
import RadixDialog from "~/components/ui/RadixDialog";

interface CreateWorkspaceModalProps {
  onClose: () => void;
  workspace?: any;
  mode?: "new" | "edit";
  isModalOpen: boolean;
  setIsModalOpen: (value: boolean) => void;
}

const CreateWorkspaceModal: React.FC<CreateWorkspaceModalProps> = ({
  onClose,
  workspace,
  mode = "new",
  isModalOpen,
  setIsModalOpen,
}) => {
  const router = useRouter();
  const { setUser } = useUserStore();
  const {
    createWorkspace,
    updateWorkspace,
    sendSelectedWorkspace,
  } = useWebSocket();

  // Form state
  const [logo, setLogo] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(
    mode === "edit" ?   workspace?.logo : null
  );
  const [companyName, setCompanyName] = useState(
    mode === "edit" ? workspace?.workspace_name : ""
  );
  const [companyMembers, setCompanyMembers] = useState(
    mode === "edit" ? workspace?.workspace_members : ""
  );
  const [industry, setIndustry] = useState(
    mode === "edit" ? workspace?.industry : ""
  );
  const [website, setWebsite] = useState(
    mode === "edit" ? workspace?.website : ""
  );

  // Modal states
  const [isAddSocialModalOpen, setIsAddSocialModalOpen] = useState(false);
  const [isTeamMemberModalOpen, setIsTeamMemberModalOpen] = useState(false);

  // Member options for dropdown
  const memberOptions = [
    { value: "1-5", label: "1 - 5" },
    { value: "5-10", label: "5 - 10" },
    { value: "10-20", label: "10 - 20" },
    { value: "20-50", label: "20 - 50" },
    { value: "50-100", label: "50 - 100" },
    { value: "100+", label: "100+" },
  ];

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setLogo(file);
      setLogoPreview(URL.createObjectURL(file));
    }
  };

  const removeLogo = () => {
    setLogo(null);
    setLogoPreview(null);
  };

  const isFormValid = () => {
    return (
      companyName.trim() !== "" &&
      companyMembers.trim() !== "" &&
      industry.trim() !== "" &&
      website.trim() !== ""
    );
  };

  const convertToBase64 = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        const base64 = result.replace("data:image/jpeg;base64,", "");
        resolve(base64);
      };
      reader.readAsDataURL(file);
    });
  };

  const handleWebsiteChange = (value: string) => {
    if (!value) {
      setWebsite("");
    } else if (!value.startsWith("http://") && !value.startsWith("https://")) {
      setWebsite(`https://${value}`);
    } else {
      setWebsite(value);
    }
  };

  const handleSaveChanges = async () => {
    if (!isFormValid()) {
      showErrorToast("Please fill in all required fields", "workspaces-toast");
      return;
    }

    const logoBase64 = logo ? await convertToBase64(logo) : null;

    if (mode === "edit") {
      const workspaceData = {
        id: workspace.id,
        industry: industry,
        website: website,
        logo: logoBase64,
        members: companyMembers,
        workspace_name: companyName,
      };

      try {
        await updateWorkspace(workspaceData);
        showSuccessToast("Workspace updated successfully", "workspaces-toast");
        setIsModalOpen(false);
      } catch (error) {
        showErrorToast("Failed to update workspace", "workspaces-toast");
      }
    } else {
      const workspaceData = {
        name: companyName,
        industry: industry,
        members: companyMembers,
        website: website,
        logo: logoBase64 || "",
      };

      try {
        const response: any = await createWorkspace(workspaceData);
        if (response?.success) {
          showSuccessToast(response?.message, "workspaces-toast");

          const workspaceDetails = {
            workspace_name: companyName,
            industry: industry,
            website: website,
            workspace_members: companyMembers,
            logo: logoBase64 || "",
            id: "",
            social_media_count: 0,
            created_at: "",
            last_activity: "",
            length: 0,
            members: [],
          };

          sendSelectedWorkspace(companyName);

          setUser({
            selectedWorkspace: workspaceDetails.workspace_name,
            selectedWorkspaceDetails: workspaceDetails,
            workspaceLogo: logoBase64
              ? `${process.env.NEXT_PUBLIC_API_URL}${logoBase64}`
              : "/default-workspace-logo.svg",
          });
          setIsModalOpen(false);
        } else {
          showErrorToast(response?.message || "Failed to create workspace", "workspaces-toast");
        }
      } catch (error) {
        showErrorToast("Failed to create workspace", "workspaces-toast");
      }
    }
    onClose();
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    onClose();
  };

  return (
    <>
      <RadixDialog
        open={isModalOpen}
        onOpenChange={(open) => {
          setIsModalOpen(open);
          if (!open) onClose();
        }}
        title={mode === "edit" ? "Edit Workspace" : "Create a New Workspace"}
      >
        <div className="space-y-8">
          {/* Logo Upload Section */}
          <div className="flex items-center justify-center">
            <div className="relative">
              {logoPreview ? (
                <>
                  <FallbackImage
                    src={logoPreview}
                    alt="Logo preview"
                    width={128}
                    height={128}
                    fallbackSize={128}
                    className="h-32 w-32 rounded-full object-cover border-2 border-gray-200 shadow-sm"
                  />
                  <button
                    type="button"
                    onClick={removeLogo}
                    className="absolute -right-2 -top-2 inline-flex h-8 w-8 items-center justify-center rounded-full bg-red-500 text-white shadow-lg hover:bg-red-600 transition-colors"
                    aria-label="Remove logo"
                  >
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </>
              ) : (
                <label className="flex h-32 w-32 cursor-pointer flex-col items-center justify-center rounded-full border-2 border-dashed border-gray-300 bg-gray-50 text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-colors">
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <span className="text-xs font-medium text-center">Upload Logo</span>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    onChange={handleLogoUpload}
                    accept="image/jpeg,image/png,image/jpg"
                  />
                </label>
              )}
            </div>
          </div>

          {/* Form Fields */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Company Name */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-900">
                Company Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                className="w-full rounded-lg border border-gray-300 px-4 py-3 text-sm placeholder-gray-500 outline-none transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                placeholder="Enter your company name"
                required
              />
            </div>

            {/* Company Members */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-900">
                Company Members <span className="text-red-500">*</span>
              </label>
              <Select.Root value={companyMembers || undefined} onValueChange={setCompanyMembers}>
                <Select.Trigger className="inline-flex w-full items-center justify-between rounded-lg border border-gray-300 bg-white px-4 py-3 text-sm outline-none transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 data-placeholder:text-gray-500">
                  <Select.Value placeholder="Select number of members" />
                  <Select.Icon>
                    <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </Select.Icon>
                </Select.Trigger>
                <Select.Portal>
                  <Select.Content className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-lg z-100">
                    <Select.Viewport className="p-1">
                      {memberOptions.map((option) => (
                        <Select.Item
                          key={option.value}
                          value={option.value}
                          className="relative flex cursor-pointer select-none items-center rounded-md px-3 py-2 text-sm outline-none hover:bg-blue-50 hover:text-blue-900 data-highlighted:bg-blue-50 data-highlighted:text-blue-900"
                        >
                          <Select.ItemText>{option.label}</Select.ItemText>
                        </Select.Item>
                      ))}
                    </Select.Viewport>
                  </Select.Content>
                </Select.Portal>
              </Select.Root>
            </div>

            {/* Industry */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-900">
                Industry <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                className="w-full rounded-lg border border-gray-300 px-4 py-3 text-sm placeholder-gray-500 outline-none transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
                value={industry}
                onChange={(e) => setIndustry(e.target.value)}
                placeholder="e.g., Technology, Healthcare, Finance"
                required
              />
            </div>

            {/* Website */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-900">
                Website <span className="text-red-500">*</span>
              </label>
              <input
                type="url"
                className="w-full rounded-lg border border-gray-300 px-4 py-3 text-sm placeholder-gray-500 outline-none transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
                value={website}
                onChange={(e) => handleWebsiteChange(e.target.value)}
                placeholder="https://example.com"
                required
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-center pt-4">
            <div className="flex w-full max-w-xs">
              {/* Mobile Button */}
              <button
                className={`md:hidden w-full rounded-lg px-6 py-3 text-sm font-semibold transition-all duration-200 ${
                  isFormValid()
                    ? "bg-slate-900 text-white hover:bg-slate-800 shadow-lg hover:shadow-xl"
                    : "bg-gray-200 text-gray-400 cursor-not-allowed"
                }`}
                onClick={handleSaveChanges}
                disabled={!isFormValid()}
              >
                {mode === "edit" ? "Update Workspace" : "Create Workspace"}
              </button>

              {/* Desktop Button */}
              <div className="hidden md:flex justify-center w-full">
                <Dashboardbtn
                  variant={!isFormValid() ? "disabled" : "default"}
                  onClick={handleSaveChanges}
                >
                  {mode === "edit" ? "Update Workspace" : "Create Workspace"}
                </Dashboardbtn>
              </div>
            </div>
          </div>
        </div>
      </RadixDialog>

      {/* Add Social Account Modal */}
      {isAddSocialModalOpen && (
        <AddSocialAccountModal
          onClose={() => setIsAddSocialModalOpen(false)}
          onAddAccount={() => {}}
          dim={true}
        />
      )}

      {/* Team Member Modal */}
      {isTeamMemberModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex justify-center items-center z-60 transition-opacity duration-200">
          <div className="bg-white rounded-2xl p-8 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                Let's add some Team members
              </h2>
              <button
                onClick={() => setIsTeamMemberModalOpen(false)}
                className="inline-flex h-10 w-10 items-center justify-center rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
              >
                <span className="sr-only">Close</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <p className="text-gray-600 mb-8 text-base">
              You only need to choose between members you have invited in Team
              page. You can always add more later!
            </p>

            <h3 className="text-xl font-semibold mb-6 text-gray-900">
              Choose the member to get access
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {/* Existing Members */}
              <div className="flex flex-col items-center p-4 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 hover:border-gray-300 transition-colors">
                <div className="w-16 h-16 rounded-full bg-gray-200 mb-3 overflow-hidden">
                  <img
                    src="/default-avatar.png"
                    alt="Member"
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-center text-sm font-medium text-gray-900">Amin Akbari</span>
              </div>

              <div className="flex flex-col items-center p-4 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 hover:border-gray-300 transition-colors">
                <div className="w-16 h-16 rounded-full bg-gray-200 mb-3 overflow-hidden">
                  <img
                    src="/default-avatar.png"
                    alt="Member"
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-center text-sm font-medium text-gray-900">Ali Riazi</span>
              </div>

              {/* Add New Member Cards */}
              {[1, 2].map((_, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-xl cursor-pointer hover:bg-gray-50 hover:border-gray-400 transition-colors"
                  onClick={() => router.push("/dashboard/team")}
                >
                  <div className="w-16 h-16 rounded-full bg-gray-100 mb-3 flex items-center justify-center">
                    <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <span className="text-center text-sm font-medium text-gray-600">Add Member</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CreateWorkspaceModal;
