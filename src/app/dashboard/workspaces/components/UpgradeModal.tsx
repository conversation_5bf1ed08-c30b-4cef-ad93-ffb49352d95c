"use client";

import React from "react";
import { useRouter } from "next/navigation";
import Dashboardbtn from "~/components/dashboardbtn";
import { ToastWrapper } from "../../../../components/toasts";

const UpgradeModal: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const router = useRouter();

  const handleUpgradeClick = () => {
    router.push("/dashboard/user-profile?modal=changePlan");
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex justify-center items-center z-50 p-4">
      <div className="bg-white rounded-lg p-4 sm:p-8 w-full max-w-md">
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <h2 className="text-xl sm:text-2xl font-bold text-center mb-4">
          You have reached maximum number of workspace.
        </h2>
        <p className="text-center mb-6">
          With our enterprise plan you can create unlimited number of dashboards
          & attach unlimited team member to manage your business social medias.
          also , you can use AI as many as you want!
        </p>
        <h3 className="text-lg sm:text-xl font-semibold text-center mb-4">
          Why Upgrade to Enterprise?
        </h3>
        <div className="flex justify-between mb-6">
          <div className="text-center">
            <p className="font-semibold">Unlimited</p>
            <p>Work Space</p>
          </div>
          <div className="text-center">
            <p className="font-semibold">Unlimited</p>
            <p>AI Usage</p>
          </div>
          <div className="text-center">
            <p className="font-semibold">Unlimited</p>
            <p>Team Member</p>
          </div>
        </div>
        <Dashboardbtn
          variant="primary"
          className="w-full"
          onClick={handleUpgradeClick}
        >
          Upgrade to Enterprise Plan
        </Dashboardbtn>
      </div>
      <ToastWrapper containerId="workspaces-toast" />
    </div>
  );
};

export default UpgradeModal;
