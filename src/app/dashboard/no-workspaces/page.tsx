"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import Dashboardbtn from "~/components/dashboardbtn";
import { useWebSocket } from "~/hooks/useWebSocket";
import { useUserStore } from "~/store/userStore";
import {
  showSuccessToast,
  showErrorToast,
  ToastWrapper,
} from "~/components/toasts";
import AddSocialAccountModal from "~/components/addsocialaccountmodal";
import { useRouter } from "next/navigation";
import AuthBtn from "~/components/authbtn";

interface WorkspaceDetails {
  workspace_name: string;
  industry: string;
  website: string;
  workspace_members: string;
  logo: string;
}

const NoWorkspaces = () => {
  const router = useRouter();
  const { createWorkspace, sendSelectedWorkspace } = useWebSocket();
  const { setUser, workspacesDetails } = useUserStore();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [logo, setLogo] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [companyName, setCompanyName] = useState<string>("");
  const [companyMembers, setCompanyMembers] = useState<string>("");
  const [industry, setIndustry] = useState<string>("");
  const [website, setWebsite] = useState<string>("");
  const [isAddSocialModalOpen, setIsAddSocialModalOpen] =
    useState<boolean>(false);

  useEffect(() => {
    const hasWorkspaces = workspacesDetails && workspacesDetails.length > 0;
    if (hasWorkspaces) {
      router.replace("/dashboard");
    }
  }, [workspacesDetails, router]);

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setLogo(file);
      setLogoPreview(URL.createObjectURL(file));
    }
  };

  const removeLogo = () => {
    setLogo(null);
    setLogoPreview(null);
  };

  const isFormValid = () => {
    return (
      companyName.trim() !== "" &&
      companyMembers.trim() !== "" &&
      industry.trim() !== "" &&
      website.trim() !== ""
    );
  };

  // Function to convert file to base64
  const convertToBase64 = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        const base64 = result.replace("data:image/jpeg;base64,", "");
        resolve(base64);
      };
      reader.readAsDataURL(file);
    });
  };

  const handleSaveChanges = async () => {
    if (!isFormValid()) {
      showErrorToast(
        "Please fill in all required fields",
        "noworkspaces-toast"
      );
      return;
    }

    setIsLoading(true);
    try {
      const logoBase64 = logo ? await convertToBase64(logo) : null;
      const workspaceData = {
        name: companyName,
        industry: industry,
        members: companyMembers,
        website: website,
        logo: logoBase64 || "",
      };
      const response = await createWorkspace(workspaceData);
      if (response?.success) {
        showSuccessToast(
          "Workspace created successfully!",
          "noworkspaces-toast"
        );
        // Set the selected workspace after creation
        const workspaceDetails: WorkspaceDetails = {
          workspace_name: companyName,
          industry: industry,
          website: website,
          workspace_members: companyMembers,
          logo: logoBase64 || "",
        };

        sendSelectedWorkspace(companyName);

        setUser({
          selectedWorkspace: workspaceDetails.workspace_name,
          selectedWorkspaceDetails: JSON.stringify(workspaceDetails),
          workspaceLogo: logoBase64
            ? `${process.env.NEXT_PUBLIC_API_URL}${logoBase64}`
            : "/default-workspace-logo.svg",
        });
        window.location.href = "/dashboard";
      } else {
        showErrorToast("Failed to create workspace", "noworkspaces-toast");
      }
    } catch (error) {
      showErrorToast(
        "Failed to create workspace. Please try again.",
        "noworkspaces-toast"
      );
      console.error("Error creating workspace:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSaveChanges();
    }
  };

  return (
    <div className="relative inset-0 flex justify-center items-center p-4 min-h-screen">
      {/* <ToastWrapper containerId="noworkspaces-toast"/> */}
      <div className="relative inset-0 bg-transparent bg-opacity-50 backdrop-blur-sm"></div>
      <div className="bg-white rounded-lg p-4 sm:p-8 w-full max-w-2xl max-h-[90vh] overflow-y-auto relative z-10">
        <div className="mb-6">
          <h2 className="text-xl sm:text-2xl font-bold">
            Create Your First Workspace
          </h2>
        </div>

        <div className="mb-6">
          <label className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 cursor-pointer">
            {logoPreview ? (
              <div className="relative group w-fit">
                <img
                  src={logoPreview}
                  alt="Logo preview"
                  className="h-20 w-20 object-cover rounded-lg"
                />
                <div
                  className="absolute inset-0 bg-black/50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center cursor-pointer"
                  onClick={removeLogo}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
              </div>
            ) : (
              <label className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 cursor-pointer">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10 sm:h-12 sm:w-12 bg-gray-200 rounded-full p-3"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                  />
                </svg>
                <span>Upload Logo</span>
                <input
                  type="file"
                  className="hidden"
                  onChange={handleLogoUpload}
                  accept="image/jpeg"
                />
              </label>
            )}
          </label>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Company Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              className="w-full p-2 border rounded-md"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              onKeyPress={handleKeyPress}
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Company members <span className="text-red-500">*</span>
            </label>
            <select
              className="w-full p-2 border rounded-md bg-white"
              value={companyMembers}
              onChange={(e) => setCompanyMembers(e.target.value)}
              required
            >
              <option value="">Select number of members</option>
              <option value="5-10">5 - 10</option>
              <option value="10-20">10 - 20</option>
              <option value="20-50">20 - 50</option>
              <option value="50-100">50 - 100</option>
              <option value="100+">100+</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Industry <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              className="w-full p-2 border rounded-md"
              value={industry}
              onChange={(e) => setIndustry(e.target.value)}
              onKeyPress={handleKeyPress}
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Website <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              className="w-full p-2 border rounded-md"
              value={website}
              onChange={(e) => {
                const value = e.target.value;
                if (!value) {
                  setWebsite("");
                } else if (
                  !value.startsWith("http://") &&
                  !value.startsWith("https://")
                ) {
                  setWebsite(`https://${value}`);
                } else {
                  setWebsite(value);
                }
              }}
              onKeyPress={handleKeyPress}
              placeholder="https://"
              required
            />
          </div>
        </div>

        <div className="flex justify-center mt-8">
          <AuthBtn
            text={isLoading ? "Creating Workspace..." : "Create Workspace"}
            style="default"
            onClick={handleSaveChanges}
            disabled={isLoading}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  );
};

export default NoWorkspaces;
