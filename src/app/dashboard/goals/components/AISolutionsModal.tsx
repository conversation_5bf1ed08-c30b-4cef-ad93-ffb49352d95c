"use client";

import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";

interface AISolution {
  id: string;
  title: string;
  description: string;
  impact: "HIGH" | "MEDIUM" | "LOW";
  category: "optimization" | "content" | "targeting" | "timing" | "engagement";
  icon: string;
  iconColor: string;
  bgColor: string;
  actionText: string;
  confidence: number;
  estimatedImprovement: string;
  detailedDescription: string;
  implementationSteps: string[];
  expectedTimeframe: string;
}

interface AISolutionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  goalId?: number;
}

// Extended AI solutions data with more details
const detailedSolutions: AISolution[] = [
  {
    id: "1",
    title: "AI Content Optimizer",
    description:
      "Machine learning analysis shows video content with captions performs 85% better. AI suggests optimal format, timing, and hashtags for maximum engagement.",
    detailedDescription:
      "Our neural network analyzed 10M+ posts similar to yours and identified key success patterns. Video content with AI-generated captions, posted during peak hours with trending hashtags, consistently outperforms other formats.",
    impact: "HIGH",
    category: "content",
    icon: "fa-wand-magic-sparkles",
    iconColor: "text-dark-blue-dark",
    bgColor: "",
    actionText: "Optimize Content",
    confidence: 94,
    estimatedImprovement: "+85% engagement",
    implementationSteps: [
      "AI analyzes your current content performance",
      "Generate optimized video content suggestions",
      "Apply AI-recommended hashtags and captions",
      "Schedule posts for AI-predicted optimal times",
      "Monitor AI-driven performance improvements",
    ],
    expectedTimeframe: "1 week to see significant results",
  },
  {
    id: "2",
    title: "AI Trend Predictor",
    description:
      "Machine learning identifies trending topics 48 hours before they peak. AI predicts #TechTuesday will trend +120% this week.",
    detailedDescription:
      "Our predictive AI model analyzes millions of social signals, news trends, and engagement patterns to forecast viral content opportunities. Early adoption of trending topics can increase reach by 200-400%.",
    impact: "HIGH",
    category: "optimization",
    icon: "fa-chart-line",
    iconColor: "text-dark-blue-dark",
    bgColor: "",
    actionText: "Apply Trends",
    confidence: 91,
    estimatedImprovement: "+250% reach",
    implementationSteps: [
      "AI scans trending topics in real-time",
      "Receive personalized trend alerts",
      "Generate content around predicted trends",
      "Schedule posts for optimal trend timing",
      "Track trend performance with AI analytics",
    ],
    expectedTimeframe: "24-48 hours to capitalize on trends",
  },
  {
    id: "3",
    title: "AI Caption Generator",
    description:
      "Neural language model creates engaging captions that increase comments by 67%. AI analyzes your brand voice and generates personalized content.",
    detailedDescription:
      "Our advanced NLP model has been trained on millions of high-performing social media posts. It understands your brand voice, audience preferences, and trending language patterns to generate captions that drive engagement.",
    impact: "HIGH",
    category: "content",
    icon: "fa-robot",
    iconColor: "text-dark-blue-dark",
    bgColor: "",
    actionText: "Generate Captions",
    confidence: 89,
    estimatedImprovement: "+67% comments",
    implementationSteps: [
      "AI analyzes your previous high-performing posts",
      "Train model on your unique brand voice",
      "Generate multiple caption variations",
      "A/B test AI-generated vs manual captions",
      "Refine AI model based on performance data",
    ],
    expectedTimeframe: "1-2 weeks to see engagement boost",
  },
  {
    id: "4",
    title: "Smart Hashtag AI",
    description:
      "AI analyzes 50M+ hashtags daily to recommend optimal tags. Current prediction: #TechTuesday +120%, #AIInnovation +89% engagement boost.",
    detailedDescription:
      "Our hashtag intelligence system monitors real-time hashtag performance across all platforms. It identifies emerging trends, calculates engagement potential, and suggests the perfect hashtag mix for your content.",
    impact: "HIGH",
    category: "optimization",
    icon: "fa-brain",
    iconColor: "text-dark-blue-dark",
    bgColor: "",
    actionText: "Get Smart Tags",
    confidence: 87,
    estimatedImprovement: "+89% discoverability",
    implementationSteps: [
      "AI scans trending hashtags in real-time",
      "Analyze hashtag performance for your niche",
      "Generate personalized hashtag recommendations",
      "Auto-suggest optimal hashtag combinations",
      "Track and optimize hashtag performance",
    ],
    expectedTimeframe: "Immediate impact with AI suggestions",
  },
  {
    id: "5",
    title: "AI Engagement Optimizer",
    description:
      "Machine learning identifies optimal posting times with 94% accuracy. AI predicts Tuesday 3 PM posts will achieve 92% success rate.",
    detailedDescription:
      "Our engagement prediction AI analyzes your audience behavior patterns, platform algorithms, and global engagement trends to identify the perfect posting windows for maximum reach and interaction.",
    impact: "HIGH",
    category: "timing",
    icon: "fa-clock",
    iconColor: "text-dark-blue-dark",
    bgColor: "",
    actionText: "Optimize Timing",
    confidence: 94,
    estimatedImprovement: "+92% success rate",
    implementationSteps: [
      "AI analyzes your audience activity patterns",
      "Identify peak engagement time windows",
      "Generate personalized posting schedule",
      "Auto-schedule posts for optimal times",
      "Monitor and adjust based on performance",
    ],
    expectedTimeframe: "Immediate optimization available",
  },
  {
    id: "6",
    title: "AI Performance Analytics",
    description:
      "Deep learning analyzes your content performance and predicts future success with 96% accuracy. Get AI-powered insights and recommendations.",
    detailedDescription:
      "Our advanced analytics AI processes millions of data points from your posts, audience interactions, and competitor analysis to provide actionable insights and predict which content will perform best.",
    impact: "HIGH",
    category: "optimization",
    icon: "fa-chart-bar",
    iconColor: "text-dark-blue-dark",
    bgColor: "",
    actionText: "View Analytics",
    confidence: 96,
    estimatedImprovement: "+150% ROI",
    implementationSteps: [
      "AI analyzes all your historical content",
      "Generate detailed performance insights",
      "Predict future content success rates",
      "Receive personalized optimization tips",
      "Track AI-recommended improvements",
    ],
    expectedTimeframe: "Real-time insights and predictions",
  },
];

const impactColors = {
  HIGH: "bg-dark-blue-dark text-white border-dark-blue-dark-hover",
  MEDIUM: "bg-dark-blue-normal text-white border-dark-blue-normal-hover",
  LOW: "bg-gray-100 text-gray-700 border-gray-200",
};

const categoryColors = {
  optimization: "bg-dark-blue-dark text-white",
  content: "bg-dark-blue-normal text-white",
  targeting: "bg-dark-blue-normal-hover text-white",
  timing: "bg-dark-blue-dark-hover text-white",
  engagement: "bg-dark-blue-darker text-white",
};

export const AISolutionsModal = ({
  isOpen,
  onClose,
  goalId,
}: AISolutionsModalProps) => {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [expandedSolution, setExpandedSolution] = useState<string | null>(null);

  const categories = [
    { id: "all", name: "All Solutions", icon: "fa-wand-magic-sparkles" },
    { id: "timing", name: "Timing", icon: "fa-clock" },
    { id: "content", name: "Content", icon: "fa-video" },
    { id: "targeting", name: "Targeting", icon: "fa-users" },
    { id: "optimization", name: "Optimization", icon: "fa-chart-line" },
    { id: "engagement", name: "Engagement", icon: "fa-heart" },
  ];

  const filteredSolutions =
    selectedCategory === "all"
      ? detailedSolutions
      : detailedSolutions.filter(
          (solution) => solution.category === selectedCategory
        );

  const averageConfidence = Math.round(
    filteredSolutions.reduce((sum, solution) => sum + solution.confidence, 0) /
      filteredSolutions.length
  );

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/30 flex items-center justify-center z-9999 overflow-hidden"
        onClick={(e) => e.target === e.currentTarget && onClose()}
        style={{
          backdropFilter: "blur(2px)",
          padding: "1rem",
        }}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="bg-white rounded-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden shadow-2xl"
        >
          {/* Header */}
          <div className="p-6 border-b border-dark-blue-dark bg-dark-blue-dark">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <i className="fas fa-wand-magic-sparkles text-white text-3xl"></i>
                <div>
                  <h2 className="text-2xl font-bold text-white">
                    AI Solutions
                  </h2>
                  <p className="text-white/80">
                    Powered by machine learning and neural networks
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">
                    {averageConfidence}%
                  </div>
                  <div className="text-sm text-white/70">AI Confidence</div>
                </div>
                <button
                  onClick={onClose}
                  className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-lg flex items-center justify-center transition-colors"
                >
                  <i className="fas fa-times text-white"></i>
                </button>
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2 mt-4">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
                    selectedCategory === category.id
                      ? "bg-dark-blue-dark text-white shadow-md"
                      : "bg-white text-dark-blue-dark hover:bg-dark-blue-light border border-dark-blue-normal"
                  }`}
                >
                  <i className={`fas ${category.icon} text-xs`}></i>
                  <span>{category.name}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredSolutions.map((solution, index) => (
                <motion.div
                  key={solution.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white rounded-xl border border-dark-blue-normal shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
                >
                  <div className="p-6">
                    <div className="flex items-start gap-4">
                      <i
                        className={`fas ${solution.icon} ${solution.iconColor} text-2xl shrink-0`}
                      ></i>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-3">
                          <h3 className="font-bold text-dark-blue-dark text-lg">
                            {solution.title}
                          </h3>
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full border ${
                              impactColors[solution.impact]
                            }`}
                          >
                            {solution.impact}
                          </span>
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full ${
                              categoryColors[solution.category]
                            }`}
                          >
                            {solution.category}
                          </span>
                        </div>
                        <p className="text-gray-600 mb-4">
                          {solution.description}
                        </p>

                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-4">
                            <div className="text-center">
                              <div className="text-lg font-bold text-dark-blue-dark">
                                {solution.confidence}%
                              </div>
                              <div className="text-xs text-dark-blue-normal">
                                Confidence
                              </div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-bold text-dark-blue-darker">
                                {solution.estimatedImprovement}
                              </div>
                              <div className="text-xs text-dark-blue-normal">
                                Expected
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-dark-blue-darker">
                              {solution.expectedTimeframe}
                            </div>
                            <div className="text-xs text-dark-blue-normal">
                              Timeline
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          <button className="flex-1 px-4 py-2 bg-dark-blue-dark text-white font-medium rounded-lg hover:bg-dark-blue-darker transition-all duration-200">
                            {solution.actionText}
                          </button>
                          <button
                            onClick={() =>
                              setExpandedSolution(
                                expandedSolution === solution.id
                                  ? null
                                  : solution.id
                              )
                            }
                            className="px-4 py-2 border border-dark-blue-normal text-dark-blue-dark font-medium rounded-lg hover:bg-dark-blue-light transition-colors"
                          >
                            {expandedSolution === solution.id
                              ? "Less"
                              : "Details"}
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Expanded Details */}
                    {expandedSolution === solution.id && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        className="mt-6 pt-6 border-t border-gray-200"
                      >
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-semibold text-dark-blue-darker mb-2">
                              Detailed Analysis
                            </h4>
                            <p className="text-sm text-dark-blue-dark">
                              {solution.detailedDescription}
                            </p>
                          </div>
                          <div>
                            <h4 className="font-semibold text-dark-blue-darker mb-2">
                              Implementation Steps
                            </h4>
                            <ul className="space-y-2">
                              {solution.implementationSteps.map(
                                (step, stepIndex) => (
                                  <li
                                    key={stepIndex}
                                    className="flex items-start gap-2 text-sm text-dark-blue-dark"
                                  >
                                    <div className="w-5 h-5 bg-dark-blue-dark text-white rounded-full flex items-center justify-center text-xs font-medium shrink-0 mt-0.5">
                                      {stepIndex + 1}
                                    </div>
                                    <span>{step}</span>
                                  </li>
                                )
                              )}
                            </ul>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
