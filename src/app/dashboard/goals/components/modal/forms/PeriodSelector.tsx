"use client";

import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { motion, AnimatePresence } from 'framer-motion';
import { UseFormRegister, UseFormSetValue } from 'react-hook-form';
import { useEffect, useRef, useState } from 'react';
import { GoalCreateFormData } from '../../../types';

interface PeriodSelectorProps {
  register: UseFormRegister<GoalCreateFormData>;
  setValue: UseFormSetValue<GoalCreateFormData>;
  watchedValues: GoalCreateFormData;
  isLoading: boolean;
  isMobile: boolean;
}

const periodNumbers = [1, 2, 3, 4, 5, 6];
const periodUnits = ['day', 'week', 'month'] as const;

export const PeriodSelector = ({ register, setValue, watchedValues, isLoading, isMobile }: PeriodSelectorProps) => {
  const numberOpenKey = 'period-number';
  const unitOpenKey = 'period-unit';

  const Menu = ({
    triggerLabel,
    items,
    onSelect,
    disabled,
    align = 'start',
    widthClass = 'w-24',
  }: {
    triggerLabel: string;
    items: { key: string; label: string; selected?: boolean }[];
    onSelect: (key: string) => void;
    disabled?: boolean;
    align?: 'start' | 'end' | 'center';
    widthClass?: string;
  }) => {
    const triggerRef = useRef<HTMLButtonElement | null>(null);
    const [contentWidth, setContentWidth] = useState<number | undefined>();

    useEffect(() => {
      const measure = () => {
        if (triggerRef.current) {
          setContentWidth(triggerRef.current.offsetWidth);
        }
      };
      measure();
      window.addEventListener('resize', measure);
      return () => window.removeEventListener('resize', measure);
    }, []);

    return (
      <DropdownMenu.Root>
        <DropdownMenu.Trigger asChild disabled={disabled}>
          <button
            ref={triggerRef}
            type="button"
            className={`${widthClass} ${isMobile ? 'h-11' : 'h-11'} px-3 bg-white rounded-lg border border-gray-300 text-left text-base flex items-center justify-between gap-2 shadow-sm hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/30 transition-all`}
          >
            <span className="truncate text-gray-800">{triggerLabel}</span>
            <i className="fas fa-chevron-down text-gray-400 text-xs" />
          </button>
        </DropdownMenu.Trigger>
        <DropdownMenu.Portal>
          <DropdownMenu.Content
            align={align}
            sideOffset={6}
            className="bg-white border border-gray-200 rounded-lg shadow-lg p-1 z-10000"
            style={contentWidth ? { width: contentWidth } : undefined}
          >
            <AnimatePresence>
              <motion.div
                initial={{ opacity: 0, y: 6, scale: 0.98 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 6, scale: 0.98 }}
                transition={{ duration: 0.15 }}
              >
                {items.map((it) => (
                  <DropdownMenu.Item asChild key={it.key}>
                    <button
                      type="button"
                      onClick={() => onSelect(it.key)}
                      className={`w-full px-3 py-2 rounded-md flex items-center justify-between ${
                        it.selected
                          ? 'bg-blue-100 text-blue-800'
                          : 'hover:bg-gray-100 text-gray-800'
                      }`}
                    >
                      <span className="truncate mr-3">{it.label}</span>
                      {it.selected && <i className="fas fa-check text-xs text-blue-800" />}
                    </button>
                  </DropdownMenu.Item>
                ))}
              </motion.div>
            </AnimatePresence>
          </DropdownMenu.Content>
        </DropdownMenu.Portal>
      </DropdownMenu.Root>
    );
  };

  const currentNumber = String(watchedValues?.period?.number ?? 1);
  const currentUnit = String(watchedValues?.period?.unit ?? 'week');

  return (
    <div className="space-y-1.5">
      <label className="text-sm font-medium text-gray-700">Period:</label>
      {/* Hidden inputs to keep RHF registration */}
      <input type="hidden" value={currentNumber} {...register('period.number', { valueAsNumber: true })} />
      <input type="hidden" value={currentUnit} {...register('period.unit')} />
      <div className="flex gap-3">
        <div className="flex-1">
          <Menu
            triggerLabel={currentUnit.charAt(0).toUpperCase() + currentUnit.slice(1)}
            items={periodUnits.map((u) => ({ key: u, label: u.charAt(0).toUpperCase() + u.slice(1), selected: u === currentUnit }))}
            onSelect={(key) => setValue('period.unit', key as GoalCreateFormData['period']['unit'], { shouldValidate: true, shouldDirty: true })}
            disabled={isLoading}
            widthClass="w-full"
            align="start"
          />
        </div>
        <Menu
          triggerLabel={currentNumber}
          items={periodNumbers.map((n) => ({ key: String(n), label: String(n), selected: String(n) === currentNumber }))}
          onSelect={(key) => setValue('period.number', Number(key), { shouldValidate: true, shouldDirty: true })}
          disabled={isLoading}
          widthClass="w-20"
        />
      </div>
    </div>
  );
};
