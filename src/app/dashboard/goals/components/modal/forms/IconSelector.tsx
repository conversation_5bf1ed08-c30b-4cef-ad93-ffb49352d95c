"use client";

import { motion } from 'framer-motion';
import { UseFormSetValue } from 'react-hook-form';
import { GoalCreateFormData } from '../../../types';

interface IconSelectorProps {
  setValue: UseFormSetValue<GoalCreateFormData>;
  watchedValues: GoalCreateFormData;
  isLoading: boolean;
  isMobile: boolean;
}

// Clean icon set without duplicates - matching Figma design
const icons = [
  'clock', 'microphone', 'location-dot', 'arrow-right', 'arrow-up-right', 'circle-minus', 'yen-sign',
  'rotate', 'rotate-right', 'rotate-left', 'bars', 'arrow-down', 'check', 'arrow-left',
  'comment', 'folder', 'file', 'triangle', 'bell', 'bold', 'underline',
  'minus', 'sun', 'list', 'calendar', 'calendar-alt', 'calendar-check', 'calendar-plus', 'calendar-times',
  'angle-double-right', 'chevron-up', 'chevron-down', 'chevron-left', 'chevron-right', 'cog', 'circle',
  'star', 'heart', 'bookmark', 'flag', 'tag', 'thumbs-up', 'eye', 'search', 'home', 'user'
];

export const IconSelector = ({ setValue, watchedValues, isLoading, isMobile }: IconSelectorProps) => {
  return (
    <div className="space-y-1.5">
      <label className="text-sm font-medium text-gray-700">Goal icon:</label>
      <div
        className={`${isMobile ? 'h-32' : 'h-36'} p-3 border border-gray-300 rounded-lg overflow-y-auto bg-white`}
      >
        <div
          className={`grid gap-2 ${
            isMobile
              ? 'grid-cols-8'
              : 'grid-cols-10'
          }`}
        >
          {icons.map((iconName, index) => (
            <motion.button
              key={`${iconName}-${index}`}
              type="button"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setValue('icon', iconName)}
              className={`${isMobile ? 'w-8 h-8' : 'w-9 h-9'} flex items-center justify-center border rounded-lg transition-all duration-200 ${
                watchedValues.icon === iconName
                  ? 'border-[#374151] bg-gray-100'
                  : 'border-gray-300 hover:border-gray-400 bg-white'
              }`}
              disabled={isLoading}
            >
              <i
                className={`fas fa-${iconName} ${isMobile ? 'text-xs' : 'text-sm'} ${
                  watchedValues.icon === iconName ? 'text-[#374151]' : 'text-gray-600'
                }`}
              />
            </motion.button>
          ))}
        </div>
      </div>
    </div>
  );
};
