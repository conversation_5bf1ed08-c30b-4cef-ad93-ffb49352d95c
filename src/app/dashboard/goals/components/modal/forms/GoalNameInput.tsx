"use client";

import { motion } from 'framer-motion';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { GoalCreateFormData } from '../../../types';

interface GoalNameInputProps {
  register: UseFormRegister<GoalCreateFormData>;
  errors: FieldErrors<GoalCreateFormData>;
  isLoading: boolean;
}

export const GoalNameInput = ({ register, errors, isLoading }: GoalNameInputProps) => {
  return (
    <div className="space-y-1.5">
      <label className="text-sm font-medium text-gray-700">Goal name:</label>
      <input
        {...register('name')}
        placeholder="My Goal"
        className={`w-full h-11 px-4 bg-white rounded-lg border text-base ${
          errors.name ? 'border-red-500 bg-red-50' : 'border-gray-300'
        } focus:border-blue-500 hover:border-gray-400 focus:outline-none transition-all duration-200`}
        disabled={isLoading}
      />
      {errors.name && (
        <motion.p
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-red-500 text-sm"
        >
          {errors.name.message}
        </motion.p>
      )}
    </div>
  );
};
