"use client";

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { UseFormSetValue } from 'react-hook-form';
import { GoalCreateFormData, GenderTarget } from '../../../types';
import { useUserStore } from "~/store/userStore";
import { getPlatformColors } from "~/app/dashboard/analytics/utils/colors";

interface GenderSectionProps {
  watchedValues: GoalCreateFormData;
  setValue: UseFormSetValue<GoalCreateFormData>;
  expandedSections: string[];
  toggleSection: (section: string) => void;
}

export const GenderSection = ({ watchedValues, setValue, expandedSections, toggleSection }: GenderSectionProps) => {
  const [hovered, setHovered] = useState<string | null>(null);
  const isExpanded = expandedSections.includes('gender');
  const { selectedSocial } = useUserStore();
  const platformColors = getPlatformColors(selectedSocial?.platform);

  const redistributePercentages = (
    items: GenderTarget[],
    changedIndex: number,
    newValue: number
  ) => {
    // Discrete 1% transfers across all items (no enabled state)
    if (changedIndex < 0 || changedIndex >= items.length) return false;
    items.forEach(i => { i.percentage = Math.max(0, Math.min(100, Math.round(Number(i.percentage) || 0))); });
    newValue = Math.max(0, Math.min(100, Math.round(newValue)));

    const oldValue = items[changedIndex]!.percentage;
    let delta = newValue - oldValue;
    if (delta === 0) return false;

    const otherIdx = items.map((_, i) => i).filter(i => i !== changedIndex);
    if (otherIdx.length === 0) return false;

    if (delta > 0) {
      const available = otherIdx.reduce((s, i) => s + Math.max(0, items[i]!.percentage), 0);
      delta = Math.min(delta, available);
    } else {
      delta = -Math.min(-delta, items[changedIndex]!.percentage);
    }
    if (delta === 0) return false;

    const steps = Math.abs(delta);
    for (let s = 0; s < steps; s++) {
      if (delta > 0) {
        // Take 1% from the largest donor (>0)
        const donor = otherIdx
          .filter(i => items[i]!.percentage > 0)
          .reduce((best, i) => (best === -1 || items[i]!.percentage > items[best]!.percentage ? i : best), -1);
        if (donor === -1) break;
        items[donor]!.percentage -= 1;
        items[changedIndex]!.percentage += 1;
      } else {
        // Give 1% to the smallest receiver
        const receiver = otherIdx.reduce((best, i) => (best === -1 || items[i]!.percentage < items[best]!.percentage ? i : best), -1);
        if (receiver === -1) break;
        if (items[changedIndex]!.percentage <= 0) break;
        items[receiver]!.percentage += 1;
        items[changedIndex]!.percentage -= 1;
      }
    }

    const total = items.reduce((s, i) => s + i.percentage, 0);
    if (total !== 100) {
      const adjust = 100 - total;
      const idx = items.reduce((max, it, i) => (it.percentage > items[max]!.percentage ? i : max), 0);
      items[idx]!.percentage += adjust;
    }
    return true;
  };

  const updateGender = (index: number, value: number) => {
    const current = (watchedValues.targetConfig?.genders ?? []) as GenderTarget[];
    const updated: GenderTarget[] = [...current];
    if (updated[index]) {
      value = Math.round(value);
      if (redistributePercentages(updated, index, value)) {
        setValue('targetConfig.genders', updated);
      }
    }
  };

  // Enabled/disabled feature removed

  const handleToggleSection = () => {
    const isOpen = expandedSections.includes('gender');
    if (!isOpen) {
      const current = (watchedValues.targetConfig?.genders ?? []) as GenderTarget[];
      // Try to seed from analytics_summary (preferred) or analytics_data (fallback)
      try {
        const savedSummary = localStorage.getItem('analytics_summary');
        const savedFull = !savedSummary ? localStorage.getItem('analytics_data') : null;
        const map: Record<string, number> = {};
        if (savedSummary) {
          const summary = JSON.parse(savedSummary);
          const src = summary?.gender;
          if (src && typeof src === 'object') {
            Object.entries(src as Record<string, number>).forEach(([k, v]) => {
              const key = String(k).toLowerCase().trim();
              // Normalize known codes to canonical keys as well
              if (key === 'm') map['male'] = (map['male'] || 0) + (Number(v) || 0);
              else if (key === 'f') map['female'] = (map['female'] || 0) + (Number(v) || 0);
              else if (key === 'u') map['other'] = (map['other'] || 0) + (Number(v) || 0);
              else if (key === 'male' || key === 'man' || key === 'men') map['male'] = (map['male'] || 0) + (Number(v) || 0);
              else if (key === 'female' || key === 'woman' || key === 'women') map['female'] = (map['female'] || 0) + (Number(v) || 0);
              else map['other'] = (map['other'] || 0) + (Number(v) || 0);
            });
          }
        } else if (savedFull) {
          const analytics = JSON.parse(savedFull);
          const src = Array.isArray(analytics?.follower_demographics) ? analytics.follower_demographics : [];
          src.forEach((dp: any) => {
            const raw = dp?.breakdown?.gender || dp?.gender;
            if (!raw) return;
            const key = String(raw).toLowerCase().trim();
            const val = Number(dp?.total_value ?? dp?.value ?? 0) || 0;
            if (key === 'm') map['male'] = (map['male'] || 0) + val;
            else if (key === 'f') map['female'] = (map['female'] || 0) + val;
            else if (key === 'u') map['other'] = (map['other'] || 0) + val;
            else if (key === 'male' || key === 'man' || key === 'men') map['male'] = (map['male'] || 0) + val;
            else if (key === 'female' || key === 'woman' || key === 'women') map['female'] = (map['female'] || 0) + val;
            else map['other'] = (map['other'] || 0) + val;
          });
        }
        const totalVal = Object.values(map).reduce((s: number, v: any) => s + Number(v || 0), 0);
        if (totalVal > 0 && current.length) {
          const isPercentages = totalVal <= 100 && Object.values(map).every((v) => Number(v) <= 100);
          const updated: GenderTarget[] = current.map(g => {
            const key = String(g.gender).toLowerCase().trim();
            const alt = key.startsWith('m') ? 'male' : key.startsWith('f') ? 'female' : 'other';
            const raw = map[alt] ?? map[key] ?? 0;
            const pct = isPercentages ? Number(raw) : Math.round((Number(raw) / totalVal) * 100);
            return { ...g, enabled: true, percentage: pct };
          });
          const newTotal = updated.reduce((s, i) => s + i.percentage, 0);
          if (newTotal !== 100 && updated.length > 0) {
            const largestIdx = updated.reduce((maxIdx, it, idx) => it.percentage > (updated[maxIdx]?.percentage || 0) ? idx : maxIdx, 0);
            updated[largestIdx]!.percentage += (100 - newTotal);
          }
          setValue('targetConfig.genders', updated);
          toggleSection('gender');
          return;
        }
      } catch {}

      const total = current.reduce((s, i) => s + i.percentage, 0);
      let enabled: GenderTarget[];
      if (total === 0 && current.length > 0) {
        const equal = Math.round(100 / current.length);
        enabled = current.map((g, idx) => ({
          ...g,
          enabled: true,
          percentage: idx === current.length - 1 ? 100 - (equal * (current.length - 1)) : equal
        }));
      } else {
        const scale = total === 0 ? 0 : 100 / total;
        enabled = current.map(g => ({ ...g, enabled: true, percentage: Math.round(g.percentage * scale) }));
        const newTotal = enabled.reduce((s, i) => s + i.percentage, 0);
        if (newTotal !== 100 && enabled.length > 0) {
          const largestIdx = enabled.reduce((maxIdx, it, idx) => it.percentage > (enabled[maxIdx]?.percentage || 0) ? idx : maxIdx, 0);
          enabled[largestIdx]!.percentage += (100 - newTotal);
        }
      }
      setValue('targetConfig.genders', enabled);
    }
    toggleSection('gender');
  };

  const genders = (watchedValues.targetConfig?.genders ?? []) as GenderTarget[];
  const totalPercentage = genders.reduce((s, i) => s + i.percentage, 0);

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <motion.button
        type="button"
        onClick={handleToggleSection}
        className="w-full flex items-center justify-between px-3 py-3 md:p-4 bg-white hover:bg-gray-50 transition-colors duration-200"
        whileHover={{ backgroundColor: '#f9fafb' }}
      >
        <div className="flex items-center gap-3">
          <span className="text-sm md:text-base font-medium text-black">Gender</span>
        </div>
        <motion.i
          className={`fas ${isExpanded ? 'fa-times' : 'fa-plus'} text-gray-400`}
          animate={{ rotate: isExpanded ? 45 : 0 }}
          transition={{ duration: 0.2 }}
        />
      </motion.button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white"
          >
            <div className="px-3 py-3 md:p-4 space-y-3 md:space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-black">Gender Targeting</h3>
                  <p className="text-xs md:text-sm text-black">Adjust audience by gender</p>
                </div>
                <div className="text-right">
                  <div className="text-xl md:text-2xl font-bold text-black">{Number(totalPercentage).toFixed(1)}%</div>
                  <div className="text-xs text-black">Total Coverage</div>
                </div>
              </div>

              <div className="bg-white rounded-lg px-3 py-3 md:p-4">
                <div className="space-y-4">
                  {genders.map((g, index) => (
                    <motion.div
                      key={g.gender}
                      className={`px-3 py-3 md:p-4 rounded-lg transition-all duration-200`}
                      onMouseEnter={() => setHovered(g.gender)}
                      onMouseLeave={() => setHovered(null)}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <span className="text-sm md:text-base font-medium text-black">{g.gender}</span>
                        </div>
                        {/* Removed top percentage for cleaner UI */}
                      </div>

                      <div className="flex items-center gap-3">
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <motion.div
                            className="h-3 rounded-full"
                            style={{ background: '#ef4444' }}
                            initial={{ width: 0 }}
                            animate={{ width: `${Number(g.percentage).toFixed(1)}%` }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                          />
                        </div>
                        <motion.button
                          type="button"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => updateGender(index, Math.max(0, g.percentage - 1))}
                          className="w-6 h-6 aspect-square rounded-full overflow-hidden flex-none shrink-0 p-0 leading-none appearance-none border-0 flex items-center justify-center transition-colors duration-200"
                          style={{ backgroundColor: '#f3f4f6' }}
                        >
                          <i className="fas fa-minus text-xs" style={{ color: 'black' }}></i>
                        </motion.button>
                        <span className="text-xs md:text-sm text-gray-700 w-14 text-center">{Number(g.percentage).toFixed(1)}%</span>
                        <motion.button
                          type="button"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => updateGender(index, Math.min(100, g.percentage + 1))}
                          className="w-6 h-6 aspect-square rounded-full overflow-hidden flex-none shrink-0 p-0 leading-none appearance-none border-0 flex items-center justify-center transition-colors duration-200"
                          style={{ backgroundColor: '#f3f4f6' }}
                        >
                          <i className="fas fa-plus text-xs" style={{ color: 'black' }}></i>
                        </motion.button>
                      </div>
                      {/* Active/Disabled label removed */}
                    </motion.div>
                  ))}
                </div>
              </div>

              <div className="bg-white rounded-lg px-3 py-3 md:p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm md:text-base font-medium text-gray-600">Total Targeting Coverage:</span>
                  <span className={`text-lg md:text-xl font-bold text-black`}>
                    {Number(totalPercentage).toFixed(1)}%
                  </span>
                </div>
                <div className="text-xs text-gray-600">Percentages automatically redistribute to maintain 100% total</div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
