"use client";

import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
} from "recharts";
import { UseFormSetValue, UseFormWatch } from "react-hook-form";
import { GoalCreateFormData } from "../../../types";

interface AccountReachSectionProps {
  expandedSections: string[];
  toggleSection: (section: string) => void;
  setValue?: UseFormSetValue<GoalCreateFormData>;
  watch?: UseFormWatch<GoalCreateFormData>;
}

// Sample reach data for the last 30 days
const reachData = [
  { day: "Day 1", reach: 1200, target: 1500 },
  { day: "Day 2", reach: 1350, target: 1500 },
  { day: "Day 3", reach: 1100, target: 1500 },
  { day: "Day 4", reach: 1600, target: 1500 },
  { day: "Day 5", reach: 1450, target: 1500 },
  { day: "Day 6", reach: 1800, target: 1500 },
  { day: "Day 7", reach: 1750, target: 1500 },
  { day: "Day 8", reach: 1300, target: 1500 },
  { day: "Day 9", reach: 1550, target: 1500 },
  { day: "Day 10", reach: 1700, target: 1500 },
  { day: "Day 11", reach: 1400, target: 1500 },
  { day: "Day 12", reach: 1650, target: 1500 },
  { day: "Day 13", reach: 1900, target: 1500 },
  { day: "Day 14", reach: 1850, target: 1500 },
];

export const AccountReachSection = ({
  expandedSections,
  toggleSection,
  setValue,
  watch,
}: AccountReachSectionProps) => {
  const [isEditingTarget, setIsEditingTarget] = useState(false);
  const isExpanded = expandedSections.includes("accountReach");

  // Get target from form or use default
  const formValues = (watch?.() as Partial<GoalCreateFormData>) || {};
  const targetConfig = (formValues as any).targetConfig || {};
  const accountReachConfig = targetConfig.accountReach || {
    enabled: false,
    target: 1500,
  };

  const [goalTarget, setGoalTarget] = useState(
    accountReachConfig.target || 1500
  );

  // Update form when target changes
  useEffect(() => {
    if (setValue && goalTarget) {
      setValue("targetConfig.accountReach.enabled", true);
      setValue("targetConfig.accountReach.target", goalTarget);
    }
  }, [goalTarget, setValue]);

  // Calculate metrics
  const currentReach = reachData[reachData.length - 1]?.reach || 0;
  const averageReach = Math.round(
    reachData.reduce((sum, day) => sum + day.reach, 0) / reachData.length
  );
  const goalProgress = Math.round((currentReach / goalTarget) * 100);
  const daysAboveTarget = reachData.filter(
    (day) => day.reach >= goalTarget
  ).length;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="text-blue-600 font-medium">
                Reach: {payload[0]?.value?.toLocaleString()}
              </span>
            </p>
            <p className="text-sm">
              <span className="text-gray-600">
                Target: {payload[1]?.value?.toLocaleString()}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const handleTargetSave = () => {
    setIsEditingTarget(false);

    // Update the form with the new target
    if (setValue) {
      setValue("targetConfig.accountReach.enabled", true);
      setValue("targetConfig.accountReach.target", goalTarget);
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <motion.button
        type="button"
        onClick={() => toggleSection("accountReach")}
        className="w-full flex items-center justify-between px-3 py-3 md:p-4 bg-white hover:bg-gray-50 transition-colors duration-200"
        whileHover={{ backgroundColor: "#f9fafb" }}
      >
        <div className="flex items-center gap-3">
          <span className="text-sm md:text-base font-medium text-black">Account Reach</span>
        </div>
        <motion.i
          className={`fas ${isExpanded ? "fa-times" : "fa-plus"} text-gray-400`}
          animate={{ rotate: isExpanded ? 45 : 0 }}
          transition={{ duration: 0.2 }}
        />
      </motion.button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white"
          >
            <div className="px-3 py-4 md:p-6 space-y-4 md:space-y-6">
              {/* Header with Goal Setting */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-black">
                    Account Reach Tracking
                  </h3>
                  <p className="text-xs md:text-sm text-gray-600">
                    Monitor your daily reach performance
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-xl md:text-2xl font-bold text-black">
                    {currentReach.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">Current Reach</div>
                </div>
              </div>

              {/* Goal Target Setting */}
              <div className="bg-white rounded-lg px-3 py-3 md:p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm md:text-base font-medium text-black">
                    Daily Reach Goal
                  </h4>
                  <button
                    onClick={() => setIsEditingTarget(!isEditingTarget)}
                    className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                  >
                    {isEditingTarget ? "Cancel" : "Edit"}
                  </button>
                </div>

                {isEditingTarget ? (
                  <div className="flex items-center gap-3">
                    <input
                      type="number"
                      value={goalTarget}
                      onChange={(e) => setGoalTarget(Number(e.target.value))}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter target reach"
                    />
                    <button
                      onClick={handleTargetSave}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Save
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center justify-between">
                    <div className="text-xl md:text-2xl font-bold text-black">
                      {goalTarget.toLocaleString()}
                    </div>
                    <div className="px-3 py-1 rounded-full text-xs md:text-sm font-medium bg-gray-100 text-black">
                      {goalProgress}% of goal
                    </div>
                  </div>
                )}
              </div>

              {/* Chart */}
              <div className="bg-white rounded-lg px-3 py-3 md:p-4">
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={reachData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="day"
                        tick={{ fontSize: 12 }}
                        interval="preserveStartEnd"
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) =>
                          `${(value / 1000).toFixed(1)}k`
                        }
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <ReferenceLine
                        y={goalTarget}
                        stroke="#ef4444"
                        strokeDasharray="5 5"
                      />
                      <Line
                        type="monotone"
                        dataKey="reach"
                        stroke="#ef4444"
                        strokeWidth={3}
                        dot={{ fill: "#ef4444", stroke: "#ef4444", strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: "#ef4444", strokeWidth: 2 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-white rounded-lg px-3 py-3 md:p-4 text-center">
                  <div className="text-xl md:text-2xl font-bold text-black">
                    {averageReach.toLocaleString()}
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">Avg. Daily</div>
                </div>
                <div className="bg-white rounded-lg px-3 py-3 md:p-4 text-center">
                  <div className="text-xl md:text-2xl font-bold text-black">
                    {daysAboveTarget}
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">Days Above Goal</div>
                </div>
                <div className="bg-white rounded-lg px-3 py-3 md:p-4 text-center">
                  <div className="text-xl md:text-2xl font-bold text-black">
                    {Math.round((daysAboveTarget / reachData.length) * 100)}%
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">Success Rate</div>
                </div>
                <div className="bg-white rounded-lg px-3 py-3 md:p-4 text-center">
                  <div className="text-xl md:text-2xl font-bold text-black">
                    {Math.max(
                      ...reachData.map((d) => d.reach)
                    ).toLocaleString()}
                  </div>
                  <div className="text-xs md:text-sm text-gray-600">Best Day</div>
                </div>
              </div>

              {/* Insights */}
              <div className="rounded-lg px-3 py-3 md:p-4 bg-white">
                <div className="flex items-start gap-3">
                  <div>
                    <h4 className="text-sm md:text-base font-medium text-black mb-1">Reach Insights</h4>
                    <p className="text-xs md:text-sm text-gray-700">
                      {goalProgress >= 100
                        ? `Great job! You're exceeding your daily reach goal by ${
                            goalProgress - 100
                          }%.`
                        : `You're ${
                            100 - goalProgress
                          }% away from your daily goal. Consider posting during peak hours to increase reach.`}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
