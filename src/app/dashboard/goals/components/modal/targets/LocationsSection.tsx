"use client";

import { motion, AnimatePresence } from 'framer-motion';
import { UseFormSetValue } from 'react-hook-form';
import { GoalCreateFormData, LocationTarget } from '../../../types';
import { useUserStore } from "~/store/userStore";
import { getPlatformColors } from "~/app/dashboard/analytics/utils/colors";

interface LocationsSectionProps {
  watchedValues: GoalCreateFormData;
  setValue: UseFormSetValue<GoalCreateFormData>;
  expandedSections: string[];
  toggleSection: (section: string) => void;
}

export const LocationsSection = ({ watchedValues, setValue, expandedSections, toggleSection }: LocationsSectionProps) => {
  const { selectedSocial } = useUserStore();
  const platformColors = getPlatformColors(selectedSocial?.platform);

  const redistribute = (items: LocationTarget[], changedIndex: number, newValue: number) => {
    // Discrete, integer-preserving redistribution to keep total exactly 100
    if (changedIndex < 0 || changedIndex >= items.length) return false;
    // Normalize to integers first
    items.forEach((it) => {
      it.percentage = Math.max(0, Math.min(100, Math.round(Number(it.percentage) || 0)));
    });
    newValue = Math.max(0, Math.min(100, Math.round(newValue)));

    const oldValue = items[changedIndex]!.percentage;
    let delta = newValue - oldValue; // desired change on the changed item
    if (delta === 0) return false;

    const otherIdx = items
      .map((_, i) => i)
      .filter((i) => i !== changedIndex);

    if (otherIdx.length === 0) return false;

    // Compute feasible steps based on available mass to transfer
    if (delta > 0) {
      // Can only increase target by taking from others' available percentages (>0)
      const available = otherIdx.reduce((s, i) => s + Math.max(0, items[i]!.percentage), 0);
      delta = Math.min(delta, available);
    } else {
      // delta < 0: we will give to others, limited by target's current value
      delta = -Math.min(-delta, items[changedIndex]!.percentage);
    }

    if (delta === 0) return false;

    const stepCount = Math.abs(delta);
    for (let step = 0; step < stepCount; step++) {
      if (delta > 0) {
        // Take 1 from the other with the largest percentage (>0)
        const donor = otherIdx
          .filter((i) => items[i]!.percentage > 0)
          .reduce((best, i) => (best === -1 || items[i]!.percentage > items[best]!.percentage ? i : best), -1);
        if (donor === -1) break; // nothing more to take
        items[donor]!.percentage -= 1;
        items[changedIndex]!.percentage += 1;
      } else {
        // Give 1 to the other with the smallest percentage (ties arbitrary)
        const receiver = otherIdx.reduce(
          (best, i) => (best === -1 || items[i]!.percentage < items[best]!.percentage ? i : best),
          -1
        );
        if (receiver === -1) break;
        if (items[changedIndex]!.percentage <= 0) break;
        items[receiver]!.percentage += 1;
        items[changedIndex]!.percentage -= 1;
      }
    }

    // Final guard: ensure total is 100 by adjusting the largest (should normally already be 100)
    const total = items.reduce((s, it) => s + it.percentage, 0);
    if (total !== 100) {
      const adjust = 100 - total;
      const idx = items.reduce((max, it, i) => (it.percentage > items[max]!.percentage ? i : max), 0);
      items[idx]!.percentage += adjust;
    }

    return true;
  };

  const updateList = (path: 'targetConfig.locationCountries' | 'targetConfig.locationCities', index: number, value: number) => {
    const current = (path === 'targetConfig.locationCountries'
      ? (watchedValues.targetConfig?.locationCountries ?? [])
      : (watchedValues.targetConfig?.locationCities ?? [])) as LocationTarget[];
    const updated: LocationTarget[] = [...current];
    if (updated[index]) {
      value = Math.round(value);
      if (redistribute(updated, index, value)) {
        setValue(path, updated as any);
      }
    }
  };
  // Enabled/disabled feature removed for locations

  const normalizeOnOpen = (setterPath: 'targetConfig.locationCountries' | 'targetConfig.locationCities') => {
    const arr = setterPath === 'targetConfig.locationCountries'
      ? watchedValues.targetConfig?.locationCountries
      : watchedValues.targetConfig?.locationCities;
    const normalize = (arrInner: LocationTarget[] | undefined, setterPathInner: 'targetConfig.locationCountries' | 'targetConfig.locationCities') => {
      const current = (arrInner ?? []) as LocationTarget[];
      if (current.length === 0) return;
      // Try seed from analytics_summary (preferred) or analytics_data (fallback)
      try {
        const savedSummary = localStorage.getItem('analytics_summary');
        const savedFull = !savedSummary ? localStorage.getItem('analytics_data') : null;
        const map: Record<string, number> = {};
        const key = setterPathInner === 'targetConfig.locationCountries' ? 'country' : 'city';
        if (savedSummary) {
          const summary = JSON.parse(savedSummary);
          const src = key === 'country' ? summary?.country : summary?.city;
          if (src && typeof src === 'object') {
            Object.entries(src as Record<string, number>).forEach(([k, v]) => {
              map[String(k).toLowerCase()] = Number(v) || 0;
            });
          }
        } else if (savedFull) {
          const analytics = JSON.parse(savedFull);
          const series: any[] = Array.isArray(analytics?.follower_demographics) ? analytics.follower_demographics : [];
          series.forEach(dp => {
            const k = dp?.breakdown?.[key] || dp?.[key];
            if (!k) return;
            const val = Number(dp?.total_value ?? dp?.value ?? 0) || 0;
            map[String(k).toLowerCase()] = (map[String(k).toLowerCase()] || 0) + val;
          });
        }
        const totalVal = Object.values(map).reduce((s, v) => s + Number(v || 0), 0);
        if (totalVal > 0) {
          const isPercentages = totalVal <= 100 && Object.values(map).every((v) => Number(v) <= 100);
          const updated = current.map(item => {
            const k = String(item.name).toLowerCase();
            const raw = map[k] ?? 0;
            const pct = isPercentages ? Number(raw) : Math.round((Number(raw) / totalVal) * 100);
            return { ...item, percentage: pct } as LocationTarget;
          });
          const newTotal = updated.reduce((s, i) => s + i.percentage, 0);
          if (newTotal !== 100 && updated.length > 0) {
            const largestIdx = updated.reduce((maxIdx, it, idx) => it.percentage > (updated[maxIdx]?.percentage || 0) ? idx : maxIdx, 0);
            updated[largestIdx]!.percentage += (100 - newTotal);
          }
          setValue(setterPathInner, updated as any);
          return;
        }
      } catch {}

      const sum = current.reduce((s, i) => s + i.percentage, 0);
      let enabled: LocationTarget[];
      if (sum === 0) {
        const equal = Math.round(100 / current.length);
        enabled = current.map((c, idx) => ({ ...c, percentage: idx === current.length - 1 ? 100 - equal * (current.length - 1) : equal }));
      } else {
        const scale = 100 / sum;
        enabled = current.map(c => ({ ...c, percentage: Math.round(c.percentage * scale) }));
        const newTotal = enabled.reduce((s, i) => s + i.percentage, 0);
        if (newTotal !== 100 && enabled.length > 0) {
          const largestIdx = enabled.reduce((maxIdx, it, idx) => it.percentage > (enabled[maxIdx]?.percentage || 0) ? idx : maxIdx, 0);
          enabled[largestIdx]!.percentage += (100 - newTotal);
        }
      }
      setValue(setterPathInner, enabled as any);
    };

    normalize(arr, setterPath);
  };

  const countries = (watchedValues.targetConfig?.locationCountries ?? []) as LocationTarget[];
  const cities = (watchedValues.targetConfig?.locationCities ?? []) as LocationTarget[];

  const renderList = (
    title: string,
    items: LocationTarget[],
    path: 'targetConfig.locationCountries' | 'targetConfig.locationCities',
    accent: 'blue' | 'indigo'
  ) => {
    const total = items.reduce((s, i) => s + i.percentage, 0);
    const accentPillStyle = { backgroundColor: '#f3f4f6', color: 'black' } as const;

    return (
      <div className="bg-white rounded-lg px-3 py-3 md:p-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h4 className="text-base md:text-lg font-semibold text-black">{title}</h4>
            <p className="text-xs md:text-sm text-gray-600">Adjust distribution to total 100%</p>
          </div>
          <span className={`px-2 py-1 rounded text-xs md:text-sm font-medium`} style={accentPillStyle}>{Number(total).toFixed(1)}% total</span>
        </div>
        <div className="space-y-3 md:space-y-4">
          {items.map((item, index) => (
            <div key={`${title}-${item.name}-${index}`} className="px-3 py-3 md:p-4 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <span className="text-sm md:text-base font-medium text-black">{item.name}</span>
                </div>
                {/* Removed top percentage for cleaner UI */}
              </div>
              <div className="flex items-center gap-3">
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <motion.div
                    className={`h-3 rounded-full`}
                    style={{ background: '#ef4444' }}
                    initial={{ width: 0 }}
                    animate={{ width: `${Number(item.percentage).toFixed(1)}%` }}
                    transition={{ duration: 0.5, delay: index * 0.05 }}
                  />
                </div>
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => updateList(path, index, Math.max(0, item.percentage - 1))}
                  className={`w-6 h-6 aspect-square rounded-full overflow-hidden flex-none shrink-0 p-0 leading-none appearance-none border-0 flex items-center justify-center transition-colors duration-200`}
                  style={{ backgroundColor: '#f3f4f6' }}
                >
                  <i className="fas fa-minus text-xs" style={{ color: 'black' }}></i>
                </motion.button>
                <span className="text-xs md:text-sm text-gray-700 w-14 text-center">{Number(item.percentage).toFixed(1)}%</span>
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => updateList(path, index, Math.min(100, item.percentage + 1))}
                  className={`w-6 h-6 aspect-square rounded-full overflow-hidden flex-none shrink-0 p-0 leading-none appearance-none border-0 flex items-center justify-center transition-colors duration-200`}
                  style={{ backgroundColor: '#f3f4f6' }}
                >
                  <i className="fas fa-plus text-xs" style={{ color: 'black' }}></i>
                </motion.button>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderSection = (
    sectionKey: 'locationCountries' | 'locationCities',
    title: string,
    iconClass: string,
    items: LocationTarget[],
    path: 'targetConfig.locationCountries' | 'targetConfig.locationCities',
    accent: 'blue' | 'indigo'
  ) => {
    const isOpen = expandedSections.includes(sectionKey);
    const onToggle = () => {
      if (!isOpen) {
        normalizeOnOpen(path);
      }
      toggleSection(sectionKey);
    };

    return (
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <motion.button
          type="button"
          onClick={onToggle}
          className="w-full flex items-center justify-between px-3 py-3 md:p-4 bg-white hover:bg-gray-50 transition-colors duration-200"
          whileHover={{ backgroundColor: '#f9fafb' }}
        >
          <div className="flex items-center gap-3">
            <span className="text-sm md:text-base font-medium text-black">{title}</span>
          </div>
          <motion.i
            className={`fas ${isOpen ? 'fa-times' : 'fa-plus'} text-gray-400`}
            animate={{ rotate: isOpen ? 45 : 0 }}
            transition={{ duration: 0.2 }}
          />
        </motion.button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white"
            >
              <div className="px-3 py-3 md:p-4 space-y-3 md:space-y-4">
                {renderList(title, items, path, accent)}
                <div className="bg-white rounded-lg px-3 py-3 md:p-4 text-xs text-black">Percentages automatically redistribute to maintain 100% within this section.</div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {renderSection('locationCountries', 'Location Countries', 'fas fa-map-marker-alt', countries, 'targetConfig.locationCountries', 'blue')}
      {renderSection('locationCities', 'Location Cities', 'fas fa-city', cities, 'targetConfig.locationCities', 'indigo')}
    </div>
  );
};
