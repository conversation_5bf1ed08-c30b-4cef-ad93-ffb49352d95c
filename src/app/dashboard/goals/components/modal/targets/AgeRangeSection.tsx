"use client";

import { motion, AnimatePresence } from 'framer-motion';
import { UseFormSetValue } from 'react-hook-form';
import { GoalCreateFormData } from '../../../types';
import { useState } from 'react';
import { useUserStore } from "~/store/userStore";
import { getPlatformColors } from "~/app/dashboard/analytics/utils/colors";

interface AgeRangeSectionProps {
  watchedValues: GoalCreateFormData;
  setValue: UseFormSetValue<GoalCreateFormData>;
  expandedSections: string[];
  toggleSection: (section: string) => void;
};

type AgeRangeItem = { range: string; percentage: number; enabled: boolean };

export const AgeRangeSection = ({
  watchedValues,
  setValue,
  expandedSections,
  toggleSection
}: AgeRangeSectionProps) => {
  const [hoveredRange, setHoveredRange] = useState<string | null>(null);
  const isExpanded = expandedSections.includes('ageRange');
  const { selectedSocial } = useUserStore();
  const platformColors = getPlatformColors(selectedSocial?.platform);

  // Improved percentage redistribution logic
  const redistributePercentages = (ranges: AgeRangeItem[], changedIndex: number, newValue: number) => {
    // Validate inputs
    if (changedIndex < 0 || changedIndex >= ranges.length) return false;
    if (newValue < 0 || newValue > 100) return false;

    const oldValue = ranges[changedIndex]?.percentage || 0;
    const adjustmentAmount = newValue - oldValue;

    // If no change, return true
    if (Math.abs(adjustmentAmount) < 0.01) return true;

    // Set the new value for the changed range
    ranges[changedIndex]!.percentage = newValue;

    // Get all other ranges (ignore enabled/disabled; all are editable now)
    const otherRanges: AgeRangeItem[] = ranges.filter((_, idx) => idx !== changedIndex);
    if (otherRanges.length === 0) {
      return newValue <= 100;
    }

    // Calculate adjustment per other range
    const adjustmentPerRange = adjustmentAmount / otherRanges.length;

    // Apply adjustment to each other range
    otherRanges.forEach(range => {
      const newPercentage = Math.max(0, range.percentage - adjustmentPerRange);
      range.percentage = newPercentage;
    });

    // Round all percentages to avoid decimal precision issues
    ranges.forEach(range => {
      range.percentage = Math.round(range.percentage * 100) / 100; // Round to 2 decimal places
    });

    // Final adjustment to ensure total equals exactly 100%
    const currentTotal = ranges.reduce((sum, range) => sum + range.percentage, 0);
    const totalError = 100 - currentTotal;

    if (Math.abs(totalError) > 0.01) {
      // Distribute the error across ranges (excluding the one we just changed)
      const rangesForAdjustment = otherRanges.filter(range => range.percentage > 0);
      if (rangesForAdjustment.length > 0) {
        const errorPerRange = totalError / rangesForAdjustment.length;
        rangesForAdjustment.forEach(range => {
          range.percentage = Math.max(0, range.percentage + errorPerRange);
        });
      }
    }

    // Final rounding to whole numbers
    ranges.forEach(range => {
      range.percentage = Math.round(range.percentage);
    });

    // One final check and adjustment for rounding errors
    const finalTotal = ranges.reduce((sum, range) => sum + range.percentage, 0);
    const finalError = 100 - finalTotal;

    if (finalError !== 0) {
      // Find the largest enabled range (excluding the changed one) to absorb the error
      let largestIdx = -1;
      let largestValue = -1;
      otherRanges.forEach((range, idx) => {
        if (range.percentage > largestValue) {
          largestValue = range.percentage;
          largestIdx = idx;
        }
      });
      if (largestIdx >= 0) {
        const target = otherRanges[largestIdx];
        if (target && target.percentage + finalError >= 0) {
          target.percentage += finalError;
        }
      }
    }

    return true;
  };

  const updateAgeRange = (index: number, value: number) => {
    const currentRanges = (watchedValues.targetConfig?.ageRanges || []) as AgeRangeItem[];
    const updatedRanges: AgeRangeItem[] = [...currentRanges];

    if (updatedRanges[index]) {
      // For percentage values, ensure proper rounding to whole numbers
      value = Math.round(value);

      // Use the improved redistribution logic to maintain 100% total
      if (redistributePercentages(updatedRanges, index, value)) {
        setValue('targetConfig.ageRanges', updatedRanges);
      }
    }
  };

  const toggleAgeRange = (index: number) => {
    const currentRanges = (watchedValues.targetConfig?.ageRanges || []) as AgeRangeItem[];
    const updatedRanges: AgeRangeItem[] = [...currentRanges];

    if (updatedRanges[index]) {
      updatedRanges[index]!.enabled = !updatedRanges[index]!.enabled;
      setValue('targetConfig.ageRanges', updatedRanges);
    }
  };

  // Auto-enable all age ranges when section is opened
  const handleToggleSection = () => {
    const isCurrentlyExpanded = expandedSections.includes('ageRange');

    if (!isCurrentlyExpanded) {
      // Try seeding from analytics_summary (preferred) or analytics_data (fallback) if available, else normalize current
      const currentRanges = (watchedValues.targetConfig?.ageRanges || []) as AgeRangeItem[];
      try {
        const savedSummary = localStorage.getItem('analytics_summary');
        const savedFull = !savedSummary ? localStorage.getItem('analytics_data') : null;
        const map: Record<string, number> = {};
        if (savedSummary) {
          const summary = JSON.parse(savedSummary);
          if (summary && summary.age && typeof summary.age === 'object') {
            Object.entries(summary.age as Record<string, number>).forEach(([k, v]) => {
              map[String(k)] = Number(v) || 0;
            });
          }
        } else if (savedFull) {
          const analytics = JSON.parse(savedFull);
          const src = Array.isArray(analytics?.follower_demographics) ? analytics.follower_demographics : [];
          src.forEach((dp: any) => {
            const age = dp?.breakdown?.age || dp?.age;
            const val = Number(dp?.total_value ?? dp?.value ?? 0) || 0;
            if (age) map[String(age)] = (map[String(age)] || 0) + val;
          });
        }

        const total = Object.values(map).reduce((s: number, v: any) => s + Number(v || 0), 0) || 0;
        if (total > 0 && currentRanges.length) {
          const updated = currentRanges.map((r: any) => {
            const key = r.range.replace(/\s/g, '');
            // Try direct match like "18-24" or with spaces "18 - 24"
            const raw = map[r.range] ?? map[key] ?? 0;
            // If summary already stores percentages (0-100), accept as-is; else convert from counts
            const pct = total <= 100 && Object.values(map).every((vv) => Number(vv) <= 100)
              ? Number(raw)
              : Math.round((Number(raw) / total) * 100);
            return { ...r, enabled: true, percentage: pct };
          });
          // Ensure total = 100 by adjusting largest
          const newTotal = updated.reduce((s: number, it: any) => s + it.percentage, 0);
          if (newTotal !== 100 && updated.length > 0) {
            const largestIdx = updated.reduce((maxIdx: number, it: any, idx: number) => it.percentage > (updated[maxIdx]?.percentage || 0) ? idx : maxIdx, 0);
            updated[largestIdx]!.percentage += (100 - newTotal);
          }
          setValue('targetConfig.ageRanges', updated);
          toggleSection('ageRange');
          return;
        }
      } catch {}

      const currentTotal = currentRanges.reduce((sum, range) => sum + range.percentage, 0);

      let enabledRanges: AgeRangeItem[];

      if (currentTotal === 0) {
        // If all percentages are 0, distribute equally
        const equalPercentage = Math.round(100 / currentRanges.length);
        enabledRanges = currentRanges.map((range, index) => ({
          ...range,
          enabled: true,
          percentage: index === currentRanges.length - 1
            ? 100 - (equalPercentage * (currentRanges.length - 1)) // Adjust last one to make total exactly 100
            : equalPercentage
        }));
      } else {
        // Normalize existing percentages to 100%
        const scaleFactor = 100 / currentTotal;
        enabledRanges = currentRanges.map(range => ({
          ...range,
          enabled: true,
          percentage: Math.round(range.percentage * scaleFactor)
        }));

        // Ensure total is exactly 100% by adjusting the largest percentage
        const newTotal = enabledRanges.reduce((sum, range) => sum + range.percentage, 0);
        if (newTotal !== 100 && enabledRanges.length > 0) {
          const largestIndex = enabledRanges.reduce((maxIndex, range, index) =>
            range.percentage > (enabledRanges[maxIndex]?.percentage || 0) ? index : maxIndex, 0
          );
          const targetRange = enabledRanges[largestIndex as number];
          if (targetRange) {
            targetRange.percentage += (100 - newTotal);
          }
        }
      }

      setValue('targetConfig.ageRanges', enabledRanges);
    }

    toggleSection('ageRange');
  };

  // Calculate metrics for display
  const ageRanges = (watchedValues.targetConfig?.ageRanges || []) as AgeRangeItem[];
  const totalPercentage = ageRanges.reduce((sum, range) => sum + range.percentage, 0);
  const dominantRange = ageRanges.reduce((max, range) =>
    range.percentage > max.percentage ? range : max,
    { range: '', percentage: 0 }
  );

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <motion.button
        type="button"
        onClick={handleToggleSection}
        className="w-full flex items-center justify-between px-3 py-3 md:p-4 bg-white hover:bg-gray-50 transition-colors duration-200"
        whileHover={{ backgroundColor: '#f9fafb' }}
      >
        <div className="flex items-center gap-3">
          <span className="text-sm md:text-base font-medium text-black">Age Range</span>
        </div>
        <motion.i
          className={`fas ${isExpanded ? 'fa-times' : 'fa-plus'} text-gray-400`}
          animate={{ rotate: isExpanded ? 45 : 0 }}
          transition={{ duration: 0.2 }}
        />
      </motion.button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white"
          >
            <div className="px-3 py-3 md:p-4 space-y-3 md:space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-base md:text-lg font-semibold text-black">Age Distribution</h3>
                  <p className="text-xs md:text-sm text-gray-600">Target audience by age groups</p>
                </div>
                <div className="text-right">
                  <div className="text-xl md:text-2xl font-bold text-black">{Number(dominantRange.percentage).toFixed(1)}%</div>
                  <div className="text-xs md:text-xs text-gray-500">Top Segment</div>
                </div>
              </div>

              {/* Age Range Controls */}
              <div className="bg-white rounded-lg px-3 py-3 md:p-4">
                <h4 className="text-sm md:text-base font-medium text-black mb-4">Age Range Targeting</h4>
                <div className="space-y-4">
                  {ageRanges.map((range, index) => (
                    <motion.div
                      key={range.range}
                      className={`px-3 py-3 md:p-4 rounded-lg transition-all duration-200`}
                      onMouseEnter={() => setHoveredRange(range.range)}
                      onMouseLeave={() => setHoveredRange(null)}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <span className="text-sm md:text-base font-medium text-black">{range.range}</span>
                        </div>
                        {/* Removed top percentage to keep UI clean */}
                      </div>

                      {/* Progress Bar with Controls */}
                      <div className="flex items-center gap-3">
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <motion.div
                            className="h-3 rounded-full"
                            style={{ background: '#ef4444' }}
                            initial={{ width: 0 }}
                            animate={{ width: `${Number(range.percentage).toFixed(1)}%` }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                          />
                        </div>
                        <motion.button
                          type="button"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => updateAgeRange(index, Math.max(0, range.percentage - 1))}
                          className="w-6 h-6 aspect-square rounded-full shrink-0 p-0 leading-none appearance-none border-0 flex items-center justify-center transition-colors duration-200"
                          style={{ backgroundColor: '#f3f4f6' }}
                        >
                          <i className="fas fa-minus text-xs" style={{ color: 'black' }}></i>
                        </motion.button>
                        <span className="text-xs md:text-sm text-gray-700 w-14 text-center">{Number(range.percentage).toFixed(1)}%</span>
                        <motion.button
                          type="button"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => updateAgeRange(index, Math.min(100, range.percentage + 1))}
                          className="w-6 h-6 aspect-square rounded-full shrink-0 p-0 leading-none appearance-none border-0 flex items-center justify-center transition-colors duration-200"
                          style={{ backgroundColor: '#f3f4f6' }}
                        >
                          <i className="fas fa-plus text-xs" style={{ color: 'black' }}></i>
                        </motion.button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Summary Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-white rounded-lg px-3 py-3 md:p-4 text-center">
                  <div className="text-2xl font-bold text-black">{ageRanges.length}</div>
                  <div className="text-sm text-gray-600">Ranges</div>
                </div>
                <div className="bg-white rounded-lg px-3 py-3 md:p-4 text-center">
                  <div className="text-2xl font-bold text-black">{Number(totalPercentage).toFixed(1)}%</div>
                  <div className="text-sm text-gray-600">Total Coverage</div>
                </div>
                <div className="bg-white rounded-lg px-3 py-3 md:p-4 text-center">
                  <div className="text-2xl font-bold text-black">{dominantRange.range.split('-')[0] || 'N/A'}</div>
                  <div className="text-sm text-gray-600">Primary Age</div>
                </div>
                <div className="bg-white rounded-lg px-3 py-3 md:p-4 text-center">
                  <div className="text-2xl font-bold text-black">{ageRanges.length ? (Number(totalPercentage / ageRanges.length).toFixed(1)) : '0.0'}%</div>
                  <div className="text-sm text-gray-600">Avg. Per Range</div>
                </div>
              </div>

              {/* Total Summary */}
              <div className="bg-white rounded-lg px-3 py-3 md:p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-black">Total Targeting Coverage:</span>
                  <span className="text-xl font-bold text-black">{Number(totalPercentage).toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                  <motion.div
                    className="h-3 rounded-full"
                    style={{ background: '#ef4444' }}
                    initial={{ width: 0 }}
                    animate={{ width: `${Number(totalPercentage).toFixed(1)}%` }}
                    transition={{ duration: 0.6 }}
                  />
                </div>
              </div>

              {/* Insights */}
              <div className="rounded-lg px-3 py-3 md:p-4 bg-white">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-lg flex items-center justify-center shrink-0" style={{ backgroundColor: '#f3f4f6' }}>
                    <i className="fas fa-chart-bar text-sm" style={{ color: 'black' }}></i>
                  </div>
                  <div>
                    <h4 className="font-medium mb-1 text-black">Age Targeting Insights</h4>
                    <p className="text-sm text-gray-700">
                      {dominantRange.percentage > 0 ?
                        `Your primary audience is ${dominantRange.range} (${Number(dominantRange.percentage).toFixed(1)}%). Consider creating content that appeals to this age group for maximum engagement.` :
                        'Adjust the sliders to target specific age groups.'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
