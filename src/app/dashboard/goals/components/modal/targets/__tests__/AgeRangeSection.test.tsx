import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { AgeRangeSection } from '../AgeRangeSection';
import { GoalCreateFormData } from '../../../../types';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    button: ({ children, onClick, ...props }: any) => (
      <button onClick={onClick} {...props}>{children}</button>
    ),
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    i: ({ children, ...props }: any) => <i {...props}>{children}</i>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

const mockSetValue = jest.fn();
const mockToggleSection = jest.fn();

const mockWatchedValues: GoalCreateFormData = {
  name: 'Test Goal',
  icon: 'clock',
  period: { number: 1, unit: 'week' },
  targetConfig: {
    ageRanges: [
      { range: '13 - 17', percentage: 7.2, enabled: false },
      { range: '18 - 24', percentage: 23.8, enabled: false },
      { range: '25 - 34', percentage: 27.2, enabled: false },
    ],
  },
};

const defaultProps = {
  watchedValues: mockWatchedValues,
  setValue: mockSetValue,
  expandedSections: [],
  toggleSection: mockToggleSection,
};

describe('AgeRangeSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the age range section header', () => {
    render(<AgeRangeSection {...defaultProps} />);
    expect(screen.getByText('Age range')).toBeInTheDocument();
  });

  it('shows plus icon when section is collapsed', () => {
    render(<AgeRangeSection {...defaultProps} />);
    expect(screen.getByText('Age range')).toBeInTheDocument();
    // The plus icon should be present (fa-plus class)
    const icon = document.querySelector('.fa-plus');
    expect(icon).toBeInTheDocument();
  });

  it('shows times icon when section is expanded', () => {
    const expandedProps = {
      ...defaultProps,
      expandedSections: ['ageRange'],
    };
    render(<AgeRangeSection {...expandedProps} />);
    // The times icon should be present (fa-times class)
    const icon = document.querySelector('.fa-times');
    expect(icon).toBeInTheDocument();
  });

  it('auto-enables all age ranges and normalizes to 100% when section is opened', () => {
    render(<AgeRangeSection {...defaultProps} />);

    const toggleButton = screen.getByText('Age range').closest('button');
    fireEvent.click(toggleButton!);

    // Should call setValue to enable all age ranges and normalize to 100%
    // Original total: 7.2 + 23.8 + 27.2 = 58.2
    // Scale factor: 100 / 58.2 ≈ 1.718
    // Scaled: 7.2 * 1.718 ≈ 12, 23.8 * 1.718 ≈ 41, 27.2 * 1.718 ≈ 47
    expect(mockSetValue).toHaveBeenCalledWith('targetConfig.ageRanges', [
      { range: '13 - 17', percentage: 12, enabled: true },
      { range: '18 - 24', percentage: 41, enabled: true },
      { range: '25 - 34', percentage: 47, enabled: true },
    ]);

    // Should call toggleSection
    expect(mockToggleSection).toHaveBeenCalledWith('ageRange');
  });

  it('displays age ranges when section is expanded', () => {
    const expandedProps = {
      ...defaultProps,
      expandedSections: ['ageRange'],
    };
    render(<AgeRangeSection {...expandedProps} />);

    // Should display all age ranges
    expect(screen.getByText('13 - 17')).toBeInTheDocument();
    expect(screen.getByText('18 - 24')).toBeInTheDocument();
    expect(screen.getByText('25 - 34')).toBeInTheDocument();
  });

  it('displays percentage controls for each age range', () => {
    const expandedProps = {
      ...defaultProps,
      expandedSections: ['ageRange'],
    };
    render(<AgeRangeSection {...expandedProps} />);

    // Should display percentage values
    expect(screen.getByText('7%')).toBeInTheDocument();
    expect(screen.getByText('24%')).toBeInTheDocument();
    expect(screen.getByText('27%')).toBeInTheDocument();

    // Should display plus and minus buttons
    const minusButtons = document.querySelectorAll('.fa-minus');
    const plusButtons = document.querySelectorAll('.fa-plus');
    expect(minusButtons).toHaveLength(3);
    expect(plusButtons).toHaveLength(3);
  });

  it('updates percentage and redistributes others to maintain 100% total', () => {
    const expandedProps = {
      ...defaultProps,
      expandedSections: ['ageRange'],
    };
    render(<AgeRangeSection {...expandedProps} />);

    // Click the first plus button (for 13-17 age range)
    const plusButtons = document.querySelectorAll('.fa-plus');
    const firstPlusButton = plusButtons[0].closest('button');
    fireEvent.click(firstPlusButton!);

    // Should call setValue with redistributed percentages
    // When 13-17 increases from 7.2 to 8, others redistribute to maintain 100% total
    expect(mockSetValue).toHaveBeenCalledWith('targetConfig.ageRanges', [
      { range: '13 - 17', percentage: 8, enabled: false },
      { range: '18 - 24', percentage: 43, enabled: false }, // Redistributed to maintain 100%
      { range: '25 - 34', percentage: 49, enabled: false }, // Redistributed to maintain 100%
    ]);
  });

  it('displays total targeting percentage', () => {
    const expandedProps = {
      ...defaultProps,
      expandedSections: ['ageRange'],
    };
    render(<AgeRangeSection {...expandedProps} />);

    // Should display total percentage (always 100% due to redistribution)
    expect(screen.getByText('Total targeting:')).toBeInTheDocument();
    expect(screen.getByText('100%')).toBeInTheDocument();
    expect(screen.getByText('Percentages automatically adjust to maintain 100% total')).toBeInTheDocument();
  });

  it('does not auto-enable ranges when section is already expanded', () => {
    const expandedProps = {
      ...defaultProps,
      expandedSections: ['ageRange'],
    };
    render(<AgeRangeSection {...expandedProps} />);

    const toggleButton = screen.getByText('Age range').closest('button');
    fireEvent.click(toggleButton!);

    // Should not call setValue since section was already expanded
    expect(mockSetValue).not.toHaveBeenCalled();

    // Should still call toggleSection to close it
    expect(mockToggleSection).toHaveBeenCalledWith('ageRange');
  });

  it('prevents setting percentage above 100%', () => {
    const highPercentageProps = {
      ...defaultProps,
      expandedSections: ['ageRange'],
      watchedValues: {
        ...mockWatchedValues,
        targetConfig: {
          ageRanges: [
            { range: '13 - 17', percentage: 99, enabled: false },
            { range: '18 - 24', percentage: 1, enabled: false },
          ],
        },
      },
    };
    render(<AgeRangeSection {...highPercentageProps} />);

    // Try to increase 13-17 from 99% to 100% (which should work)
    const plusButtons = document.querySelectorAll('.fa-plus');
    const firstPlusButton = plusButtons[0].closest('button');
    fireEvent.click(firstPlusButton!);

    // Should allow setting to 100%
    expect(mockSetValue).toHaveBeenCalledWith('targetConfig.ageRanges', [
      { range: '13 - 17', percentage: 100, enabled: false },
      { range: '18 - 24', percentage: 0, enabled: false },
    ]);
  });

  it('prevents setting percentage below 0%', () => {
    const lowPercentageProps = {
      ...defaultProps,
      expandedSections: ['ageRange'],
      watchedValues: {
        ...mockWatchedValues,
        targetConfig: {
          ageRanges: [
            { range: '13 - 17', percentage: 0, enabled: false },
            { range: '18 - 24', percentage: 100, enabled: false },
          ],
        },
      },
    };
    render(<AgeRangeSection {...lowPercentageProps} />);

    // Try to decrease 13-17 from 0% to -1% (Math.max(0, 0 - 1) = 0, so it stays at 0)
    const minusButtons = document.querySelectorAll('.fa-minus');
    const firstMinusButton = minusButtons[0].closest('button');
    fireEvent.click(firstMinusButton!);

    // Should call setValue but with no change (stays at 0%)
    expect(mockSetValue).toHaveBeenCalledWith('targetConfig.ageRanges', [
      { range: '13 - 17', percentage: 0, enabled: false },
      { range: '18 - 24', percentage: 100, enabled: false },
    ]);
  });

  it('maintains total of 100% when decreasing percentage', () => {
    const expandedProps = {
      ...defaultProps,
      expandedSections: ['ageRange'],
    };
    render(<AgeRangeSection {...expandedProps} />);

    // Click the first minus button (for 13-17 age range)
    const minusButtons = document.querySelectorAll('.fa-minus');
    const firstMinusButton = minusButtons[0].closest('button');
    fireEvent.click(firstMinusButton!);

    // Should call setValue with redistributed percentages
    // When 13-17 decreases from 7.2 to 7, others redistribute to maintain 100% total
    // Math.max(0, 7.2 - 1) = 6.2, rounded to 7
    expect(mockSetValue).toHaveBeenCalledWith('targetConfig.ageRanges', [
      { range: '13 - 17', percentage: 7, enabled: false },
      { range: '18 - 24', percentage: 43, enabled: false }, // Redistributed to maintain 100%
      { range: '25 - 34', percentage: 50, enabled: false }, // Redistributed to maintain 100%
    ]);
  });

  it('demonstrates clear redistribution with simple percentages', () => {
    const simpleProps = {
      ...defaultProps,
      expandedSections: ['ageRange'],
      watchedValues: {
        ...mockWatchedValues,
        targetConfig: {
          ageRanges: [
            { range: '18 - 24', percentage: 50, enabled: false },
            { range: '25 - 34', percentage: 50, enabled: false },
          ],
        },
      },
    };
    render(<AgeRangeSection {...simpleProps} />);

    // Increase first range from 50% to 51%
    const plusButtons = document.querySelectorAll('.fa-plus');
    const firstPlusButton = plusButtons[0].closest('button');
    fireEvent.click(firstPlusButton!);

    // Should redistribute: 51% + 49% = 100%
    expect(mockSetValue).toHaveBeenCalledWith('targetConfig.ageRanges', [
      { range: '18 - 24', percentage: 51, enabled: false },
      { range: '25 - 34', percentage: 49, enabled: false }, // Decreased to maintain 100%
    ]);
  });
});
