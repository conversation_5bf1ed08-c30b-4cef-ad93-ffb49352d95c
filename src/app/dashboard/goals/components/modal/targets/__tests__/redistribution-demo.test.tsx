import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { AgeRangeSection } from '../AgeRangeSection';
import { GoalCreateFormData } from '../../../../types';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    button: ({ children, onClick, ...props }: any) => (
      <button onClick={onClick} {...props}>{children}</button>
    ),
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    i: ({ children, ...props }: any) => <i {...props}>{children}</i>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

const mockSetValue = jest.fn();
const mockToggleSection = jest.fn();

describe('AgeRangeSection - Redistribution Demo', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('demonstrates redistribution with equal percentages', () => {
    const equalProps = {
      watchedValues: {
        name: 'Test Goal',
        icon: 'clock',
        period: { number: 1, unit: 'week' },
        targetConfig: {
          ageRanges: [
            { range: '18 - 24', percentage: 33, enabled: false },
            { range: '25 - 34', percentage: 33, enabled: false },
            { range: '35 - 44', percentage: 34, enabled: false }, // Total = 100%
          ],
        },
      } as GoalCreateFormData,
      setValue: mockSetValue,
      expandedSections: ['ageRange'],
      toggleSection: mockToggleSection,
    };

    render(<AgeRangeSection {...equalProps} />);

    // Increase first range from 33% to 34%
    const plusButtons = document.querySelectorAll('.fa-plus');
    const firstPlusButton = plusButtons[0].closest('button');
    fireEvent.click(firstPlusButton!);

    // Should redistribute: 34% + 33% + 33% = 100%
    expect(mockSetValue).toHaveBeenCalledWith('targetConfig.ageRanges', [
      { range: '18 - 24', percentage: 34, enabled: false }, // Increased by 1
      { range: '25 - 34', percentage: 33, enabled: false }, // Decreased by 0.5, rounded to 33
      { range: '35 - 44', percentage: 33, enabled: false }, // Decreased by 0.5, rounded to 33
    ]);
  });

  it('demonstrates redistribution with unequal percentages', () => {
    const unequalProps = {
      watchedValues: {
        name: 'Test Goal',
        icon: 'clock',
        period: { number: 1, unit: 'week' },
        targetConfig: {
          ageRanges: [
            { range: '18 - 24', percentage: 10, enabled: false },
            { range: '25 - 34', percentage: 60, enabled: false },
            { range: '35 - 44', percentage: 30, enabled: false }, // Total = 100%
          ],
        },
      } as GoalCreateFormData,
      setValue: mockSetValue,
      expandedSections: ['ageRange'],
      toggleSection: mockToggleSection,
    };

    render(<AgeRangeSection {...unequalProps} />);

    // Increase first range from 10% to 11%
    const plusButtons = document.querySelectorAll('.fa-plus');
    const firstPlusButton = plusButtons[0].closest('button');
    fireEvent.click(firstPlusButton!);

    // Should redistribute proportionally: larger ranges lose more
    expect(mockSetValue).toHaveBeenCalledWith('targetConfig.ageRanges', [
      { range: '18 - 24', percentage: 11, enabled: false }, // Increased by 1
      { range: '25 - 34', percentage: 59, enabled: false }, // Lost more (was 60, now 59)
      { range: '35 - 44', percentage: 30, enabled: false }, // Lost less (was 30, stays 30)
    ]);
  });

  it('demonstrates setting one range to 100%', () => {
    const testProps = {
      watchedValues: {
        name: 'Test Goal',
        icon: 'clock',
        period: { number: 1, unit: 'week' },
        targetConfig: {
          ageRanges: [
            { range: '18 - 24', percentage: 50, enabled: false },
            { range: '25 - 34', percentage: 50, enabled: false },
          ],
        },
      } as GoalCreateFormData,
      setValue: mockSetValue,
      expandedSections: ['ageRange'],
      toggleSection: mockToggleSection,
    };

    render(<AgeRangeSection {...testProps} />);

    // Click plus button 50 times to get from 50% to 100%
    const plusButtons = document.querySelectorAll('.fa-plus');
    const firstPlusButton = plusButtons[0].closest('button');
    
    // Simulate clicking plus 50 times
    for (let i = 0; i < 50; i++) {
      fireEvent.click(firstPlusButton!);
    }

    // The last call should set first range to 100% and second to 0%
    const lastCall = mockSetValue.mock.calls[mockSetValue.mock.calls.length - 1];
    expect(lastCall).toEqual(['targetConfig.ageRanges', [
      { range: '18 - 24', percentage: 100, enabled: false },
      { range: '25 - 34', percentage: 0, enabled: false },
    ]]);
  });

  it('shows total is always 100% after any change', () => {
    const testProps = {
      watchedValues: {
        name: 'Test Goal',
        icon: 'clock',
        period: { number: 1, unit: 'week' },
        targetConfig: {
          ageRanges: [
            { range: '18 - 24', percentage: 25, enabled: false },
            { range: '25 - 34', percentage: 25, enabled: false },
            { range: '35 - 44', percentage: 25, enabled: false },
            { range: '45 - 54', percentage: 25, enabled: false },
          ],
        },
      } as GoalCreateFormData,
      setValue: mockSetValue,
      expandedSections: ['ageRange'],
      toggleSection: mockToggleSection,
    };

    render(<AgeRangeSection {...testProps} />);

    // Make several random changes
    const plusButtons = document.querySelectorAll('.fa-plus');
    const minusButtons = document.querySelectorAll('.fa-minus');

    // Increase first range
    fireEvent.click(plusButtons[0].closest('button')!);
    let lastCall = mockSetValue.mock.calls[mockSetValue.mock.calls.length - 1][1];
    let total = lastCall.reduce((sum: number, range: any) => sum + range.percentage, 0);
    expect(total).toBe(100);

    // Decrease second range
    fireEvent.click(minusButtons[1].closest('button')!);
    lastCall = mockSetValue.mock.calls[mockSetValue.mock.calls.length - 1][1];
    total = lastCall.reduce((sum: number, range: any) => sum + range.percentage, 0);
    expect(total).toBe(100);

    // Increase third range
    fireEvent.click(plusButtons[2].closest('button')!);
    lastCall = mockSetValue.mock.calls[mockSetValue.mock.calls.length - 1][1];
    total = lastCall.reduce((sum: number, range: any) => sum + range.percentage, 0);
    expect(total).toBe(100);
  });
});
