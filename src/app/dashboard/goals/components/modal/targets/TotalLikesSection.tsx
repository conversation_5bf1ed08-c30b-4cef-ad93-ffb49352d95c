"use client";

import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
  Bar<PERSON>hart,
  Bar,
} from "recharts";
import { UseFormSetValue, UseFormWatch } from "react-hook-form";
import { GoalCreateFormData } from "../../../types";

interface TotalLikesSectionProps {
  expandedSections: string[];
  toggleSection: (section: string) => void;
  setValue?: UseFormSetValue<GoalCreateFormData>;
  watch?: UseFormWatch<GoalCreateFormData>;
}

// Sample likes data for the last 14 days
const likesData = [
  { day: "Day 1", likes: 245, posts: 2, avgLikesPerPost: 122 },
  { day: "Day 2", likes: 189, posts: 1, avgLikesPerPost: 189 },
  { day: "Day 3", likes: 312, posts: 2, avgLikesPerPost: 156 },
  { day: "Day 4", likes: 278, posts: 1, avgLikesPerPost: 278 },
  { day: "Day 5", likes: 456, posts: 3, avgLikesPerPost: 152 },
  { day: "Day 6", likes: 334, posts: 2, avgLikesPerPost: 167 },
  { day: "Day 7", likes: 523, posts: 3, avgLikesPerPost: 174 },
  { day: "Day 8", likes: 298, posts: 1, avgLikesPerPost: 298 },
  { day: "Day 9", likes: 412, posts: 2, avgLikesPerPost: 206 },
  { day: "Day 10", likes: 367, posts: 2, avgLikesPerPost: 183 },
  { day: "Day 11", likes: 445, posts: 2, avgLikesPerPost: 222 },
  { day: "Day 12", likes: 389, posts: 1, avgLikesPerPost: 389 },
  { day: "Day 13", likes: 567, posts: 3, avgLikesPerPost: 189 },
  { day: "Day 14", likes: 478, posts: 2, avgLikesPerPost: 239 },
];

// Post performance data
const postPerformance = [
  { type: "Photos", likes: 1245, posts: 8, color: "#E91E63" },
  { type: "Videos", likes: 2134, posts: 6, color: "#2196F3" },
  { type: "Reels", likes: 3456, posts: 4, color: "#FF9800" },
  { type: "Stories", likes: 892, posts: 12, color: "#9C27B0" },
];

export const TotalLikesSection = ({
  expandedSections,
  toggleSection,
}: TotalLikesSectionProps) => {
  const [goalTarget, setGoalTarget] = useState(400);
  const [isEditingTarget, setIsEditingTarget] = useState(false);
  const [viewMode, setViewMode] = useState<"daily" | "performance">("daily");
  const isExpanded = expandedSections.includes("totalLike");

  // Calculate metrics
  const totalLikes = likesData.reduce((sum, day) => sum + day.likes, 0);
  const averageLikes = Math.round(totalLikes / likesData.length);
  const currentLikes = likesData[likesData.length - 1]?.likes || 0;
  const goalProgress = Math.round((currentLikes / goalTarget) * 100);
  const bestDay = Math.max(...likesData.map((d) => d.likes));
  const totalPosts = likesData.reduce((sum, day) => sum + day.posts, 0);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="text-red-600 font-medium">
                Likes: {data.likes}
              </span>
            </p>
            <p className="text-sm">
              <span className="text-gray-600">Posts: {data.posts}</span>
            </p>
            <p className="text-sm">
              <span className="text-blue-600">
                Avg per post: {data.avgLikesPerPost}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const handleTargetSave = () => {
    setIsEditingTarget(false);
  };

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <motion.button
        type="button"
        onClick={() => toggleSection("totalLike")}
        className="w-full flex items-center justify-between p-4 bg-white hover:bg-gray-50 transition-colors duration-200"
        whileHover={{ backgroundColor: "#f9fafb" }}
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
            <i className="fas fa-heart text-red-600 text-sm"></i>
          </div>
          <span className="text-base font-medium text-gray-700">
            Total Likes
          </span>
        </div>
        <motion.i
          className={`fas ${isExpanded ? "fa-times" : "fa-plus"} text-gray-400`}
          animate={{ rotate: isExpanded ? 45 : 0 }}
          transition={{ duration: 0.2 }}
        />
      </motion.button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="border-t border-gray-200 bg-gray-50"
          >
            <div className="p-6 space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">
                    Likes Performance
                  </h3>
                  <p className="text-sm text-gray-600">
                    Track your engagement metrics
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-red-600">
                    {totalLikes.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">Total Likes</div>
                </div>
              </div>

              {/* Goal Target Setting */}
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-800">
                    Daily Likes Goal
                  </h4>
                  <button
                    onClick={() => setIsEditingTarget(!isEditingTarget)}
                    className="text-sm text-red-600 hover:text-red-700 font-medium"
                  >
                    {isEditingTarget ? "Cancel" : "Edit"}
                  </button>
                </div>

                {isEditingTarget ? (
                  <div className="flex items-center gap-3">
                    <input
                      type="number"
                      value={goalTarget}
                      onChange={(e) => setGoalTarget(Number(e.target.value))}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      placeholder="Enter target likes"
                    />
                    <button
                      onClick={handleTargetSave}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                      Save
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold text-red-600">
                      {goalTarget.toLocaleString()}
                    </div>
                    <div
                      className={`px-3 py-1 rounded-full text-sm font-medium ${
                        goalProgress >= 100
                          ? "bg-green-100 text-green-800"
                          : goalProgress >= 80
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {goalProgress}% of goal
                    </div>
                  </div>
                )}
              </div>

              {/* View Toggle */}
              <div className="flex items-center gap-2 bg-white rounded-lg p-1 border border-gray-200 w-fit">
                <button
                  onClick={() => setViewMode("daily")}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    viewMode === "daily"
                      ? "bg-red-600 text-white"
                      : "text-gray-600 hover:text-gray-800"
                  }`}
                >
                  Daily Trend
                </button>
                <button
                  onClick={() => setViewMode("performance")}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    viewMode === "performance"
                      ? "bg-red-600 text-white"
                      : "text-gray-600 hover:text-gray-800"
                  }`}
                >
                  Post Performance
                </button>
              </div>

              {/* Chart */}
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    {viewMode === "daily" ? (
                      <AreaChart
                        data={likesData}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <defs>
                          <linearGradient
                            id="likesGradient"
                            x1="0"
                            y1="0"
                            x2="0"
                            y2="1"
                          >
                            <stop
                              offset="5%"
                              stopColor="#ef4444"
                              stopOpacity={0.8}
                            />
                            <stop
                              offset="95%"
                              stopColor="#ef4444"
                              stopOpacity={0.1}
                            />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis
                          dataKey="day"
                          tick={{ fontSize: 12 }}
                          interval="preserveStartEnd"
                        />
                        <YAxis tick={{ fontSize: 12 }} />
                        <Tooltip content={<CustomTooltip />} />
                        <ReferenceLine
                          y={goalTarget}
                          stroke="#ef4444"
                          strokeDasharray="5 5"
                          label={{ value: "Goal", position: "topRight" }}
                        />
                        <Area
                          type="monotone"
                          dataKey="likes"
                          stroke="#ef4444"
                          strokeWidth={3}
                          fill="url(#likesGradient)"
                        />
                      </AreaChart>
                    ) : (
                      <BarChart
                        data={postPerformance}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="type" tick={{ fontSize: 12 }} />
                        <YAxis tick={{ fontSize: 12 }} />
                        <Tooltip
                          formatter={(value, name) => [
                            name === "likes"
                              ? `${value} likes`
                              : `${value} posts`,
                            name === "likes" ? "Total Likes" : "Posts Count",
                          ]}
                        />
                        <Bar
                          dataKey="likes"
                          fill="#ef4444"
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    )}
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {averageLikes}
                  </div>
                  <div className="text-sm text-gray-500">Avg. Daily</div>
                </div>
                <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round(totalLikes / totalPosts)}
                  </div>
                  <div className="text-sm text-gray-500">Avg. Per Post</div>
                </div>
                <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {bestDay}
                  </div>
                  <div className="text-sm text-gray-500">Best Day</div>
                </div>
                <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {totalPosts}
                  </div>
                  <div className="text-sm text-gray-500">Total Posts</div>
                </div>
              </div>

              {/* Post Performance Breakdown */}
              {viewMode === "performance" && (
                <div className="bg-white rounded-lg p-4 border border-gray-200">
                  <h4 className="font-medium text-gray-800 mb-4">
                    Content Performance
                  </h4>
                  <div className="space-y-3">
                    {postPerformance.map((item) => (
                      <div
                        key={item.type}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: item.color }}
                          ></div>
                          <span className="font-medium text-gray-700">
                            {item.type}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-800">
                            {item.likes} likes
                          </div>
                          <div className="text-sm text-gray-500">
                            {Math.round(item.likes / item.posts)} avg/post
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Insights */}
              <div className="bg-linear-to-r from-red-50 to-pink-50 rounded-lg p-4 border border-red-200">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center shrink-0">
                    <i className="fas fa-heart text-red-600 text-sm"></i>
                  </div>
                  <div>
                    <h4 className="font-medium text-red-800 mb-1">
                      Engagement Insights
                    </h4>
                    <p className="text-sm text-red-700">
                      {viewMode === "daily"
                        ? `Your average daily likes (${averageLikes}) ${
                            averageLikes >= goalTarget ? "exceeds" : "is below"
                          } your goal. ${
                            averageLikes >= goalTarget
                              ? "Keep up the great work!"
                              : "Try posting during peak hours to boost engagement."
                          }`
                        : `Reels perform best with ${Math.round(
                            postPerformance[2].likes / postPerformance[2].posts
                          )} likes per post on average. Consider creating more video content.`}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
