"use client";

import { motion } from 'framer-motion';
import Image from 'next/image';

interface ModalHeaderProps {
  editMode: boolean;
  onClose: () => void;
  isLoading: boolean;
  isMobile: boolean;
}

export const ModalHeader = ({ editMode, onClose, isLoading, isMobile }: ModalHeaderProps) => {
  return (
    <div className="sticky top-0 bg-white z-100 border-b border-gray-200 rounded-t-2xl">
      <div className={`flex items-center justify-between ${isMobile ? 'p-4' : 'p-6'}`}>
        <motion.div
          className="flex items-center gap-3"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="flex items-center justify-center">
            <Image
              src="/icons/performance/instagram-on.svg"
              alt="Instagram"
              width={28}
              height={28}
              className="inline-block"
              priority
            />
          </div>
          <motion.span
            className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold text-gray-800`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            {editMode ? 'Edit goal' : 'Set your goal setting'}
          </motion.span>
        </motion.div>

        <motion.button
          whileHover={{
            scale: 1.05,
            backgroundColor: "rgba(239, 68, 68, 0.1)",
            borderColor: "rgba(239, 68, 68, 0.2)"
          }}
          whileTap={{ scale: 0.95 }}
          onClick={onClose}
          className="text-gray-400 hover:text-red-500 w-10 h-10 rounded-xl border border-gray-200 hover:border-red-200 flex items-center justify-center transition-all duration-200 bg-gray-50 hover:bg-red-50"
          disabled={isLoading}
          initial={{ opacity: 0, rotate: -90 }}
          animate={{ opacity: 1, rotate: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </motion.button>
      </div>
    </div>
  );
};
