"use client";

import { motion } from 'framer-motion';
import { GoalCreateFormData } from '../../types';

interface ModalFooterProps {
  currentStep: number;
  isMobile: boolean;
  isLoading: boolean;
  isValid: boolean;
  editMode: boolean;
  watchedValues: GoalCreateFormData;
  onBack: () => void;
  onNext: () => void;
  onSubmit: () => void;
}

export const ModalFooter = ({
  currentStep,
  isMobile,
  isLoading,
  isValid,
  editMode,
  watchedValues,
  onBack,
  onNext,
  onSubmit
}: ModalFooterProps) => {
  return (
    <div className={`bg-white border-t border-gray-200 ${isMobile ? 'p-4 rounded-b-3xl' : 'p-6 rounded-b-2xl'} z-100 shrink-0`}>
      <div className={`flex ${isMobile ? 'justify-center' : 'justify-center'} gap-3 ${isMobile ? 'w-full' : 'max-w-md mx-auto'}`}>
        {currentStep > 1 && (
          <motion.button
            type="button"
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
            onClick={onBack}
            className={`${isMobile ? 'flex-1 max-w-[120px]' : ''} px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:border-gray-400 transition-all duration-200 font-medium bg-white`}
            disabled={isLoading}
          >
            Back
          </motion.button>
        )}

        {currentStep === 1 ? (
          <motion.button
            type="button"
            whileHover={{
              scale: !watchedValues.name?.trim() || isLoading ? 1 : 1.01,
            }}
            whileTap={{ scale: !watchedValues.name?.trim() || isLoading ? 1 : 0.99 }}
            onClick={onNext}
            className={`${isMobile ? 'w-full' : 'px-8'} py-3 text-white rounded-lg transition-all duration-200 font-medium ${
              !watchedValues.name?.trim() || isLoading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-[#374151] hover:bg-[#4b5563]'
            }`}
            disabled={!watchedValues.name?.trim() || isLoading}
          >
            Select Item
          </motion.button>
        ) : currentStep === 2 ? (
          <motion.button
            type="button"
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
            onClick={onNext}
            className={`${isMobile ? 'w-full' : 'px-8'} py-3 bg-[#374151] hover:bg-[#4b5563] text-white rounded-lg transition-all duration-200 font-medium`}
            disabled={isLoading}
          >
            View Preview
          </motion.button>
        ) : (
          <motion.button
            type="button"
            onClick={onSubmit}
            whileHover={{
              scale: !isValid || isLoading ? 1 : 1.01,
            }}
            whileTap={{ scale: !isValid || isLoading ? 1 : 0.99 }}
            className={`${isMobile ? 'w-full' : 'px-8'} py-3 text-white rounded-lg transition-all duration-200 font-medium ${
              !isValid || isLoading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-[#374151] hover:bg-[#4b5563]'
            }`}
            disabled={!isValid || isLoading}
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {editMode ? 'Saving...' : 'Creating...'}
              </div>
            ) : (
              editMode ? 'Save Changes' : 'Create Goal'
            )}
          </motion.button>
        )}
      </div>
    </div>
  );
};
