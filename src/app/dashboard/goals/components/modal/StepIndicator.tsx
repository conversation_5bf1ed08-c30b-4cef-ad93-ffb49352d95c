"use client";

import { motion } from 'framer-motion';

interface StepIndicatorProps {
  currentStep: number;
  isMobile: boolean;
}

const steps = [
  { step: 1, label: 'Goal Settings', icon: 'cog' },
  { step: 2, label: 'Item', icon: 'list' },
  { step: 3, label: 'Preview', icon: 'eye' }
];

export const StepIndicator = ({ currentStep, isMobile }: StepIndicatorProps) => {
  if (isMobile) {
    return (
      <div className="px-6 py-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between relative">
          {steps.map((item) => (
            <div key={item.step} className="flex flex-col items-center relative z-10">
              <motion.div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border-2 ${
                  item.step === currentStep
                    ? 'bg-[#374151] text-white border-[#374151]'
                    : item.step < currentStep
                    ? 'bg-[#374151] text-white border-[#374151]'
                    : 'bg-gray-200 text-gray-500 border-gray-300'
                }`}
                animate={{
                  backgroundColor: item.step <= currentStep ? '#374151' : '#e5e7eb',
                  borderColor: item.step <= currentStep ? '#374151' : '#d1d5db',
                  color: item.step <= currentStep ? '#ffffff' : '#6b7280'
                }}
                transition={{ duration: 0.3 }}
              >
                {item.step < currentStep ? (
                  <i className="fas fa-check text-xs"></i>
                ) : (
                  item.step
                )}
              </motion.div>
              <span className={`text-xs mt-2 text-center font-medium ${
                item.step <= currentStep ? 'text-[#374151]' : 'text-gray-400'
              }`}>
                {item.label}
              </span>
            </div>
          ))}

          {/* Connection Lines */}
          <div className="absolute top-4 left-0 right-0 flex justify-between px-4">
            <div className="flex-1 h-[2px] bg-gray-200 mx-4">
              <motion.div
                className="h-full bg-[#374151]"
                initial={{ width: '0%' }}
                animate={{
                  width: currentStep > 1 ? '100%' : '0%'
                }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              />
            </div>
            <div className="flex-1 h-[2px] bg-gray-200 mx-4">
              <motion.div
                className="h-full bg-[#374151]"
                initial={{ width: '0%' }}
                animate={{
                  width: currentStep > 2 ? '100%' : '0%'
                }}
                transition={{ duration: 0.5, ease: "easeInOut", delay: 0.1 }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Desktop version - Only show sidebar steps
  return (
    <div className="w-64 border-r border-gray-200 bg-gray-50 shrink-0">
      <div className="p-6">
        <div className="relative">
          {steps.map((item, index) => (
            <div key={item.step} className="relative">
              <div className="flex items-center gap-3 relative z-10">
                <motion.div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    item.step === currentStep
                      ? 'bg-[#374151] text-white'
                      : item.step < currentStep
                      ? 'bg-[#374151] text-white'
                      : 'bg-gray-300 text-gray-600'
                  }`}
                  animate={{
                    backgroundColor: item.step <= currentStep ? '#374151' : '#d1d5db',
                    color: item.step <= currentStep ? '#ffffff' : '#6b7280'
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {item.step < currentStep ? (
                    <i className="fas fa-check text-xs"></i>
                  ) : (
                    item.step
                  )}
                </motion.div>
                <span className={`text-sm ${
                  item.step <= currentStep ? 'text-gray-800 font-medium' : 'text-gray-400'
                }`}>
                  {item.label}
                </span>
              </div>

              {/* Dotted connecting line */}
              {index < steps.length - 1 && (
                <div
                  className="absolute left-4 top-8 w-0 h-8 border-l-2 border-dotted border-gray-300"
                  style={{
                    borderLeftStyle: 'dotted',
                    borderLeftWidth: '2px',
                    borderLeftColor: '#d1d5db'
                  }}
                />
              )}

              {/* Spacing between steps */}
              {index < steps.length - 1 && <div className="h-8" />}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
