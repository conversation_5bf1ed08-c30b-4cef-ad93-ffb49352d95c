"use client";

import { motion } from 'framer-motion';
import { addDays, addWeeks, addMonths } from 'date-fns';
import { UseFormWatch } from 'react-hook-form';
import { GoalCreateFormData } from '../../../types';
import { useUserStore } from "~/store/userStore";
import { getPlatformColors } from '../../../../analytics/utils/colors';

interface PreviewStepProps {
  watch: UseFormWatch<GoalCreateFormData>;
  expandedSections: string[];
}

export const PreviewStep = ({ watch, expandedSections }: PreviewStepProps) => {
  const watchedValues = watch();
  const { selectedSocial } = useUserStore();
  const platform = selectedSocial?.platform || 'Instagram';
  const platformColors = getPlatformColors(platform);

  const calculateEndDate = (period: { number: number; unit: string }) => {
    const today = new Date();

    switch (period.unit.toLowerCase()) {
      case 'day':
        return addDays(today, period.number);
      case 'week':
        return addWeeks(today, period.number);
      case 'month':
        return addMonths(today, period.number);
      default:
        return today;
    }
  };

  // Helper function to get enabled targets
  const getEnabledTargets = () => {
    const targets: any[] = [];
    const config = watchedValues.targetConfig;

    if (config?.ageRanges?.some(range => range.enabled)) {
      targets.push({
        type: 'Age Ranges',
        icon: 'fas fa-users',
        data: config.ageRanges.filter(range => range.enabled)
      });
    }

    if (config?.locationCountries?.some((location) => location.enabled)) {
      targets.push({
        type: 'Locations: Countries',
        icon: 'fas fa-map-marker-alt',
        data: config.locationCountries.filter((location) => location.enabled),
      });
    }

    if (config?.locationCities?.some((location) => location.enabled)) {
      targets.push({
        type: 'Locations: Cities',
        icon: 'fas fa-city',
        data: config.locationCities.filter((location) => location.enabled),
      });
    }

    if (config?.genders?.some(gender => gender.enabled)) {
      targets.push({
        type: 'Gender',
        icon: 'fas fa-venus-mars',
        data: config.genders.filter(gender => gender.enabled)
      });
    }

    if (config?.accountReach?.enabled) {
      targets.push({
        type: 'Account Reach',
        icon: 'fas fa-eye',
        data: [{ target: config.accountReach.target || 0 }]
      });
    }

    return targets;
  };

  const enabledTargets = getEnabledTargets();

  // Helper function to calculate total percentages for validation
  const getPercentageSummary = () => {
    const config = watchedValues.targetConfig;
    const summary = [];

    if (config?.ageRanges?.some(range => range.enabled)) {
      const total = config.ageRanges
        .filter(range => range.enabled)
        .reduce((sum, range) => sum + range.percentage, 0);
      summary.push({ type: 'Age Ranges', total: Math.round(total) });
    }

    if (config?.locationCountries?.some((location) => location.enabled)) {
      const total = config.locationCountries
        .filter((location) => location.enabled)
        .reduce((sum: number, location) => sum + location.percentage, 0);
      summary.push({ type: 'Locations: Countries', total: Math.round(total) });
    }

    if (config?.locationCities?.some((location) => location.enabled)) {
      const total = config.locationCities
        .filter((location) => location.enabled)
        .reduce((sum: number, location) => sum + location.percentage, 0);
      summary.push({ type: 'Locations: Cities', total: Math.round(total) });
    }

    if (config?.genders?.some(gender => gender.enabled)) {
      const total = config.genders
        .filter(gender => gender.enabled)
        .reduce((sum, gender) => sum + gender.percentage, 0);
      summary.push({ type: 'Gender', total: Math.round(total) });
    }

    return summary;
  };

  const percentageSummary = getPercentageSummary();

  return (
    <motion.div
      key="preview"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-6"
    >
      {/* Goal Header */}
      <motion.div
        className="flex items-center gap-4 mb-6"
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
      >
        <div
          className="w-12 h-12 rounded-xl flex items-center justify-center shadow-sm border"
          style={{ backgroundColor: platformColors.primaryOpacity, borderColor: '#e5e7eb' }}
        >
          <i className={`fas fa-${watchedValues.icon} text-xl`} style={{ color: platformColors.primary }}></i>
        </div>
        <div className="text-left">
          <h3 className="text-xl font-semibold text-gray-900">{watchedValues.name || 'My Goal'}</h3>
          <p className="text-sm text-gray-600 flex items-center gap-2">
            <span className="inline-block w-2 h-2 rounded-full" style={{ backgroundColor: platformColors.primary }}></span>
            {platform} Goal
          </p>
        </div>
      </motion.div>

      {/* Goal Summary */}
      <motion.div
        className="p-6 rounded-xl border bg-white"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <h4 className="font-medium text-base mb-4 text-gray-900 flex items-center gap-2">
          <span className="inline-block w-1.5 h-4 rounded-sm" style={{ backgroundColor: platformColors.primary }}></span>
          Goal Summary
        </h4>
        <div className="space-y-3">
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
            <span className="text-gray-600 flex items-center gap-2">
              <i className="fas fa-clock text-gray-500"></i>
              Duration
            </span>
            <span className="font-medium text-gray-900">
              {watchedValues.period ? `${watchedValues.period.number} ${watchedValues.period.unit}${watchedValues.period.number > 1 ? 's' : ''}` : '1 week'}
            </span>
          </div>
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
            <span className="text-gray-600 flex items-center gap-2">
              <i className="fas fa-calendar-alt text-gray-500"></i>
              End Date
            </span>
            <span className="font-medium text-gray-900">
              {watchedValues.period ? calculateEndDate(watchedValues.period).toLocaleDateString() : 'Not set'}
            </span>
          </div>
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
            <span className="text-gray-600 flex items-center gap-2">
              <span className="inline-block w-2 h-2 rounded-full" style={{ backgroundColor: platformColors.primary }}></span>
              Platform
            </span>
            <span className="font-medium text-gray-900">{platform}</span>
          </div>
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
            <span className="text-gray-600 flex items-center gap-2">
              <i className="fas fa-target text-gray-500"></i>
              Targets Configured
            </span>
            <span className="font-medium text-gray-900">
              {enabledTargets.length} {enabledTargets.length === 1 ? 'target' : 'targets'}
            </span>
          </div>
        </div>
      </motion.div>

      {/* Target Configurations */}
      {enabledTargets.length > 0 && (
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <h4 className="font-medium text-base text-gray-900 flex items-center gap-2">
            <span className="inline-block w-1.5 h-4 rounded-sm" style={{ backgroundColor: platformColors.primary }}></span>
            Target Configurations
          </h4>

          {enabledTargets.map((target, index) => (
            <motion.div
              key={target.type}
              className="bg-white p-4 rounded-xl border border-gray-200"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 + index * 0.1 }}
            >
              <div className="flex items-center gap-2 mb-3">
                <i className={`${target.icon} text-gray-600 text-lg`}></i>
                <h5 className="font-medium text-gray-900">{target.type}</h5>
              </div>

              <div className="space-y-2">
                {target.type === 'Age Ranges' && target.data.map((item: any, idx: number) => (
                  <div key={idx} className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                    <span className="text-sm text-gray-600">{item.range}</span>
                    <span className="text-sm font-medium text-gray-900">{Math.round(item.percentage)}%</span>
                  </div>
                ))}

                {target.type.startsWith('Locations') && target.data.map((item: { name: string; percentage: number }, idx: number) => (
                  <div key={idx} className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                    <span className="text-sm text-gray-600">{item.name}</span>
                    <span className="text-sm font-medium text-gray-900">{Math.round(item.percentage)}%</span>
                  </div>
                ))}

                {target.type === 'Gender' && target.data.map((item: any, idx: number) => (
                  <div key={idx} className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                    <span className="text-sm text-gray-600">{item.gender}</span>
                    <span className="text-sm font-medium text-gray-900">{Math.round(item.percentage)}%</span>
                  </div>
                ))}

                {target.type === 'Account Reach' && (
                  <div className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                    <span className="text-sm text-gray-600">Target Reach</span>
                    <span className="text-sm font-medium text-gray-900">
                      {target.data[0]?.target?.toLocaleString() || 0} accounts
                    </span>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </motion.div>
      )}

      {/* Percentage Validation Summary */}
      {percentageSummary.length > 0 && (
        <motion.div
          className="p-4 rounded-xl border bg-white"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <h5 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
            <span className="inline-block w-1.5 h-4 rounded-sm" style={{ backgroundColor: platformColors.primary }}></span>
            Target Distribution Summary
          </h5>
          <div className="space-y-2">
            {percentageSummary.map((item, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{item.type} Total:</span>
                <span className="text-sm font-medium text-gray-900">
                  {item.total}%
                </span>
              </div>
            ))}
          </div>
          {percentageSummary.some(item => item.total !== 100) && (
            <div className="mt-3 p-2 bg-gray-50 rounded-lg border border-gray-200">
              <p className="text-xs text-gray-600">
                Tip: For best results, ensure each target category totals 100%
              </p>
            </div>
          )}
        </motion.div>
      )}

      {/* No Targets Message */}
      {enabledTargets.length === 0 && (
        <motion.div
          className="bg-gray-50 p-6 rounded-xl border border-gray-200 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <i className="fas fa-info-circle text-gray-400 text-2xl mb-2"></i>
          <p className="text-gray-600 text-sm">
            No specific targets configured. This goal will track general {platform} performance.
          </p>
        </motion.div>
      )}
    </motion.div>
  );
};
