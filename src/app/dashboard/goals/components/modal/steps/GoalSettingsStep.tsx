"use client";

import { motion } from 'framer-motion';
import { UseFormRegister, FieldErrors, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { GoalCreateFormData } from '../../../types';

// Import form components
import { GoalNameInput } from '../forms/GoalNameInput';
import { PeriodSelector } from '../forms/PeriodSelector';
import { IconSelector } from '../forms/IconSelector';

interface GoalSettingsStepProps {
  register: UseFormRegister<GoalCreateFormData>;
  errors: FieldErrors<GoalCreateFormData>;
  setValue: UseFormSetValue<GoalCreateFormData>;
  watch: UseFormWatch<GoalCreateFormData>;
  isLoading: boolean;
  isMobile: boolean;
}

export const GoalSettingsStep = ({
  register,
  errors,
  setValue,
  watch,
  isLoading,
  isMobile
}: GoalSettingsStepProps) => {
  const watchedValues = watch();

  return (
    <motion.div
      key="goal-settings"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className={`${isMobile ? 'space-y-6' : 'space-y-4'}`}
    >
      {/* Goal Header - show on both mobile and desktop with selected icon */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <div className={`${isMobile ? 'w-8 h-8' : 'w-9 h-9'} bg-[#374151] rounded-full flex items-center p-1 justify-center`}>
            <i
              className={`fas fa-${watchedValues?.icon ? watchedValues.icon : 'target'} ${isMobile ? 'text-sm' : 'text-base'} text-white`}
            />
          </div>
          <label className="text-base font-medium text-gray-700">My Goal</label>
        </div>
      </div>

      {/* Goal Name Input */}
      <GoalNameInput
        register={register}
        errors={errors}
        isLoading={isLoading}
      />

      {/* Period Selection */}
      <PeriodSelector
        register={register}
        setValue={setValue}
        watchedValues={watchedValues}
        isLoading={isLoading}
        isMobile={isMobile}
      />

      {/* Icon Selection */}
      <IconSelector
        setValue={setValue}
        watchedValues={watchedValues}
        isLoading={isLoading}
        isMobile={isMobile}
      />
    </motion.div>
  );
};
