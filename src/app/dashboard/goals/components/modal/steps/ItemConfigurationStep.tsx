"use client";

import { motion } from "framer-motion";
import { UseFormSetValue, UseFormWatch } from "react-hook-form";
import { GoalCreateFormData } from "../../../types";

// Import target configuration components
import { AgeRangeSection } from "../targets/AgeRangeSection";
import { LocationsSection } from "../targets/LocationsSection";
import { GenderSection } from "../targets/GenderSection";
import { AccountReachSection } from "../targets/AccountReachSection";

interface ItemConfigurationStepProps {
  setValue: UseFormSetValue<GoalCreateFormData>;
  watch: UseFormWatch<GoalCreateFormData>;
  expandedSections: string[];
  toggleSection: (section: string) => void;
  isMobile: boolean;
}

export const ItemConfigurationStep = ({
  setValue,
  watch,
  expandedSections,
  toggleSection,
  isMobile,
}: ItemConfigurationStepProps) => {
  const watchedValues = watch();

  return (
    <motion.div
      key="item-configuration"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-3 md:space-y-4"
    >
      {!isMobile && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Item Configuration
          </h2>
          <p className="text-gray-600">Configure your targeting options</p>
        </div>
      )}

      {/* Age Range Section */}
      <AgeRangeSection
        watchedValues={watchedValues}
        setValue={setValue}
        expandedSections={expandedSections}
        toggleSection={toggleSection}
      />

      {/* Locations Section */}
      <LocationsSection
        watchedValues={watchedValues}
        setValue={setValue}
        expandedSections={expandedSections}
        toggleSection={toggleSection}
      />

      {/* Gender Section */}
      <GenderSection
        watchedValues={watchedValues}
        setValue={setValue}
        expandedSections={expandedSections}
        toggleSection={toggleSection}
      />

      {/* Account Reach Section */}
      <AccountReachSection
        expandedSections={expandedSections}
        toggleSection={toggleSection}
        setValue={setValue}
        watch={watch}
      />

      {!isMobile && (
        <div className="text-center py-6">
          <p className="text-gray-500 text-sm">
            Additional targeting options coming soon
          </p>
        </div>
      )}
    </motion.div>
  );
};
