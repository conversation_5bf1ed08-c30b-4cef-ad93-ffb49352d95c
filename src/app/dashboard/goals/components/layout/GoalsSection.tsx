"use client";

import { motion, AnimatePresence } from 'framer-motion';
import { Goal } from '../../types';
import { GoalCard } from '../GoalCard';
import { EmptyState } from '../EmptyState';

interface GoalsSectionProps {
  goals: Goal[];
  selectedSocial: any;
  isLoading: boolean;
  onCreateGoal: () => void;
  onGoalClick: (goal: Goal) => void;
  onEditGoal: (goal: Goal) => void;
  onDeleteGoal: (goal: Goal) => void;
  onPinGoal: (id: number) => void;
}

export const GoalsSection = ({
  goals,
  selectedSocial,
  isLoading,
  onCreateGoal,
  onGoalClick,
  onEditGoal,
  onDeleteGoal,
  onPinGoal
}: GoalsSectionProps) => {
  const renderContent = () => {
    if (!selectedSocial?.social_id) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col items-center justify-center h-full text-center"
        >
          <i className="fas fa-link text-4xl text-gray-400 mb-4"></i>
          <p className="text-gray-600 mb-2">Please select a social media account</p>
          <p className="text-sm text-gray-500">Choose an Instagram or Facebook account from the sidebar to view your goals</p>
        </motion.div>
      );
    }

    if (isLoading) {
      return (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex justify-center items-center h-full"
        >
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </motion.div>
      );
    }

    if (goals.length > 0) {
      return (
        <AnimatePresence>
          {goals.map((goal, index) => (
            <motion.div
              key={goal.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
            >
              <GoalCard
                goal={goal}
                onPin={onPinGoal}
                onClick={onGoalClick}
                onEdit={onEditGoal}
                onDelete={onDeleteGoal}
                isLoading={isLoading}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      );
    }

    return <EmptyState onCreateGoal={onCreateGoal} />;
  };

  return (
    <motion.div 
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.3 }}
      className="w-full md:w-[65%] relative bg-white flex flex-col h-full rounded-2xl min-h-[40vh]"
    >
      {/* Header */}
      <div className="px-4 md:px-[1.6rem] py-[1.6rem] h-[10vh]">
        <motion.h1 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="text-md font-semibold"
        >
          Goals
        </motion.h1>
      </div>
      
      {/* Content */}
      <div className="px-4 md:px-[1.6rem] relative overflow-y-auto h-[30vh]">
        {renderContent()}
      </div>
      
      {/* Footer */}
      <div className="flex w-full justify-center md:justify-end h-[10vh]">
        <div className="p-4 md:p-[1.6rem]">
          <motion.button
            whileHover={{ scale: selectedSocial?.social_id ? 1.05 : 1, backgroundColor: selectedSocial?.social_id ? 'rgba(21, 101, 192, 0.1)' : undefined }}
            whileTap={{ scale: selectedSocial?.social_id ? 0.95 : 1 }}
            onClick={() => selectedSocial?.social_id && onCreateGoal()}
            className={`transition-colors px-3 py-1.5 rounded-md ${
              selectedSocial?.social_id
                ? 'text-[#1565c0] hover:bg-blue-50 cursor-pointer'
                : 'text-gray-400 cursor-not-allowed'
            }`}
            disabled={isLoading || !selectedSocial?.social_id}
          >
            + Add a goal
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};
