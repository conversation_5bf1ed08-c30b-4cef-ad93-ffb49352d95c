"use client";

import { motion } from "framer-motion";
import { useState } from "react";

interface AISolution {
  id: string;
  title: string;
  description: string;
  impact: "HIGH" | "MEDIUM" | "LOW";
  icon: string;
  iconColor: string;
  bgColor: string;
  actionText: string;
  confidence: number;
  estimatedImprovement: string;
}

// Compact AI solutions for sidebar
const compactSolutions: AISolution[] = [
  {
    id: "1",
    title: "Smart Content Optimizer",
    description: "AI suggests video format for +85% engagement boost",
    impact: "HIGH",
    icon: "fa-wand-magic-sparkles",
    iconColor: "text-dark-blue-dark",
    bgColor: "",
    actionText: "Optimize",
    confidence: 94,
    estimatedImprovement: "+85%",
  },
  {
    id: "2",
    title: "AI Trend Detector",
    description: "Trending hashtag #TechTuesday +120% this week",
    impact: "HIGH",
    icon: "fa-chart-line",
    iconColor: "text-dark-blue-dark",
    bgColor: "",
    actionText: "Apply",
    confidence: 89,
    estimatedImprovement: "+120%",
  },
  {
    id: "3",
    title: "Engagement Predictor",
    description: "92% success rate for Tuesday 3 PM posts",
    impact: "MEDIUM",
    icon: "fa-brain",
    iconColor: "text-dark-blue-dark",
    bgColor: "",
    actionText: "Schedule",
    confidence: 92,
    estimatedImprovement: "+67%",
  },
  {
    id: "4",
    title: "Caption AI Assistant",
    description: "Smart captions increase comments by 40%",
    impact: "MEDIUM",
    icon: "fa-robot",
    iconColor: "text-dark-blue-dark",
    bgColor: "",
    actionText: "Generate",
    confidence: 86,
    estimatedImprovement: "+40%",
  },
];

interface AISolutionsSectionProps {
  onViewAllSolutions?: () => void;
}

export const AISolutionsSection = ({
  onViewAllSolutions,
}: AISolutionsSectionProps) => {
  const [selectedSolution, setSelectedSolution] = useState<string | null>(null);

  return (
    <motion.div
      initial={{ x: 20, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ delay: 0.4 }}
      className="w-full md:w-[30%] bg-white rounded-2xl border border-gray-200 mt-4 md:mt-0 overflow-hidden"
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <i className="fas fa-wand-magic-sparkles text-gray-700 text-lg"></i>
            <div>
              <h2 className="text-gray-900 font-semibold text-base">
                AI Solutions
              </h2>
              <p className="text-sm text-gray-500">Smart recommendations</p>
            </div>
          </div>
          <div className="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full select-none">
            AI
          </div>
        </div>
      </div>

      {/* Solutions List */}
      <div className="p-4 space-y-3 max-h-[calc(40vh-80px)] overflow-y-auto">
        {compactSolutions.map((solution, index) => (
          <motion.div
            key={solution.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 + index * 0.1 }}
            className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer select-none"
            onClick={() =>
              setSelectedSolution(
                selectedSolution === solution.id ? null : solution.id
              )
            }
          >
            <div className="flex items-start gap-3">
              <i
                className={`fas ${solution.icon} text-gray-700 text-lg shrink-0`}
              ></i>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-gray-900 text-base truncate">
                    {solution.title}
                  </h4>
                  <span className="px-1.5 py-0.5 text-sm font-medium rounded-full bg-gray-100 text-gray-700 border border-gray-200 select-none">
                    {solution.impact}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  {solution.description}
                </p>

                {selectedSolution === solution.id && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-2"
                  >
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">AI Confidence:</span>
                      <span className="font-medium text-gray-900 text-base">
                        {solution.confidence}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${solution.confidence}%` }}
                        transition={{ delay: 0.2, duration: 0.8 }}
                        className="h-1.5 rounded-full bg-gray-800"
                      />
                    </div>
                    <button className="w-full px-3 py-1.5 border border-gray-300 bg-white text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 transition-colors select-none">
                      {solution.actionText} Now
                    </button>
                  </motion.div>
                )}

                {selectedSolution !== solution.id && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 select-none">
                      {solution.estimatedImprovement}
                    </span>
                    <i className="fas fa-chevron-down text-gray-400 text-xs select-none"></i>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 bg-white">
        <button
          onClick={onViewAllSolutions}
          className="w-full px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md transition-colors select-none"
        >
          View All AI Solutions
        </button>
      </div>
    </motion.div>
  );
};
