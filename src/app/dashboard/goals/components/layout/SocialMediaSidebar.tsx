"use client";

import { motion } from 'framer-motion';

interface SocialAccount {
  platform: string;
  social_id: string;
  social_name: string;
  username?: string;
}

interface SelectedSocial {
  platform: string;
  social_id: string;
  social_name: string;
  username: string;
}

interface SocialMediaSidebarProps {
  socialAccounts: SocialAccount[];
  selectedSocial: SelectedSocial | null;
  onSelectSocial: (social: SelectedSocial) => void;
  onAddSocial: () => void;
}

export const SocialMediaSidebar = ({
  socialAccounts,
  selectedSocial,
  onSelectSocial,
  onAddSocial
}: SocialMediaSidebarProps) => {
  return (
    <motion.div 
      initial={{ x: -20, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ delay: 0.2 }}
      className="hidden md:flex w-16 bg-gray-100 p-12 flex-col items-center"
    >
      {socialAccounts?.map((account) => (
        <div key={account.platform} className="flex flex-col items-center mb-4">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="b-1 rounded-full p-2 transition-colors duration-200"
            onClick={() => onSelectSocial({
              platform: account.platform,
              social_id: account.social_id,
              social_name: account.social_name,
              username: account.username || account.social_name || ''
            })}
          >
            <img
              src={selectedSocial?.platform === account.platform
                ? `/icons/performance/${account.platform}-on.svg`
                : `/icons/performance/${account.platform}-off.svg`}
              alt={account.platform.charAt(0).toUpperCase() + account.platform.slice(1)}
              className="w-8 h-8 transition-all duration-200"
            />
          </motion.button>
          <span className="text-xs text-center">
            {account.platform.charAt(0).toUpperCase() + account.platform.slice(1)}
          </span>
        </div>
      ))}
      
      <div className="flex flex-col items-center mb-4">
        <motion.button 
          whileHover={{ scale: 1.1, backgroundColor: '#d1d5db' }}
          whileTap={{ scale: 0.9 }}
          className="mb-1 bg-gray-200 rounded-full p-2 transition-colors duration-200"
          onClick={onAddSocial}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </motion.button>
        <span className="text-xs text-center">Add More</span>
      </div>
    </motion.div>
  );
};
