"use client";

import { motion } from "framer-motion";
import { Goal } from "../../types";
import { GoalsSection } from "./GoalsSection";
import { AISolutionsSection } from "./AISolutionsSection";
import { SocialAccountSelectionSidebar } from "../../../analytics/components/shared/SocialAccountSelectionSidebar";

interface SocialAccount {
  platform: string;
  social_id: string;
  social_name: string;
  username: string;
}

interface SelectedSocial {
  platform: string;
  social_id: string;
  social_name: string;
  username: string;
}

interface GoalsLayoutProps {
  goals: Goal[];
  socialAccounts: SocialAccount[];
  selectedSocial: SelectedSocial | null;
  isLoading: boolean;
  onSelectSocial: (social: SelectedSocial) => void;
  onAddSocial: () => void;
  onCreateGoal: () => void;
  onGoalClick: (goal: Goal) => void;
  onEditGoal: (goal: Goal) => void;
  onDeleteGoal: (goal: Goal) => void;
  onPinGoal: (id: number) => void;
  onViewAllSolutions?: () => void;
}

export const GoalsLayout = ({
  goals,
  socialAccounts,
  selectedSocial,
  isLoading,
  onSelectSocial,
  onAddSocial,
  onCreateGoal,
  onGoalClick,
  onEditGoal,
  onDeleteGoal,
  onPinGoal,
  onViewAllSolutions,
}: GoalsLayoutProps) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="flex flex-col mt-20 md:mt-0 md:flex-row justify-center items-start gap-4 md:gap-[1.6rem] p-4 md:p-4"
    >
      {/* Social Media Sidebar */}
      <SocialAccountSelectionSidebar
        socialAccounts={socialAccounts}
        selectedSocial={selectedSocial}
        onSocialSelect={(account) =>
          onSelectSocial({
            platform: account.platform,
            social_id: account.social_id,
            social_name: account.social_name,
            username: account.username || account.social_name || "",
          })
        }
        onAddClick={onAddSocial}
      />

      {/* Main Goals Section */}
      <GoalsSection
        goals={goals}
        selectedSocial={selectedSocial}
        isLoading={isLoading}
        onCreateGoal={onCreateGoal}
        onGoalClick={onGoalClick}
        onEditGoal={onEditGoal}
        onDeleteGoal={onDeleteGoal}
        onPinGoal={onPinGoal}
      />

      {/* AI Solutions Section */}
      <AISolutionsSection onViewAllSolutions={onViewAllSolutions} />
    </motion.div>
  );
};
