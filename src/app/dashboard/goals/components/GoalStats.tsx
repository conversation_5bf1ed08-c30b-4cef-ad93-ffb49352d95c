"use client";

import { motion } from "framer-motion";
import { GoalStatsModalProps, AgeRangeData } from "../types";
import RadixDialog from "~/components/ui/RadixDialog";

const mockAgeData: AgeRangeData[] = [
  {
    range: "13 - 17",
    percentage: 7.2,
    change: { value: 30, type: "increase" },
  },
  {
    range: "35 - 44",
    percentage: 26.1,
    change: { value: 10, type: "decrease" },
  },
  { range: "18 - 24", percentage: 23.8, change: { value: 0, type: "same" } },
  { range: "25 - 34", percentage: 27.2, change: { value: 0, type: "same" } },
  {
    range: "45 - 54",
    percentage: 11.1,
    change: { value: 40.2, type: "increase" },
  },
  { range: "55 - 64", percentage: 2.2, change: { value: 0, type: "same" } },
  { range: "65+", percentage: 2.2, change: { value: 0, type: "same" } },
];

export const GoalStats = ({
  isOpen,
  onClose,
  goal,
  insights,
}: GoalStatsModalProps) => {
  if (!isOpen || !goal) return null;

  return (
    <RadixDialog open={isOpen} onOpenChange={(open) => !open && onClose()} title={goal.title}>
      {/* Progress Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="pt-2 pb-4"
      >
            <div className="text-center mb-6">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className="text-4xl text-[#ffc600] font-semibold mb-2"
              >
                {Math.round(goal.percentage * 100)}%
              </motion.div>
              <p className="text-gray-600">Goal Progress</p>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-3 mb-6">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${goal.percentage * 100}%` }}
                transition={{ delay: 0.3, duration: 1, ease: "easeOut" }}
                className={`h-3 rounded-full ${
                  goal.status === "Completed" ? "bg-green-500" : "bg-yellow-500"
                }`}
              />
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-center p-4 bg-gray-50 rounded-lg"
              >
                <div className="text-2xl font-medium text-[#2c3e50]">834</div>
                <div className="text-sm text-gray-600 mt-1">
                  Accounts reached
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="text-center p-4 bg-gray-50 rounded-lg"
              >
                <div className="text-2xl font-medium text-[#2c3e50]">
                  <span>280</span>
                  <span className="text-lg text-gray-500">/1250</span>
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Accounts engaged
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="text-center p-4 bg-gray-50 rounded-lg"
              >
                <div className="text-2xl font-medium text-[#2c3e50]">15</div>
                <div className="text-sm text-gray-600 mt-1">New followers</div>
              </motion.div>
            </div>
      </motion.div>

      {/* Demographics Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="p-3 md:p-5 border rounded-lg border-[#cccccc] m-2 md:m-4"
      >
            <div className="mb-3 md:mb-4">
              <h3 className="text-sm md:text-base font-medium">Age range</h3>
            </div>

            <div className="space-y-2 md:space-y-3">
              {mockAgeData.map((data, index) => (
                <motion.div
                  key={data.range}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8 + index * 0.1 }}
                  className="flex items-center gap-2 md:gap-3 text-sm md:text-base"
                >
                  <div className="w-14 md:w-18">{data.range}</div>
                  <div className="flex-1 flex items-center gap-2 md:gap-3">
                    <div className="flex-1 h-4 md:h-5 bg-gray-100 rounded-lg relative overflow-hidden">
                      {/* Progress bar */}
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${data.percentage}%` }}
                        transition={{ delay: 1 + index * 0.1, duration: 0.8 }}
                        className={`h-full bg-blue-500 ${
                          data.change && data.change.type !== "same"
                            ? "rounded-l-lg rounded-r-none"
                            : "rounded-lg"
                        }`}
                      />
                      {/* Change indicator */}
                      {data.change && data.change.type !== "same" && (
                        <motion.div
                          initial={{ width: 0, opacity: 0 }}
                          animate={{
                            width: `${data.change.value}%`,
                            opacity: 0.65,
                          }}
                          transition={{
                            delay: 1.5 + index * 0.1,
                            duration: 0.5,
                          }}
                          className={`h-full absolute top-0 rounded-r-lg rounded-l-none ${
                            data.change.type === "increase"
                              ? "bg-green-400"
                              : "bg-red-400"
                          }`}
                          style={{ left: `${data.percentage}%` }}
                        />
                      )}
                    </div>
                    <div className="w-10 md:w-12 text-right">
                      {data.percentage}%
                    </div>
                    <div
                      className={`w-14 md:w-16 text-right text-xs ${
                        data.change?.type === "increase"
                          ? "text-green-500"
                          : data.change?.type === "decrease"
                          ? "text-red-500"
                          : "text-gray-700"
                      }`}
                    >
                      {data.change?.type === "same"
                        ? "~same"
                        : data.change
                        ? `${data.change.type === "increase" ? "+" : "-"}${
                            data.change.value
                          }%`
                        : ""}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
      </motion.div>

      

      {/* Footer */}
      <div className="flex justify-center p-4 md:p-6">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onClose}
          className="bg-[#2c3e50] text-white rounded-full py-1.5 md:py-2 px-6 md:px-8 text-sm md:text-base hover:bg-[#34495e] transition-colors"
        >
          OK
        </motion.button>
      </div>
    </RadixDialog>
  );
};
