"use client";

import { motion } from "framer-motion";
import { useState } from "react";

interface AISolution {
  id: string;
  title: string;
  description: string;
  impact: "HIGH" | "MEDIUM" | "LOW";
  category: "optimization" | "content" | "targeting" | "timing" | "engagement";
  icon: string;
  iconColor: string;
  bgColor: string;
  actionText: string;
  confidence: number;
  estimatedImprovement: string;
}

interface AISolutionsProps {
  goalId?: number;
  compact?: boolean;
  maxSolutions?: number;
}

// Dummy AI solutions data
const aiSolutions: AISolution[] = [
  {
    id: "1",
    title: "Optimize Posting Schedule",
    description:
      "Your audience is most active between 2-4 PM on weekdays. Posting during these hours could increase your reach by up to 35%.",
    impact: "HIGH",
    category: "timing",
    icon: "fa-clock",
    iconColor: "text-dark-blue-dark",
    bgColor: "bg-dark-blue-light",
    actionText: "Schedule Posts",
    confidence: 92,
    estimatedImprovement: "+35% reach",
  },
  {
    id: "2",
    title: "Create More Video Content",
    description:
      "Video posts get 3x more engagement than static images in your niche. Consider creating short-form videos or reels.",
    impact: "HIGH",
    category: "content",
    icon: "fa-video",
    iconColor: "text-dark-blue-dark",
    bgColor: "bg-dark-blue-light",
    actionText: "Get Video Ideas",
    confidence: 88,
    estimatedImprovement: "+200% engagement",
  },
  {
    id: "3",
    title: "Target 25-34 Age Group",
    description:
      "This demographic shows 40% higher engagement with your content type and has the highest conversion rate.",
    impact: "MEDIUM",
    category: "targeting",
    icon: "fa-users",
    iconColor: "text-dark-blue-dark",
    bgColor: "bg-dark-blue-light",
    actionText: "Adjust Targeting",
    confidence: 85,
    estimatedImprovement: "+40% engagement",
  },
  {
    id: "4",
    title: "Use Trending Hashtags",
    description:
      "Adding #MondayMotivation, #TechTips, and #BusinessGrowth could increase discoverability by 25%.",
    impact: "MEDIUM",
    category: "optimization",
    icon: "fa-hashtag",
    iconColor: "text-dark-blue-dark",
    bgColor: "bg-dark-blue-light",
    actionText: "Apply Hashtags",
    confidence: 78,
    estimatedImprovement: "+25% discoverability",
  },
  {
    id: "5",
    title: "Add Interactive Stories",
    description:
      "Stories with polls, questions, or quizzes get 25% more interactions and keep your audience engaged longer.",
    impact: "MEDIUM",
    category: "engagement",
    icon: "fa-poll",
    iconColor: "text-dark-blue-dark",
    bgColor: "bg-dark-blue-light",
    actionText: "Create Stories",
    confidence: 82,
    estimatedImprovement: "+25% interactions",
  },
  {
    id: "6",
    title: "Collaborate with Micro-Influencers",
    description:
      "Partnering with influencers in your niche (10K-100K followers) could expand your reach to 50K+ new users.",
    impact: "HIGH",
    category: "targeting",
    icon: "fa-handshake",
    iconColor: "text-dark-blue-dark",
    bgColor: "bg-dark-blue-light",
    actionText: "Find Influencers",
    confidence: 75,
    estimatedImprovement: "+50K reach",
  },
];

const impactColors = {
  HIGH: "bg-dark-blue-dark text-white border-dark-blue-dark-hover",
  MEDIUM: "bg-dark-blue-normal text-white border-dark-blue-normal-hover",
  LOW: "bg-gray-100 text-gray-700 border-gray-200",
};

export const AISolutions = ({
  goalId,
  compact = false,
  maxSolutions = 6,
}: AISolutionsProps) => {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [expandedSolution, setExpandedSolution] = useState<string | null>(null);

  const categories = [
    { id: "all", name: "All Solutions", icon: "fa-wand-magic-sparkles" },
    { id: "timing", name: "Timing", icon: "fa-clock" },
    { id: "content", name: "Content", icon: "fa-video" },
    { id: "targeting", name: "Targeting", icon: "fa-users" },
    { id: "optimization", name: "Optimization", icon: "fa-chart-line" },
    { id: "engagement", name: "Engagement", icon: "fa-heart" },
  ];

  const filteredSolutions =
    selectedCategory === "all"
      ? aiSolutions.slice(0, maxSolutions)
      : aiSolutions
          .filter((solution) => solution.category === selectedCategory)
          .slice(0, maxSolutions);

  const averageConfidence = Math.round(
    filteredSolutions.reduce((sum, solution) => sum + solution.confidence, 0) /
      filteredSolutions.length
  );

  return (
    <div className="bg-linear-to-br from-dark-blue-light/30 via-white to-dark-blue-light/20 rounded-xl border border-dark-blue-normal overflow-hidden">
      {/* Header */}
      <div className="p-4 md:p-6 border-b border-dark-blue-normal bg-linear-to-r from-dark-blue-dark to-dark-blue-darker">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
              <i className="fas fa-wand-magic-sparkles text-white text-lg"></i>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">AI Solutions</h3>
              <p className="text-sm text-white/80">
                {compact
                  ? "Quick recommendations"
                  : "Personalized recommendations to boost your goal"}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="text-lg font-bold text-white">
                {averageConfidence}%
              </div>
              <div className="text-xs text-white/70">Confidence</div>
            </div>
            <div className="px-3 py-1 bg-white/20 text-white text-xs font-medium rounded-full">
              POWERED BY AI
            </div>
          </div>
        </div>

        {/* Category Filter */}
        {!compact && (
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
                  selectedCategory === category.id
                    ? "bg-dark-blue-dark text-white shadow-md"
                    : "bg-white text-dark-blue-dark hover:bg-dark-blue-light border border-dark-blue-normal"
                }`}
              >
                <i className={`fas ${category.icon} text-xs`}></i>
                <span>{category.name}</span>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Solutions */}
      <div className="p-4 md:p-6">
        <div
          className={`space-y-4 ${compact ? "max-h-96 overflow-y-auto" : ""}`}
        >
          {filteredSolutions.map((solution, index) => (
            <motion.div
              key={solution.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-lg border border-dark-blue-normal shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
            >
              <div className="p-4">
                <div className="flex items-start gap-3">
                  <div
                    className={`w-8 h-8 ${solution.bgColor} rounded-lg flex items-center justify-center shrink-0`}
                  >
                    <i
                      className={`fas ${solution.icon} ${solution.iconColor} text-sm`}
                    ></i>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-semibold text-dark-blue-dark">
                        {solution.title}
                      </h4>
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full border ${
                          impactColors[solution.impact]
                        }`}
                      >
                        {solution.impact} IMPACT
                      </span>
                      <span className="px-2 py-1 bg-dark-blue-light text-dark-blue-dark text-xs font-medium rounded-full">
                        {solution.estimatedImprovement}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">
                      {solution.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <button
                          className={`px-3 py-1.5 bg-linear-to-r from-dark-blue-dark to-dark-blue-darker text-white text-xs font-medium rounded-lg hover:from-dark-blue-darker hover:to-dark-blue-dark-active transition-all duration-200`}
                        >
                          {solution.actionText}
                        </button>
                        <button
                          onClick={() =>
                            setExpandedSolution(
                              expandedSolution === solution.id
                                ? null
                                : solution.id
                            )
                          }
                          className="px-3 py-1.5 border border-dark-blue-normal text-dark-blue-dark text-xs font-medium rounded-lg hover:bg-dark-blue-light transition-colors"
                        >
                          {expandedSolution === solution.id
                            ? "Less Info"
                            : "Learn More"}
                        </button>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <i className="fas fa-brain text-dark-blue-dark"></i>
                        <span>{solution.confidence}% confidence</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Expanded Details */}
              {expandedSolution === solution.id && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="border-t border-dark-blue-normal bg-dark-blue-light/30 p-4"
                >
                  <div className="space-y-3">
                    <div>
                      <h5 className="font-medium text-dark-blue-darker mb-1">
                        Why this works:
                      </h5>
                      <p className="text-sm text-dark-blue-dark">
                        Based on analysis of similar accounts and industry
                        benchmarks, this strategy has shown consistent results.
                      </p>
                    </div>
                    <div>
                      <h5 className="font-medium text-dark-blue-darker mb-1">
                        Implementation steps:
                      </h5>
                      <ul className="text-sm text-dark-blue-dark space-y-1">
                        <li className="flex items-center gap-2">
                          <i className="fas fa-check-circle text-dark-blue-normal text-xs"></i>
                          <span>Analyze your current performance metrics</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <i className="fas fa-check-circle text-dark-blue-normal text-xs"></i>
                          <span>
                            Implement the recommended changes gradually
                          </span>
                        </li>
                        <li className="flex items-center gap-2">
                          <i className="fas fa-check-circle text-dark-blue-normal text-xs"></i>
                          <span>Monitor results and adjust as needed</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </motion.div>
              )}
            </motion.div>
          ))}
        </div>

        {/* Quick Stats */}
        {!compact && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-6 bg-linear-to-r from-dark-blue-light/50 to-dark-blue-light/30 rounded-lg p-4 border border-dark-blue-normal"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-dark-blue-light rounded-lg flex items-center justify-center">
                  <i className="fas fa-chart-bar text-dark-blue-dark text-sm"></i>
                </div>
                <div>
                  <h4 className="font-medium text-dark-blue-darker">
                    Potential Impact
                  </h4>
                  <p className="text-sm text-dark-blue-dark">
                    If you implement all high-impact solutions
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-dark-blue-darker">
                  +127%
                </div>
                <div className="text-xs text-dark-blue-dark">
                  Goal Achievement
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};
