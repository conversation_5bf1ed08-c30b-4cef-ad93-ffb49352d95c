"use client";

import { motion } from 'framer-motion';

interface EmptyStateProps {
  onCreateGoal?: () => void;
}

export const EmptyState = ({ onCreateGoal }: EmptyStateProps) => {
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex flex-col items-center justify-center h-[30vh] w-full"
    >
      <motion.div 
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        className="text-[#1565c0] text-[3rem] mb-8"
      >
        <i className="fa-solid fa-bullseye"></i>
      </motion.div>
      
      <motion.p 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="text-center text-md text-black/80 mb-4"
      >
        You have not set any goals yet
      </motion.p>
      
      {onCreateGoal && (
        <motion.button
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onCreateGoal}
          className="text-[#1565c0] hover:bg-blue-50 transition-colors px-4 py-2 rounded-md border border-[#1565c0] hover:border-blue-600"
        >
          <i className="fas fa-plus mr-2"></i>
          Create your first goal
        </motion.button>
      )}
    </motion.div>
  );
};
