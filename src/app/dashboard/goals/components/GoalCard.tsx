"use client";

import type React from 'react';
import { useState, useCallback, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { GoalCardProps, goalCardVariants, progressBarVariants } from '../types';

const ContextMenu = ({ 
  x, 
  y, 
  onPin, 
  onClose, 
  isPinned, 
  onEdit, 
  onDelete 
}: { 
  x: number;
  y: number;
  onPin: () => void;
  onClose: () => void;
  isPinned: boolean;
  onEdit: () => void;
  onDelete: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.15 }}
      className="fixed bg-white shadow-lg rounded-md py-2 min-w-32 z-50"
      style={{ 
        top: y, 
        left: x,
        maxWidth: 'calc(100vw - 24px)',
        transform: `translateX(${x + 128 > window.innerWidth ? '-100%' : '0'})`
      }}
    >
      <motion.button 
        whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.05)' }}
        transition={{ duration: 0.15 }}
        className="w-full px-4 py-2 text-left flex items-center gap-2"
        onClick={() => {
          onPin();
          onClose();
        }}
      >
        <i className="fas fa-thumbtack"></i> {isPinned ? 'Unpin' : 'Pin'}
      </motion.button>
      <motion.button 
        whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.05)' }}
        transition={{ duration: 0.15 }}
        className="w-full px-4 py-2 text-left flex items-center gap-2"
        onClick={() => {
          onEdit();
          onClose();
        }}
      >
        <i className="fas fa-edit"></i> Edit
      </motion.button>
      <motion.button 
        whileHover={{ backgroundColor: 'rgba(239, 68, 68, 0.1)' }}
        transition={{ duration: 0.15 }}
        className="w-full px-4 py-2 text-left flex items-center gap-2 text-red-500"
        onClick={() => {
          onDelete();
          onClose();
        }}
      >
        <i className="fas fa-trash"></i> Delete
      </motion.button>
    </motion.div>
  );
};

export const GoalCard = ({ 
  goal, 
  onPin, 
  onClick, 
  onEdit, 
  onDelete,
  isLoading = false
}: GoalCardProps) => {
  const [showContext, setShowContext] = useState(false);
  const [contextPosition, setContextPosition] = useState({ x: 0, y: 0 });
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  const [isMobile] = useState(() => typeof window !== 'undefined' && window.innerWidth <= 768);
  const contextMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setShowContext(false);
      }
    };

    if (showContext) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showContext]);

  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    if (isMobile) return;
    e.preventDefault();
    setContextPosition({ x: e.pageX, y: e.pageY });
    setShowContext(true);
  }, [isMobile]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    if (!touch) return;

    const timer = setTimeout(() => {
      setContextPosition({ x: touch.pageX, y: touch.pageY });
      setShowContext(true);
    }, 500);
    setLongPressTimer(timer);
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  }, [longPressTimer]);

  const handleTouchMove = useCallback(() => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  }, [longPressTimer]);

  const progressPercentage = Math.round(goal.percentage * 100);

  return (
    <>
      <motion.div
        variants={goalCardVariants}
        initial="hidden"
        animate="visible"
        layout
        className={`bg-white rounded-lg p-4 mb-1 flex flex-col w-full relative cursor-pointer transition-shadow duration-200 hover:shadow-md ${
          isLoading ? 'opacity-50 pointer-events-none' : ''
        }`}
        onContextMenu={handleContextMenu}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onTouchMove={handleTouchMove}
        onClick={() => !showContext && !isLoading && onClick(goal)}
      >
        {goal.isPinned && (
          <motion.div 
            initial={{ opacity: 0, rotate: -45 }}
            animate={{ opacity: 1, rotate: -45 }}
            className="absolute left-6 md:left-5 top-1 text-blue-500"
          >
            <i className="fas fa-thumbtack" />
          </motion.div>
        )}
        
        <div className="flex items-center justify-start gap-2 -mb-7 w-full pl-0 md:pl-0">
          <motion.button
            whileHover={{ scale: 1.05, backgroundColor: 'rgba(0,0,0,0.05)' }}
            whileTap={{ scale: 0.95 }}
            aria-label="Open goal actions"
            type="button"
            className="p-2 px-4 rounded-lg text-gray-600 hover:text-gray-800 opacity-80 hover:opacity-100 relative z-20 pointer-events-auto"
            onPointerDown={(e: React.PointerEvent<HTMLButtonElement>) => {
              e.stopPropagation();
              e.preventDefault();
            }}
            onMouseDown={(e: React.MouseEvent<HTMLButtonElement>) => {
              e.stopPropagation();
              e.preventDefault();
            }}
            onMouseUp={(e: React.MouseEvent<HTMLButtonElement>) => {
              e.stopPropagation();
              e.preventDefault();
            }}
            onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
              e.stopPropagation();
              e.preventDefault();
              const rect = (e.currentTarget as HTMLButtonElement).getBoundingClientRect();
              setContextPosition({ x: rect.right + window.scrollX, y: rect.bottom + window.scrollY });
              setShowContext(!showContext);
            }}
            onTouchStart={(e: React.TouchEvent<HTMLButtonElement>) => {
              e.stopPropagation();
              e.preventDefault();
            }}
            onTouchEnd={(e: React.TouchEvent<HTMLButtonElement>) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            <i className="fa-solid fa-ellipsis-vertical" />
          </motion.button>
          <div className="flex items-center gap-2">
            <motion.i 
              className={`fa-solid fa-${goal.icon} text-lg text-[#2c3e50]`}
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.2 }}
            />
            <h3 className="font-semibold text-md">{goal.title}</h3>
          </div>
        </div>
        
        <div className="relative w-full">
          <div className="flex mb-2 items-center justify-end">
            <motion.div 
              className={`text-md font-semibold ${
                goal.status === "Completed" 
                  ? "text-green-600"
                  : "text-yellow-600"
              }`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {progressPercentage}%
            </motion.div>
          </div>
          
          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
            <motion.div 
              variants={progressBarVariants}
              initial="hidden"
              animate="visible"
              custom={progressPercentage}
              className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center ${
                goal.status === "Completed"
                  ? "bg-green-500" 
                  : "bg-yellow-500"
              }`}
            />
          </div>
        </div>

        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center rounded-lg"
          >
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
          </motion.div>
        )}
      </motion.div>
      
      {showContext && (
        <div ref={contextMenuRef}>
          <ContextMenu 
            x={contextPosition.x} 
            y={contextPosition.y}
            onPin={() => onPin(goal.id)}
            onClose={() => setShowContext(false)}
            isPinned={goal.isPinned ?? false}
            onEdit={() => onEdit(goal)}
            onDelete={() => onDelete(goal)}
          />
        </div>
      )}
    </>
  );
};
