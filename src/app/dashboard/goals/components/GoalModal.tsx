"use client";

import { useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { addDays, addWeeks, addMonths } from "date-fns";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  GoalModalProps,
  goalCreateSchema,
  GoalCreateFormData,
  modalVariants,
  mobileModalVariants,
} from "../types";
import { useUserStore } from "~/store/userStore";

// Import modular components
import { ModalHeader } from "./modal/ModalHeader";
import { ModalFooter } from "./modal/ModalFooter";
import { StepIndicator } from "./modal/StepIndicator";
import { GoalSettingsStep } from "./modal/steps/GoalSettingsStep";
import { ItemConfigurationStep } from "./modal/steps/ItemConfigurationStep";
import { PreviewStep } from "./modal/steps/PreviewStep";
import { useModalState } from "../hooks/useModalState";

export const GoalModal = ({
  isOpen,
  onClose,
  onSubmit,
  editMode = false,
  goalToEdit,
  isLoading = false,
}: GoalModalProps) => {
  // Use custom modal state hook
  const {
    currentStep,
    expandedSections,
    isMobile,
    handleNext,
    handleBack,
    toggleSection,
    resetModal,
  } = useModalState();

  // Access workspace and social selection for dynamic analytics key
  const { selectedWorkspace, selectedSocial } = useUserStore();

  // Limit floating-point precision and clamp to [0, 100]
  const roundPct = (value: number): number => {
    const n = Number.isFinite(value) ? value : 0;
    const r = Math.round(n * 100) / 100; // 2 decimals
    return Math.max(0, Math.min(100, r));
  };

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isValid },
  } = useForm<GoalCreateFormData>({
    resolver: zodResolver(goalCreateSchema),
    defaultValues: {
      name: "",
      icon: "clock",
      period: {
        number: 1,
        unit: "week",
      },
      duration: "1 week",
      end: "",
      targetConfig: {
        ageRanges: [],
        locationCountries: [],
        locationCities: [],
        genders: [],
        accountReach: { enabled: false },
        engagement: { enabled: false },
        followers: { enabled: false },
        impressions: { enabled: false },
        profileViews: { enabled: false },
        websiteClicks: { enabled: false },
      },
    },
  });

  const watchedValues = watch();

  // Initialize form data when editing
  useEffect(() => {
    if (editMode && goalToEdit) {
      setValue("name", goalToEdit.title || "");
      setValue("icon", goalToEdit.icon || "clock");

      if (goalToEdit.duration) {
        // Parse duration like "1 week" into number and unit
        const parts = goalToEdit.duration.split(" ");
        if (parts.length >= 2 && parts[0] && parts[1]) {
          const number = parseInt(parts[0]);
          const unit = parts[1].replace(/s$/, ""); // Remove plural 's'
          setValue("period.number", number);
          setValue("period.unit", unit as "day" | "week" | "month");
        }
      }
    }
  }, [editMode, goalToEdit, setValue]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      resetModal();
      if (!editMode) {
        reset();
      }
    }
  }, [isOpen, editMode, reset, resetModal]);

  // Hydrate demographics from analytics summary (no fallbacks)
  useEffect(() => {
    if (!isOpen) return;
    try {
      const raw = localStorage.getItem("analytics_summary");
      if (!raw) return; // do not use any fallback data

      const summary = JSON.parse(raw) as {
        age?: Record<string, number>;
        gender?: Record<string, number>;
        country?: Record<string, number>;
        city?: Record<string, number>;
      } | null;
      if (!summary) return;

      // Utility to sort entries by value desc
      const sortDesc = <T extends { percentage: number }>(arr: T[]) =>
        arr.sort((a, b) => (b.percentage || 0) - (a.percentage || 0));

      // Age ranges
      if (summary.age && typeof summary.age === "object") {
        const ages = Object.entries(summary.age).map(([k, v]) => ({
          range: k.replace(/\s*/g, " ").replace(/-/g, " - ").trim(),
          percentage: roundPct(Number(v) || 0),
          enabled: false,
        }));
        setValue("targetConfig.ageRanges", sortDesc(ages));
      }

      // Countries
      if (summary.country && typeof summary.country === "object") {
        const countries = Object.entries(summary.country).map(([k, v]) => ({
          name: k,
          percentage: roundPct(Number(v) || 0),
          enabled: false,
        }));
        setValue("targetConfig.locationCountries", sortDesc(countries));
      }

      // Cities
      if (summary.city && typeof summary.city === "object") {
        const cities = Object.entries(summary.city).map(([k, v]) => ({
          name: k,
          percentage: roundPct(Number(v) || 0),
          enabled: false,
        }));
        setValue("targetConfig.locationCities", sortDesc(cities));
      }

      // Gender (coerce to Male | Female | Other) - robustly handle 'M','F','U' and synonyms
      if (summary.gender && typeof summary.gender === "object") {
        const buckets: Record<"Male" | "Female" | "Other", number> = {
          Male: 0,
          Female: 0,
          Other: 0,
        };
        Object.entries(summary.gender).forEach(([k, v]) => {
          const key = String(k).trim().toLowerCase();
          const val = Number(v) || 0;
          const isMale = key === "m" || key === "male" || key === "man" || key === "men";
          const isFemale = key === "f" || key === "female" || key === "woman" || key === "women";
          const isOther =
            key === "u" ||
            key === "other" ||
            key === "unknown" ||
            key === "undisclosed" ||
            key === "non-binary" ||
            key === "nonbinary" ||
            key === "nb";
          if (isMale) buckets.Male += val;
          else if (isFemale) buckets.Female += val;
          else if (isOther) buckets.Other += val;
          else {
            // Fallback heuristics
            if (key.includes("female")) buckets.Female += val;
            else if (key.includes("male")) buckets.Male += val;
            else buckets.Other += val;
          }
        });
        const genders = Object.entries(buckets).map(([g, v]) => ({
          gender: g as "Male" | "Female" | "Other",
          percentage: roundPct(Number(v) || 0),
          enabled: false,
        }));
        setValue("targetConfig.genders", sortDesc(genders));
      }
    } catch (e) {
      // Intentionally ignore to avoid any fallback paths
      // console.warn("Failed to load analytics_summary for GoalModal", e);
    }
  }, [isOpen, setValue]);

  const calculateEndDate = (period: { number: number; unit: string }) => {
    const today = new Date();

    switch (period.unit.toLowerCase()) {
      case "day":
        return addDays(today, period.number);
      case "week":
        return addWeeks(today, period.number);
      case "month":
        return addMonths(today, period.number);
      default:
        return today;
    }
  };

  const handleFormSubmit = (data: GoalCreateFormData) => {
    const endDate = calculateEndDate(data.period);
    // Build analytics js_data by overriding the RAW analytics payload with target values in-place
    // Source key provided by analytics page
    const workspaceName = String(selectedWorkspace || "");
    const socialId = String(selectedSocial?.social_id || "");
    const RAW_KEY = `analytics_${workspaceName}_${socialId}`;
    let rawAnalytics: Record<string, any> | null = null;
    try {
      const raw = localStorage.getItem(RAW_KEY);
      if (raw) rawAnalytics = JSON.parse(raw);
    } catch {}

    // Helper: ensure nested structure exists
    const ensureMetric = (dataObj: any, key: string, dimension_keys?: string[]) => {
      if (!dataObj[key]) {
        dataObj[key] = { name: key.replace(/_.*/, ''), period: "lifetime" };
      }
      if (dimension_keys && !dataObj[key].total_value) {
        dataObj[key].total_value = { breakdowns: [{ dimension_keys, results: [] }] };
      }
      if (dimension_keys && (!Array.isArray(dataObj[key].total_value?.breakdowns) || !dataObj[key].total_value.breakdowns.length)) {
        dataObj[key].total_value = { breakdowns: [{ dimension_keys, results: [] }] };
      }
    };

    const tc = data?.targetConfig || {};

    // Construct maps from targets
    const ageMap: Record<string, number> = {};
    (tc.ageRanges || []).forEach((it: any) => {
      if (it?.range != null) ageMap[String(it.range)] = Number(it.percentage) || 0;
    });
    const genderMap: Record<string, number> = {};
    (tc.genders || []).forEach((it: any) => {
      if (!it?.gender) return;
      const key = String(it.gender).toLowerCase();
      const code = key.startsWith('m') ? 'M' : key.startsWith('f') ? 'F' : 'U';
      genderMap[code] = Number(it.percentage) || 0;
    });
    const countryMap: Record<string, number> = {};
    (tc.locationCountries || []).forEach((it: any) => {
      if (it?.name) countryMap[String(it.name)] = Number(it.percentage) || 0;
    });
    const cityMap: Record<string, number> = {};
    (tc.locationCities || []).forEach((it: any) => {
      if (it?.name) cityMap[String(it.name)] = Number(it.percentage) || 0;
    });

    // Build combined age x gender percentages by proportional product so totals remain ~100
    const buildAgeGenderResults = () => {
      const ages = Object.keys(ageMap);
      const genders = Object.keys(genderMap);
      const results: any[] = [];
      if (!ages.length || !genders.length) return results;
      // Compute products
      let total = 0;
      ages.forEach((a) => {
        genders.forEach((g) => {
          const aVal = Number(ageMap[a] ?? 0);
          const gVal = Number(genderMap[g] ?? 0);
          const v = Math.round((aVal * gVal) / 100);
          results.push({ dimension_values: [a, g], value: v });
          total += v;
        });
      });
      // Adjust rounding error to hit 100 by tweaking the max cell
      const targetTotal = 100;
      const delta = targetTotal - total;
      if (delta !== 0 && results.length > 0) {
        let idx = 0;
        for (let i = 1; i < results.length; i++) {
          if (results[i].value > results[idx].value) idx = i;
        }
        results[idx].value = Math.max(0, (Number(results[idx].value) || 0) + delta);
      }
      return results;
    };

    let js_data: Record<string, any> = {};
    if (rawAnalytics && typeof rawAnalytics === 'object') {
      // We will mutate a shallow clone to avoid accidental shared refs
      const base = JSON.parse(JSON.stringify(rawAnalytics));
      const dataNode = base?.data || base; // Some payloads store metrics directly at root

      // 1) follower_demographics age,gender
      const ageGenderKey = 'follower_demographics_age,gender';
      ensureMetric(dataNode, ageGenderKey, ["age", "gender"]);
      const ageGender = dataNode[ageGenderKey];
      if (!ageGender.total_value) ageGender.total_value = { breakdowns: [{ dimension_keys: ["age", "gender"], results: [] }] };
      const agBreakdowns = ageGender.total_value.breakdowns;
      if (Array.isArray(agBreakdowns) && agBreakdowns.length > 0) {
        agBreakdowns[0].dimension_keys = ["age", "gender"];
        agBreakdowns[0].results = buildAgeGenderResults();
      }

      // 2) follower_demographics_city
      const cityKey = 'follower_demographics_city';
      ensureMetric(dataNode, cityKey, ["city"]);
      const cityMetric = dataNode[cityKey];
      const cityResults = Object.keys(cityMap).map((city) => ({ dimension_values: [city], value: Number(cityMap[city]) || 0 }));
      if (!cityMetric.total_value) cityMetric.total_value = { breakdowns: [{ dimension_keys: ["city"], results: [] }] };
      if (Array.isArray(cityMetric.total_value.breakdowns) && cityMetric.total_value.breakdowns.length > 0) {
        cityMetric.total_value.breakdowns[0].dimension_keys = ["city"];
        cityMetric.total_value.breakdowns[0].results = cityResults;
      }

      // 3) follower_demographics_country
      const countryKey = 'follower_demographics_country';
      ensureMetric(dataNode, countryKey, ["country"]);
      const countryMetric = dataNode[countryKey];
      const countryResults = Object.keys(countryMap).map((c) => ({ dimension_values: [c], value: Number(countryMap[c]) || 0 }));
      if (!countryMetric.total_value) countryMetric.total_value = { breakdowns: [{ dimension_keys: ["country"], results: [] }] };
      if (Array.isArray(countryMetric.total_value.breakdowns) && countryMetric.total_value.breakdowns.length > 0) {
        countryMetric.total_value.breakdowns[0].dimension_keys = ["country"];
        countryMetric.total_value.breakdowns[0].results = countryResults;
      }

      // 4) reach target override if provided
      if (tc?.accountReach?.enabled && typeof tc.accountReach.target === 'number') {
        if (!dataNode.reach) dataNode.reach = { name: 'reach', period: 'day' };
        const reachTarget = Number(tc.accountReach.target) || 0;
        if (Array.isArray(dataNode.reach.values)) {
          if (dataNode.reach.values.length > 0) {
            dataNode.reach.values[0] = { ...(dataNode.reach.values[0] || {}), value: reachTarget };
          } else {
            dataNode.reach.values = [{ value: reachTarget }];
          }
        } else {
          if (!dataNode.reach.total_value) dataNode.reach.total_value = { value: 0 };
          dataNode.reach.total_value.value = reachTarget;
        }
      }

      js_data = base;
    } else {
      // Fallback: if RAW not available, attach only targeting summary so backend can still read it
      js_data = {
        targeting: {
          age: ageMap,
          gender: genderMap,
          country: countryMap,
          city: cityMap,
          ...(tc?.accountReach?.enabled && typeof tc.accountReach.target === 'number' ? { account_reach_target: Number(tc.accountReach.target) || 0 } : {}),
        },
      } as any;
    }

    const formattedData = {
      ...data,
      duration: `${data.period.number} ${data.period.unit}${
        data.period.number > 1 ? "s" : ""
      }`,
      end: endDate.toISOString().slice(0, 19).replace("T", " "),
      js_data,
    };
    onSubmit(formattedData);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/30 flex items-center justify-center z-9999 overflow-hidden overflow-x-hidden"
        onClick={(e: any) => e.target === e.currentTarget && onClose()}
        style={{
          backdropFilter: "blur(2px)",
          padding: isMobile ? "0.5rem" : "1rem",
        }}
        transition={{ duration: 0.3 }}
      >
        <motion.div
          variants={isMobile ? mobileModalVariants : modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className={`bg-white relative shadow-2xl flex flex-col border border-gray-100
            ${
              isMobile
                ? "w-full max-w-sm mx-auto rounded-3xl"
                : "w-[85%] max-w-4xl max-h-[90vh] rounded-2xl"
            }`}
          style={{
            height: isMobile ? "auto" : "auto",
            maxHeight: isMobile ? "92vh" : "90vh",
            boxShadow: isMobile
              ? "0 -10px 25px -5px rgba(0, 0, 0, 0.1), 0 -10px 10px -5px rgba(0, 0, 0, 0.04)"
              : "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)",
          }}
        >
          {/* Header */}
          <ModalHeader
            editMode={editMode}
            onClose={onClose}
            isLoading={isLoading}
            isMobile={isMobile}
          />

          {/* Content */}
          <div className="flex-1 overflow-y-auto overflow-x-hidden min-h-0">
            {isMobile ? (
              /* Mobile Layout - With Step Indicators */
              <>
                {/* Mobile Step Indicators */}
                <StepIndicator currentStep={currentStep} isMobile={true} />
                <div className="px-4 lg:px-6 py-4">
                  <AnimatePresence mode="wait">
                    {currentStep === 1 && (
                      <GoalSettingsStep
                        register={register}
                        errors={errors}
                        setValue={setValue}
                        watch={watch}
                        isLoading={isLoading}
                        isMobile={isMobile}
                      />
                    )}

                    {currentStep === 2 && (
                      <ItemConfigurationStep
                        setValue={setValue}
                        watch={watch}
                        expandedSections={expandedSections}
                        toggleSection={toggleSection}
                        isMobile={isMobile}
                      />
                    )}

                    {currentStep === 3 && (
                      <PreviewStep
                        watch={watch}
                        expandedSections={expandedSections}
                      />
                    )}
                  </AnimatePresence>
                </div>
              </>
            ) : (
              /* Desktop Layout - With Sidebar */
              <div className="flex h-full">
                {/* Desktop Sidebar */}
                <StepIndicator currentStep={currentStep} isMobile={false} />

                {/* Desktop Main Content */}
                <div className="flex-1 overflow-y-auto">
                  <div className="p-6 pt-4">
                    <AnimatePresence mode="wait">
                      {currentStep === 1 && (
                        <GoalSettingsStep
                          register={register}
                          errors={errors}
                          setValue={setValue}
                          watch={watch}
                          isLoading={isLoading}
                          isMobile={false}
                        />
                      )}

                      {currentStep === 2 && (
                        <ItemConfigurationStep
                          setValue={setValue}
                          watch={watch}
                          expandedSections={expandedSections}
                          toggleSection={toggleSection}
                          isMobile={false}
                        />
                      )}

                      {currentStep === 3 && (
                        <PreviewStep
                          watch={watch}
                          expandedSections={expandedSections}
                        />
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <ModalFooter
            currentStep={currentStep}
            isMobile={isMobile}
            isLoading={isLoading}
            isValid={isValid}
            editMode={editMode}
            watchedValues={watchedValues}
            onBack={handleBack}
            onNext={() => handleNext(watchedValues)}
            onSubmit={handleSubmit(handleFormSubmit)}
          />
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
