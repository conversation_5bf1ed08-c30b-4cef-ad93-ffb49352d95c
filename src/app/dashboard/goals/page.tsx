"use client";

import { useState, useCallback, useEffect } from "react";
import { useUserStore } from "~/store/userStore";
import { showErrorToast } from "~/components/toasts";
import AddSocialAccountModal from "~/components/addsocialaccountmodal";

// Import new components and hooks
import { GoalModal } from "./components/GoalModal";
import { GoalStats } from "./components/GoalStats";
import { AISolutionsModal } from "./components/AISolutionsModal";
import { GoalsLayout } from "./components/layout/GoalsLayout";
import { useGoals } from "./hooks/useGoals";
import { Goal, GoalCreateFormData } from "./types";
import { useGoalWebSocket } from "./hooks/useGoalWebSocket";

const Goals = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);
  const [showAddSocialModal, setShowAddSocialModal] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(null);
  const [showAISolutionsModal, setShowAISolutionsModal] = useState(false);

  const store = useUserStore();
  const { selectedWorkspaceDetails, selectedSocial } = useUserStore();
  const goals = (store.goals || []) as Goal[];
  const selectedWorkspace = store.selectedWorkspace as string;
  const setUser = store.setUser;

  // Use custom hooks
  const {
    isLoading: goalsLoading,
    createNewGoal,
    updateExistingGoal,
    deleteExistingGoal,
    toggleGoalPin,
    getSortedGoals,
  } = useGoals();

  const { sendGoalMessage, transformServerGoal } = useGoalWebSocket();

  // Handle goal submission (create/edit)
  const handleSubmitGoal = useCallback(
    async (goalData: GoalCreateFormData) => {
      try {
        let success = false;

        if (editingGoal) {
          // Handle edit
          success = await updateExistingGoal({
            goal_id: editingGoal.id,
            workspace_name: selectedWorkspace,
            name: goalData.name,
            icon: goalData.icon,
            end: goalData.end,
            duration: goalData.duration,
            is_active: true,
            is_pinned: editingGoal.isPinned,
          });
        } else {
          // Handle create
          success = await createNewGoal(goalData);
        }

        if (success) {
          setEditingGoal(null);
          setIsModalOpen(false);
        }
      } catch (error) {
        console.error("Error handling goal:", error);
        showErrorToast("Failed to handle goal");
      }
    },
    [editingGoal, selectedWorkspace, updateExistingGoal, createNewGoal]
  );

  // Handle goal deletion
  const handleDeleteGoal = useCallback(
    async (goal: Goal) => {
      const confirmed = window.confirm(
        `Are you sure you want to delete "${goal.title}"?`
      );
      if (!confirmed) return;

      await deleteExistingGoal(goal.id);
    },
    [deleteExistingGoal]
  );

  // Handle goal pin toggle
  const handlePin = useCallback(
    async (id: number) => {
      const goal = goals.find((g) => g.id === id);
      if (!goal) return;

      await toggleGoalPin(id, goal.isPinned ?? false);
    },
    [goals, toggleGoalPin]
  );

  // Handle goal click (show stats)
  const handleGoalClick = useCallback(
    async (goal: Goal) => {
      setSelectedGoal(goal);

      // Request full goal details over WebSocket
      try {
        if (!selectedWorkspace) return;
        sendGoalMessage(
          {
            job: "get_goal",
            goal_id: goal.id,
            workspace_name: selectedWorkspace,
          },
          (response: any) => {
            if (response?.goal) {
              const fullGoal = transformServerGoal(response.goal);
              setSelectedGoal(fullGoal);
            }
          },
          (err: string) => {
            console.error("get_goal error:", err);
          }
        );
      } catch (e) {
        console.error("Failed to send get_goal:", e);
      }
    },
    [selectedWorkspace, sendGoalMessage, transformServerGoal]
  );

  // Handle goal edit
  const handleEditGoal = useCallback((goal: Goal) => {
    setEditingGoal(goal);
    setIsModalOpen(true);
  }, []);

  // Get sorted goals
  const sortedGoals = getSortedGoals(goals);

  // Auto-select Instagram as default social account
  useEffect(() => {
    const workspaceDetails = selectedWorkspaceDetails as any;
    if (workspaceDetails?.social_accounts && !selectedSocial?.social_id) {
      const instagramAccount = workspaceDetails.social_accounts.find(
        (account: any) => account.platform === "instagram"
      );

      if (instagramAccount) {
        setUser({
          selectedSocial: {
            platform: instagramAccount.platform,
            social_id: instagramAccount.social_id,
            social_name: instagramAccount.social_name,
            username:
              instagramAccount.username || instagramAccount.social_name || "",
          },
        });
      }
    }
  }, [selectedWorkspaceDetails, selectedSocial, setUser]);

  return (
    <>
      <GoalsLayout
        goals={sortedGoals}
        socialAccounts={
          (selectedWorkspaceDetails as any)?.social_accounts || []
        }
        selectedSocial={selectedSocial}
        isLoading={goalsLoading}
        onSelectSocial={(social) => setUser({ selectedSocial: social })}
        onAddSocial={() => setShowAddSocialModal(true)}
        onCreateGoal={() => setIsModalOpen(true)}
        onGoalClick={handleGoalClick}
        onEditGoal={handleEditGoal}
        onDeleteGoal={handleDeleteGoal}
        onPinGoal={handlePin}
        onViewAllSolutions={() => setShowAISolutionsModal(true)}
      />

      {/* Goal Modal */}
      <GoalModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingGoal(null);
        }}
        onSubmit={handleSubmitGoal}
        editMode={!!editingGoal}
        goalToEdit={editingGoal || undefined}
        isLoading={goalsLoading}
      />

      {/* Goal Stats Modal */}
      <GoalStats
        isOpen={!!selectedGoal}
        onClose={() => setSelectedGoal(null)}
        goal={selectedGoal || undefined}
      />

      {/* AI Solutions Modal */}
      <AISolutionsModal
        isOpen={showAISolutionsModal}
        onClose={() => setShowAISolutionsModal(false)}
        goalId={selectedGoal?.id}
      />

      {/* Add Social Account Modal */}
      {showAddSocialModal && (
        <AddSocialAccountModal
          onClose={() => setShowAddSocialModal(false)}
          dim={true}
          onAddAccount={(platform) => {
            console.log(`Adding ${platform} account`);
            setShowAddSocialModal(false);
          }}
        />
      )}
    </>
  );
};

export default Goals;
