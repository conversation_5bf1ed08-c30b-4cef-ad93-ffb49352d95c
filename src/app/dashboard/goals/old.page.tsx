"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import OldGoalModal from './old.GoalModal';
import { useWebSocket } from '~/hooks/useWebSocket';
import { useUserStore } from '~/store/userStore';
import { showErrorToast, showSuccessToast } from "~/components/toasts";
import AddSocialAccountModal from "~/components/addsocialaccountmodal";

interface Goal {
  id: number;
  title: string;
  description: string;
  percentage: number;
  status: string;
  icon: string;
  isPinned?: boolean;
}

interface ServerGoal {
  id: number;
  name: string;
  end: string;
  progress: number;
  icon: string;
  is_pinned: boolean;
  is_active: boolean;
  duration: string
}

interface UserState {
  goals: Goal[];
  selectedGoal: Goal | null;
  selectedWorkspace: string;
  selectedSocial: { social_id: string } | null;
  setUser: (update: Partial<UserState>) => void;
}

interface AgeRangeData {
  range: string;
  percentage: number;
  change: {
    value: number;
    type: 'increase' | 'decrease' | 'same';
  };
}

interface GoalStatsModalProps {
  isOpen: boolean;
  onClose: () => void;
  goalTitle?: string;
  goalIcon?: string;
}

const ContextMenu = ({ x, y, onPin, onClose, isPinned, onEdit, onDelete }: { 
  x: number, 
  y: number, 
  onPin: () => void, 
  onClose: () => void, 
  isPinned: boolean,
  onEdit: () => void,
  onDelete: () => void 
}) => {
  return (
    <div 
      className="fixed bg-white shadow-lg rounded-md py-2 min-w-32 z-50"
      style={{ 
        top: y, 
        left: x,
        maxWidth: 'calc(100vw - 24px)',
        transform: `translateX(${x + 128 > window.innerWidth ? '-100%' : '0'})`
      }}
    >
      <button 
        className="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center gap-2"
        onClick={() => {
          onPin();
          onClose();
        }}
      >
        <i className="fas fa-thumbtack"></i> {isPinned ? 'Unpin' : 'Pin'}
      </button>
      <button 
        className="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center gap-2"
        onClick={() => {
          onEdit();
          onClose();
        }}
      >
        <i className="fas fa-edit"></i> Edit
      </button>
      <button 
        className="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center gap-2 text-red-500"
        onClick={() => {
          onDelete();
          onClose();
        }}
      >
        <i className="fas fa-trash"></i> Delete
      </button>
    </div>
  );
};

const GoalCard = ({ goal, onPin, onClick, onEdit, onDelete }: { 
  goal: Goal; 
  onPin: (id: number) => void; 
  onClick: (goal: Goal) => void;
  onEdit: (goal: Goal) => void;
  onDelete: (goal: Goal) => void;
}) => {
  const [showContext, setShowContext] = useState(false);
  const [contextPosition, setContextPosition] = useState({ x: 0, y: 0 });
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  const [isMobile] = useState(() => window.innerWidth <= 768);
  const contextMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setShowContext(false);
      }
    };

    if (showContext) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showContext]);

  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    if (isMobile) return;
    e.preventDefault();
    setContextPosition({ x: e.pageX, y: e.pageY });
    setShowContext(true);
  }, [isMobile]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    if (!touch) return;

    const timer = setTimeout(() => {
      setContextPosition({ x: touch.pageX, y: touch.pageY });
      setShowContext(true);
    }, 500);
    setLongPressTimer(timer);
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  }, [longPressTimer]);

  const handleTouchMove = useCallback(() => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  }, [longPressTimer]);

  return (
    <>
      <div 
        className={`bg-white rounded-lg p-4 mb-1 flex flex-col w-full relative cursor-pointer ${
          goal.isPinned ? '' : ''
        }`}
        onContextMenu={handleContextMenu}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onTouchMove={handleTouchMove}
        onClick={() => !showContext && onClick(goal)}
      >
        {goal.isPinned && (
          <div className="absolute md:-left-3 left-[0.1rem] top-4 transform -rotate-45 text-blue-500">
            <i className="fas fa-thumbtack" />
          </div>
        )}
        <div className="flex items-center justify-between -mb-7 w-2/3 pl-0 md:pl-0">
          <div className="flex items-center gap-2">
            <i className={`fa-solid fa-${goal.icon} text-lg text-[#2c3e50]`}></i>
            <h3 className="font-semibold text-md">{goal.title}</h3>
          </div>
        </div>
        
        <div className="relative w-full ">
          <div className="flex mb-2 items-center justify-end">
            <div className={`text-md font-semibold ${
              goal.status === "Completed" 
                ? "text-green-600"
                : "text-yellow-600"
            }`}>
              {Math.round(goal.percentage * 100)}%
            </div>
          </div>
          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
            <div 
              style={{ width: `${goal.percentage * 100}%` }}
              className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center ${
                goal.status === "Completed"
                  ? "bg-green-500" 
                  : "bg-yellow-500"
              }`}
            />
          </div>
        </div>
      </div>
      {showContext && (
        <div ref={contextMenuRef}>
          <ContextMenu 
            x={contextPosition.x} 
            y={contextPosition.y}
            onPin={() => onPin(goal.id)}
            onClose={() => setShowContext(false)}
            isPinned={goal.isPinned ?? false}
            onEdit={() => onEdit(goal)}
            onDelete={() => onDelete(goal)}
          />
        </div>
      )}
    </>
  );
};

const EmptyGoalsState = () => {
  return (
    <div className="flex flex-col items-center justify-center h-[30vh] w-full">
      <div className="text-[#1565c0] text-[3rem] mb-8">
        <i className="fa-solid fa-bullseye"></i>
      </div>
      <p className="text-center text-md text-black/80">
        You have not set any goals yet
      </p>
    </div>
  );
};

const WelcomeModal = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      <div className="relative bg-white rounded-2xl flex flex-col justify-between p-4 items-center md:p-4 shadow-lg md:w-150 w-dvw h-dvh md:h-auto">
        {/* Header */}
        <div className="flex justify-between items-center w-full p-2">
        <div className="flex items-center gap-2">
          <h2 className="text-2xl">Goals</h2>
          <div className="text-base">next 30 days</div>
        </div>
          <button onClick={onClose} className="text-xl">
            <i className="fas fa-times" />
          </button>
        </div>

        <div className="p-8">
          
          
          {/* Probability Section */}
          <div className="text-center mt-4">
            <div className="text-4xl text-[#ffc600] font-semibold">65%</div>
            <div className="mt-4">Probability of reaching the goal</div>
          </div>

          {/* AI Help Section */}
          <div className="flex items-center gap-2 justify-center mt-4">
            <i className="fas fa-wand-magic-sparkles text-[#0a66c2]" />
            <span className="text-[#2c3e50] font-medium">How can I increase the probability?</span>
         
            
          </div>

          <div className="my-6 w-full flex justify-center">
            <img src="/Performance/line.svg" alt="Performance trend line" className="w-[60%] h-auto" />
          </div>

          {/* Stats Section */}
          <div className="flex justify-between mt-16 gap-4">
            <div className="text-center">
              <div className="text-2xl font-medium text-[#2c3e50]">834</div>
              <div className="mt-2">Accounts reached</div>
            </div>
            <div className="text-center border-l border-r border-[#cccccc] px-8">
              <div className="text-2xl font-medium text-[#2c3e50]">
                <span>280</span>
                <span className="text-xl">/1250</span>
              </div>
              <div className="mt-2">Accounts engaged</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-medium text-[#2c3e50]">15</div>
              <div className="mt-2">New followers</div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-center mt-8">
          <button
            onClick={onClose}
            className="bg-[#2c3e50] text-white rounded-full py-2 px-8"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
};

const GoalStatsModal = ({ isOpen, onClose, goalTitle = 'My goal', goalIcon = 'fa-bullseye' }: GoalStatsModalProps) => {
  if (!isOpen) return null;

  const ageData: AgeRangeData[] = [
    { range: '13 - 17', percentage: 7.2, change: { value: 30, type: 'increase' } },
    { range: '35 - 44', percentage: 26.1, change: { value: 10, type: 'decrease' } }, 
    { range: '18 - 24', percentage: 23.8, change: { value: 0, type: 'same' } },
    { range: '25 - 34', percentage: 27.2, change: { value: 0, type: 'same' } },
    { range: '45 - 54', percentage: 11.1, change: { value: 40.2, type: 'increase' } },
    { range: '55 - 64', percentage: 2.2, change: { value: 0, type: 'same' } },
    { range: '65+', percentage: 2.2, change: { value: 0, type: 'same' } },
  ];

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      <div className="relative bg-white rounded-2xl w-full md:w-184 max-h-[90vh] overflow-y-auto p-4 md:p-0 mx-4 md:mx-0">
        {/* Header */}
        <div className="sticky top-0 bg-white p-4 border-b border-[#cccccc]">
          <div className="flex justify-between items-center">
            <h2 className="text-base md:text-lg text-[#2c3e50] font-semibold text-center flex-1">
              <i className={`fas ${goalIcon} mr-2 md:mr-3`} />
              {goalTitle}
            </h2>
            <button onClick={onClose} className="text-xl">
              <i className="fas fa-times" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-3 md:p-5 border rounded-lg border-[#cccccc] m-2 md:m-4">
          <div className="mb-3 md:mb-4">
            <h3 className="text-sm md:text-base font-medium">Age range</h3>
          </div>

          <div className="space-y-2 md:space-y-3">
            {ageData.map((data) => (
              <div key={data.range} className="flex items-center gap-2 md:gap-3 text-sm md:text-base">
                <div className="w-14 md:w-18">{data.range}</div>
                <div className="flex-1 flex items-center gap-2 md:gap-3">
                  <div className="flex-1 h-4 md:h-5 bg-gray-100 rounded-lg relative">
                    {/* Initial range bar */}
                    <div 
                      className="h-full rounded-lg bg-blue-500"
                      style={{ width: `${data.percentage}%` }}
                    />
                    {/* Difference bar */}
                    {data.change.type !== 'same' && (
                      <div 
                        className={`h-full rounded-lg absolute top-0 left-[${data.percentage}%] ${
                          data.change.type === 'increase' ? 'bg-green-400' : 'bg-red-400'
                        }`}
                        style={{ 
                          width: `${data.change.value}%`,
                          opacity: 0.65
                        }}
                      />
                    )}
                  </div>
                  <div className="w-10 md:w-12 text-right">{data.percentage}%</div>
                  <div className={`w-14 md:w-16 text-right ${
                    data.change.type === 'increase' ? 'text-green-500' :
                    data.change.type === 'decrease' ? 'text-red-500' :
                    'text-gray-700'
                  }`}>
                    {data.change.type === 'same' ? '~same' :
                     `${data.change.type === 'increase' ? '+' : '-'}${data.change.value}%`}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-center p-4 md:p-6">
          <button
            onClick={onClose}
            className="bg-[#2c3e50] text-white rounded-full py-1.5 md:py-2 px-6 md:px-8 text-sm md:text-base"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
};

const Goals = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingGoal, setEditingGoal] = useState<Goal | null>(null);
    const { createGoal, getGoals, updateGoal, deleteGoal } = useWebSocket();
    const store = useUserStore();
    const { selectedWorkspaceDetails } = useUserStore();
    const goals = (store.goals || []) as Goal[];
    const [showAddSocialModal, setShowAddSocialModal] = useState(false);
    const selectedGoal = store.selectedGoal as Goal | null;
    const selectedWorkspace = store.selectedWorkspace as string;
    const selectedSocial = store.selectedSocial as { social_id: string } | null;
    const setUser = store.setUser as UserState['setUser'];
    
    const [showWelcomeModal, setShowWelcomeModal] = useState(true);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchGoals = async () => {
            if (!selectedWorkspace) return;
            
            try {
                const response = await getGoals({
                    workspace_name: selectedWorkspace,
                    is_active: true
                });
                
                if (response && Array.isArray(response.goals)) {
                    const transformedGoals = response.goals.map((goal: ServerGoal) => ({
                        id: goal.id,
                        title: goal.name,
                        description: goal.end,
                        percentage: goal.progress / 100,
                        status: goal.progress === 100 ? "Completed" : "In Progress",
                        icon: goal.icon || "fa-bullseye",
                        isPinned: goal.is_pinned,
                        duration: goal.duration
                    }));
                    
                    setUser({ goals: transformedGoals });
                }
            } catch (error) {
                console.error("Error fetching goals:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchGoals();
    }, [selectedWorkspace, getGoals, setUser]);

    const handleDeleteGoal = async (goalId: number) => {
        if (!selectedWorkspace) return;

        try {
            await deleteGoal({
                goal_id: goalId,
                workspace_name: selectedWorkspace
            }).then((response) => {
                console.log("Response from delete goal:", response);
                if (response.success) {
                    showSuccessToast(response.message);
                    const updatedGoals = goals.filter(g => g.id !== goalId);
                    setUser({ goals: updatedGoals });
                } else {
                    showErrorToast(response.message);
                }
            });
            
            
        } catch (error) {
            console.error("Error deleting goal:", error);
        }
    };

    const handleSubmitGoal = async (goalData: { title: string; period: string; icon: string; endDate: string, duration: string }) => {
        if (!selectedWorkspace) return;

        try {
            if (editingGoal) {
                // Handle edit
                await updateGoal({
                    goal_id: editingGoal.id,
                    workspace_name: selectedWorkspace,
                    name: goalData.title,
                    icon: goalData.icon,
                    end: goalData.endDate,
                    duration: goalData.duration,
                    is_active: true,
                    is_pinned: editingGoal.isPinned
                }).then((response) => {
                    if (response.success) {
                        showSuccessToast(response.message);
                        // Update local state
                        const updatedGoals = goals.map(g => 
                            g.id === editingGoal.id 
                                ? {
                                    ...g,
                                    title: goalData.title,
                                    icon: goalData.icon,
                                    description: goalData.endDate
                                  }
                                : g
                        );
                        setUser({ goals: updatedGoals });
                    } else {
                        showErrorToast(response.message);
                    }
                });
            } else {
                // Handle create
                await createGoal({
                    workspace_name: selectedWorkspace,
                    name: goalData.title,
                    icon: goalData.icon,
                    end: goalData.endDate,
                    duration: goalData.duration,
                    js_data: {},
                    social: selectedSocial ? [selectedSocial.social_id] : []
                }).then((response) => {
                    if (response?.success) {
                        showSuccessToast(response?.message);
                    } else {
                        showErrorToast(response?.message);
                    }
                });

                // Refresh goals after adding
                const response = await getGoals({
                    workspace_name: selectedWorkspace,
                    is_active: true
                });

                if (response && Array.isArray(response.goals)) {
                    const transformedGoals = response.goals.map((goal: ServerGoal) => ({
                        id: goal.id,
                        title: goal.name,
                        description: goal.end,
                        percentage: goal.progress / 100,
                        status: goal.progress === 100 ? "Completed" : "In Progress",
                        icon: goal.icon || "fa-bullseye",
                        isPinned: goal.is_pinned
                    }));
                    
                    setUser({ goals: transformedGoals });
                }
            }
        } catch (error) {
            console.error("Error handling goal:", error);
            showErrorToast("Failed to handle goal");
        }

        setEditingGoal(null);
        setIsModalOpen(false);
    };

    const handlePin = useCallback(async (id: number) => {
        const goal = goals.find(g => g.id === id);
        if (!goal || !selectedWorkspace) return;

        try {
            // Update the pin status on the server
            const response = await updateGoal({
                goal_id: id,
                workspace_name: selectedWorkspace,
                name: goal.title,
                
                icon: goal.icon,
                is_active: true,
                is_pinned: !goal.isPinned
            });

            if (response.success) {
                showSuccessToast(response.message);
                // Update local state only after successful server update
                const updatedGoals = goals.map(g => 
                    g.id === id ? { ...g, isPinned: !goal.isPinned } : g
                );
                setUser({ goals: updatedGoals });
            } else {
                showErrorToast(response.message || 'Failed to update pin status');
            }
        } catch (error) {
            console.error("Error updating goal pin status:", error);
            showErrorToast("Failed to update goal pin status");
        }
    }, [goals, selectedWorkspace, updateGoal, setUser]);

    const handleGoalClick = (goal: Goal) => {
        setUser({ selectedGoal: goal });
    };

    const sortedGoals = [...goals].sort((a, b) => {
        if (a.isPinned === b.isPinned) return 0;
        return a.isPinned ? -1 : 1;
    });

    if (isLoading) {
        return <div className="flex justify-center items-center h-[40vh]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>;
    }

    return (
        <>
            <WelcomeModal 
                isOpen={showWelcomeModal} 
                onClose={() => setShowWelcomeModal(false)} 
            />
            
            <GoalStatsModal 
                isOpen={!!selectedGoal}
                onClose={() => setUser({ selectedGoal: null })}
                goalTitle={selectedGoal?.title}
                goalIcon={selectedGoal?.icon}
            />
            
            <div className="flex flex-col mt-20 md:mt-0 md:flex-row justify-center items-start gap-4 md:gap-[1.6rem] p-4 md:p-4">
            <div className="hidden md:flex w-16 bg-gray-100  p-12 flex-col items-center">
            {selectedWorkspaceDetails?.social_accounts.map((account) => (
                <div key={account.platform} className="flex flex-col items-center mb-4">
                    <button 
                    className="b-1 rounded-full p-2 transition-colors duration-200"
                    onClick={() => setUser({selectedSocial: {platform:account.platform, social_id: account.social_id, social_name: account.social_name}})}
                    >
                    <img 
                        src={selectedSocial.platform === account.platform 
                        ? `/icons/performance/${account.platform}-on.svg` 
                        : `/icons/performance/${account.platform}-off.svg`}
                        alt={account.platform.charAt(0).toUpperCase() + account.platform.slice(1)} 
                        className="w-8 h-8 transition-all duration-200"
                    />
                    </button>
                    <span className="text-xs text-center">
                    {account.platform.charAt(0).toUpperCase() + account.platform.slice(1)}
                    </span>
                    </div>
                ))}
                <div className="flex flex-col items-center mb-4">
                    <button 
                        className="mb-1 bg-gray-200 rounded-full p-2 hover:bg-gray-300 transition-colors duration-200"
                        onClick={() => setShowAddSocialModal(true)}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                    </button>
                    <span className="text-xs text-center">Add More</span>
                </div>
            </div>
                <div className="w-full md:w-[65%] relative bg-white flex flex-col h-full rounded-2xl min-h-[40vh]">
                    <div className="px-4 md:px-[1.6rem] py-[1.6rem] h-[10vh]">
                        <h1 className="text-md">Goals</h1>
                    </div>
                    <div className="px-4 md:px-[1.6rem] relative overflow-y-auto h-[30vh]">
                        {sortedGoals.length > 0 ? (
                            sortedGoals.map((goal) => (
                                <GoalCard 
                                    key={goal.id} 
                                    goal={goal} 
                                    onPin={handlePin}
                                    onClick={handleGoalClick}
                                    onEdit={(goal) => {
                                        setEditingGoal(goal);
                                        setIsModalOpen(true);
                                    }}
                                    onDelete={(goal) => handleDeleteGoal(goal.id)}
                                />
                            ))
                        ) : (
                            <EmptyGoalsState />
                        )}
                    </div>
                    <div className="flex w-full justify-center md:justify-end h-[10vh]">
                        <div className="p-4 md:p-[1.6rem]">
                            <button 
                                onClick={() => setIsModalOpen(true)}
                                className="text-[#1565c0] hover:bg-blue-50 transition-colors px-3 py-1.5 rounded-md"
                            >
                                + Add a goal
                            </button>
                        </div>
                    </div>
                </div>
                <div className="w-full md:w-[30%] bg-white rounded-2xl h-[40vh] relative mt-4 md:mt-0">
                    <div className="p-4">
                        <h2 className="text-[#9c9c9c] text-md">AI solutions</h2>
                    </div>
                    <div className="absolute top-[50%] left-[50%] transform -translate-x-[50%] -translate-y-[50%]">
                        <p className="text-[#9c9c9c] text-md text-center">coming soon</p>
                    </div>
                </div>
            </div>

            <GoalModal 
                isOpen={isModalOpen}
                onClose={() => {
                    setIsModalOpen(false);
                    setEditingGoal(null);
                }}
                onSubmit={handleSubmitGoal}
                editMode={!!editingGoal}
                goalToEdit={editingGoal || undefined}
            />
            {showAddSocialModal && (
            <AddSocialAccountModal
                onClose={() => setShowAddSocialModal(false)}
                dim={true}
                onAddAccount={(platform) => {
                    console.log(`Adding ${platform} account`);
                    setShowAddSocialModal(false);
                }}
            />
            )}
        </>
    );
};

export default Goals;