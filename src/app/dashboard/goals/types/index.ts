import { z } from "zod";

// Base Goal Interface
export interface Goal {
  id: number;
  title: string;
  description: string;
  percentage: number;
  status: "In Progress" | "Completed" | "Paused";
  icon: string;
  isPinned?: boolean;
  duration?: string;
  startDate?: string;
  endDate?: string;
  socialMedia?: SocialMedia[];
  metrics?: GoalMetrics;
  probability?: number;
}

// Server Goal Response Interface
export interface ServerGoal {
  id: number;
  name: string;
  end: string;
  start?: string;
  progress: number;
  icon: string;
  is_pinned: boolean;
  is_active: boolean;
  duration: string;
  js_data?: Record<string, any>;
  social?: string[];
}

// Social Media Interface
export interface SocialMedia {
  platform: "instagram" | "facebook" | "twitter" | "linkedin";
  social_id: string;
  social_name?: string;
  username?: string;
  profile_photo?: string;
}

// Instagram Insights Data
export interface InstagramInsights {
  follower_count: number;
  following_count: number;
  media_count: number;
  reach: {
    value: number;
    previous_period?: number;
    change_percentage?: number;
  };
  impressions: {
    value: number;
    previous_period?: number;
    change_percentage?: number;
  };
  accounts_engaged: {
    value: number;
    target?: number;
    previous_period?: number;
    change_percentage?: number;
  };
  profile_views: {
    value: number;
    previous_period?: number;
    change_percentage?: number;
  };
  website_clicks: {
    value: number;
    previous_period?: number;
    change_percentage?: number;
  };
}

// Goal Metrics Interface
export interface GoalMetrics {
  reach?: {
    current: number;
    target: number;
    organic: number;
    paid: number;
  };
  engagement?: {
    likes: number;
    comments: number;
    shares: number;
    saves: number;
  };
  followers?: {
    current: number;
    target: number;
    growth_rate: number;
  };
  demographics?: {
    age_ranges: AgeRangeData[];
    locations: LocationData[];
    gender: GenderData[];
  };
}

// Demographic Data Interfaces
export interface AgeRangeData {
  range: string;
  percentage: number;
  change?: {
    value: number;
    type: "increase" | "decrease" | "same";
  };
}

export interface LocationData {
  name: string;
  percentage: number;
  change?: {
    value: number;
    type: "increase" | "decrease" | "same";
  };
}

export interface GenderData {
  gender: "Male" | "Female" | "Other";
  percentage: number;
  change?: {
    value: number;
    type: "increase" | "decrease" | "same";
  };
}

// API Response Interfaces
export interface GoalsResponse {
  success: boolean;
  message?: string;
  goals?: ServerGoal[];
  goal?: ServerGoal;
}

export interface GoalProbabilityResponse {
  success: boolean;
  message?: string;
  probability?: number;
  factors?: {
    current_progress: number;
    time_remaining: number;
    historical_performance: number;
    engagement_trend: number;
  };
}

// WebSocket Job Interfaces
export interface CreateGoalJob {
  job: "create_goal";
  workspace_name: string;
  name: string;
  icon: string;
  duration: string;
  start: string;
  end: string;
  is_active?: boolean;
  js_data?: Record<string, any>;
  social?: string[];
}

export interface UpdateGoalJob {
  job: "update_goal";
  goal_id: number;
  workspace_name: string;
  name?: string;
  icon?: string;
  duration?: string;
  start?: string;
  end?: string;
  is_active?: boolean;
  is_pinned?: boolean;
  js_data?: Record<string, any>;
  social?: string[];
}

export interface DeleteGoalJob {
  job: "delete_goal";
  goal_id: number;
  workspace_name: string;
}

export interface GetGoalsJob {
  job: "get_goals";
  workspace_name: string;
  social: number;
  is_active?: boolean | null;
}

export interface GetGoalJob {
  job: "get_goal";
  goal_id: number;
  workspace_name: string;
}

export interface GetGoalProbabilityJob {
  job: "get_goal_probability";
  goal_id: number;
  workspace_name: string;
}

// Target Configuration Interfaces
export interface AgeRangeTarget {
  range: string;
  percentage: number;
  enabled: boolean;
}

export interface LocationTarget {
  name: string;
  percentage: number;
  enabled: boolean;
}

export interface GenderTarget {
  gender: "Male" | "Female" | "Other";
  percentage: number;
  enabled: boolean;
}

export interface GoalTargetConfig {
  ageRanges?: AgeRangeTarget[];
  locationCountries?: LocationTarget[];
  locationCities?: LocationTarget[];
  genders?: GenderTarget[];
  accountReach?: {
    enabled: boolean;
    target?: number;
  };
  engagement?: {
    enabled: boolean;
    target?: number;
  };
  followers?: {
    enabled: boolean;
    target?: number;
  };
  impressions?: {
    enabled: boolean;
    target?: number;
  };
  profileViews?: {
    enabled: boolean;
    target?: number;
  };
  websiteClicks?: {
    enabled: boolean;
    target?: number;
  };
}

// Zod Validation Schemas
export const goalCreateSchema = z.object({
  name: z
    .string()
    .min(1, "Goal name is required")
    .max(100, "Goal name too long"),
  icon: z.string().min(1, "Icon is required"),
  period: z.object({
    number: z
      .number()
      .min(1, "Period number must be at least 1")
      .max(12, "Period number too large"),
    unit: z.enum(["day", "week", "month"], {
      required_error: "Period unit is required",
    }),
  }),
  duration: z.string().optional(),
  end: z.string().optional(),
  targetConfig: z
    .object({
      ageRanges: z
        .array(
          z.object({
            range: z.string(),
            percentage: z.number(),
            enabled: z.boolean(),
          })
        )
        .optional(),
      locationCountries: z
        .array(
          z.object({
            name: z.string(),
            percentage: z.number(),
            enabled: z.boolean(),
          })
        )
        .optional(),
      locationCities: z
        .array(
          z.object({
            name: z.string(),
            percentage: z.number(),
            enabled: z.boolean(),
          })
        )
        .optional(),
      genders: z
        .array(
          z.object({
            gender: z.enum(["Male", "Female", "Other"]),
            percentage: z.number(),
            enabled: z.boolean(),
          })
        )
        .optional(),
      accountReach: z
        .object({
          enabled: z.boolean(),
          target: z.number().optional(),
        })
        .optional(),
      engagement: z
        .object({
          enabled: z.boolean(),
          target: z.number().optional(),
        })
        .optional(),
      followers: z
        .object({
          enabled: z.boolean(),
          target: z.number().optional(),
        })
        .optional(),
      impressions: z
        .object({
          enabled: z.boolean(),
          target: z.number().optional(),
        })
        .optional(),
      profileViews: z
        .object({
          enabled: z.boolean(),
          target: z.number().optional(),
        })
        .optional(),
      websiteClicks: z
        .object({
          enabled: z.boolean(),
          target: z.number().optional(),
        })
        .optional(),
    })
    .optional(),
  workspace_name: z.string().optional(),
  social: z.array(z.string()).optional(),
  js_data: z.record(z.any()).optional(),
});

export const goalUpdateSchema = z.object({
  goal_id: z.number().positive("Valid goal ID required"),
  workspace_name: z.string().min(1, "Workspace is required"),
  name: z
    .string()
    .min(1, "Goal name is required")
    .max(100, "Goal name too long")
    .optional(),
  icon: z.string().optional(),
  duration: z.string().optional(),
  end: z.string().optional(),
  is_active: z.boolean().optional(),
  is_pinned: z.boolean().optional(),
  social: z.array(z.string()).optional(),
  js_data: z.record(z.any()).optional(),
});

export const goalFiltersSchema = z.object({
  status: z.enum(["all", "active", "completed", "paused"]).optional(),
  sortBy: z.enum(["newest", "oldest", "progress", "name"]).optional(),
  showPinned: z.boolean().optional(),
});

// Form Data Types
export type GoalCreateFormData = z.infer<typeof goalCreateSchema>;
export type GoalUpdateFormData = z.infer<typeof goalUpdateSchema>;
export type GoalFiltersFormData = z.infer<typeof goalFiltersSchema>;

// Component Props Interfaces
export interface GoalCardProps {
  goal: Goal;
  onPin: (id: number) => void;
  onClick: (goal: Goal) => void;
  onEdit: (goal: Goal) => void;
  onDelete: (goal: Goal) => void;
  isLoading?: boolean;
}

export interface GoalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (goalData: GoalCreateFormData) => void;
  editMode?: boolean;
  goalToEdit?: Goal;
  isLoading?: boolean;
}

export interface GoalStatsModalProps {
  isOpen: boolean;
  onClose: () => void;
  goal?: Goal;
  insights?: InstagramInsights;
}

// State Management Interfaces
export interface GoalsState {
  goals: Goal[];
  selectedGoal: Goal | null;
  isLoading: boolean;
  error: string | null;
  filters: GoalFiltersFormData;
  lastUpdated: string | null;
}

// Animation Variants for Framer Motion
export const goalCardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
  hover: { scale: 1.02, transition: { duration: 0.2 } },
  tap: { scale: 0.98 },
};

export const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.85,
    y: 60,
    rotateX: -15,
    filter: "blur(10px)",
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    rotateX: 0,
    filter: "blur(0px)",
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.6,
    },
  },
  exit: {
    opacity: 0,
    scale: 0.85,
    y: 60,
    rotateX: -15,
    filter: "blur(10px)",
    transition: {
      duration: 0.3,
      ease: "easeInOut",
    },
  },
};

export const mobileModalVariants = {
  hidden: {
    opacity: 0,
    y: "100%",
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      damping: 30,
      stiffness: 400,
      duration: 0.5,
    },
  },
  exit: {
    opacity: 0,
    y: "100%",
    scale: 0.95,
    transition: {
      duration: 0.3,
      ease: "easeInOut",
    },
  },
};

export const progressBarVariants = {
  hidden: { width: 0 },
  visible: (progress: number) => ({
    width: `${progress}%`,
    transition: { duration: 1, ease: "easeOut" },
  }),
};
