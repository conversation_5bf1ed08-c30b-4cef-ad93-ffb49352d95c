import { useState, useEffect, useRef } from 'react';
import { addDays, addWeeks, addMonths } from 'date-fns';

interface GoalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (goalData: { title: string; period: string; icon: string; endDate: string }) => void;
  editMode?: boolean;
  goalToEdit?: {
    id: number;
    title: string;
    icon: string;
    description: string;
    duration: string;
  };
}

const GoalModal = ({ isOpen, onClose, onSubmit, editMode = false, goalToEdit }: GoalModalProps) => {
  const [goalName, setGoalName] = useState('');
  const [period, setPeriod] = useState('day');
  const [showPeriodDropdown, setShowPeriodDropdown] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState('fa-bullseye');
  const [number, setNumber] = useState('1');
  const [showNumberDropdown, setShowNumberDropdown] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [expandedSection, setExpandedSection] = useState<string | null>(null);
  const [ageRanges, setAgeRanges] = useState([
    { range: '13 - 17', percentage: 7.2 },
    { range: '18 - 24', percentage: 23.8 },
    { range: '25 - 34', percentage: 27.2 },
    { range: '35 - 44', percentage: 26.1 },
    { range: '45 - 54', percentage: 11.1 },
    { range: '55 - 64', percentage: 2.2 },
    { range: '65+', percentage: 2.2 },
  ]);

  const [locations, setLocations] = useState([
    { name: 'United States', percentage: 35.5 },
    { name: 'United Kingdom', percentage: 18.2 },
    { name: 'Canada', percentage: 15.7 },
    { name: 'Australia', percentage: 12.3 },
    { name: 'Germany', percentage: 10.1 },
    { name: 'Others', percentage: 8.2 },
  ]);

  const [genderData, setGenderData] = useState([
    { gender: 'Male', percentage: 45.8 },
    { gender: 'Female', percentage: 52.2 },
    { gender: 'Other', percentage: 2.0 },
  ]);

  interface AccountReachData {
    total: { value: number };
    organic: { value: number };
    paid: { value: number };
  }

  interface TotalLikesData {
    value: number;
    previous: number;
  }

  const [accountReach, setAccountReach] = useState<AccountReachData>({
    total: { value: 12500 },
    organic: { value: 8750 },
    paid: { value: 3750 }
  });

  const [totalLikes, setTotalLikes] = useState<TotalLikesData>({
    value: 2840,
    previous: 2150
  });

  const periods = ['day', 'week',  'month', "year"];
  const numbers = Array.from({length: 6}, (_, i) => (i + 1).toString());
  
  // Sample icons - replace with Font Awesome icons in production
  const icons = ['fa-bullseye', 'fa-trophy', 'fa-chart-line', 'fa-dumbbell', 'fa-book', 'fa-lightbulb', 'fa-star', 'fa-palette', 'fa-running', 'fa-laptop-code'];

  // Add mobile detection
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // Add refs for the dropdown containers
  const periodDropdownRef = useRef<HTMLDivElement>(null);
  const numberDropdownRef = useRef<HTMLDivElement>(null);

  // Add tracking state for changes
  const [changedItems, setChangedItems] = useState<{
    locations: string[];
    gender: string[];
    accountReach: ('total' | 'organic' | 'paid')[];
    totalLikes: boolean;
    ageRanges: string[];
  }>({
    locations: [],
    gender: [],
    accountReach: [],
    totalLikes: false,
    ageRanges: []
  });

  const [nameError, setNameError] = useState<string>('');

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Add click outside handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        periodDropdownRef.current && 
        !periodDropdownRef.current.contains(event.target as Node) &&
        numberDropdownRef.current && 
        !numberDropdownRef.current.contains(event.target as Node)
      ) {
        setShowPeriodDropdown(false);
        setShowNumberDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Constants for maximum values
  const MAX_VALUES = {
    total: 15000,
    organic: 10000,
    paid: 5000,
    likes: 3500
  } as const;

  // Initialize form data when editing
  useEffect(() => {
    if (editMode && goalToEdit) {
      setGoalName(goalToEdit.title || '');
      setSelectedIcon(goalToEdit.icon || 'fa-bullseye');
      
      // Handle duration if available
      if (goalToEdit.duration) {
        const [durationNumber, durationPeriod] = goalToEdit.duration.split(' ');
        setNumber(durationNumber);
        setPeriod(durationPeriod.toLowerCase());
      }
    }
  }, [editMode, goalToEdit]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setCurrentStep(1);
      if (!editMode) {
        setGoalName('');
        setPeriod('week');
        setNumber('1');
        setSelectedIcon('fa-bullseye');
      }
    }
  }, [isOpen, editMode]);

  // Update error state when name changes
  useEffect(() => {
    if (goalName.trim()) {
      setNameError('');
    }
  }, [goalName]);

  const handleSectionToggle = (section: string) => {
    if (expandedSection === section) {
      // If closing the section, clear its changes
      setChangedItems(prev => {
        const newState = { ...prev };
        switch (section) {
          case 'age':
            newState.ageRanges = [];
            break;
          case 'locations':
            newState.locations = [];
            break;
          case 'gender':
            newState.gender = [];
            break;
          case 'reach':
            newState.accountReach = [];
            break;
          case 'likes':
            newState.totalLikes = false;
            break;
        }
        return newState;
      });
      setExpandedSection(null);
    } else {
      setExpandedSection(section);
    }
  };

  if (!isOpen) return null;

  const adjustOtherPercentages = (items: Array<{ percentage: number }>, changedIndex: number, newValue: number) => {
    const oldValue = items[changedIndex].percentage;
    const diff = newValue - oldValue;
    
    // If trying to decrease below 0 or increase above 100, don't adjust
    if (newValue < 0 || newValue > 100) return false;
    
    // Calculate total of other percentages
    const otherTotal = items.reduce((sum, item, idx) => 
      idx !== changedIndex ? sum + item.percentage : sum, 0
    );
    
    // If diff is positive (increasing), check if there's enough room to decrease others
    if (diff > 0 && otherTotal - diff < 0) return false;
    
    // Calculate adjustment factor for other items
    const adjustmentFactor = (otherTotal - diff) / otherTotal;
    
    // Adjust other percentages proportionally
    items.forEach((item, idx) => {
      if (idx !== changedIndex) {
        item.percentage = Number((item.percentage * adjustmentFactor).toFixed(1));
      }
    });
    
    // Set the new value for the changed item
    items[changedIndex].percentage = newValue;
    
    return true;
  };

  const handlePercentageChange = (index: number, change: number) => {
    const newRanges = [...ageRanges];
    const newValue = Number((newRanges[index].percentage + change).toFixed(1));
    
    if (adjustOtherPercentages(newRanges, index, newValue)) {
      setAgeRanges(newRanges);
      
      // Track the change
      if (!changedItems.ageRanges.includes(newRanges[index].range)) {
        setChangedItems(prev => ({
          ...prev,
          ageRanges: [...prev.ageRanges, newRanges[index].range]
        }));
      }
    }
  };

  const calculateEndDate = () => {
    const today = new Date();
    const numberValue = parseInt(number);
    
    switch (period) {
      case 'day':
        return addDays(today, numberValue);
      case 'week':
        return addWeeks(today, numberValue);
      case 'month':
        return addMonths(today, numberValue);
      default:
        return today;
    }
  };

  const handleLocationTargetChange = (index: number, change: number) => {
    const newLocations = [...locations];
    const newValue = Number((newLocations[index].percentage + change).toFixed(1));
    
    if (adjustOtherPercentages(newLocations, index, newValue)) {
      setLocations(newLocations);
      
      // Track the change
      if (!changedItems.locations.includes(newLocations[index].name)) {
        setChangedItems(prev => ({
          ...prev,
          locations: [...prev.locations, newLocations[index].name]
        }));
      }
    }
  };

  const handleGenderTargetChange = (index: number, change: number) => {
    const newGenderData = [...genderData];
    const newValue = Number((newGenderData[index].percentage + change).toFixed(1));
    
    if (adjustOtherPercentages(newGenderData, index, newValue)) {
      setGenderData(newGenderData);
      
      // Track the change
      if (!changedItems.gender.includes(newGenderData[index].gender)) {
        setChangedItems(prev => ({
          ...prev,
          gender: [...prev.gender, newGenderData[index].gender]
        }));
      }
    }
  };

  const handleReachTargetChange = (type: keyof AccountReachData, change: number) => {
    setAccountReach(prev => ({
      ...prev,
      [type]: {
        value: Math.max(prev[type].value + change, 0)
      }
    }));
    
    // Track the change
    if (!changedItems.accountReach.includes(type)) {
      setChangedItems(prev => ({
        ...prev,
        accountReach: [...prev.accountReach, type]
      }));
    }
  };

  const handleLikesTargetChange = (change: number) => {
    setTotalLikes(prev => ({
      ...prev,
      value: Math.max(prev.value + change, 0)
    }));
    
    // Track the change
    setChangedItems(prev => ({
      ...prev,
      totalLikes: true
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && goalName.trim()) {
      if (currentStep === 1) {
        setCurrentStep(2);
      } else if (currentStep === 2) {
        setCurrentStep(3);
      } else {
        const endDate = calculateEndDate();
        const duration = number + " " + period
        onSubmit({ 
          title: goalName, 
          period,
          duration: duration,
          icon: selectedIcon,
          endDate: endDate.toISOString().slice(0, 19).replace('T', ' ')
        });
        onClose();
      }
    }
  };

  const renderMetricRow = (label: string, current: number, target: number, onDecrease: () => void, onIncrease: () => void) => (
    <div className="flex items-center justify-between gap-4">
      <div className="flex items-center gap-4 flex-1">
        <span className="w-32 text-[1rem]">{label}</span>
        <div className="flex-1 flex items-center gap-4">
          <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                      <div 
                        className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg"
              style={{ width: `${(current / target) * 100}%` }}
                      />
                    </div>
          <div className="flex items-center gap-2">
            <span className="text-[0.875rem] w-18">{current.toLocaleString()}</span>
            <div className="flex gap-1 items-center">
              <span className="text-[0.875rem] text-gray-600">Goal: {target.toLocaleString()}</span>
              <div className="flex gap-1">
                <button
                  onClick={onDecrease}
                  className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                >-</button>
                <button
                  onClick={onIncrease}
                  className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                >+</button>
                </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    );

  const renderStepTwo = () => {
    return (
      <div className="flex flex-row h-full">
        {/* Left Side Progress Steps */}
        <div className="hidden md:flex flex-col p-8 items-start h-full gap-8 mb-8 pr-4 mr-4  border-[#cccccc]">
            {[
              { step: 1, label: 'Goal Settings' },
              { step: 2, label: 'Item' }, 
              { step: 3, label: 'Preview' }
            ].map((item, index) => (
              <div key={item.step} className="flex items-center gap-4">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  item.step === 2 ? 'bg-[#2c3e50] text-white' : 'bg-[#949ba2] text-white'
                }`}>
                  {item.step}
                </div>
                <span className="text-[1rem] text-gray-700">{item.label}</span>
                {index < 2 && (
                  <div className="absolute left-[2.9rem] w-[2px] h-8 bg-[#cccccc] translate-y-8" />
                )}
              </div>
            ))}
          </div>

        {/* Right Side Content */}
        <div className="flex-1 p-8 overflow-y-auto max-h-[calc(80vh-8rem)]">
          <div className="flex flex-col gap-[1.3rem]">
            {/* Age Range Section */}
            <div className={`w-full rounded-2xl border border-[#cccccc] transition-all duration-300 ease-in-out`}>
              <button
                onClick={() => handleSectionToggle('age')}
                className="w-full h-20 px-6 flex items-center justify-between"
              >
                <span className="text-[1rem] font-medium">Age range</span>
                <span className='text-[1.5rem]'>{expandedSection === 'age' ? '−' : '+'}</span>
              </button>

              <div className={`transition-all duration-300 ease-in-out ${
                expandedSection === 'age' 
                  ? 'max-h-[500px] opacity-100 visible p-6 pt-0 space-y-[0.7rem]'
                  : 'max-h-0 opacity-0 invisible'
              }`}>
                  {ageRanges.map((item, index) => (
                    <div key={item.range} className="flex items-center justify-between gap-4">
                      <div className="flex items-center gap-4 flex-1">
                        <span className="w-16 text-[1rem]">{item.range}</span>
                        <div className="flex-1 flex items-center gap-4">
                          <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                            <div 
                              className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg will-change-[width] transition-[width] duration-300 ease-in-out"
                              style={{ width: `${item.percentage}%` }}
                            />
                          </div>
                          <span className="text-[0.875rem] w-12">{item.percentage}%</span>
                        </div>
                      </div>
                      <div className="flex gap-[0.6rem]">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePercentageChange(index, -1);
                          }}
                          className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                        >
                          -
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePercentageChange(index, 1);
                          }}
                          className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
            </div>

            {/* Locations Section */}
            <div className={`w-full rounded-2xl border border-[#cccccc] transition-all duration-300 ease-in-out`}>
              <button
                onClick={() => handleSectionToggle('locations')}
                className="w-full h-20 px-6 flex items-center justify-between"
              >
                <span className="text-[1rem] font-medium">Locations</span>
                <span className='text-[1.5rem]'>{expandedSection === 'locations' ? '−' : '+'}</span>
              </button>

              <div className={`transition-all duration-300 ease-in-out ${
                expandedSection === 'locations' 
                  ? 'max-h-[500px] opacity-100 visible p-6 pt-0 space-y-[0.7rem]'
                  : 'max-h-0 opacity-0 invisible'
              }`}>
                {locations.map((location, index) => (
                  <div key={location.name} className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-4 flex-1">
                      <span className="w-[20%] md:w-32 text-[1rem]">{location.name}</span>
                      <div className="flex-1 flex items-center gap-4">
                        <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                          <div 
                            className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg"
                            style={{ width: `${Math.min(location.percentage, 100)}%` }}
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-[0.875rem] w-12">{location.percentage}%</span>
                          <div className="flex gap-1 items-center">
                            <div className="flex gap-1">
                              <button
                                onClick={() => handleLocationTargetChange(index, -1)}
                                className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                              >-</button>
                              <button
                                onClick={() => handleLocationTargetChange(index, 1)}
                                className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                              >+</button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Gender Section */}
            <div className={`w-full rounded-2xl border border-[#cccccc] transition-all duration-300 ease-in-out`}>
              <button
                onClick={() => handleSectionToggle('gender')}
                className="w-full h-20 px-6 flex items-center justify-between"
              >
                <span className="text-[1rem] font-medium">Gender</span>
                <span className='text-[1.5rem]'>{expandedSection === 'gender' ? '−' : '+'}</span>
              </button>

              <div className={`transition-all duration-300 ease-in-out ${
                expandedSection === 'gender' 
                  ? 'max-h-[500px] opacity-100 visible p-6 pt-0 space-y-[0.7rem]'
                  : 'max-h-0 opacity-0 invisible'
              }`}>
                {genderData.map((item, index) => (
                  <div key={item.gender} className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-4 flex-1">
                      <span className="w-[20%] text-[1rem]">{item.gender}</span>
                      <div className="flex-1 flex items-center gap-4">
                        <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                          <div 
                            className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg"
                            style={{ width: `${Math.min(item.percentage, 100)}%` }}
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-[0.875rem] w-12">{item.percentage}%</span>
                          <div className="flex gap-1 items-center">
                            <div className="flex gap-1">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleGenderTargetChange(index, -1);
                                }}
                                className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                              >-</button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleGenderTargetChange(index, 1);
                                }}
                                className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                              >+</button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Account Reach Section */}
            <div className={`w-full rounded-2xl border border-[#cccccc] transition-all duration-300 ease-in-out`}>
              <button
                onClick={() => handleSectionToggle('reach')}
                className="w-full h-20 px-6 flex items-center justify-between"
              >
                <span className="text-[1rem] font-medium">Account Reach</span>
                <span className='text-[1.5rem]'>{expandedSection === 'reach' ? '−' : '+'}</span>
              </button>

              <div className={`transition-all duration-300 ease-in-out ${
                expandedSection === 'reach' 
                  ? 'max-h-[500px] opacity-100 visible p-6 pt-0 space-y-[0.7rem]'
                  : 'max-h-0 opacity-0 invisible'
              }`}>
                {/* Total Reach */}
                <div className="flex flex-col gap-4">
                  <div className="flex items-center justify-between gap-4">
                    <span className="text-[1rem]">Total Reach</span>
                    <div className="flex items-center gap-2">
                    <div className="flex gap-1 items-center">
                    <span className="text-[0.875rem]">{accountReach.total.value.toLocaleString()}</span>

                      <button
                        onClick={() => handleReachTargetChange('total', -500)}
                        className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                      >-</button>
                      <button
                        onClick={() => handleReachTargetChange('total', 500)}
                        className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                      >+</button>
                    </div>
                  </div>
                  </div>
                 
                </div>

                {/* Organic Reach */}
                <div className="flex flex-col gap-4">
                  <div className="flex items-center justify-between gap-4">
                    <span className="text-[1rem]">Organic Reach</span>
                    <div className="flex gap-1 items-center">
                    <span className="text-[0.875rem]">{accountReach.organic.value.toLocaleString()}</span>

                      <button
                        onClick={() => handleReachTargetChange('organic', -250)}
                        className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                      >-</button>
                      <button
                        onClick={() => handleReachTargetChange('organic', 250)}
                        className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                      >+</button>
                    </div>
                  </div>

                </div>

                {/* Paid Reach */}
                <div className="flex flex-col gap-4">
                  <div className="flex items-center justify-between gap-4">
                    <span className="text-[1rem]">Paid Reach</span>
                    
                    <div className="flex gap-1 items-center">
                    <span className="text-[0.875rem]">{accountReach.paid.value.toLocaleString()}</span>
                      <div className="flex gap-1">
                        <button
                          onClick={() => handleReachTargetChange('paid', -250)}
                          className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                        >-</button>
                        <button
                          onClick={() => handleReachTargetChange('paid', 250)}
                          className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                        >+</button>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                  
                  </div>
                </div>
              </div>
            </div>

            {/* Total Like Section */}
            <div className={`w-full rounded-2xl border border-[#cccccc] transition-all duration-300 ease-in-out`}>
              <button
                onClick={() => handleSectionToggle('likes')}
                className="w-full h-20 px-6 flex items-center justify-between"
              >
                <span className="text-[1rem] font-medium">Total Like</span>
                <span className='text-[1.5rem]'>{expandedSection === 'likes' ? '−' : '+'}</span>
              </button>

              <div className={`transition-all duration-300 ease-in-out ${
                expandedSection === 'likes' 
                  ? 'max-h-[500px] opacity-100 visible p-6 pt-0 space-y-[0.7rem]'
                  : 'max-h-0 opacity-0 invisible'
              }`}>
                <div className="flex flex-row justify-between gap-4">
                  <span className="text-[1rem] font-medium">Current Likes: {totalLikes.value.toLocaleString()}</span>
                  <div className="flex gap-1 items-center">
                    <button
                      onClick={() => handleLikesTargetChange(-100)}
                      className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                    >-</button>
                    <button
                      onClick={() => handleLikesTargetChange(100)}
                      className="w-6 h-6 bg-[#eeeeee] hover:bg-[#e0e0e0] active:bg-[#d0d0d0] rounded-lg flex items-center justify-center transition-colors"
                    >+</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scrollbar Styling - Optional */}
        {/* <div className="w-[0.3rem] h-35 absolute right-6 top-[5.8rem] bg-[#a7a7a7] rounded-2xl" /> */}
      </div>
    );
  };

  const renderStepThree = () => {
    return (
      <div className="flex flex-row h-full">
        {/* Left Side Progress Steps */}
        <div className="hidden md:flex flex-col p-8 items-start h-full gap-8 mb-8 pr-4 mr-4 border-[#cccccc]">
          {[
            { step: 1, label: 'Goal Settings' },
            { step: 2, label: 'Item' },
            { step: 3, label: 'Preview' }
          ].map((item, index) => (
            <div key={item.step} className="flex items-center gap-4">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                item.step === 3 ? 'bg-[#2c3e50]' : 'bg-[#949ba2]'
              } text-white`}>
                {item.step}
              </div>
              <span className="text-[1rem] text-gray-700">{item.label}</span>
              {index < 2 && (
                <div className="absolute left-[2.9rem] w-0.5 h-8 bg-[#cccccc] translate-y-8" />
              )}
            </div>
          ))}
        </div>
  
        {/* Right Side Content */}
        <div className="flex-1 flex flex-col h-full">
          <div className="flex-1 p-8 overflow-y-auto">
            {/* Title Section */}
            <div className="flex flex-col items-center mb-8">
              <div className="flex items-center gap-4 justify-center">
                <i className={`fa-solid fa-${selectedIcon} text-lg text-[#2c3e50]`}></i>
                <h2 className="text-lg text-[#2c3e50] font-semibold">{goalName}</h2>
              </div>
              <div className="w-full h-[0.063rem] bg-[#d2d2d2] mt-4" />
            </div>
  
            {/* Preview Content */}
            <div className="space-y-6 max-h-[50vh] overflow-y-auto no-scrollbar">
              {/* Age Ranges Section - Only if changed */}
              {changedItems.ageRanges.length > 0 && (
                <div className="w-full rounded-2xl border border-[#cccccc] p-6">
                  <h3 className="text-[1rem] font-medium mb-4">Age Ranges</h3>
                  <div className="space-y-[0.7rem]">
                    {ageRanges
                      .filter(range => changedItems.ageRanges.includes(range.range))
                      .map((range) => (
                        <div key={range.range} className="flex items-center gap-4">
                          <span className="w-32 text-[1rem]">{range.range}</span>
                          <div className="flex-1 flex items-center gap-4">
                            <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                              <div 
                                className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg"
                                style={{ width: `${range.percentage}%` }}
                              />
                            </div>
                            <span className="text-[0.875rem] w-12">{range.percentage}%</span>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Locations Section - Only if changed */}
              {changedItems.locations.length > 0 && (
                <div className="w-full rounded-2xl border border-[#cccccc] p-6">
                  <h3 className="text-[1rem] font-medium mb-4">Locations</h3>
                  <div className="space-y-[0.7rem]">
                    {locations
                      .filter(location => changedItems.locations.includes(location.name))
                      .map((location) => (
                        <div key={location.name} className="flex items-center gap-4">
                          <span className="w-32 text-[1rem]">{location.name}</span>
                          <div className="flex-1 flex items-center gap-4">
                            <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                              <div 
                                className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg"
                                style={{ width: `${location.percentage}%` }}
                              />
                            </div>
                            <span className="text-[0.875rem] w-12">{location.percentage}%</span>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Gender Section - Only if changed */}
              {changedItems.gender.length > 0 && (
                <div className="w-full rounded-2xl border border-[#cccccc] p-6">
                  <h3 className="text-[1rem] font-medium mb-4">Gender</h3>
                  <div className="space-y-[0.7rem]">
                    {genderData
                      .filter(item => changedItems.gender.includes(item.gender))
                      .map((item) => (
                        <div key={item.gender} className="flex items-center gap-4">
                          <span className="w-32 text-[1rem]">{item.gender}</span>
                          <div className="flex-1 flex items-center gap-4">
                            <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                              <div 
                                className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg"
                                style={{ width: `${item.percentage}%` }}
                              />
                            </div>
                            <span className="text-[0.875rem] w-12">{item.percentage}%</span>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Account Reach Section - Only if changed */}
              {changedItems.accountReach.length > 0 && (
                <div className="w-full rounded-2xl border border-[#cccccc] p-6">
                  <h3 className="text-[1rem] font-medium mb-4">Account Reach</h3>
                  <div className="space-y-[0.7rem]">
                    {changedItems.accountReach.includes('total') && (
                      <div className="flex items-center gap-4">
                        <span className="w-32 text-[1rem]">Total</span>
                        <div className="flex-1 flex items-center gap-4">
                          <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                            <div 
                              className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg"
                              style={{ width: `${(accountReach.total.value / MAX_VALUES.total) * 100}%` }}
                            />
                          </div>
                          <span className="text-[0.875rem] w-20">{accountReach.total.value.toLocaleString()}</span>
                        </div>
                      </div>
                    )}

                    {changedItems.accountReach.includes('organic') && (
                      <div className="flex items-center gap-4">
                        <span className="w-32 text-[1rem]">Organic</span>
                        <div className="flex-1 flex items-center gap-4">
                          <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                            <div 
                              className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg"
                              style={{ width: `${(accountReach.organic.value / MAX_VALUES.organic) * 100}%` }}
                            />
                          </div>
                          <span className="text-[0.875rem] w-20">{accountReach.organic.value.toLocaleString()}</span>
                        </div>
                      </div>
                    )}

                    {changedItems.accountReach.includes('paid') && (
                      <div className="flex items-center gap-4">
                        <span className="w-32 text-[1rem]">Paid</span>
                        <div className="flex-1 flex items-center gap-4">
                          <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                            <div 
                              className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg"
                              style={{ width: `${(accountReach.paid.value / MAX_VALUES.paid) * 100}%` }}
                            />
                          </div>
                          <span className="text-[0.875rem] w-20">{accountReach.paid.value.toLocaleString()}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Total Likes Section - Only if changed */}
              {changedItems.totalLikes && (
                <div className="w-full rounded-2xl border border-[#cccccc] p-6">
                  <h3 className="text-[1rem] font-medium mb-4">Total Likes</h3>
                  <div className="flex items-center gap-4">
                    <span className="w-32 text-[1rem]">Current</span>
                    <div className="flex-1 flex items-center gap-4">
                      <div className="relative w-full h-5 bg-[#eeeeee] rounded-lg overflow-hidden">
                        <div 
                          className="absolute left-0 top-0 h-full bg-[#007aff] rounded-lg"
                          style={{ width: `${(totalLikes.value / MAX_VALUES.likes) * 100}%` }}
                        />
                      </div>
                      <span className="text-[0.875rem] w-20">{totalLikes.value.toLocaleString()}</span>
                    </div>
                  </div>
                  <div className="mt-2 text-sm text-gray-600">Previous: {totalLikes.previous.toLocaleString()}</div>
                </div>
              )}
            </div>
          </div>

          
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 overflow-y-hidden">
      <div className={`bg-white relative overflow-y-auto no-scrollbar
        ${isMobile ? 'w-dvw h-dvh' : 'w-[70%] max-h-[80%] rounded-2xl'}`}>
        
        {/* Header with Steps (Mobile) */}
        {isMobile && (
          <div className="sticky top-0 bg-white z-10">
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center gap-2">
                <img src="/icons/instagram.svg" alt="" className="w-6 h-6"/>
                <span className="text-lg">Set your goal</span>
              </div>
              <button onClick={onClose} className="text-xl">✕</button>
            </div>
            
            {/* Mobile Steps */}
            <div className="flex justify-between px-4 py-4 border-b relative">
              {[
                { step: 1, label: 'Settings' },
                { step: 2, label: 'Item' },
                { step: 3, label: 'Preview' }
              ].map((item, index) => (
                <div key={item.step} className="flex flex-col items-center relative">
                  <div className={`flex flex-col items-center ${
                    item.step === currentStep ? 'text-[#2c3e50]' : 'text-gray-400'
                  }`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1
                      ${item.step === currentStep ? 'bg-[#2c3e50] text-white' : 'bg-gray-200'}`}>
                      {item.step}
                    </div>
                    <span className="text-sm">{item.label}</span>
                  </div>
                  {index < 2 && (
                    <div className="absolute left-[54px] w-[calc(100vw/4)] h-[2px] top-[15px] bg-gray-200" />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Desktop Header */}
        {!isMobile && (
          <div className="h-12 flex items-center justify-between p-6 border-b">
            <div className="flex items-center gap-2">
              <img src="/icons/instagram.svg" alt=""  className='w-6 h-6'/>
              <h2 className="text-lg">{editMode ? 'Edit goal' : 'Set your goal'}</h2>
            </div>
            <button 
              onClick={onClose}
              className="text-xl hover:text-gray-700"
            >
              ✕
            </button>
          </div>
        )}

        {/* Content */}
        <div className={`${isMobile ? '' : ''}`}>
          {currentStep === 1 ? (
            // Step One Content
            <div className="flex flex-row h-full">
              <div className="hidden md:flex flex-col p-8 items-start h-full gap-8 mb-8 pr-4 mr-4  border-[#cccccc]">
              {[
                { step: 1, label: 'Goal Settings' },
                { step: 2, label: 'Item' }, 
                { step: 3, label: 'Preview' }
              ].map((item, index) => (
                <div key={item.step} className="flex items-center gap-4">
                  <div className={`w-8 h-8 rounded-full aspect-square flex items-center justify-center ${
                    item.step === 1 ? 'bg-[#2c3e50] text-white' : 'bg-[#949ba2] text-white'
                  }`}>
                    {item.step}
                  </div>
                  <span className="text-[1rem] text-gray-700">{item.label}</span>
                  {index < 2 && (
                    <div className="absolute left-[2.9rem] w-[2px] h-8 bg-[#cccccc] translate-y-8" />
                  )}
                </div>
              ))}
            </div>

            
              {/* Form */}
            <div className="space-y-8 p-8 w-full">
              {/* Goal Name */}
              <div className="flex gap-8 md:flex-row flex-col">
                <label className="text-lg w-24 pt-[0.8rem]">Goal name:</label>
                <div className="w-full md:w-[60%]">
                  <input
                    type="text"
                    value={goalName}
                    onChange={(e) => setGoalName(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="My Goal"
                    className={`w-full h-[3.3rem] px-4 bg-neutral-50 rounded-lg border ${
                      nameError ? 'border-red-500' : 'border-[#9e9e9e]'
                    }`}
                  />
                  {nameError && (
                    <p className="text-red-500 text-sm mt-1">{nameError}</p>
                  )}
                </div>
              </div>

              {/* Period Selection */}
              <div className="flex gap-[3.2rem] relative w-full  md:flex-row flex-col">
                <label className="text-lg w-24 pt-[0.3rem]">Period:</label>
                <div className="btns flex flex-row w-full md:gap-0 gap-2">
                <div className="relative flex flex-row w-1/2 gap-2 " ref={numberDropdownRef}>
                  <button
                    onClick={() => {
                      setShowNumberDropdown(!showNumberDropdown);
                      setShowPeriodDropdown(false);
                    }}
                    className="w-full md:w-32 h-10 px-2 md:px-4 rounded-4xl border border-[#9e9e9e] text-left flex items-center justify-between text-sm md:text-base"
                  >
                    <span>{number}</span>
                    <i className={`fas fa-chevron-down ml-1 md:ml-2 transition-transform ${showNumberDropdown ? 'rotate-180' : ''}`} />
                  </button>
                  {showNumberDropdown && (
                    <div className="absolute top-full left-0 w-20 md:w-32 bg-white rounded-2xl border border-[#9e9e9e] mt-2">
                      {numbers.map((n) => (
                        <button
                          key={n}
                          onClick={() => {
                            setNumber(n);
                            setShowNumberDropdown(false);
                          }}
                          className="w-full p-2 md:p-[0.7rem] text-center hover:bg-[#f3f3f3] first:rounded-t-2xl last:rounded-b-2xl text-sm md:text-base"
                        >
                          {n}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
                <div className="relative w-1/2" ref={periodDropdownRef}>
                  <button
                    onClick={() => {
                      setShowPeriodDropdown(!showPeriodDropdown);
                      setShowNumberDropdown(false);
                    }}
                    className="w-full md:w-32 h-10 px-2 md:px-4 rounded-4xl border border-[#9e9e9e] text-left flex items-center justify-between text-sm md:text-base"
                  >
                    <span>{period}</span>
                    <i className={`fas fa-chevron-down ml-1 md:ml-2 transition-transform ${showPeriodDropdown ? 'rotate-180' : ''}`} />
                  </button>
                  {showPeriodDropdown && (
                    <div className="absolute top-full left-0 w-20 md:w-32 bg-white rounded-2xl border border-[#9e9e9e] mt-2">
                      {periods.map((p) => (
                        <button
                          key={p}
                          onClick={() => {
                            setPeriod(p);
                            setShowPeriodDropdown(false);
                          }}
                          className="w-full p-2 md:p-[0.7rem] text-center hover:bg-[#f3f3f3] first:rounded-t-2xl last:rounded-b-2xl text-sm md:text-base"
                        >
                          {p}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
                </div>
              </div>


              {/* Icon Selection */}
              <div className="flex gap-8 w-full md:flex-row flex-col">
                <label className="text-lg w-24 pt-[0.8rem]">Goal icon:</label>
                <div className="w-full md:w-[60%] h-52 p-4 border border-[#9e9e9e] rounded-lg overflow-y-auto">
                  <div className="grid grid-cols-5 gap-4">
                    {[
                      'trophy', 'bullseye', 'star', 'heart', 'book', 
                      'graduation-cap', 'dumbbell', 'money-bill',
                      'briefcase', 'plane', 'home', 'music',
                      'palette', 'camera', 'code', 'microscope'
                    ].map((iconName, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedIcon(iconName)}
                        className={`w-8 h-8 flex items-center justify-center border rounded ${
                          selectedIcon === iconName ? 'border-[#2c3e50] shadow' : ''
                        }`}
                      >
                        <i className={`fas text-[#2c3e50] fa-${iconName}`} />
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : currentStep === 2 ? (
          // Step Two Content
          renderStepTwo()
        ) : (
          // Step Three Content 
          renderStepThree()
        )}
        </div>

        {/* Footer */}
        <div className={`w-full flex justify-center gap-4 p-4 bg-white 
          ${isMobile ? 'fixed bottom-0' : ''}`}>
          {(currentStep === 2 || currentStep === 3) && (
            <button
              onClick={() => setCurrentStep(currentStep - 1)}
              className="px-8 py-[0.7rem] border border-[#b7b7b7] text-[#b7b7b7] rounded-lg shadow"
            >
              Back
            </button>
          )}
          <button
            onClick={() => {
              if (currentStep === 1) {
                if (!goalName.trim()) {
                  setNameError('Please enter a goal name');
                  return;
                }
                setCurrentStep(2);
              } else if (currentStep === 2) {
                setCurrentStep(3);
              } else {
                const endDate = calculateEndDate();
                const duration = number + " " + period
                onSubmit({ 
                  title: goalName, 
                  period,
                  duration: duration,
                  icon: selectedIcon,
                  endDate: endDate.toISOString().slice(0, 19).replace('T', ' ')
                });
                onClose();
              }
            }}
            className={`px-8 py-[0.7rem] ${
              currentStep === 1 && !goalName.trim() 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-[#2c3e50]'
            } text-white rounded-lg shadow`}
          >
            {currentStep === 1 ? 'Select Item' : currentStep === 2 ? 'View Preview' : editMode ? 'Save Changes' : 'Create'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default GoalModal; 