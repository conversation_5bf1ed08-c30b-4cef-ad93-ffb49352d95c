import { useCallback, useState } from 'react';
import { useWebSocket } from '~/hooks/useWebSocket';
import { useUserStore } from '~/store/userStore';
import { showErrorToast } from '~/components/toasts';
import { GoalProbabilityResponse } from '../types';

export const useGoalProbability = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [probabilityData, setProbabilityData] = useState<GoalProbabilityResponse | null>(null);

  const { sendMessage, socket } = useWebSocket();
  const store = useUserStore();
  const selectedWorkspace = store.selectedWorkspace as string;

  // Calculate goal probability
  const calculateGoalProbability = useCallback(async (goalId: number) => {
    if (!selectedWorkspace) {
      setError('No workspace selected');
      showErrorToast('No workspace selected');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      return new Promise<GoalProbabilityResponse | null>((resolve) => {
        const messageHandler = (event: MessageEvent) => {
          const response = JSON.parse(event.data) as GoalProbabilityResponse;

          if (response.job === 'get_goal_probability') {
            socket?.removeEventListener('message', messageHandler);
            
            if (response.success) {
              setProbabilityData(response);
              resolve(response);
            } else {
              const errorMessage = response.message || 'Failed to calculate goal probability';
              setError(errorMessage);
              showErrorToast(errorMessage);
              resolve(null);
            }
            setIsLoading(false);
          }
        };

        if (socket) {
          socket.addEventListener('message', messageHandler);
        }

        sendMessage({
          job: 'get_goal_probability',
          goal_id: goalId,
          workspace_name: selectedWorkspace,
        });

        // Timeout after 10 seconds
        setTimeout(() => {
          socket?.removeEventListener('message', messageHandler);
          if (isLoading) {
            setIsLoading(false);
            setError('Request timeout');
            showErrorToast('Request timeout - please try again');
            resolve(null);
          }
        }, 10000);
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to calculate goal probability';
      setError(errorMessage);
      showErrorToast(errorMessage);
      setIsLoading(false);
      return null;
    }
  }, [selectedWorkspace, sendMessage, socket, isLoading]);

  // Get probability color based on percentage
  const getProbabilityColor = useCallback((probability: number) => {
    if (probability >= 80) return '#10b981'; // green-500
    if (probability >= 60) return '#f59e0b'; // amber-500
    if (probability >= 40) return '#f97316'; // orange-500
    return '#ef4444'; // red-500
  }, []);

  // Get probability status text
  const getProbabilityStatus = useCallback((probability: number) => {
    if (probability >= 80) return 'Very Likely';
    if (probability >= 60) return 'Likely';
    if (probability >= 40) return 'Possible';
    return 'Unlikely';
  }, []);

  // Calculate estimated completion date based on current progress
  const getEstimatedCompletion = useCallback((
    currentProgress: number,
    startDate: string,
    endDate: string
  ) => {
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const now = new Date();
      
      const totalDuration = end.getTime() - start.getTime();
      const elapsedTime = now.getTime() - start.getTime();
      const remainingTime = end.getTime() - now.getTime();
      
      if (currentProgress === 0) {
        return end; // No progress, use original end date
      }
      
      // Calculate rate of progress
      const progressRate = currentProgress / elapsedTime;
      const remainingProgress = 100 - currentProgress;
      const estimatedTimeToComplete = remainingProgress / progressRate;
      
      const estimatedCompletion = new Date(now.getTime() + estimatedTimeToComplete);
      
      // Don't return a date earlier than now
      return estimatedCompletion > now ? estimatedCompletion : now;
    } catch (error) {
      console.error('Error calculating estimated completion:', error);
      return new Date(endDate);
    }
  }, []);

  // Generate AI suggestions for improving goal probability
  const generateImprovementSuggestions = useCallback((
    probability: number,
    factors?: GoalProbabilityResponse['factors']
  ) => {
    const suggestions: string[] = [];

    if (!factors) {
      return ['Connect your social media accounts for personalized suggestions'];
    }

    if (factors.current_progress < 25) {
      suggestions.push('Focus on consistent daily actions to build momentum');
    }

    if (factors.engagement_trend < 0) {
      suggestions.push('Improve content quality to boost engagement rates');
      suggestions.push('Post at optimal times when your audience is most active');
    }

    if (factors.historical_performance < 50) {
      suggestions.push('Analyze your most successful posts and replicate their elements');
      suggestions.push('Consider collaborating with other accounts in your niche');
    }

    if (factors.time_remaining < 30) {
      suggestions.push('Increase posting frequency to maximize remaining time');
      suggestions.push('Consider running targeted ads to accelerate growth');
    }

    if (probability < 40) {
      suggestions.push('Review and adjust your goal timeline if needed');
      suggestions.push('Break down your goal into smaller, achievable milestones');
    }

    return suggestions.length > 0 ? suggestions : [
      'Keep up the great work! Your goal is on track.',
      'Maintain consistent posting schedule',
      'Engage actively with your audience'
    ];
  }, []);

  // Clear probability data
  const clearProbabilityData = useCallback(() => {
    setProbabilityData(null);
    setError(null);
  }, []);

  return {
    isLoading,
    error,
    probabilityData,
    calculateGoalProbability,
    getProbabilityColor,
    getProbabilityStatus,
    getEstimatedCompletion,
    generateImprovementSuggestions,
    clearProbabilityData,
  };
};
