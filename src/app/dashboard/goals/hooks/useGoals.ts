import { useCallback, useEffect, useState } from 'react';
import { useWebSocket } from '~/hooks/useWebSocket';
import { useUserStore } from '~/store/userStore';
import { showErrorToast, showSuccessToast } from '~/components/toasts';
import {
  Goal,
  ServerGoal,
  GoalCreateFormData,
  GoalUpdateFormData,
  GoalsResponse,
  GoalProbabilityResponse,
} from '../types';

export const useGoals = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { createGoal, getGoals, updateGoal, deleteGoal } = useWebSocket();
  const store = useUserStore();
  const selectedWorkspace = store.selectedWorkspace as string;
  const selectedSocial = store.selectedSocial as { social_id: string } | null;
  const setUser = store.setUser;

  // Transform server goal to client goal format
  const transformServerGoal = useCallback((serverGoal: ServerGoal): Goal => {
    return {
      id: serverGoal.id,
      title: serverGoal.name,
      description: serverGoal.end,
      percentage: serverGoal.progress / 100,
      status: serverGoal.progress === 100 ? 'Completed' : 'In Progress',
      icon: serverGoal.icon || 'bullseye',
      isPinned: serverGoal.is_pinned,
      duration: serverGoal.duration,
      endDate: serverGoal.end,
      startDate: serverGoal.start,
    };
  }, []);

  // Fetch all goals
  const fetchGoals = useCallback(async () => {
    if (!selectedWorkspace) {
      setError('No workspace selected');
      return;
    }

    if (!selectedSocial?.social_id) {
      setError('No social account selected');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const requestData = {
        workspace_name: selectedWorkspace,
        social: selectedSocial.social_id,
        is_active: null,
      };

      console.log('Sending get_goals request with format:', requestData);

      const response = await getGoals(requestData) as GoalsResponse;

      if (response && response.success && Array.isArray(response.goals)) {
        const transformedGoals = response.goals.map(transformServerGoal);
        setUser({ goals: transformedGoals });
      } else {
        throw new Error(response?.message || 'Failed to fetch goals');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch goals';
      setError(errorMessage);
      showErrorToast(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [selectedWorkspace, selectedSocial, getGoals, setUser, transformServerGoal]);

  // Create a new goal
  const createNewGoal = useCallback(async (goalData: GoalCreateFormData) => {
    if (!selectedWorkspace) {
      showErrorToast('No workspace selected');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
      
      const response = await createGoal({
        workspace_name: selectedWorkspace,
        name: goalData.name,
        icon: goalData.icon,
        end: goalData.end,
        duration: goalData.duration,
        js_data: goalData.js_data || {},
        social: goalData.social || (selectedSocial ? [selectedSocial.social_id] : []),
      }) as GoalsResponse;

      if (response?.success) {
        showSuccessToast(response.message || 'Goal created successfully');
        await fetchGoals(); // Refresh goals list
        return true;
      } else {
        throw new Error(response?.message || 'Failed to create goal');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create goal';
      setError(errorMessage);
      showErrorToast(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [selectedWorkspace, selectedSocial, createGoal, fetchGoals]);

  // Update an existing goal
  const updateExistingGoal = useCallback(async (goalData: GoalUpdateFormData) => {
    if (!selectedWorkspace) {
      showErrorToast('No workspace selected');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await updateGoal(goalData) as GoalsResponse;

      if (response?.success) {
        showSuccessToast(response.message || 'Goal updated successfully');
        await fetchGoals(); // Refresh goals list
        return true;
      } else {
        throw new Error(response?.message || 'Failed to update goal');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update goal';
      setError(errorMessage);
      showErrorToast(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [selectedWorkspace, updateGoal, fetchGoals]);

  // Delete a goal
  const deleteExistingGoal = useCallback(async (goalId: number) => {
    if (!selectedWorkspace) {
      showErrorToast('No workspace selected');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await deleteGoal({
        goal_id: goalId,
        workspace_name: selectedWorkspace,
      }) as GoalsResponse;

      if (response?.success) {
        showSuccessToast(response.message || 'Goal deleted successfully');
        await fetchGoals(); // Refresh goals list
        return true;
      } else {
        throw new Error(response?.message || 'Failed to delete goal');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete goal';
      setError(errorMessage);
      showErrorToast(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [selectedWorkspace, deleteGoal, fetchGoals]);

  // Toggle goal pin status
  const toggleGoalPin = useCallback(async (goalId: number, currentPinStatus: boolean) => {
    const goals = (store.goals || []) as Goal[];
    const goal = goals.find(g => g.id === goalId);
    
    if (!goal || !selectedWorkspace) {
      showErrorToast('Goal not found or no workspace selected');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await updateGoal({
        goal_id: goalId,
        workspace_name: selectedWorkspace,
        name: goal.title,
        icon: goal.icon,
        is_active: true,
        is_pinned: !currentPinStatus,
      }) as GoalsResponse;

      if (response?.success) {
        showSuccessToast(response.message || `Goal ${!currentPinStatus ? 'pinned' : 'unpinned'} successfully`);
        
        // Update local state immediately for better UX
        const updatedGoals = goals.map(g => 
          g.id === goalId ? { ...g, isPinned: !currentPinStatus } : g
        );
        setUser({ goals: updatedGoals });
        return true;
      } else {
        throw new Error(response?.message || 'Failed to update pin status');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update pin status';
      setError(errorMessage);
      showErrorToast(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [selectedWorkspace, updateGoal, setUser, store.goals]);

  // Get sorted goals (pinned first)
  const getSortedGoals = useCallback((goals: Goal[]) => {
    return [...goals].sort((a, b) => {
      if (a.isPinned === b.isPinned) return 0;
      return a.isPinned ? -1 : 1;
    });
  }, []);

  // Initialize goals on mount
  useEffect(() => {
    if (selectedWorkspace && selectedSocial?.social_id) {
      fetchGoals();
    }
  }, [selectedWorkspace, selectedSocial, fetchGoals]);

  return {
    isLoading,
    error,
    fetchGoals,
    createNewGoal,
    updateExistingGoal,
    deleteExistingGoal,
    toggleGoalPin,
    getSortedGoals,
    transformServerGoal,
  };
};
