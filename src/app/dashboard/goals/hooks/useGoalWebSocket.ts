import { useCallback, useEffect, useRef } from 'react';
import { useWebSocket } from '~/hooks/useWebSocket';
import { useUserStore } from '~/store/userStore';
import { showSuccessToast, showErrorToast } from '~/components/toasts';
import {
  Goal,
  ServerGoal,
  GoalsResponse,
  CreateGoalJob,
  UpdateGoalJob,
  DeleteGoalJob,
  GetGoalsJob,
  GetGoalJob,
} from '../types';

export const useGoalWebSocket = () => {
  const { socket, sendMessage } = useWebSocket();
  const store = useUserStore();
  const setUser = store.setUser;
  const messageHandlersRef = useRef<Map<string, (event: MessageEvent) => void>>(new Map());

  // Transform server goal to client goal format
  const transformServerGoal = useCallback((serverGoal: ServerGoal): Goal => {
    return {
      id: serverGoal.id,
      title: serverGoal.name,
      description: serverGoal.end,
      percentage: serverGoal.progress / 100,
      status: serverGoal.progress === 100 ? 'Completed' : 'In Progress',
      icon: serverGoal.icon || 'bullseye',
      isPinned: serverGoal.is_pinned,
      duration: serverGoal.duration,
      endDate: serverGoal.end,
      startDate: serverGoal.start,
    };
  }, []);

  // Generic WebSocket message handler
  const createMessageHandler = useCallback((
    jobType: string,
    onSuccess: (response: any) => void,
    onError?: (error: string) => void
  ) => {
    return (event: MessageEvent) => {
      try {
        const response = JSON.parse(event.data);
        
        if (response.job === jobType) {
          // Remove the handler after processing
          const handler = messageHandlersRef.current.get(jobType);
          if (handler && socket) {
            socket.removeEventListener('message', handler);
            messageHandlersRef.current.delete(jobType);
          }

          if (response.success) {
            onSuccess(response);
          } else {
            const errorMessage = response.message || `Failed to ${jobType}`;
            if (onError) {
              onError(errorMessage);
            } else {
              showErrorToast(errorMessage);
            }
          }
        }
      } catch (error) {
        console.error(`Error parsing ${jobType} response:`, error);
        const errorMessage = `Failed to process ${jobType} response`;
        if (onError) {
          onError(errorMessage);
        } else {
          showErrorToast(errorMessage);
        }
      }
    };
  }, [socket]);

  // Real-time goal updates listener
  useEffect(() => {
    if (!socket) return;

    const handleGoalUpdate = (event: MessageEvent) => {
      try {
        const response = JSON.parse(event.data);
        
        // Handle real-time goal updates
        if (response.type === 'goal_update' && response.goal) {
          const updatedGoal = transformServerGoal(response.goal);
          const currentGoals = (store.goals || []) as Goal[];
          
          const updatedGoals = currentGoals.map(goal =>
            goal.id === updatedGoal.id ? updatedGoal : goal
          );
          
          setUser({ goals: updatedGoals });
          showSuccessToast('Goal updated in real-time');
        }
        
        // Handle real-time goal deletion
        if (response.type === 'goal_deleted' && response.goal_id) {
          const currentGoals = (store.goals || []) as Goal[];
          const updatedGoals = currentGoals.filter(goal => goal.id !== response.goal_id);
          
          setUser({ goals: updatedGoals });
          showSuccessToast('Goal removed');
        }
        
        // Handle real-time goal creation
        if (response.type === 'goal_created' && response.goal) {
          const newGoal = transformServerGoal(response.goal);
          const currentGoals = (store.goals || []) as Goal[];
          
          setUser({ goals: [...currentGoals, newGoal] });
          showSuccessToast('New goal added');
        }
      } catch (error) {
        console.error('Error handling real-time goal update:', error);
      }
    };

    socket.addEventListener('message', handleGoalUpdate);

    return () => {
      socket.removeEventListener('message', handleGoalUpdate);
    };
  }, [socket, store.goals, setUser, transformServerGoal]);

  // Send WebSocket message with automatic handler setup
  const sendGoalMessage = useCallback((
    message: CreateGoalJob | UpdateGoalJob | DeleteGoalJob | GetGoalsJob | GetGoalJob,
    onSuccess: (response: any) => void,
    onError?: (error: string) => void
  ) => {
    if (!socket) {
      const errorMessage = 'WebSocket not connected';
      if (onError) {
        onError(errorMessage);
      } else {
        showErrorToast(errorMessage);
      }
      return;
    }

    // Create and register message handler
    const handler = createMessageHandler(message.job, onSuccess, onError);
    messageHandlersRef.current.set(message.job, handler);
    socket.addEventListener('message', handler);

    // Send the message
    sendMessage(message);

    // Set timeout to clean up handler if no response
    setTimeout(() => {
      const existingHandler = messageHandlersRef.current.get(message.job);
      if (existingHandler && socket) {
        socket.removeEventListener('message', existingHandler);
        messageHandlersRef.current.delete(message.job);
        
        const timeoutMessage = `Request timeout for ${message.job}`;
        if (onError) {
          onError(timeoutMessage);
        } else {
          showErrorToast(timeoutMessage);
        }
      }
    }, 15000); // 15 second timeout
  }, [socket, sendMessage, createMessageHandler]);

  // Cleanup handlers on unmount
  useEffect(() => {
    return () => {
      if (socket) {
        messageHandlersRef.current.forEach((handler, jobType) => {
          socket.removeEventListener('message', handler);
        });
        messageHandlersRef.current.clear();
      }
    };
  }, [socket]);

  // Subscribe to goal progress updates
  const subscribeToGoalProgress = useCallback((goalId: number) => {
    if (!socket) return;

    sendMessage({
      job: 'subscribe_goal_progress',
      goal_id: goalId,
    });
  }, [socket, sendMessage]);

  // Unsubscribe from goal progress updates
  const unsubscribeFromGoalProgress = useCallback((goalId: number) => {
    if (!socket) return;

    sendMessage({
      job: 'unsubscribe_goal_progress',
      goal_id: goalId,
    });
  }, [socket, sendMessage]);

  // Check WebSocket connection status
  const isConnected = useCallback(() => {
    return socket?.readyState === WebSocket.OPEN;
  }, [socket]);

  return {
    sendGoalMessage,
    subscribeToGoalProgress,
    unsubscribeFromGoalProgress,
    isConnected,
    transformServerGoal,
  };
};
