import { useState, useCallback } from 'react';
import { GoalCreateFormData } from '../types';

export interface ModalState {
  currentStep: number;
  expandedSections: string[];
  isMobile: boolean;
}

export const useModalState = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const [isMobile] = useState(() => typeof window !== 'undefined' && window.innerWidth <= 768);

  const handleNext = useCallback((watchedValues: GoalCreateFormData) => {
    if (currentStep === 1 && watchedValues.name?.trim()) {
      setCurrentStep(2);
    } else if (currentStep === 2) {
      setCurrentStep(3);
    }
  }, [currentStep]);

  const handleBack = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const toggleSection = useCallback((section: string) => {
    setExpandedSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  }, []);

  const resetModal = useCallback(() => {
    setCurrentStep(1);
    setExpandedSections([]);
  }, []);

  return {
    currentStep,
    expandedSections,
    isMobile,
    handleNext,
    handleBack,
    toggleSection,
    resetModal,
    setCurrentStep,
  };
};
