"use client";

import React, { useState, useEffect, useRef, useMemo } from "react";
import Image from "next/image";
import {
  showErrorToast,
  showSuccessToast,
  ToastWrapper,
} from "../../../components/toasts";
import Dashboardbtn from "~/components/dashboardbtn";
import { useUserStore } from "../../../store/userStore";
import { useWebSocket } from "../../../hooks/useWebSocket";
import { usePathname, useRouter } from "next/navigation";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { AnimatedTooltip } from "~/components/ui/animated-tooltip";
import axios from "axios";

interface TeamMember {
  id: number;
  name: string;
  email: string;
  workspaceAccess: number;
  dateJoined: string;
  lastActivity: string;
  avatarUrl: string;
}

interface Activity {
  id: string;
  user: string;
  action: string;
  platform: string;
  timestamp: string;
  avatarUrl: string;
}

const TeamMemberCard: React.FC<{
  member: TeamMember;
  onOpenLastActivity: (member: TeamMember) => void;
  onOpenEditAccess: (member: TeamMember) => void;
  onDelete: (member: TeamMember) => void;
  id: number;
}> = ({ member, onOpenLastActivity, onOpenEditAccess, onDelete, id }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(
    sessionStorage.getItem("accessToken")
  );
  const [openDropdownId, setOpenDropdownId] = useState<number | null>(null);
  const dropdownRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  const { workspacesDetails, setUser, userId, selectedWorkspaceDetails } =
    useUserStore();

  const toggleDropdown = (memberId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (openDropdownId === memberId) {
      setOpenDropdownId(null);
    } else {
      setOpenDropdownId(memberId);
    }
  };

  const openEditModal = () => {
    setIsEditModalOpen(true);
    setShowDropdown(false);
  };

  const closeEditModal = () => {
    console.log("Closing modal");
    setIsEditModalOpen(false);
    setEditingMember(null);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdownId !== null) {
        const dropdownRef = dropdownRefs.current[id];
        const buttonRef = buttonRefs.current[id];

        if (
          dropdownRef &&
          buttonRef &&
          !dropdownRef.contains(event.target as Node) &&
          !buttonRef.contains(event.target as Node)
        ) {
          setOpenDropdownId(null);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openDropdownId, id]);

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <img
            src={member.profile_photo || "/default-avatar.png"}
            alt={member.first_name + " " + member.last_name}
            className="rounded-full mr-4 w-12 h-12"
          />
          <div>
            <h3 className="font-semibold text-lg">
              {member.first_name} {member.last_name}
            </h3>
            <p
              className="text-sm text-gray-500 truncate max-w-[200px]"
              title={member.email}
            >
              {member.email}
            </p>
          </div>
        </div>
        <div className="relative">
          {selectedWorkspaceDetails.owner !== userId && (
            <button
              ref={(el) => (buttonRefs.current[id] = el)}
              className="text-gray-500 hover:bg-gray-100 rounded-full p-2 dropdown-toggle"
              onClick={(e) => toggleDropdown(id, e)}
              aria-label="More options"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="5" cy="12" r="1"></circle>
                <circle cx="12" cy="12" r="1"></circle>
                <circle cx="19" cy="12" r="1"></circle>
              </svg>
            </button>
          )}

          {openDropdownId === id && (
            <div
              ref={(el) => (dropdownRefs.current[id] = el)}
              className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10"
            >
              <div className="py-1">
                <button
                  className="block w-full flex flow-row items-center text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  onClick={() => onOpenLastActivity(member)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="inline mr-2"
                  >
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                  Last Activity
                </button>
                <button
                  className="block w-full flex flow-row items-center text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  onClick={() => openEditModal(member)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="inline mr-2"
                  >
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                  </svg>
                  Edit Access
                </button>
                <button
                  className="block w-full flex flow-row items-center text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                  onClick={() => onDelete(member)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="inline mr-2 text-red-600"
                  >
                    <polyline points="3 6 5 6 21 6"></polyline>
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    <line x1="10" y1="11" x2="10" y2="17"></line>
                    <line x1="14" y1="11" x2="14" y2="17"></line>
                  </svg>
                  Delete
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="space-y-2 pt-4 text-sm">
        <p>
          <span className="text-gray-500">Workspace access:</span>
          <span className="flex flex-row items-center gap-2 mt-1">
            {member.workspaces?.map((workspace, idx) => (
              <span key={idx} className="flex flex-col items-center gap-1">
                <img
                  src={
                    workspace.logo ||
                    `data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 40 40\"><text x=\"50%\" y=\"50%\" font-size=\"20\" text-anchor=\"middle\" dy=\".35em\" fill=\"%23666\">${workspace.workspace_name.charAt(
                      0
                    )}</text></svg>`
                  }
                  alt={workspace.workspace_name}
                  className="w-8 h-8 rounded-full"
                />
                <span className="text-gray-700 text-xs">
                  {workspace.workspace_name}
                  {idx < member.workspaces.length - 1 ? "," : ""}
                </span>
              </span>
            ))}
          </span>
        </p>
        <p>
          <span className="text-gray-500">Date Joined Business Insight:</span>{" "}
          {new Date(member.register_date).toISOString().split("T")[0]}
        </p>
        <p>
          <span className="text-gray-500">Last activity:</span>{" "}
          {member.last_activity}
        </p>
      </div>
    </div>
  );
};

const EditAccessModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  member: TeamMember | null;
}> = ({ isOpen, onClose, onSave, member }) => {
  const {
    addWorkspaceMember,
    getLogs,
    deleteWorkspaceMember,
    toggleAddAccess,
    getEditAccess,
    toggleDeleteAccess,
    editAccess,
    deleteAccess,
  } = useWebSocket();
  const { workspacesDetails } = useUserStore();
  const [enabledWorkspaces, setEnabledWorkspaces] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedPlatforms, setSelectedPlatforms] = useState<{
    [key: string]: Set<string>;
  }>({});
  const { selectedWorkspace, setUser } = useUserStore();

  // Track initial state when modal opens
  const [initialWorkspaceStates, setInitialWorkspaceStates] = useState<{
    [key: string]: boolean;
  }>({});

  // Track initial platform states
  const [initialPlatformStates, setInitialPlatformStates] = useState<{
    [key: string]: Set<string>;
  }>({});

  useEffect(() => {
    if (isOpen && member) {
      getEditAccess({
        target_email: member.email,
      }).then((response) => {
        if (response.success) {
          // Initialize workspace toggles
          const initialStates = response.workspaces.reduce(
            (acc: { [key: string]: boolean }, workspace) => {
              acc[workspace.id] = workspace.has_access;
              return acc;
            },
            {}
          );
          setEnabledWorkspaces(initialStates);
          setInitialWorkspaceStates(initialStates);

          // Initialize platform toggles
          const initialPlatformStates = response.workspaces.reduce(
            (acc: { [key: string]: Set<string> }, workspace) => {
              const enabledPlatforms = new Set(
                workspace.social_accounts
                  .filter((account) => account.has_access)
                  .map((account) => account.platform)
              );
              acc[workspace.id] = enabledPlatforms;
              return acc;
            },
            {}
          );
          setSelectedPlatforms(initialPlatformStates);
          setInitialPlatformStates(initialPlatformStates);
        }
      });
    }
  }, [isOpen, member, getEditAccess]);

  const toggleWorkspace = (workspaceId: string) => {
    setEnabledWorkspaces((prev) => ({
      ...prev,
      [workspaceId]: !prev[workspaceId],
    }));
  };

  const togglePlatform = (workspaceId: string, platform: string) => {
    setSelectedPlatforms((prev) => {
      const workspacePlatforms = prev[workspaceId] || new Set();
      const updatedPlatforms = new Set(workspacePlatforms);

      if (updatedPlatforms.has(platform)) {
        updatedPlatforms.delete(platform);
      } else {
        updatedPlatforms.add(platform);
      }

      return {
        ...prev,
        [workspaceId]: updatedPlatforms,
      };
    });
  };

  const isPlatformSelected = (workspaceId: string, platform: string) => {
    return selectedPlatforms[workspaceId]?.has(platform) || false;
  };

  const handleSave = async () => {
    if (!member) return;

    try {
      const workspaceChanges = [];
      const socialChanges = [];

      // Process workspace access changes
      for (const workspace of workspacesDetails) {
        const currentState = enabledWorkspaces[workspace.id];
        const initialState = initialWorkspaceStates[workspace.id];

        if (currentState !== initialState) {
          workspaceChanges.push({
            workspace_name: workspace.workspace_name,
            target_email: member.email,
            currentAccess: currentState,
          });
        }

        // Only process social changes if workspace is enabled
        if (currentState) {
          const initialPlatforms =
            initialPlatformStates[workspace.id] || new Set();
          const currentPlatforms = selectedPlatforms[workspace.id] || new Set();

          // Find social accounts that need to be added or removed
          workspace.social_accounts?.forEach((social) => {
            const wasEnabled = initialPlatforms.has(social.platform);
            const isEnabled = currentPlatforms.has(social.platform);

            if (wasEnabled !== isEnabled) {
              socialChanges.push({
                workspace_name: workspace.workspace_name,
                target_email: member.email,
                social_id: social.social_id,
                isEnabled: isEnabled,
              });
            }
          });
        }
      }

      // Process all changes
      if (workspaceChanges.length > 0 || socialChanges.length > 0) {
        console.log("Processing workspace changes:", workspaceChanges);
        console.log("Processing social changes:", socialChanges);

        // Execute all changes in parallel
        await Promise.all([
          // Workspace access changes
          ...workspaceChanges.map((change) =>
            change.currentAccess
              ? toggleAddAccess({
                  workspace_name: change.workspace_name,
                  target_email: change.target_email,
                })
              : toggleDeleteAccess({
                  workspace_name: change.workspace_name,
                  target_email: change.target_email,
                })
          ),
          // Social account access changes
          ...socialChanges.map((change) =>
            change.isEnabled
              ? editAccess({
                  workspace_name: change.workspace_name,
                  target_email: change.target_email,
                  social_id: change.social_id,
                })
              : deleteAccess({
                  workspace_name: change.workspace_name,
                  target_email: change.target_email,
                  social_id: change.social_id,
                })
          ),
        ]);

        showSuccessToast("Access updated successfully", "team-toast");
        onClose();
      } else {
        console.log("No changes to save");
        onClose();
      }
    } catch (error) {
      console.error("Error updating access:", error);
      showErrorToast("Failed to update access", "team-toast");
    }
  };

  if (!isOpen || !member) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-[16px] md:rounded-3xl p-6 w-[95%] md:w-[480px] max-w-[90%] max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Edit Access</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div className="space-y-4">
          {workspacesDetails.map((workspace) => (
            <div
              key={workspace.id}
              className="border border-[#CCCCCC] rounded-lg p-4"
            >
              <div
                className={`flex items-center justify-between mb-4 pb-4 ${
                  enabledWorkspaces[workspace.id]
                    ? "border-b-2 border-[#CCCCCC]"
                    : ""
                }`}
              >
                <span className="font-semibold">
                  {workspace.workspace_name}
                </span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={enabledWorkspaces[workspace.id] || false}
                    onChange={() => toggleWorkspace(workspace.id)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
              {enabledWorkspaces[workspace.id] && workspace.social_accounts && (
                <div className="flex space-x-4">
                  {workspace.social_accounts.map((social) => (
                    <div
                      key={social.platform}
                      className="relative cursor-pointer"
                      onClick={() =>
                        togglePlatform(workspace.id, social.platform)
                      }
                    >
                      <div
                        className={`relative ${
                          isPlatformSelected(workspace.id, social.platform)
                            ? "bg-green-500/40 rounded-full"
                            : "grayscale scale-90"
                        }`}
                      >
                        <Image
                          src={
                            social.profile_photo ||
                            `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><text x="50%" y="50%" font-size="20" text-anchor="middle" dy=".35em" fill="%23666">${social.username.charAt(
                              0
                            )}</text></svg>`
                          }
                          alt={social.username}
                          width={40}
                          height={40}
                          className={`rounded-full ${
                            isPlatformSelected(workspace.id, social.platform)
                              ? ""
                              : "grayscale scale-90"
                          }`}
                        />
                        <div className="absolute bottom-0 right-0">
                          <Image
                            src={`/icons/${social.platform}.svg`}
                            alt={social.platform}
                            width={
                              isPlatformSelected(workspace.id, social.platform)
                                ? 20
                                : 16
                            }
                            height={
                              isPlatformSelected(workspace.id, social.platform)
                                ? 20
                                : 16
                            }
                            className="rounded-full"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
        <div className="mt-6 flex justify-end">
          <Dashboardbtn
            onClick={handleSave}
            className="py-4 md:px-32 w-full bg-[#263645] md:text-xl text-nowrap text-md px-16 rounded-md text-white hover:bg-[#DADDE1] hover:text-[#263645] font-bold transition-all duration-200 relative overflow-hidden"
          >
            <span className="relative z-10 flex items-center justify-center">
              Save Changes
            </span>
          </Dashboardbtn>
        </div>
      </div>
    </div>
  );
};

const TeamPage: React.FC = () => {
  const [inviteEmail, setInviteEmail] = useState("");
  const pathname = usePathname();
  const [emailError, setEmailError] = useState("");
  const { addWorkspaceMember, getLogs, deleteWorkspaceMember } = useWebSocket();
  const { selectedWorkspaceDetails, selectedWorkspace } = useUserStore();

  const { workspacesDetails, setUser } = useUserStore();

  const { userId } = useUserStore();
  const router = useRouter();

  useEffect(() => {
    if (selectedWorkspaceDetails && selectedWorkspaceDetails.owner != userId) {
      showErrorToast("You don't have access to this page", "team-toast");
      router.push("/dashboard");
    }
  }, [selectedWorkspace, pathname]);

  // Add email validation function
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleDeleteMember = async (member: TeamMember) => {
    await deleteWorkspaceMember({
      workspace_name: selectedWorkspace,
      target_email: member.email,
    }).then((response) => {
      if (response.job === "delete_workspace_member") {
        // Update selectedWorkspaceDetails by filtering out the deleted member
        const updatedSelectedWorkspaceDetails = {
          ...selectedWorkspaceDetails,
          members: selectedWorkspaceDetails.members.filter(
            (m) => m.email !== member.email
          ),
        };
        setUser({
          selectedWorkspaceDetails: updatedSelectedWorkspaceDetails,
        });

        // Find and update the specific workspace's members
        const updatedWorkspaces = workspacesDetails.map((workspace) => {
          if (workspace.workspace_name === selectedWorkspace) {
            return {
              ...workspace,
              members: workspace.members.filter(
                (m) => m.email !== member.email
              ),
            };
          }
          return workspace;
        });

        console.log("updatedWorkspaces", updatedWorkspaces);

        // Update zustand store with new workspaces array
        setUser({
          workspacesDetails: updatedWorkspaces,
        });

        console.log("response of delete member", response);
        if (response.success) {
          showSuccessToast("Member deleted successfully", "team-toast");
        } else {
          showErrorToast("Failed to delete member", "team-toast");
        }
      }
    });
  };

  const handleInvite = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous error
    setEmailError("");

    // Validate email
    if (!inviteEmail) {
      setEmailError("Email is required");
      return;
    }

    if (!validateEmail(inviteEmail)) {
      setEmailError("Please enter a valid email address");
      return;
    }

    try {
      // Call your websocket method here
      await addWorkspaceMember({
        workspace_name: selectedWorkspace,
        email: inviteEmail,
      }).then((response) => {
        if (response.success) {
          showSuccessToast("Invitation sent successfully", "team-toast");
        } else {
          showErrorToast(response.message, "team-toast");
        }
      });

      // Clear form on success
      setInviteEmail("");
    } catch (error) {
      console.error("Error sending invitation:", error);
      showErrorToast(
        "Failed to send invitation. Please try again.",
        "team-toast"
      );
    }
  };

  const teamMembers = useMemo(() => {
    return selectedWorkspaceDetails?.members;
  }, [selectedWorkspaceDetails, selectedWorkspace, pathname]);

  console.log("Selected Workspace:", selectedWorkspace);
  console.log("Team Members:", teamMembers);

  const [activities, setActivities] = useState<any>();

  const [showDropdown, setShowDropdown] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(
    sessionStorage.getItem("accessToken")
  );

  console.log(isEditModalOpen);

  const [isLastActivityModalOpen, setIsLastActivityModalOpen] = useState(false);
  const [selectedMemberActivities, setSelectedMemberActivities] = useState<
    Activity[]
  >([]);

  const openLastActivityModal = async (member: TeamMember) => {
    console.log("member", member);

    await getLogs({
      target_email: member.email,
    }).then((response) => {
      console.log("response of logs of member", response);
      setActivities(response.logs);
    });
    // setSelectedMemberActivities();
    setIsLastActivityModalOpen(true);
  };

  const openEditAccessModal = (member: TeamMember) => {
    setEditingMember(member);
    setIsEditModalOpen(true);
  };

  const closeLastActivityModal = () => {
    setIsLastActivityModalOpen(false);
    setSelectedMemberActivities([]);
  };

  const handleCopyInviteLink = async () => {
    // Implement copy invite link logic here
    // Create a temporary input element to copy the text
    // const csrfResponse = await fetch(
    //   `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
    //   {
    //     method: "GET",
    //   },
    // );
    // const csrfData = await csrfResponse.json() as CSRFResponse;
    // const csrfToken: string = csrfData.csrf;
    const csrfToken = useUserStore.getState().csrfToken;

    const { data: inviteLinkData } = await axios.get(
      `${process.env.NEXT_PUBLIC_API_URL}/api/ws-invite/`,
      {
        params: {
          workspace_name: selectedWorkspace,
        },
        headers: {
          Authorization: `Bearer ${accessToken}`,
          csrf_token: csrfToken,
        },
      }
    );

    console.log(inviteLinkData);

    const tempInput = document.createElement("input");
    tempInput.value = inviteLinkData.url;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand("copy");
    document.body.removeChild(tempInput);

    // Show success toast
    return showSuccessToast("Invite link copied to clipboard", "team-toast");
  };

  const [openDropdownId, setOpenDropdownId] = useState<number | null>(null);
  const dropdownRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});
  const buttonRefs = useRef<{ [key: number]: HTMLButtonElement | null }>({});

  const toggleDropdown = (memberId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (openDropdownId === memberId) {
      setOpenDropdownId(null);
    } else {
      setOpenDropdownId(memberId);
    }
  };

  const openEditModal = () => {
    setIsEditModalOpen(true);
    setShowDropdown(false);
  };

  const closeEditModal = () => {
    console.log("Closing modal");
    setIsEditModalOpen(false);
    setEditingMember(null);
  };

  const saveChanges = () => {
    // Implement save logic here
    closeEditModal();
  };

  const togglePlatform = (platform: string) => {
    setEnabledPlatforms((prev) =>
      prev.indexOf(platform) !== -1
        ? prev.filter((p) => p !== platform)
        : [...prev, platform]
    );
  };

  const [enabledPlatforms, setEnabledPlatforms] = useState<string[]>([]);

  const [enabledTeslaPlatforms, setEnabledTeslaPlatforms] = useState<string[]>(
    []
  );

  const [showSocialMedia, setShowSocialMedia] = useState(false);
  const [showTeslaSocialMedia, setShowTeslaSocialMedia] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdownId !== null) {
        const dropdownRef = dropdownRefs.current[openDropdownId];
        const buttonRef = buttonRefs.current[openDropdownId];

        if (
          dropdownRef &&
          buttonRef &&
          !dropdownRef.contains(event.target as Node) &&
          !buttonRef.contains(event.target as Node)
        ) {
          setOpenDropdownId(null);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openDropdownId]);

  return (
    <div className="p-4 md:p-6 relative md:mt-0 mt-[8vh]">
      <h1 className="text-2xl md:text-3xl font-bold mb-4 md:mb-6">Team</h1>

      {!selectedWorkspace ? (
        <div className="text-center py-8">
          <p className="text-gray-600 text-lg">
            Please select a workspace to view team members
          </p>
        </div>
      ) : (
        <>
          <div className="bg-[#005EDD]/5 md:p-4 p-2 rounded-3xl mb-6 md:mb-8">
            <form
              onSubmit={handleInvite}
              className="flex flex-col md:flex-row items-center justify-between gap-2 md:gap-4 md:px-4 px-4 py-2"
            >
              <h2 className="text-lg font-semibold md:text-xl mb-2 md:mb-0 self-center text-nowrap">
                Invite new members
              </h2>
              <div className="relative flex flex-row bg-white p-2 rounded-3xl w-full">
                <input
                  type="email"
                  placeholder="Enter Email To Invite"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  className={`w-full p-2 pr-12 border-none rounded-3xl focus:outline-none  ${
                    emailError ? "border-red-500" : ""
                  }`}
                  required
                />
                {emailError && (
                  <span className="text-red-500 text-sm mt-1">
                    {emailError}
                  </span>
                )}
                <button
                  type="submit"
                  className="sendbtn px-4 py-2 rounded-2xl bg-gray-500/10 hover:bg-blue-600 transition-colors duration-200"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="hover:text-white transition-colors duration-200 text-blue-600"
                  >
                    <path d="M22 2L11 13" />
                    <path d="M22 2L15 22L11 13L2 9L22 2Z" />
                  </svg>
                </button>
              </div>
              <div className="flex flex-col md:flex-row items-center mt-2 md:mt-0">
                <span className="mb-2 md:mb-0 md:mx-4">Or</span>
                <button
                  type="button"
                  onClick={handleCopyInviteLink}
                  className="text-gray-800 underline flex items-center decoration-blue-600 decoration-dotted underline-offset-8 md:text-base text-lg text-nowrap"
                >
                  Copy Invite link{" "}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="inline ml-1 md:w-4 md:h-4 w-5 h-5"
                  >
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                  </svg>
                </button>
              </div>
            </form>
          </div>

          <div className="md:hidden">
            {teamMembers
              ?.filter(
                (member: {
                  workspaces?: { workspace_name: string; role: string }[];
                }) =>
                  member.workspaces?.some(
                    (workspace: { workspace_name: string; role: string }) =>
                      workspace.workspace_name === selectedWorkspace &&
                      workspace.role !== "owner"
                  )
              )
              .map((member: any, index: number) => {
                // Move TeamMemberCard render into a separate variable to avoid setState during render
                const memberCard = (
                  <TeamMemberCard
                    key={member.id}
                    member={member}
                    onOpenLastActivity={openLastActivityModal}
                    onOpenEditAccess={openEditAccessModal}
                    onDelete={handleDeleteMember}
                    id={index}
                  />
                );
                return memberCard;
              })}
          </div>

          <div className="hidden md:block">
            <div className="space-y-4">
              {teamMembers
                .filter((member) =>
                  member.workspaces?.some(
                    (workspace) =>
                      workspace.workspace_name === selectedWorkspace &&
                      workspace.role !== "owner"
                  )
                )
                .map((member, index) => (
                  <div
                    key={index}
                    className="flex flex-col md:flex-row items-start md:items-center justify-between border-b gap-4 py-4"
                  >
                    <div className="flex items-center space-x-4 mb-2 md:mb-0 w-2/4">
                      <img
                        src={member.profile_photo || "/default-avatar.png"}
                        alt={member.first_name + " " + member.last_name}
                        className="rounded-full w-16 h-16"
                      />
                      <div>
                        <div className="font-semibold text-lg">
                          {member.first_name} {member.last_name}
                        </div>
                        <div
                          className="text-sm text-gray-500 truncate max-w-[200px]"
                          title={member.email}
                        >
                          {member.email}
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-4 w-3/5 gap-8">
                      <div className="text-center">
                        <AnimatedTooltip
                          items={
                            member.workspaces?.map((workspace, idx) => ({
                              id: idx,
                              name: workspace.workspace_name,
                              designation: "Workspace",
                              image:
                                workspace.logo ||
                                `data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 40 40\"><text x=\"50%\" y=\"50%\" font-size=\"20\" text-anchor=\"middle\" dy=\".35em\" fill=\"%23666\">${workspace.workspace_name.charAt(
                                  0
                                )}</text></svg>`,
                            })) || []
                          }
                        />
                        <div className="text-sm text-gray-500 mt-2">
                          Workspace access
                        </div>
                      </div>
                      <div className="text-center flex flex-col justify-center">
                        <div className="font-semibold whitespace-nowrap">
                          {
                            new Date(member.register_date)
                              .toISOString()
                              .split("T")[0]
                          }
                        </div>
                        <div className="text-sm text-gray-500">
                          Date Joined Business Insight
                        </div>
                      </div>
                      <div className="text-center flex flex-col justify-center">
                        <div className="font-semibold">
                          {member.last_activity
                            ? new Date(member.last_activity)
                                .toISOString()
                                .split("T")[0]
                            : "N/A"}
                        </div>
                        <div className="text-sm text-gray-500">
                          Last activity
                        </div>
                      </div>
                      <div className="flex justify-end items-center">
                        <button
                          ref={(el) => (buttonRefs.current[index] = el)}
                          className="text-gray-500 hover:bg-gray-100 rounded-full p-2 dropdown-toggle"
                          onClick={(e) => toggleDropdown(index, e)}
                          aria-label="More options"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <circle cx="5" cy="12" r="1"></circle>
                            <circle cx="12" cy="12" r="1"></circle>
                            <circle cx="19" cy="12" r="1"></circle>
                          </svg>
                        </button>
                        {openDropdownId === index && (
                          <div
                            ref={(el) => (dropdownRefs.current[index] = el)}
                            className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10"
                          >
                            <div className="py-1">
                              <button
                                className="block w-full flex flow-row items-center text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                onClick={() => openLastActivityModal(member)}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="inline mr-2"
                                >
                                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                  <polyline points="14 2 14 8 20 8"></polyline>
                                  <line x1="16" y1="13" x2="8" y2="13"></line>
                                  <line x1="16" y1="17" x2="8" y2="17"></line>
                                  <polyline points="10 9 9 9 8 9"></polyline>
                                </svg>
                                Last Activity
                              </button>
                              <button
                                className="block w-full flex flow-row items-center text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                onClick={() => openEditAccessModal(member)}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="inline mr-2"
                                >
                                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                </svg>
                                Edit Access
                              </button>
                              <button
                                className="block w-full flex flow-row items-center text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                                onClick={() => handleDeleteMember(member)}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="inline mr-2 text-red-600"
                                >
                                  <polyline points="3 6 5 6 21 6"></polyline>
                                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                  <line x1="10" y1="11" x2="10" y2="17"></line>
                                  <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                                Delete
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </>
      )}

      {isEditModalOpen && (
        <EditAccessModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setEditingMember(null);
          }}
          onSave={() => {
            saveChanges;
          }}
          member={editingMember}
        />
      )}
      {isLastActivityModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white md:rounded-3xl rounded-[16px] p-6 w-[800px] max-w-[90%] max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-bold">Last Activity</h2>
              <button
                onClick={closeLastActivityModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <div className="space-y-4">
              {activities && activities.length > 0 ? (
                activities.map((activity) => (
                  <div
                    key={activity.date_time}
                    className="flex items-center space-x-4 py-2 bg-gray-100 rounded-lg p-4"
                  >
                    <Image
                      src={activity.user.profile_photo || "/default-avatar.png"}
                      alt={`${activity.user.first_name} ${activity.user.last_name}`}
                      width={32}
                      height={32}
                      className="rounded-full"
                    />
                    <div className="grow">
                      <div className="font-semibold text-sm">
                        {activity.user.first_name} {activity.user.last_name}
                      </div>
                      <div className="text-sm">{activity.log}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-gray-500">
                        {new Date(activity.date_time).toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  No activity logs found
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {/* <ToastWrapper containerId="team-toast"/> */}
    </div>
  );
};

export default TeamPage;
