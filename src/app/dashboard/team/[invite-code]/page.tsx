"use client"

import { useState, useEffect } from "react";
import { useWebSocket } from "~/hooks/useWebSocket";
import { useRouter, useParams } from "next/navigation";
import { showErrorToast, showSuccessToast, ToastWrapper } from "~/components/toasts";

const TeamInvitePage = () => {
    const router = useRouter();
    const params = useParams();
    const { acceptInvite } = useWebSocket();
    const [access, setAccess] = useState(sessionStorage.getItem("accessToken"))
    const inviteCode = params?.["invite-code"] as string;

    console.log("Invite code:", inviteCode);

    useEffect(() => {
      const handleInvite = async () => {
        if (!access || !inviteCode) return;

        try {
          const response = await acceptInvite({invite_code: inviteCode});
          
          if (response.success) {
            showSuccessToast("Invite accepted successfully", "teaminvite-toast");
            setTimeout(() => {
              window.location.href = "/dashboard/workspaces";
            }, 500);
          } else {
            showErrorToast(response.message, "teaminvite-toast"); 
            setTimeout(() => {
              window.location.href = "/dashboard";
            }, 500);          }
        } catch (error) {
          showErrorToast("Error accepting invite", "teaminvite-toast");
          router.push("/dashboard");
        }
      };

      // Small delay to ensure everything is loaded
      const timer = setTimeout(handleInvite, 650);
      return () => clearTimeout(timer);

    }, [access, inviteCode, router, acceptInvite]);


    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        {/* <ToastWrapper containerId="teaminvite-toast"/> */}
      </div>
    );
};

export default TeamInvitePage;