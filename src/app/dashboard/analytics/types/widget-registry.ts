import { ComponentType } from "react";
import { WidgetSize } from "../../../../types/drag-drop";

// Widget configuration interface
export interface WidgetConfig {
  type: string;
  title: string;
  description: string;
  category: WidgetCategory;
  minSize: WidgetSize;
  maxSize: WidgetSize;
  defaultSize: WidgetSize;
  props?: Record<string, any>;
  tags?: string[];
  isEnabled?: boolean;
}

// Widget categories for organization
export type WidgetCategory =
  | "engagement"
  | "demographics"
  | "performance"
  | "reach"
  | "content"
  | "followers"
  | "interactions";

// Widget component props interface
export interface WidgetComponentProps {
  data?: any[];
  timeRange?: "7d" | "30d";
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
  isLoading?: boolean;
  error?: Error | null;
  onRetry?: () => void;
  // Additional props can be passed through
  [key: string]: any;
}

// Widget preview props interface
export interface WidgetPreviewProps {
  config: WidgetConfig;
  isSelected?: boolean;
  onClick?: () => void;
  className?: string;
}

// Widget registry entry
export interface WidgetRegistryEntry {
  component: ComponentType<WidgetComponentProps>;
  config: WidgetConfig;
  preview: ComponentType<WidgetPreviewProps>;
  category: WidgetCategory;
}

// Widget registry interface
export interface WidgetRegistry {
  [widgetType: string]: WidgetRegistryEntry;
}

// Widget factory interface
export interface WidgetFactory {
  createWidget: (
    type: string,
    props?: WidgetComponentProps
  ) => JSX.Element | null;
  createPreview: (
    type: string,
    props?: WidgetPreviewProps
  ) => JSX.Element | null;
  getWidgetConfig: (type: string) => WidgetConfig | null;
  getAvailableWidgets: () => WidgetConfig[];
  getWidgetsByCategory: (category: WidgetCategory) => WidgetConfig[];
  isWidgetSupported: (type: string) => boolean;
}

// Widget creation options
export interface CreateWidgetOptions {
  id?: string;
  position?: { row: number; col: number };
  size?: WidgetSize;
  props?: Record<string, any>;
}
