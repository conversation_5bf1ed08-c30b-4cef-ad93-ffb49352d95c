// Analytics data types
export interface AnalyticsDataPoint {
  total_value: number;
  date?: string;
  breakdown?: {
    // Mirrors API breakdown dimension_keys -> dimension_values
    // Keep flexible for best DX across providers and future keys
    media_product_type?:
      | 'POST'
      | 'REEL'
      | 'STORY'
      | 'VIDEO'
      | 'CAROUSEL_CONTAINER'
      | 'IGTV'
      | 'post'
      | 'reel'
      | 'story'
      | 'video'
      | string;
    follow_type?: 'FOLLOWER' | 'NON_FOLLOWER' | 'follow' | 'unfollow' | 'follower' | 'non_follower' | string;
    follower_type?: 'FOLLOWER' | 'NON_FOLLOWER' | 'follower' | 'non_follower' | string;
    contact_button_type?: 'email' | 'phone' | 'address' | 'text' | string;
    age?: string;
    gender?: 'M' | 'F' | 'U' | 'male' | 'female' | 'other' | string;
    country?: string;
    city?: string;
    [key: string]: any;
  };
  percentage?: number;
}

export interface AnalyticsData {
  accounts_engaged: AnalyticsDataPoint[];
  comments: AnalyticsDataPoint[];
  engaged_audience_demographics: AnalyticsDataPoint[];
  follows_and_unfollows: AnalyticsDataPoint[];
  follower_demographics: AnalyticsDataPoint[];
  likes: AnalyticsDataPoint[];
  profile_links_taps: AnalyticsDataPoint[];
  reach: AnalyticsDataPoint[];
  replies: AnalyticsDataPoint[];
  saves: AnalyticsDataPoint[];
  shares: AnalyticsDataPoint[];
  total_interactions: AnalyticsDataPoint[];
  views: AnalyticsDataPoint[];
  follower_count?: AnalyticsDataPoint[];
  // Optionally carried alongside normalized series for UI consumption
  top_posts?: any[];
  // Optional counters from raw payload
  followers_count?: number;
  follows_count?: number;
}

export type TimeRange = '7d' | '30d';

export type MetricType = keyof AnalyticsData;

export interface ChartProps {
  data: AnalyticsDataPoint[];
  timeRange?: TimeRange;
}

export interface DemographicChartProps {
  data: AnalyticsDataPoint[];
}

// Chart data interfaces
export interface ChartDataPoint {
  day: string;
  date: string;
  [key: string]: any;
}

export interface BreakdownDataPoint {
  type: string;
  [key: string]: any;
}

// Metric configuration
export interface MetricConfig {
  id: MetricType;
  label: string;
  icon: string;
  description: string;
  scale: 'daily' | 'lifetime';
  hasBreakdown?: boolean;
  breakdownTypes?: string[];
}
