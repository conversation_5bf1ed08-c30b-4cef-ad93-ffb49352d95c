"use client";

import React, {
  createContext,
  useContext,
  useReducer,
  useCallback,
  useEffect,
} from "react";
import {
  DragDropContextType,
  DragDropState,
  DragDropAction,
  WidgetLayout,
  WidgetConfig,
  WidgetSize,
  GridPosition,
  DragDropError,
} from "@/types/drag-drop";

// Initial state for the drag-drop system
const initialState: DragDropState = {
  widgets: [],
  isDragging: false,
  draggedWidget: null,
  history: [[]],
  historyIndex: 0,
  maxHistorySize: 50,
};

// Drag-drop reducer function
function dragDropReducer(
  state: DragDropState,
  action: DragDropAction
): DragDropState {
  switch (action.type) {
    case "MOVE_WIDGET": {
      const { dragId, hoverId } = action.payload;
      const dragIndex = state.widgets.findIndex((w) => w.id === dragId);
      const hoverIndex = state.widgets.findIndex((w) => w.id === hoverId);

      if (dragIndex === -1 || hoverIndex === -1) return state;

      const newWidgets = [...state.widgets];
      const draggedWidget = newWidgets[dragIndex];
      const hoverWidget = newWidgets[hoverIndex];

      // Swap positions
      newWidgets[dragIndex] = {
        ...hoverWidget,
        position: draggedWidget.position,
      };
      newWidgets[hoverIndex] = {
        ...draggedWidget,
        position: hoverWidget.position,
      };

      return addToHistory(state, newWidgets);
    }

    case "ADD_WIDGET": {
      const { widget, position } = action.payload;

      // Generate unique ID for the widget
      const widgetId = `${widget.type}-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      // Find next available position if not provided
      const widgetPosition =
        position ||
        findNextAvailablePosition(state.widgets, widget.defaultSize);

      const newWidget: WidgetLayout = {
        id: widgetId,
        type: widget.type,
        position: widgetPosition,
        size: widget.defaultSize,
        config: widget,
        zIndex: 1,
      };

      const newWidgets = [...state.widgets, newWidget];
      return addToHistory(state, newWidgets);
    }

    case "REMOVE_WIDGET": {
      const { widgetId } = action.payload;
      const newWidgets = state.widgets.filter((w) => w.id !== widgetId);
      return addToHistory(state, newWidgets);
    }

    case "UPDATE_WIDGET_SIZE": {
      const { widgetId, size } = action.payload;
      const newWidgets = state.widgets.map((widget) =>
        widget.id === widgetId ? { ...widget, size } : widget
      );
      return addToHistory(state, newWidgets);
    }

    case "UPDATE_WIDGET_POSITION": {
      const { widgetId, position } = action.payload;
      const newWidgets = state.widgets.map((widget) =>
        widget.id === widgetId ? { ...widget, position } : widget
      );
      return addToHistory(state, newWidgets);
    }

    case "SET_DRAGGING": {
      const { isDragging, draggedWidget } = action.payload;
      return {
        ...state,
        isDragging,
        draggedWidget,
      };
    }

    case "LOAD_LAYOUT": {
      const { layout } = action.payload;
      return {
        ...state,
        widgets: layout,
        history: [layout],
        historyIndex: 0,
      };
    }

    case "RESET_LAYOUT": {
      return {
        ...state,
        widgets: [],
        history: [[]],
        historyIndex: 0,
      };
    }

    case "UNDO_LAYOUT": {
      if (state.historyIndex > 0) {
        const newIndex = state.historyIndex - 1;
        return {
          ...state,
          widgets: state.history[newIndex],
          historyIndex: newIndex,
        };
      }
      return state;
    }

    case "REDO_LAYOUT": {
      if (state.historyIndex < state.history.length - 1) {
        const newIndex = state.historyIndex + 1;
        return {
          ...state,
          widgets: state.history[newIndex],
          historyIndex: newIndex,
        };
      }
      return state;
    }

    default:
      return state;
  }
}

// Helper function to add layout to history
function addToHistory(
  state: DragDropState,
  newWidgets: WidgetLayout[]
): DragDropState {
  const newHistory = state.history.slice(0, state.historyIndex + 1);
  newHistory.push(newWidgets);

  // Limit history size
  if (newHistory.length > state.maxHistorySize) {
    newHistory.shift();
  }

  return {
    ...state,
    widgets: newWidgets,
    history: newHistory,
    historyIndex: newHistory.length - 1,
  };
}

// Helper function to find next available position in grid
function findNextAvailablePosition(
  widgets: WidgetLayout[],
  size: WidgetSize,
  gridColumns: number = 12
): GridPosition {
  const occupiedPositions = new Set<string>();

  // Mark all occupied positions
  widgets.forEach((widget) => {
    for (
      let row = widget.position.row;
      row < widget.position.row + widget.size.height;
      row++
    ) {
      for (
        let col = widget.position.col;
        col < widget.position.col + widget.size.width;
        col++
      ) {
        occupiedPositions.add(`${row}-${col}`);
      }
    }
  });

  // Find first available position
  let row = 0;
  while (true) {
    for (let col = 0; col <= gridColumns - size.width; col++) {
      let canPlace = true;

      // Check if this position is available
      for (let r = row; r < row + size.height && canPlace; r++) {
        for (let c = col; c < col + size.width && canPlace; c++) {
          if (occupiedPositions.has(`${r}-${c}`)) {
            canPlace = false;
          }
        }
      }

      if (canPlace) {
        return { row, col };
      }
    }
    row++;
  }
}

// Create the context
const DragDropContext = createContext<DragDropContextType | null>(null);

// Context provider component
interface DragDropProviderProps {
  children: React.ReactNode;
  initialLayout?: WidgetLayout[];
  onLayoutChange?: (layout: WidgetLayout[]) => void;
  onError?: (error: DragDropError) => void;
}

export function DragDropProvider({
  children,
  initialLayout = [],
  onLayoutChange,
  onError,
}: DragDropProviderProps) {
  const [state, dispatch] = useReducer(dragDropReducer, {
    ...initialState,
    widgets: initialLayout,
    history: [initialLayout],
  });

  // Notify parent component of layout changes
  useEffect(() => {
    if (onLayoutChange) {
      onLayoutChange(state.widgets);
    }
  }, [state.widgets, onLayoutChange]);

  // Context value with memoized functions
  const contextValue: DragDropContextType = {
    widgets: state.widgets,
    isDragging: state.isDragging,
    draggedWidget: state.draggedWidget,

    moveWidget: useCallback(
      (dragId: string, hoverId: string) => {
        try {
          dispatch({ type: "MOVE_WIDGET", payload: { dragId, hoverId } });
        } catch (error) {
          onError?.({
            type: "DRAG_ERROR",
            message: "Failed to move widget",
            widgetId: dragId,
            originalError: error as Error,
          });
        }
      },
      [onError]
    ),

    addWidget: useCallback(
      (widget: WidgetConfig, position?: GridPosition) => {
        try {
          dispatch({ type: "ADD_WIDGET", payload: { widget, position } });
        } catch (error) {
          onError?.({
            type: "LAYOUT_ERROR",
            message: "Failed to add widget",
            originalError: error as Error,
          });
        }
      },
      [onError]
    ),

    removeWidget: useCallback(
      (widgetId: string) => {
        try {
          dispatch({ type: "REMOVE_WIDGET", payload: { widgetId } });
        } catch (error) {
          onError?.({
            type: "LAYOUT_ERROR",
            message: "Failed to remove widget",
            widgetId,
            originalError: error as Error,
          });
        }
      },
      [onError]
    ),

    updateWidgetSize: useCallback(
      (widgetId: string, size: WidgetSize) => {
        try {
          dispatch({ type: "UPDATE_WIDGET_SIZE", payload: { widgetId, size } });
        } catch (error) {
          onError?.({
            type: "LAYOUT_ERROR",
            message: "Failed to update widget size",
            widgetId,
            originalError: error as Error,
          });
        }
      },
      [onError]
    ),

    updateWidgetPosition: useCallback(
      (widgetId: string, position: GridPosition) => {
        try {
          dispatch({
            type: "UPDATE_WIDGET_POSITION",
            payload: { widgetId, position },
          });
        } catch (error) {
          onError?.({
            type: "LAYOUT_ERROR",
            message: "Failed to update widget position",
            widgetId,
            originalError: error as Error,
          });
        }
      },
      [onError]
    ),
  };

  return (
    <DragDropContext.Provider value={contextValue}>
      {children}
    </DragDropContext.Provider>
  );
}

// Custom hook to use the drag-drop context
export function useDragDrop(): DragDropContextType {
  const context = useContext(DragDropContext);
  if (!context) {
    throw new Error("useDragDrop must be used within a DragDropProvider");
  }
  return context;
}

// Additional utility hooks
export function useDragDropActions() {
  const {
    moveWidget,
    addWidget,
    removeWidget,
    updateWidgetSize,
    updateWidgetPosition,
  } = useDragDrop();
  return {
    moveWidget,
    addWidget,
    removeWidget,
    updateWidgetSize,
    updateWidgetPosition,
  };
}

export function useDragDropState() {
  const { widgets, isDragging, draggedWidget } = useDragDrop();
  return { widgets, isDragging, draggedWidget };
}
