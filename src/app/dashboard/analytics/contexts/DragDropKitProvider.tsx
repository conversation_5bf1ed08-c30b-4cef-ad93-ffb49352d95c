"use client";

import React, { useState, useCallback } from "react";
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  DragOverEvent,
  DragMoveEvent,
  DragCancelEvent,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import { DragDropProvider, useDragDrop } from "./DragDropContext";
import { WidgetLayout, DragDropError } from "@/types/drag-drop";

interface DragDropKitProviderProps {
  children: React.ReactNode;
  initialLayout?: WidgetLayout[];
  onLayoutChange?: (layout: WidgetLayout[]) => void;
  onError?: (error: DragDropError) => void;
}

// Inner component that uses the DragDropContext
function DragDropKitInner({ children }: { children: React.ReactNode }) {
  const { widgets, moveWidget, isDragging, draggedWidget } = useDragDrop();
  const [activeId, setActiveId] = useState<string | null>(null);

  // Configure sensors for drag interactions
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Minimum distance before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag start
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
  }, []);

  // Handle drag over (for real-time feedback)
  const handleDragOver = useCallback((event: DragOverEvent) => {
    // This can be used for real-time visual feedback
    // Currently just logging for debugging
    console.log("Drag over:", event);
  }, []);

  // Handle drag move (for continuous feedback)
  const handleDragMove = useCallback((event: DragMoveEvent) => {
    // This can be used for continuous visual feedback
    // Currently just logging for debugging
    console.log("Drag move:", event);
  }, []);

  // Handle drag end
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (over && active.id !== over.id) {
        moveWidget(active.id as string, over.id as string);
      }

      setActiveId(null);
    },
    [moveWidget]
  );

  // Handle drag cancel
  const handleDragCancel = useCallback((event: DragCancelEvent) => {
    setActiveId(null);
  }, []);

  // Get the currently dragged widget for overlay
  const activeWidget = widgets.find((widget) => widget.id === activeId);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
    >
      <SortableContext
        items={widgets.map((widget) => widget.id)}
        strategy={rectSortingStrategy}
      >
        {children}
      </SortableContext>

      {/* Drag overlay for visual feedback */}
      <DragOverlay>
        {activeWidget ? (
          <div className="bg-white shadow-lg rounded-lg border-2 border-blue-500 opacity-90 transform rotate-3">
            <div className="p-4">
              <h3 className="font-semibold text-sm text-gray-800">
                {activeWidget.config.title}
              </h3>
              <p className="text-xs text-gray-600 mt-1">
                {activeWidget.config.description || "Moving widget..."}
              </p>
            </div>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
}

// Main provider component that combines both contexts
export function DragDropKitProvider({
  children,
  initialLayout = [],
  onLayoutChange,
  onError,
}: DragDropKitProviderProps) {
  return (
    <DragDropProvider
      initialLayout={initialLayout}
      onLayoutChange={onLayoutChange}
      onError={onError}
    >
      <DragDropKitInner>{children}</DragDropKitInner>
    </DragDropProvider>
  );
}

// Export the inner component for cases where DragDropProvider is already available
export { DragDropKitInner };
