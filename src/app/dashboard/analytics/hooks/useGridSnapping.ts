"use client";

import { use<PERSON><PERSON><PERSON>, useMemo, useRef, useEffect, useState } from "react";
import {
  WidgetLayout,
  GridPosition,
  WidgetSize,
  GridConfig,
  DragMoveEvent,
  DragEndEvent,
} from "@/types/drag-drop";
import {
  GridCollisionDetector,
  Grid<PERSON>napper,
  AutoLayoutManager,
  createCollisionDetector,
  createGridSnapper,
  createAutoLayoutManager,
} from "../utils/collision-detection";
import { DEFAULT_GRID_CONFIG } from "../utils/grid-utils";

interface UseGridSnappingOptions {
  gridConfig?: GridConfig;
  containerRef?: React.RefObject<HTMLElement>;
  snapThreshold?: number;
  enableAutoLayout?: boolean;
  onCollisionDetected?: (
    widget: WidgetLayout,
    conflicts: WidgetLayout[]
  ) => void;
  onPositionSnapped?: (widget: WidgetLayout, newPosition: GridPosition) => void;
}

interface UseGridSnappingReturn {
  snapToGrid: (
    widget: WidgetLayout,
    pixelPosition: { x: number; y: number },
    otherWidgets: WidgetLayout[]
  ) => GridPosition;
  checkCollision: (
    widget: WidgetLayout,
    otherWidgets: WidgetLayout[]
  ) => boolean;
  resolveCollisions: (
    widget: WidgetLayout,
    otherWidgets: WidgetLayout[]
  ) => WidgetLayout[];
  compactLayout: (widgets: WidgetLayout[]) => WidgetLayout[];
  insertWidget: (
    widget: WidgetLayout,
    existingWidgets: WidgetLayout[],
    preferredPosition?: GridPosition
  ) => { widget: WidgetLayout; updatedWidgets: WidgetLayout[] };
  handleDragMove: (
    event: DragMoveEvent,
    widget: WidgetLayout,
    otherWidgets: WidgetLayout[]
  ) => GridPosition | null;
  handleDragEnd: (
    event: DragEndEvent,
    widget: WidgetLayout,
    otherWidgets: WidgetLayout[]
  ) => { position: GridPosition; resolvedWidgets: WidgetLayout[] };
  getDropPreview: (
    widget: WidgetLayout,
    position: GridPosition,
    otherWidgets: WidgetLayout[]
  ) => { isValid: boolean; conflicts: WidgetLayout[] };
}

export const useGridSnapping = (
  options: UseGridSnappingOptions = {}
): UseGridSnappingReturn => {
  const {
    gridConfig = DEFAULT_GRID_CONFIG,
    containerRef,
    snapThreshold = 10,
    enableAutoLayout = true,
    onCollisionDetected,
    onPositionSnapped,
  } = options;

  // Refs for managers
  const collisionDetectorRef = useRef<GridCollisionDetector>();
  const gridSnapperRef = useRef<GridSnapper>();
  const autoLayoutManagerRef = useRef<AutoLayoutManager>();
  const containerSizeRef = useRef({ width: 0, height: 0 });

  // Initialize managers
  useEffect(() => {
    collisionDetectorRef.current = createCollisionDetector(gridConfig);
    autoLayoutManagerRef.current = createAutoLayoutManager(gridConfig);
  }, [gridConfig]);

  // Update container size and grid snapper
  useEffect(() => {
    const updateContainerSize = () => {
      if (containerRef?.current) {
        const rect = containerRef.current.getBoundingClientRect();
        containerSizeRef.current = {
          width: rect.width,
          height: rect.height,
        };

        gridSnapperRef.current = createGridSnapper(
          gridConfig,
          containerSizeRef.current
        );
      }
    };

    updateContainerSize();

    // Update on resize
    const resizeObserver = new ResizeObserver(updateContainerSize);
    if (containerRef?.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [containerRef, gridConfig]);

  // Snap widget to grid
  const snapToGrid = useCallback(
    (
      widget: WidgetLayout,
      pixelPosition: { x: number; y: number },
      otherWidgets: WidgetLayout[]
    ): GridPosition => {
      if (!gridSnapperRef.current) {
        return widget.position;
      }

      const snappedPosition = gridSnapperRef.current.snapWidget(
        widget,
        pixelPosition,
        otherWidgets
      );

      if (onPositionSnapped) {
        onPositionSnapped(widget, snappedPosition);
      }

      return snappedPosition;
    },
    [onPositionSnapped]
  );

  // Check collision
  const checkCollision = useCallback(
    (widget: WidgetLayout, otherWidgets: WidgetLayout[]): boolean => {
      if (!collisionDetectorRef.current) return false;

      const hasCollision = collisionDetectorRef.current.checkCollision(
        widget,
        otherWidgets
      );

      if (hasCollision && onCollisionDetected) {
        const conflicts = otherWidgets.filter(
          (other) =>
            other.id !== widget.id &&
            collisionDetectorRef.current!.checkCollision(widget, [other])
        );
        onCollisionDetected(widget, conflicts);
      }

      return hasCollision;
    },
    [onCollisionDetected]
  );

  // Resolve collisions
  const resolveCollisions = useCallback(
    (widget: WidgetLayout, otherWidgets: WidgetLayout[]): WidgetLayout[] => {
      if (!collisionDetectorRef.current) return otherWidgets;

      return collisionDetectorRef.current.resolveCollision(
        widget,
        otherWidgets
      );
    },
    []
  );

  // Compact layout
  const compactLayout = useCallback(
    (widgets: WidgetLayout[]): WidgetLayout[] => {
      if (!autoLayoutManagerRef.current || !enableAutoLayout) {
        return widgets;
      }

      return autoLayoutManagerRef.current.compactLayout(widgets);
    },
    [enableAutoLayout]
  );

  // Insert widget
  const insertWidget = useCallback(
    (
      widget: WidgetLayout,
      existingWidgets: WidgetLayout[],
      preferredPosition?: GridPosition
    ) => {
      if (!autoLayoutManagerRef.current) {
        return { widget, updatedWidgets: [...existingWidgets, widget] };
      }

      return autoLayoutManagerRef.current.insertWidget(
        widget,
        existingWidgets,
        preferredPosition
      );
    },
    []
  );

  // Handle drag move with snapping
  const handleDragMove = useCallback(
    (
      event: DragMoveEvent,
      widget: WidgetLayout,
      otherWidgets: WidgetLayout[]
    ): GridPosition | null => {
      if (!gridSnapperRef.current) return null;

      const { delta } = event;
      const currentPixelPos = gridSnapperRef.current.gridToPixels(
        widget.position
      );
      const newPixelPos = {
        x: currentPixelPos.x + delta.x,
        y: currentPixelPos.y + delta.y,
      };

      // Only snap if we've moved beyond the threshold
      const distance = Math.sqrt(delta.x ** 2 + delta.y ** 2);
      if (distance < snapThreshold) {
        return null;
      }

      return snapToGrid(widget, newPixelPos, otherWidgets);
    },
    [snapToGrid, snapThreshold]
  );

  // Handle drag end with collision resolution
  const handleDragEnd = useCallback(
    (
      event: DragEndEvent,
      widget: WidgetLayout,
      otherWidgets: WidgetLayout[]
    ): { position: GridPosition; resolvedWidgets: WidgetLayout[] } => {
      if (!gridSnapperRef.current || !collisionDetectorRef.current) {
        return { position: widget.position, resolvedWidgets: otherWidgets };
      }

      const { delta } = event;
      const currentPixelPos = gridSnapperRef.current.gridToPixels(
        widget.position
      );
      const finalPixelPos = {
        x: currentPixelPos.x + delta.x,
        y: currentPixelPos.y + delta.y,
      };

      // Snap to final position
      const finalPosition = snapToGrid(widget, finalPixelPos, otherWidgets);
      const updatedWidget = { ...widget, position: finalPosition };

      // Resolve any collisions
      const resolvedWidgets = resolveCollisions(updatedWidget, otherWidgets);

      return {
        position: finalPosition,
        resolvedWidgets,
      };
    },
    [snapToGrid, resolveCollisions]
  );

  // Get drop preview information
  const getDropPreview = useCallback(
    (
      widget: WidgetLayout,
      position: GridPosition,
      otherWidgets: WidgetLayout[]
    ): { isValid: boolean; conflicts: WidgetLayout[] } => {
      if (!collisionDetectorRef.current) {
        return { isValid: true, conflicts: [] };
      }

      const testWidget = { ...widget, position };
      const hasCollision = collisionDetectorRef.current.checkCollision(
        testWidget,
        otherWidgets
      );

      const conflicts = hasCollision
        ? otherWidgets.filter(
            (other) =>
              other.id !== widget.id &&
              collisionDetectorRef.current!.checkCollision(testWidget, [other])
          )
        : [];

      return {
        isValid: !hasCollision,
        conflicts,
      };
    },
    []
  );

  return {
    snapToGrid,
    checkCollision,
    resolveCollisions,
    compactLayout,
    insertWidget,
    handleDragMove,
    handleDragEnd,
    getDropPreview,
  };
};

// Hook for visual feedback during drag operations
export const useDragFeedback = (
  widget: WidgetLayout | null,
  otherWidgets: WidgetLayout[],
  gridSnapping: UseGridSnappingReturn
) => {
  const [dragPreview, setDragPreview] = useState<{
    position: GridPosition;
    isValid: boolean;
    conflicts: WidgetLayout[];
  } | null>(null);

  useEffect(() => {
    if (!widget) {
      setDragPreview(null);
      return;
    }

    const preview = gridSnapping.getDropPreview(
      widget,
      widget.position,
      otherWidgets
    );

    setDragPreview({
      position: widget.position,
      ...preview,
    });
  }, [widget, otherWidgets, gridSnapping]);

  return dragPreview;
};
