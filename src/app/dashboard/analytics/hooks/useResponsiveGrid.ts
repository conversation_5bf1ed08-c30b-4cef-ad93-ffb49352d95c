"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import {
  GridBreakpoint,
  GridConfig,
  WidgetSize,
  WidgetLayout,
} from "@/types/drag-drop";
import {
  getCurrentBreakpoint,
  getColumnsForBreakpoint,
  getResponsiveWidgetSize,
  DEFAULT_GRID_CONFIG,
  BREAKPOINT_VALUES,
} from "../utils/grid-utils";

interface UseResponsiveGridOptions {
  gridConfig?: Partial<GridConfig>;
  debounceMs?: number;
}

interface UseResponsiveGridReturn {
  currentBreakpoint: GridBreakpoint;
  columns: number;
  windowSize: { width: number; height: number };
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  getResponsiveSize: (size: WidgetSize) => WidgetSize;
  adaptLayoutForBreakpoint: (widgets: WidgetLayout[]) => WidgetLayout[];
}

export const useResponsiveGrid = (
  options: UseResponsiveGridOptions = {}
): UseResponsiveGridReturn => {
  const { gridConfig = DEFAULT_GRID_CONFIG, debounceMs = 150 } = options;

  // Merge with default config
  const config = useMemo(
    () => ({
      ...DEFAULT_GRID_CONFIG,
      ...gridConfig,
    }),
    [gridConfig]
  );

  // Window size state
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== "undefined" ? window.innerWidth : 1280,
    height: typeof window !== "undefined" ? window.innerHeight : 720,
  });

  // Current breakpoint
  const currentBreakpoint = useMemo(() => {
    return getCurrentBreakpoint(windowSize.width);
  }, [windowSize.width]);

  // Current column count
  const columns = useMemo(() => {
    return getColumnsForBreakpoint(currentBreakpoint, config);
  }, [currentBreakpoint, config]);

  // Device type helpers
  const isMobile = useMemo(() => {
    return currentBreakpoint === "xs" || currentBreakpoint === "sm";
  }, [currentBreakpoint]);

  const isTablet = useMemo(() => {
    return currentBreakpoint === "md";
  }, [currentBreakpoint]);

  const isDesktop = useMemo(() => {
    return currentBreakpoint === "lg" || currentBreakpoint === "xl";
  }, [currentBreakpoint]);

  // Debounced resize handler
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setWindowSize({
          width: window.innerWidth,
          height: window.innerHeight,
        });
      }, debounceMs);
    };

    // Set initial size
    if (typeof window !== "undefined") {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    // Add event listener
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
      clearTimeout(timeoutId);
    };
  }, [debounceMs]);

  // Get responsive size for current breakpoint
  const getResponsiveSize = useCallback(
    (size: WidgetSize): WidgetSize => {
      return getResponsiveWidgetSize(size, currentBreakpoint, columns);
    },
    [currentBreakpoint, columns]
  );

  // Adapt entire layout for current breakpoint
  const adaptLayoutForBreakpoint = useCallback(
    (widgets: WidgetLayout[]): WidgetLayout[] => {
      return widgets.map((widget) => {
        const responsiveSize = getResponsiveSize(widget.size);

        // Adjust position if widget would overflow
        let adjustedPosition = { ...widget.position };

        if (adjustedPosition.col + responsiveSize.width > columns) {
          adjustedPosition.col = Math.max(0, columns - responsiveSize.width);
        }

        return {
          ...widget,
          size: responsiveSize,
          position: adjustedPosition,
        };
      });
    },
    [getResponsiveSize, columns]
  );

  return {
    currentBreakpoint,
    columns,
    windowSize,
    isMobile,
    isTablet,
    isDesktop,
    getResponsiveSize,
    adaptLayoutForBreakpoint,
  };
};

// Hook for detecting breakpoint changes
export const useBreakpointChange = (
  callback: (breakpoint: GridBreakpoint) => void,
  deps: React.DependencyList = []
) => {
  const { currentBreakpoint } = useResponsiveGrid();
  const [previousBreakpoint, setPreviousBreakpoint] =
    useState(currentBreakpoint);

  useEffect(() => {
    if (currentBreakpoint !== previousBreakpoint) {
      callback(currentBreakpoint);
      setPreviousBreakpoint(currentBreakpoint);
    }
  }, [currentBreakpoint, previousBreakpoint, callback, ...deps]);
};

// Hook for media query matching
export const useMediaQuery = (breakpoint: GridBreakpoint): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(
      `(min-width: ${BREAKPOINT_VALUES[breakpoint]}px)`
    );

    setMatches(mediaQuery.matches);

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener("change", handler);

    return () => {
      mediaQuery.removeEventListener("change", handler);
    };
  }, [breakpoint]);

  return matches;
};
