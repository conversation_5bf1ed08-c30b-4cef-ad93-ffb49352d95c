"use client";

import { useState, useEffect, useCallback } from "react";

interface UseChartDataOptions {
  initialData?: any[];
  autoRetry?: boolean;
  retryDelay?: number;
  maxRetries?: number;
}

interface UseChartDataReturn {
  data: any[];
  isLoading: boolean;
  error: Error | null;
  retry: () => void;
  refetch: () => Promise<void>;
}

export const useChartData = (
  fetchFn: () => Promise<any[]>,
  options: UseChartDataOptions = {}
): UseChartDataReturn => {
  const {
    initialData = [],
    autoRetry = false,
    retryDelay = 1000,
    maxRetries = 3,
  } = options;

  const [data, setData] = useState<any[]>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await fetchFn();
      setData(result);
      setRetryCount(0);
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error("Failed to fetch chart data");
      setError(error);

      // Auto retry logic
      if (autoRetry && retryCount < maxRetries) {
        setTimeout(() => {
          setRetryCount((prev) => prev + 1);
          fetchData();
        }, retryDelay * Math.pow(2, retryCount)); // Exponential backoff
      }
    } finally {
      setIsLoading(false);
    }
  }, [fetchFn, autoRetry, retryCount, maxRetries, retryDelay]);

  const retry = useCallback(() => {
    setRetryCount(0);
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(async () => {
    setRetryCount(0);
    await fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    retry,
    refetch,
  };
};

// Specific hooks for different chart types
export const useGenderDemographicsData = (selectedSocial?: {
  platform: string;
  username?: string;
}) => {
  const fetchGenderData = useCallback(async () => {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Simulate potential error (10% chance)
    if (Math.random() < 0.1) {
      throw new Error("Failed to load gender demographics data");
    }

    // Mock data
    return [
      {
        breakdown: { gender: "male" },
        total_value: 1250,
        percentage: 62.5,
      },
      {
        breakdown: { gender: "female" },
        total_value: 750,
        percentage: 37.5,
      },
    ];
  }, [selectedSocial]);

  return useChartData(fetchGenderData, {
    autoRetry: true,
    maxRetries: 2,
  });
};

export const useLineChartData = (selectedSocial?: {
  platform: string;
  username?: string;
}) => {
  const fetchLineData = useCallback(async () => {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 800));

    // Simulate potential error (5% chance)
    if (Math.random() < 0.05) {
      throw new Error("Failed to load time series data");
    }

    // Mock time series data
    const now = new Date();
    return Array.from({ length: 30 }, (_, i) => ({
      date: new Date(now.getTime() - (29 - i) * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
      value: Math.floor(Math.random() * 1000) + 500,
      engagement: Math.floor(Math.random() * 100) + 50,
    }));
  }, [selectedSocial]);

  return useChartData(fetchLineData, {
    autoRetry: true,
    maxRetries: 3,
  });
};
