import { AnalyticsData, AnalyticsDataPoint } from "../types";

// Generate demo data for testing with realistic growth patterns
export const generateDemoData = (days: number = 30): AnalyticsData => {
  // Seed for consistent but varied data across sessions
  let seed = 12345;
  const seededRandom = () => {
    seed = (seed * 9301 + 49297) % 233280;
    return seed / 233280;
  };

  // Generate viral content events (occasional spikes)
  const viralEvents = new Set<number>();
  const numViralEvents = Math.floor(days / 10); // 1 viral event per ~10 days
  for (let i = 0; i < numViralEvents; i++) {
    viralEvents.add(Math.floor(seededRandom() * days));
  }

  const generateDailyData = (
    baseValue: number,
    variance: number = 0.15,
    growthRate: number = 0.25
  ): AnalyticsDataPoint[] => {
    let momentum = 1.0; // Tracks momentum from previous days

    return Array.from({ length: days }, (_, index) => {
      // Realistic weekly patterns
      const dayOfWeek = (index + 1) % 7;
      let weekdayMultiplier = 1.0;

      // Monday-Thursday: baseline, Friday: higher, Weekend: peak, Sunday evening: lower
      if (dayOfWeek === 1) weekdayMultiplier = 0.85; // Monday slower start
      else if (dayOfWeek === 5) weekdayMultiplier = 1.15; // Friday boost
      else if (dayOfWeek === 6) weekdayMultiplier = 1.25; // Saturday peak
      else if (dayOfWeek === 0) weekdayMultiplier = 1.2; // Sunday high but slightly lower

      // Sigmoid growth curve for more realistic progression
      const progress = index / days;
      const sigmoidGrowth = 1 / (1 + Math.exp(-8 * (progress - 0.5))); // S-curve
      const trendMultiplier = 1 + sigmoidGrowth * growthRate;

      // Viral content boost
      const viralBoost = viralEvents.has(index)
        ? 2.5 + seededRandom() * 1.5
        : 1.0;

      // Momentum effect - good days influence next few days
      if (viralEvents.has(index - 1)) momentum = Math.min(momentum * 1.3, 1.8);
      else if (viralEvents.has(index - 2))
        momentum = Math.min(momentum * 1.15, 1.5);
      else momentum = Math.max(momentum * 0.95, 0.9); // Gradual decay

      // Reduced random variance for smoother data
      const randomMultiplier = 1 + (seededRandom() - 0.5) * variance;

      const value = Math.round(
        baseValue *
          randomMultiplier *
          weekdayMultiplier *
          trendMultiplier *
          viralBoost *
          momentum
      );

      return {
        total_value: Math.max(0, value),
        date: new Date(Date.now() - (days - index - 1) * 24 * 60 * 60 * 1000)
          .toISOString()
          .split("T")[0],
      };
    });
  };

  const generateBreakdownData = (
    baseValue: number,
    breakdownType: string,
    options: string[],
    percentages?: number[]
  ): AnalyticsDataPoint[] => {
    return options.map((option, index) => {
      const percentage = percentages ? percentages[index] : Math.random() * 100;
      const value = Math.round(baseValue * (percentage / 100));

      return {
        total_value: value,
        breakdown: { [breakdownType]: option },
        percentage: percentage,
      };
    });
  };

  const generateMediaBreakdownData = (
    baseValue: number,
    days: number,
    growthRate: number = 0.3
  ): AnalyticsDataPoint[] => {
    const mediaTypes = ["post", "reel", "story", "video"];
    // Reels are growing faster, stories declining slightly
    const mediaDistribution = [0.35, 0.4, 0.15, 0.1]; // Reels now most common
    const mediaGrowthRates = [0.2, 0.4, 0.1, 0.25]; // Different growth rates per media type

    const result: AnalyticsDataPoint[] = [];
    let momentum = 1.0;

    for (let day = 0; day < days; day++) {
      // Apply viral events and momentum to all media types for this day
      const dayOfWeek = (day + 1) % 7;
      let weekdayMultiplier = 1.0;

      if (dayOfWeek === 1) weekdayMultiplier = 0.85;
      else if (dayOfWeek === 5) weekdayMultiplier = 1.15;
      else if (dayOfWeek === 6) weekdayMultiplier = 1.25;
      else if (dayOfWeek === 0) weekdayMultiplier = 1.2;

      const viralBoost = viralEvents.has(day)
        ? 2.2 + seededRandom() * 1.3
        : 1.0;

      // Update momentum
      if (viralEvents.has(day - 1)) momentum = Math.min(momentum * 1.25, 1.7);
      else if (viralEvents.has(day - 2))
        momentum = Math.min(momentum * 1.1, 1.4);
      else momentum = Math.max(momentum * 0.96, 0.92);

      mediaTypes.forEach((type, index) => {
        // Individual media type growth
        const progress = day / days;
        const sigmoidGrowth = 1 / (1 + Math.exp(-8 * (progress - 0.5)));
        const trendMultiplier = 1 + sigmoidGrowth * mediaGrowthRates[index];

        // Media-specific patterns (reels perform better on weekends)
        let mediaSpecificBoost = 1.0;
        if (type === "reel" && (dayOfWeek === 0 || dayOfWeek === 6)) {
          mediaSpecificBoost = 1.3;
        } else if (type === "story" && dayOfWeek >= 1 && dayOfWeek <= 5) {
          mediaSpecificBoost = 1.1; // Stories better on weekdays
        }

        const randomMultiplier = 1 + (seededRandom() - 0.5) * 0.12; // Reduced variance

        const value = Math.round(
          baseValue *
            mediaDistribution[index] *
            randomMultiplier *
            weekdayMultiplier *
            trendMultiplier *
            viralBoost *
            momentum *
            mediaSpecificBoost
        );

        result.push({
          total_value: Math.max(0, value),
          date: new Date(Date.now() - (days - day - 1) * 24 * 60 * 60 * 1000)
            .toISOString()
            .split("T")[0],
          breakdown: { media_product_type: type as any },
        });
      });
    }

    return result;
  };

  return {
    // Daily metrics with realistic growth patterns and reduced noise
    accounts_engaged: generateDailyData(1200, 0.12, 0.35), // Higher base, lower variance, good growth
    comments: generateDailyData(85, 0.15, 0.4), // Comments grow well with engagement
    reach: generateMediaBreakdownData(5400, days, 0.3), // Steady reach growth
    likes: generateMediaBreakdownData(320, days, 0.45), // Likes grow faster with viral content
    replies: generateDailyData(28, 0.18, 0.3), // Story replies grow moderately
    saved: generateMediaBreakdownData(156, days, 0.35), // Saves indicate quality content
    shares: generateMediaBreakdownData(89, days, 0.5), // Shares grow fastest - viral indicator
    total_interactions: generateMediaBreakdownData(2100, days, 0.4), // Overall engagement growth
    views: generateMediaBreakdownData(8900, days, 0.25), // Views grow steadily but slower

    // Follows and unfollows with realistic ratios and growth
    follows_and_unfollows: [
      ...generateDailyData(45, 0.1, 0.3).map((item) => ({
        ...item,
        breakdown: { follow_type: "follow" as const },
      })),
      ...generateDailyData(12, 0.15, 0.1).map((item) => ({
        ...item,
        breakdown: { follow_type: "unfollow" as const },
      })),
    ],

    // Profile links taps with realistic distribution and growth
    profile_links_taps: [
      ...generateDailyData(15, 0.12, 0.25).map((item) => ({
        ...item,
        breakdown: { contact_button_type: "email" as const },
      })),
      ...generateDailyData(8, 0.15, 0.2).map((item) => ({
        ...item,
        breakdown: { contact_button_type: "phone" as const },
      })),
      ...generateDailyData(5, 0.18, 0.15).map((item) => ({
        ...item,
        breakdown: { contact_button_type: "address" as const },
      })),
      ...generateDailyData(3, 0.2, 0.1).map((item) => ({
        ...item,
        breakdown: { contact_button_type: "text" as const },
      })),
    ],

    // Engaged audience demographics with realistic distributions
    engaged_audience_demographics: [
      // Age breakdown - realistic Instagram engaged audience (skews younger)
      ...generateBreakdownData(
        3200, // Higher engaged audience
        "age",
        ["13-17", "18-24", "25-34", "35-44", "45-54", "55-64", "65+"],
        [8.5, 34.2, 28.8, 16.4, 7.8, 3.1, 1.2] // More engaged younger users
      ),

      // Gender breakdown - separate for pie chart
      ...generateBreakdownData(
        3200,
        "gender",
        ["male", "female", "other"],
        [42.8, 56.2, 1.0] // Slightly more female engagement
      ),

      // Country breakdown - top Instagram markets with engagement focus
      ...generateBreakdownData(
        3200,
        "country",
        ["United States", "Canada", "United Kingdom", "Australia", "Germany"],
        [38.2, 15.4, 12.8, 9.6, 8.1] // English-speaking markets more engaged
      ),

      // City breakdown - major metropolitan areas
      ...generateBreakdownData(
        3200,
        "city",
        ["New York", "Los Angeles", "Toronto", "London", "Sydney"],
        [14.2, 11.8, 8.9, 7.4, 6.2]
      ),
    ],

    // Follower demographics with broader, more diverse patterns
    follower_demographics: [
      // Age breakdown - broader follower base
      ...generateBreakdownData(
        18500, // Growing follower base
        "age",
        ["13-17", "18-24", "25-34", "35-44", "45-54", "55-64", "65+"],
        [12.1, 29.4, 26.7, 18.2, 8.9, 3.8, 0.9] // More diverse age range
      ),

      // Gender breakdown - separate for pie chart
      ...generateBreakdownData(
        18500,
        "gender",
        ["male", "female", "other"],
        [44.2, 54.8, 1.0] // Balanced gender distribution
      ),

      // Country breakdown - global reach
      ...generateBreakdownData(
        18500,
        "country",
        ["United States", "India", "Brazil", "United Kingdom", "Canada"],
        [28.4, 16.8, 12.3, 9.7, 8.2] // More global distribution
      ),

      // City breakdown - diverse metropolitan areas
      ...generateBreakdownData(
        18500,
        "city",
        ["New York", "Mumbai", "São Paulo", "Los Angeles", "London"],
        [9.8, 8.4, 7.2, 6.9, 5.8] // More international cities
      ),
    ],
  };
};

// Demo data for immediate use
export const demoAnalyticsData = generateDemoData(30);
