import {
  GridPosition,
  WidgetSize,
  WidgetLayout,
  GridConfig,
  GridBreakpoint,
  ResponsiveWidgetSize,
} from "@/types/drag-drop";

// Breakpoint pixel values for media queries
export const BREAKPOINT_VALUES = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
} as const;

// Default responsive column counts
export const DEFAULT_RESPONSIVE_COLUMNS = {
  xs: 1,
  sm: 2,
  md: 4,
  lg: 8,
  xl: 12,
} as const;

// Default grid configuration
export const DEFAULT_GRID_CONFIG: GridConfig = {
  columns: 12,
  gap: "1rem",
  breakpoints: DEFAULT_RESPONSIVE_COLUMNS,
  minRowHeight: 200,
};

/**
 * Get current breakpoint based on window width
 */
export const getCurrentBreakpoint = (width: number): GridBreakpoint => {
  if (width >= BREAKPOINT_VALUES.xl) return "xl";
  if (width >= BREAKPOINT_VALUES.lg) return "lg";
  if (width >= BREAKPOINT_VALUES.md) return "md";
  if (width >= BREAKPOINT_VALUES.sm) return "sm";
  return "xs";
};

/**
 * Get column count for a specific breakpoint
 */
export const getColumnsForBreakpoint = (
  breakpoint: GridBreakpoint,
  gridConfig: GridConfig = DEFAULT_GRID_CONFIG
): number => {
  return (
    gridConfig.breakpoints[breakpoint] || DEFAULT_RESPONSIVE_COLUMNS[breakpoint]
  );
};

/**
 * Calculate responsive widget size for a breakpoint
 */
export const getResponsiveWidgetSize = (
  size: WidgetSize,
  breakpoint: GridBreakpoint,
  maxColumns: number
): WidgetSize => {
  const maxWidth = Math.min(size.width, maxColumns);

  // Adjust width based on breakpoint constraints
  let adjustedWidth = maxWidth;

  switch (breakpoint) {
    case "xs":
      adjustedWidth = 1; // Always full width on mobile
      break;
    case "sm":
      adjustedWidth = Math.min(maxWidth, 2);
      break;
    case "md":
      adjustedWidth = Math.min(maxWidth, 4);
      break;
    case "lg":
      adjustedWidth = Math.min(maxWidth, 8);
      break;
    case "xl":
      adjustedWidth = maxWidth;
      break;
  }

  return {
    width: adjustedWidth,
    height: size.height,
  };
};

/**
 * Calculate grid area CSS string
 */
export const calculateGridArea = (
  position: GridPosition,
  size: WidgetSize,
  maxColumns: number = 12
): string => {
  const { row, col } = position;
  const { width, height } = size;

  // Ensure we don't exceed grid boundaries
  const startCol = Math.max(1, Math.min(col + 1, maxColumns));
  const endCol = Math.min(startCol + width, maxColumns + 1);
  const startRow = Math.max(1, row + 1);
  const endRow = startRow + height;

  return `${startRow} / ${startCol} / ${endRow} / ${endCol}`;
};

/**
 * Check if two widgets overlap
 */
export const doWidgetsOverlap = (
  widget1: { position: GridPosition; size: WidgetSize },
  widget2: { position: GridPosition; size: WidgetSize }
): boolean => {
  const { position: pos1, size: size1 } = widget1;
  const { position: pos2, size: size2 } = widget2;

  return (
    pos1.row < pos2.row + size2.height &&
    pos1.row + size1.height > pos2.row &&
    pos1.col < pos2.col + size2.width &&
    pos1.col + size1.width > pos2.col
  );
};

/**
 * Find all widgets that overlap with a given widget
 */
export const findOverlappingWidgets = (
  widget: WidgetLayout,
  allWidgets: WidgetLayout[]
): WidgetLayout[] => {
  return allWidgets.filter(
    (other) => other.id !== widget.id && doWidgetsOverlap(widget, other)
  );
};

/**
 * Check if a position is valid within grid boundaries
 */
export const isValidGridPosition = (
  position: GridPosition,
  size: WidgetSize,
  maxColumns: number = 12,
  maxRows: number = 100
): boolean => {
  const { row, col } = position;
  const { width, height } = size;

  // Check boundaries
  if (row < 0 || col < 0) return false;
  if (col + width > maxColumns) return false;
  if (row + height > maxRows) return false;

  return true;
};

/**
 * Find the next available position for a widget
 */
export const findNextAvailablePosition = (
  size: WidgetSize,
  existingWidgets: WidgetLayout[],
  maxColumns: number = 12,
  startRow: number = 0
): GridPosition => {
  const { width, height } = size;

  // Create a map of occupied positions for faster lookup
  const occupiedPositions = new Set<string>();
  existingWidgets.forEach((widget) => {
    for (
      let r = widget.position.row;
      r < widget.position.row + widget.size.height;
      r++
    ) {
      for (
        let c = widget.position.col;
        c < widget.position.col + widget.size.width;
        c++
      ) {
        occupiedPositions.add(`${r},${c}`);
      }
    }
  });

  // Find first available position
  for (let row = startRow; row < 100; row++) {
    for (let col = 0; col <= maxColumns - width; col++) {
      const position: GridPosition = { row, col };

      // Check if this position is available
      let isAvailable = true;
      for (let r = row; r < row + height && isAvailable; r++) {
        for (let c = col; c < col + width && isAvailable; c++) {
          if (occupiedPositions.has(`${r},${c}`)) {
            isAvailable = false;
          }
        }
      }

      if (isAvailable) {
        return position;
      }
    }
  }

  // Fallback to bottom of grid
  return { row: 100, col: 0 };
};

/**
 * Compact the grid by moving widgets up to fill gaps
 */
export const compactGrid = (
  widgets: WidgetLayout[],
  maxColumns: number = 12
): WidgetLayout[] => {
  // Sort widgets by row, then by column
  const sortedWidgets = [...widgets].sort((a, b) => {
    if (a.position.row !== b.position.row) {
      return a.position.row - b.position.row;
    }
    return a.position.col - b.position.col;
  });

  const compactedWidgets: WidgetLayout[] = [];

  sortedWidgets.forEach((widget) => {
    // Find the highest available position for this widget
    const newPosition = findNextAvailablePosition(
      widget.size,
      compactedWidgets,
      maxColumns,
      0
    );

    compactedWidgets.push({
      ...widget,
      position: newPosition,
    });
  });

  return compactedWidgets;
};

/**
 * Calculate the total height of the grid
 */
export const calculateGridHeight = (
  widgets: WidgetLayout[],
  minRowHeight: number = 200
): number => {
  if (widgets.length === 0) return minRowHeight;

  const maxRow = Math.max(
    ...widgets.map((widget) => widget.position.row + widget.size.height)
  );

  return Math.max(maxRow * minRowHeight, minRowHeight);
};

/**
 * Generate CSS classes for responsive grid columns
 */
export const generateResponsiveGridClasses = (
  breakpoints: Record<GridBreakpoint, number> = DEFAULT_RESPONSIVE_COLUMNS
): string => {
  const classes = [
    `grid-cols-${breakpoints.xs}`,
    `sm:grid-cols-${breakpoints.sm}`,
    `md:grid-cols-${breakpoints.md}`,
    `lg:grid-cols-${breakpoints.lg}`,
    `xl:grid-cols-${breakpoints.xl}`,
  ];

  return classes.join(" ");
};

/**
 * Generate CSS classes for widget column span
 */
export const generateWidgetSpanClasses = (
  size: WidgetSize,
  breakpoints: Record<GridBreakpoint, number> = DEFAULT_RESPONSIVE_COLUMNS
): string => {
  const classes = [
    `col-span-${Math.min(size.width, breakpoints.xs)}`,
    `sm:col-span-${Math.min(size.width, breakpoints.sm)}`,
    `md:col-span-${Math.min(size.width, breakpoints.md)}`,
    `lg:col-span-${Math.min(size.width, breakpoints.lg)}`,
    `xl:col-span-${Math.min(size.width, breakpoints.xl)}`,
    `row-span-${size.height}`,
  ];

  return classes.join(" ");
};

/**
 * Snap position to grid
 */
export const snapToGrid = (
  position: { x: number; y: number },
  gridSize: { width: number; height: number },
  offset: { x: number; y: number } = { x: 0, y: 0 }
): GridPosition => {
  const col = Math.round((position.x - offset.x) / gridSize.width);
  const row = Math.round((position.y - offset.y) / gridSize.height);

  return {
    row: Math.max(0, row),
    col: Math.max(0, col),
  };
};

/**
 * Convert grid position to pixel coordinates
 */
export const gridToPixels = (
  position: GridPosition,
  gridSize: { width: number; height: number },
  offset: { x: number; y: number } = { x: 0, y: 0 }
): { x: number; y: number } => {
  return {
    x: position.col * gridSize.width + offset.x,
    y: position.row * gridSize.height + offset.y,
  };
};

/**
 * Validate widget layout configuration
 */
export const validateWidgetLayout = (
  widget: WidgetLayout,
  maxColumns: number = 12
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Check position validity
  if (!isValidGridPosition(widget.position, widget.size, maxColumns)) {
    errors.push("Widget position exceeds grid boundaries");
  }

  // Check size constraints
  if (widget.size.width < widget.config.minSize.width) {
    errors.push("Widget width is below minimum size");
  }

  if (widget.size.height < widget.config.minSize.height) {
    errors.push("Widget height is below minimum size");
  }

  if (widget.size.width > widget.config.maxSize.width) {
    errors.push("Widget width exceeds maximum size");
  }

  if (widget.size.height > widget.config.maxSize.height) {
    errors.push("Widget height exceeds maximum size");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
