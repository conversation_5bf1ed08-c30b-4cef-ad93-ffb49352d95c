import {
  WidgetLayout,
  GridPosition,
  WidgetSize,
  GridConfig,
  CollisionDetection,
} from "@/types/drag-drop";
import {
  doWidgetsOverlap,
  findNextAvailablePosition,
  isValidGridPosition,
} from "./grid-utils";

/**
 * Advanced collision detection system for grid-based layouts
 */
export class GridCollisionDetector implements CollisionDetection {
  private gridConfig: GridConfig;

  constructor(gridConfig: GridConfig) {
    this.gridConfig = gridConfig;
  }

  /**
   * Check if a widget collides with any other widgets
   */
  checkCollision(widget: WidgetLayout, otherWidgets: WidgetLayout[]): boolean {
    return otherWidgets.some(
      (other) => other.id !== widget.id && doWidgetsOverlap(widget, other)
    );
  }

  /**
   * Resolve collisions by repositioning conflicting widgets
   */
  resolveCollision(
    widget: WidgetLayout,
    otherWidgets: WidgetLayout[]
  ): WidgetLayout[] {
    const conflictingWidgets = otherWidgets.filter(
      (other) => other.id !== widget.id && doWidgetsOverlap(widget, other)
    );

    if (conflictingWidgets.length === 0) {
      return otherWidgets;
    }

    // Strategy 1: Try to move conflicting widgets down
    const resolvedWidgets = [...otherWidgets];
    const movedWidgets = new Set<string>();

    for (const conflicting of conflictingWidgets) {
      if (movedWidgets.has(conflicting.id)) continue;

      const newPosition = this.findAlternativePosition(
        conflicting,
        resolvedWidgets.filter((w) => w.id !== conflicting.id),
        "down"
      );

      if (newPosition) {
        const widgetIndex = resolvedWidgets.findIndex(
          (w) => w.id === conflicting.id
        );
        if (widgetIndex !== -1) {
          resolvedWidgets[widgetIndex] = {
            ...conflicting,
            position: newPosition,
          };
          movedWidgets.add(conflicting.id);
        }
      }
    }

    return resolvedWidgets;
  }

  /**
   * Find the nearest valid position for a widget
   */
  findNearestValidPosition(
    widget: WidgetLayout,
    gridConfig: GridConfig = this.gridConfig
  ): GridPosition {
    const { position, size } = widget;

    // First, try the current position if it's valid
    if (isValidGridPosition(position, size, gridConfig.columns)) {
      return position;
    }

    // Try positions in expanding circles around the original position
    const maxDistance = Math.max(gridConfig.columns, 20);

    for (let distance = 1; distance <= maxDistance; distance++) {
      // Check positions at this distance
      for (let deltaRow = -distance; deltaRow <= distance; deltaRow++) {
        for (let deltaCol = -distance; deltaCol <= distance; deltaCol++) {
          // Only check positions on the edge of the current distance
          if (
            Math.abs(deltaRow) !== distance &&
            Math.abs(deltaCol) !== distance
          ) {
            continue;
          }

          const candidatePosition: GridPosition = {
            row: Math.max(0, position.row + deltaRow),
            col: Math.max(0, position.col + deltaCol),
          };

          if (
            isValidGridPosition(candidatePosition, size, gridConfig.columns)
          ) {
            return candidatePosition;
          }
        }
      }
    }

    // Fallback: find any available position
    return { row: 0, col: 0 };
  }

  /**
   * Find alternative position for a widget in a specific direction
   */
  private findAlternativePosition(
    widget: WidgetLayout,
    otherWidgets: WidgetLayout[],
    direction: "up" | "down" | "left" | "right" = "down"
  ): GridPosition | null {
    const { position, size } = widget;
    const maxAttempts = 50;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      let candidatePosition: GridPosition;

      switch (direction) {
        case "down":
          candidatePosition = {
            row: position.row + attempt,
            col: position.col,
          };
          break;
        case "up":
          candidatePosition = {
            row: Math.max(0, position.row - attempt),
            col: position.col,
          };
          break;
        case "right":
          candidatePosition = {
            row: position.row,
            col: position.col + attempt,
          };
          break;
        case "left":
          candidatePosition = {
            row: position.row,
            col: Math.max(0, position.col - attempt),
          };
          break;
      }

      // Check if position is valid and doesn't conflict
      if (
        isValidGridPosition(candidatePosition, size, this.gridConfig.columns) &&
        !this.hasConflictAtPosition(candidatePosition, size, otherWidgets)
      ) {
        return candidatePosition;
      }
    }

    return null;
  }

  /**
   * Check if a position would cause conflicts with existing widgets
   */
  private hasConflictAtPosition(
    position: GridPosition,
    size: WidgetSize,
    widgets: WidgetLayout[]
  ): boolean {
    const testWidget = { position, size };
    return widgets.some((widget) => doWidgetsOverlap(testWidget, widget));
  }
}

/**
 * Grid snapping utilities
 */
export class GridSnapper {
  private gridConfig: GridConfig;
  private cellSize: { width: number; height: number };

  constructor(
    gridConfig: GridConfig,
    containerSize: { width: number; height: number }
  ) {
    this.gridConfig = gridConfig;
    this.cellSize = {
      width: containerSize.width / gridConfig.columns,
      height: gridConfig.minRowHeight,
    };
  }

  /**
   * Snap pixel coordinates to grid position
   */
  snapToGrid(
    pixelPosition: { x: number; y: number },
    offset: { x: number; y: number } = { x: 0, y: 0 }
  ): GridPosition {
    const adjustedX = pixelPosition.x - offset.x;
    const adjustedY = pixelPosition.y - offset.y;

    const col = Math.round(adjustedX / this.cellSize.width);
    const row = Math.round(adjustedY / this.cellSize.height);

    return {
      row: Math.max(0, row),
      col: Math.max(0, Math.min(col, this.gridConfig.columns - 1)),
    };
  }

  /**
   * Convert grid position to pixel coordinates
   */
  gridToPixels(
    gridPosition: GridPosition,
    offset: { x: number; y: number } = { x: 0, y: 0 }
  ): { x: number; y: number } {
    return {
      x: gridPosition.col * this.cellSize.width + offset.x,
      y: gridPosition.row * this.cellSize.height + offset.y,
    };
  }

  /**
   * Snap widget to nearest valid grid position
   */
  snapWidget(
    widget: WidgetLayout,
    pixelPosition: { x: number; y: number },
    otherWidgets: WidgetLayout[]
  ): GridPosition {
    const snappedPosition = this.snapToGrid(pixelPosition);

    // Ensure the position is valid
    if (
      !isValidGridPosition(
        snappedPosition,
        widget.size,
        this.gridConfig.columns
      )
    ) {
      // Find nearest valid position
      return this.findNearestValidPosition(snappedPosition, widget.size);
    }

    // Check for collisions
    const testWidget = { ...widget, position: snappedPosition };
    const collisionDetector = new GridCollisionDetector(this.gridConfig);

    if (collisionDetector.checkCollision(testWidget, otherWidgets)) {
      // Find alternative position
      return collisionDetector.findNearestValidPosition(testWidget);
    }

    return snappedPosition;
  }

  /**
   * Find nearest valid position within grid boundaries
   */
  private findNearestValidPosition(
    position: GridPosition,
    size: WidgetSize
  ): GridPosition {
    // Clamp to grid boundaries
    const maxCol = this.gridConfig.columns - size.width;
    const clampedPosition: GridPosition = {
      row: Math.max(0, position.row),
      col: Math.max(0, Math.min(position.col, maxCol)),
    };

    return clampedPosition;
  }

  /**
   * Update cell size when container resizes
   */
  updateCellSize(containerSize: { width: number; height: number }): void {
    this.cellSize = {
      width: containerSize.width / this.gridConfig.columns,
      height: this.gridConfig.minRowHeight,
    };
  }
}

/**
 * Auto-layout system for automatic widget positioning
 */
export class AutoLayoutManager {
  private gridConfig: GridConfig;
  private collisionDetector: GridCollisionDetector;

  constructor(gridConfig: GridConfig) {
    this.gridConfig = gridConfig;
    this.collisionDetector = new GridCollisionDetector(gridConfig);
  }

  /**
   * Automatically arrange widgets to minimize gaps
   */
  compactLayout(widgets: WidgetLayout[]): WidgetLayout[] {
    // Sort widgets by area (larger widgets first) and then by current position
    const sortedWidgets = [...widgets].sort((a, b) => {
      const areaA = a.size.width * a.size.height;
      const areaB = b.size.width * b.size.height;

      if (areaA !== areaB) {
        return areaB - areaA; // Larger first
      }

      // Same area, sort by position
      if (a.position.row !== b.position.row) {
        return a.position.row - b.position.row;
      }

      return a.position.col - b.position.col;
    });

    const compactedWidgets: WidgetLayout[] = [];

    for (const widget of sortedWidgets) {
      const newPosition = findNextAvailablePosition(
        widget.size,
        compactedWidgets,
        this.gridConfig.columns,
        0
      );

      compactedWidgets.push({
        ...widget,
        position: newPosition,
      });
    }

    return compactedWidgets;
  }

  /**
   * Reflow layout after widget removal
   */
  reflowAfterRemoval(
    widgets: WidgetLayout[],
    removedWidgetId: string
  ): WidgetLayout[] {
    const remainingWidgets = widgets.filter((w) => w.id !== removedWidgetId);
    return this.compactLayout(remainingWidgets);
  }

  /**
   * Insert widget at optimal position
   */
  insertWidget(
    widget: WidgetLayout,
    existingWidgets: WidgetLayout[],
    preferredPosition?: GridPosition
  ): { widget: WidgetLayout; updatedWidgets: WidgetLayout[] } {
    let targetPosition: GridPosition;

    if (preferredPosition) {
      // Try preferred position first
      const testWidget = { ...widget, position: preferredPosition };
      if (
        isValidGridPosition(
          preferredPosition,
          widget.size,
          this.gridConfig.columns
        ) &&
        !this.collisionDetector.checkCollision(testWidget, existingWidgets)
      ) {
        targetPosition = preferredPosition;
      } else {
        // Find nearest valid position to preferred
        targetPosition =
          this.collisionDetector.findNearestValidPosition(testWidget);
      }
    } else {
      // Find next available position
      targetPosition = findNextAvailablePosition(
        widget.size,
        existingWidgets,
        this.gridConfig.columns
      );
    }

    const placedWidget = { ...widget, position: targetPosition };

    // Resolve any conflicts
    const updatedWidgets = this.collisionDetector.resolveCollision(
      placedWidget,
      existingWidgets
    );

    return {
      widget: placedWidget,
      updatedWidgets: [...updatedWidgets, placedWidget],
    };
  }
}

/**
 * Factory function to create collision detection system
 */
export const createCollisionDetector = (gridConfig: GridConfig) => {
  return new GridCollisionDetector(gridConfig);
};

/**
 * Factory function to create grid snapper
 */
export const createGridSnapper = (
  gridConfig: GridConfig,
  containerSize: { width: number; height: number }
) => {
  return new GridSnapper(gridConfig, containerSize);
};

/**
 * Factory function to create auto-layout manager
 */
export const createAutoLayoutManager = (gridConfig: GridConfig) => {
  return new AutoLayoutManager(gridConfig);
};
