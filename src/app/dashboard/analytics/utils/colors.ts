// Centralized color configuration for analytics charts
// Following blue theme with variations, except for likes section

export const CHART_COLORS = {
  // Primary blue tones for most charts
  primary: "#0065ff",
  primaryLight: "#3b82f6",
  primaryDark: "#0040a0",

  // Blue gradient variations
  blue100: "#dbeafe",
  blue200: "#bfdbfe",
  blue300: "#93c5fd",
  blue400: "#60a5fa",
  blue500: "#3b82f6",
  blue600: "#2563eb",
  blue700: "#1d4ed8",
  blue800: "#1e40af",
  blue900: "#1e3a8a",

  // Instagram red colors
  instagram: "#E4405F",
  instagramLight: "#F56565",
  instagramDark: "#C53030",
  instagramGradient: "#E1306C",

  // Special colors for likes section (red theme)
  likes: "#ef4444",
  likesLight: "#f87171",
  likesDark: "#dc2626",

  // Neutral colors
  gray100: "#f3f4f6",
  gray200: "#e5e7eb",
  gray300: "#d1d5db",
  gray400: "#9ca3af",
  gray500: "#6b7280",
  gray600: "#4b5563",
  gray700: "#374151",
  gray800: "#1f2937",
  gray900: "#111827",

  // Success and error colors
  success: "#10b981",
  error: "#ef4444",
  warning: "#f59e0b",

  // Pie chart colors (blue variations)
  pieChart: [
    "#0065ff", // Primary blue
    "#3b82f6", // Light blue
    "#1d4ed8", // Medium blue
    "#60a5fa", // Lighter blue
    "#1e40af", // Dark blue
    "#93c5fd", // Very light blue
    "#2563eb", // Medium-dark blue
    "#bfdbfe", // Pale blue
  ],

  // Instagram pie chart colors (red variations)
  instagramPieChart: [
    "#E4405F", // Primary Instagram red
    "#F56565", // Light red
    "#C53030", // Dark red
    "#E1306C", // Instagram gradient red
    "#FC8181", // Lighter red
    "#F687B3", // Pink red
    "#ED64A6", // Medium pink
    "#D53F8C", // Dark pink
  ],

  // Gender colors (matching GoalModal design)
  gender: {
    male: "#2196F3", // Blue for male
    female: "#E91E63", // Pink for female
    other: "#9C27B0", // Purple for other
  },

  // Media type colors (blue variations)
  mediaTypes: {
    post: "#0065ff",
    reel: "#3b82f6",
    story: "#60a5fa",
    video: "#1d4ed8",
    igtv: "#2563eb",
  },

  // Contact type colors (blue variations)
  contactTypes: {
    email: "#0065ff",
    phone: "#3b82f6",
    address: "#60a5fa",
    text: "#1d4ed8",
    website: "#2563eb",
  },

  // Age group colors (blue gradient)
  ageGroups: [
    "#0065ff", // 13-17
    "#1d4ed8", // 18-24
    "#3b82f6", // 25-34
    "#60a5fa", // 35-44
    "#2563eb", // 45-54
    "#93c5fd", // 55-64
    "#bfdbfe", // 65+
  ],

  // Instagram age group colors (red gradient)
  instagramAgeGroups: [
    "#E4405F", // 13-17
    "#C53030", // 18-24
    "#F56565", // 25-34
    "#E1306C", // 35-44
    "#FC8181", // 45-54
    "#F687B3", // 55-64
    "#ED64A6", // 65+
  ],
};

// Gradient definitions for area charts
export const GRADIENTS = {
  primary: {
    id: "primaryGradient",
    stops: [
      { offset: "5%", color: CHART_COLORS.primary, opacity: 0.8 },
      { offset: "95%", color: CHART_COLORS.primary, opacity: 0.1 },
    ],
  },
  instagram: {
    id: "instagramGradient",
    stops: [
      { offset: "5%", color: CHART_COLORS.instagram, opacity: 0.8 },
      { offset: "95%", color: CHART_COLORS.instagram, opacity: 0.1 },
    ],
  },
  likes: {
    id: "likesGradient",
    stops: [
      { offset: "5%", color: CHART_COLORS.likes, opacity: 0.8 },
      { offset: "95%", color: CHART_COLORS.likes, opacity: 0.1 },
    ],
  },
  secondary: {
    id: "secondaryGradient",
    stops: [
      { offset: "5%", color: CHART_COLORS.primaryLight, opacity: 0.6 },
      { offset: "95%", color: CHART_COLORS.primaryLight, opacity: 0.05 },
    ],
  },
};

// Helper function to get media type color
export const getMediaTypeColor = (type: string): string => {
  const normalizedType = type.toLowerCase();
  return (
    CHART_COLORS.mediaTypes[
      normalizedType as keyof typeof CHART_COLORS.mediaTypes
    ] || CHART_COLORS.primary
  );
};

// Helper function to get contact type color
export const getContactTypeColor = (type: string): string => {
  const normalizedType = type.toLowerCase();
  return (
    CHART_COLORS.contactTypes[
      normalizedType as keyof typeof CHART_COLORS.contactTypes
    ] || CHART_COLORS.primary
  );
};

// Helper function to get age group color
export const getAgeGroupColor = (index: number, platform?: string): string => {
  const colors = platform?.toLowerCase() === 'instagram'
    ? CHART_COLORS.instagramAgeGroups
    : CHART_COLORS.ageGroups;
  return colors[index % colors.length] || CHART_COLORS.primary;
};

// Helper function to get platform-specific colors
export const getPlatformColors = (platform?: string) => {
  const isInstagram = platform?.toLowerCase() === 'instagram';
  
  return {
    primary: isInstagram ? CHART_COLORS.instagram : CHART_COLORS.primary,
    primaryLight: isInstagram ? CHART_COLORS.instagramLight : CHART_COLORS.primaryLight,
    primaryDark: isInstagram ? CHART_COLORS.instagramDark : CHART_COLORS.primaryDark,
    primaryOpacity: isInstagram ? 'rgba(228, 64, 95, 0.3)' : 'rgba(0, 101, 255, 0.3)', // Low opacity version
    pieChart: isInstagram ? CHART_COLORS.instagramPieChart : CHART_COLORS.pieChart,
    ageGroups: isInstagram ? CHART_COLORS.instagramAgeGroups : CHART_COLORS.ageGroups,
    iconColor: isInstagram ? 'text-red-600' : 'text-blue-600',
    iconBgColor: 'bg-gray-100', // Always gray background for chart icons
    metricIconBgColor: isInstagram ? 'bg-red-100' : 'bg-blue-100', // Separate for metric cards
    gradientId: isInstagram ? 'instagramGradient' : 'primaryGradient',
  };
};

// Helper function to get pie chart color with platform support
export const getPieChartColor = (index: number, platform?: string): string => {
  const colors = platform?.toLowerCase() === 'instagram'
    ? CHART_COLORS.instagramPieChart
    : CHART_COLORS.pieChart;
  return colors[index % colors.length] || CHART_COLORS.primary;
};
