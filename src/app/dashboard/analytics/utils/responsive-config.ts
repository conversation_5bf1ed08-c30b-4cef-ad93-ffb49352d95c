import {
  GridBreakpoint,
  GridConfig,
  WidgetSize,
  ResponsiveWidgetSize,
  WidgetLayout,
} from "@/types/drag-drop";

// Responsive breakpoint configurations
export const RESPONSIVE_BREAKPOINTS = {
  xs: { min: 0, max: 639, columns: 1 },
  sm: { min: 640, max: 767, columns: 2 },
  md: { min: 768, max: 1023, columns: 4 },
  lg: { min: 1024, max: 1279, columns: 8 },
  xl: { min: 1280, max: Infinity, columns: 12 },
} as const;

// Default responsive widget sizes for common widget types
export const DEFAULT_RESPONSIVE_SIZES: Record<string, ResponsiveWidgetSize> = {
  small: {
    xs: { width: 1, height: 1 },
    sm: { width: 1, height: 1 },
    md: { width: 2, height: 1 },
    lg: { width: 2, height: 1 },
    xl: { width: 3, height: 1 },
  },
  medium: {
    xs: { width: 1, height: 2 },
    sm: { width: 2, height: 2 },
    md: { width: 2, height: 2 },
    lg: { width: 3, height: 2 },
    xl: { width: 4, height: 2 },
  },
  large: {
    xs: { width: 1, height: 3 },
    sm: { width: 2, height: 3 },
    md: { width: 4, height: 3 },
    lg: { width: 4, height: 3 },
    xl: { width: 6, height: 3 },
  },
  wide: {
    xs: { width: 1, height: 2 },
    sm: { width: 2, height: 2 },
    md: { width: 4, height: 2 },
    lg: { width: 6, height: 2 },
    xl: { width: 8, height: 2 },
  },
  tall: {
    xs: { width: 1, height: 4 },
    sm: { width: 1, height: 4 },
    md: { width: 2, height: 4 },
    lg: { width: 3, height: 4 },
    xl: { width: 4, height: 4 },
  },
  fullWidth: {
    xs: { width: 1, height: 2 },
    sm: { width: 2, height: 2 },
    md: { width: 4, height: 2 },
    lg: { width: 8, height: 2 },
    xl: { width: 12, height: 2 },
  },
};

// Mobile-specific configurations
export const MOBILE_CONFIG = {
  forceStackLayout: true,
  singleColumnWidth: 1,
  minHeight: 150,
  maxHeight: 400,
  gap: "0.5rem",
  padding: "0.75rem",
};

// Tablet-specific configurations
export const TABLET_CONFIG = {
  preferredColumns: 2,
  minWidgetWidth: 1,
  maxWidgetWidth: 2,
  gap: "0.75rem",
  padding: "1rem",
};

// Desktop-specific configurations
export const DESKTOP_CONFIG = {
  preferredColumns: 12,
  minWidgetWidth: 2,
  maxWidgetWidth: 12,
  gap: "1rem",
  padding: "1.5rem",
};

/**
 * Get responsive configuration for a specific breakpoint
 */
export const getResponsiveConfig = (breakpoint: GridBreakpoint) => {
  switch (breakpoint) {
    case "xs":
    case "sm":
      return {
        ...MOBILE_CONFIG,
        columns: RESPONSIVE_BREAKPOINTS[breakpoint].columns,
      };
    case "md":
      return {
        ...TABLET_CONFIG,
        columns: RESPONSIVE_BREAKPOINTS[breakpoint].columns,
      };
    case "lg":
    case "xl":
      return {
        ...DESKTOP_CONFIG,
        columns: RESPONSIVE_BREAKPOINTS[breakpoint].columns,
      };
    default:
      return DESKTOP_CONFIG;
  }
};

/**
 * Adapt widget size for specific breakpoint
 */
export const adaptWidgetSizeForBreakpoint = (
  size: WidgetSize,
  breakpoint: GridBreakpoint,
  maxColumns: number
): WidgetSize => {
  const config = getResponsiveConfig(breakpoint);

  // For mobile, force single column
  if (breakpoint === "xs" && MOBILE_CONFIG.forceStackLayout) {
    return {
      width: 1,
      height: size.height,
    };
  }

  // Clamp width to available columns
  const adaptedWidth = Math.min(size.width, maxColumns);

  // Apply breakpoint-specific constraints
  let finalWidth = adaptedWidth;

  if ("minWidgetWidth" in config) {
    finalWidth = Math.max(finalWidth, config.minWidgetWidth);
  }

  if ("maxWidgetWidth" in config) {
    finalWidth = Math.min(finalWidth, config.maxWidgetWidth);
  }

  return {
    width: finalWidth,
    height: size.height,
  };
};

/**
 * Create responsive widget layout for mobile stacking
 */
export const createMobileStackLayout = (
  widgets: WidgetLayout[],
  stackingOrder: "original" | "priority" | "size" = "original"
): WidgetLayout[] => {
  let sortedWidgets = [...widgets];

  // Apply stacking order
  switch (stackingOrder) {
    case "priority":
      sortedWidgets.sort((a, b) => {
        const priorityA = (a.config as any).priority || 0;
        const priorityB = (b.config as any).priority || 0;

        if (priorityA !== priorityB) {
          return priorityB - priorityA;
        }

        // Fallback to area
        const areaA = a.size.width * a.size.height;
        const areaB = b.size.width * b.size.height;
        return areaB - areaA;
      });
      break;

    case "size":
      sortedWidgets.sort((a, b) => {
        const areaA = a.size.width * a.size.height;
        const areaB = b.size.width * b.size.height;
        return areaB - areaA;
      });
      break;

    case "original":
    default:
      sortedWidgets.sort((a, b) => {
        if (a.position.row !== b.position.row) {
          return a.position.row - b.position.row;
        }
        return a.position.col - b.position.col;
      });
      break;
  }

  // Stack widgets vertically
  return sortedWidgets.map((widget, index) => ({
    ...widget,
    position: { row: index, col: 0 },
    size: {
      width: MOBILE_CONFIG.singleColumnWidth,
      height: Math.max(
        widget.size.height,
        Math.ceil(MOBILE_CONFIG.minHeight / 200) // Convert px to grid units
      ),
    },
  }));
};

/**
 * Generate CSS media queries for breakpoints
 */
export const generateMediaQueries = () => {
  return Object.entries(RESPONSIVE_BREAKPOINTS).map(([breakpoint, config]) => {
    const minWidth = config.min;
    const maxWidth =
      config.max === Infinity ? "" : ` and (max-width: ${config.max}px)`;

    return {
      breakpoint: breakpoint as GridBreakpoint,
      query: `(min-width: ${minWidth}px)${maxWidth}`,
      columns: config.columns,
    };
  });
};

/**
 * Get optimal widget size for content type
 */
export const getOptimalWidgetSize = (
  contentType: "chart" | "metric" | "table" | "text" | "image",
  breakpoint: GridBreakpoint
): WidgetSize => {
  const sizeMap = {
    chart: DEFAULT_RESPONSIVE_SIZES.medium,
    metric: DEFAULT_RESPONSIVE_SIZES.small,
    table: DEFAULT_RESPONSIVE_SIZES.large,
    text: DEFAULT_RESPONSIVE_SIZES.medium,
    image: DEFAULT_RESPONSIVE_SIZES.medium,
  };

  return sizeMap[contentType][breakpoint] || sizeMap[contentType].md;
};

/**
 * Calculate responsive grid template columns CSS
 */
export const generateGridTemplateColumns = (
  breakpoint: GridBreakpoint,
  customColumns?: number
): string => {
  const columns = customColumns || RESPONSIVE_BREAKPOINTS[breakpoint].columns;
  return `repeat(${columns}, minmax(0, 1fr))`;
};

/**
 * Generate responsive CSS classes for Tailwind
 */
export const generateResponsiveTailwindClasses = (
  baseClasses: string,
  responsiveClasses: Partial<Record<GridBreakpoint, string>>
): string => {
  const classes = [baseClasses];

  Object.entries(responsiveClasses).forEach(([breakpoint, className]) => {
    if (className) {
      const prefix = breakpoint === "xs" ? "" : `${breakpoint}:`;
      classes.push(`${prefix}${className}`);
    }
  });

  return classes.join(" ");
};

/**
 * Validate responsive widget configuration
 */
export const validateResponsiveConfig = (
  responsiveSize: ResponsiveWidgetSize
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Check that all breakpoints are defined
  const requiredBreakpoints: GridBreakpoint[] = ["xs", "sm", "md", "lg", "xl"];

  for (const breakpoint of requiredBreakpoints) {
    if (!responsiveSize[breakpoint]) {
      errors.push(`Missing size configuration for breakpoint: ${breakpoint}`);
      continue;
    }

    const size = responsiveSize[breakpoint];
    const maxColumns = RESPONSIVE_BREAKPOINTS[breakpoint].columns;

    // Validate size constraints
    if (size.width > maxColumns) {
      errors.push(
        `Width ${size.width} exceeds maximum columns ${maxColumns} for breakpoint ${breakpoint}`
      );
    }

    if (size.width < 1 || size.height < 1) {
      errors.push(
        `Invalid size ${size.width}x${size.height} for breakpoint ${breakpoint} (minimum 1x1)`
      );
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Create default responsive size based on widget type
 */
export const createDefaultResponsiveSize = (
  widgetType: string,
  customSizes?: Partial<ResponsiveWidgetSize>
): ResponsiveWidgetSize => {
  // Determine base size category
  let baseSizeKey: keyof typeof DEFAULT_RESPONSIVE_SIZES = "medium";

  if (widgetType.includes("metric") || widgetType.includes("counter")) {
    baseSizeKey = "small";
  } else if (widgetType.includes("table") || widgetType.includes("list")) {
    baseSizeKey = "large";
  } else if (widgetType.includes("chart") && widgetType.includes("wide")) {
    baseSizeKey = "wide";
  } else if (widgetType.includes("full")) {
    baseSizeKey = "fullWidth";
  }

  const baseSize = DEFAULT_RESPONSIVE_SIZES[baseSizeKey];

  // Merge with custom sizes
  return {
    xs: customSizes?.xs || baseSize.xs,
    sm: customSizes?.sm || baseSize.sm,
    md: customSizes?.md || baseSize.md,
    lg: customSizes?.lg || baseSize.lg,
    xl: customSizes?.xl || baseSize.xl,
  };
};
