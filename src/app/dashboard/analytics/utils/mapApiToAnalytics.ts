import { AnalyticsData, AnalyticsDataPoint } from "../types";

// Normalize various API response series formats into AnalyticsDataPoint[]
function normalizeSeries(input: any): AnalyticsDataPoint[] {
  if (!input) return [];

  // If already in desired shape
  if (Array.isArray(input) && input.length > 0 && ("total_value" in input[0] || "breakdown" in input[0] || "date" in input[0])) {
    // Ensure date is a YYYY-MM-DD string if present
    return input.map((it: any) => ({
      total_value: Number(it.total_value ?? it.value ?? 0),
      date: it.date ? String(it.date).split("T")[0] : it.end_time ? String(it.end_time).split("T")[0] : undefined,
      breakdown: it.breakdown,
      percentage: it.percentage,
    }));
  }

  // New: handle object-shaped metric with values/total_value (e.g., { name, period, values: [...]} or { total_value: {...} })
  if (!Array.isArray(input) && typeof input === 'object') {
    const series: AnalyticsDataPoint[] = [];

    // Case: time series values on object
    if (Array.isArray(input.values)) {
      input.values.forEach((v: any) => {
        const date = (v.date || v.end_time) ? String(v.date || v.end_time).split("T")[0] : undefined;
        if (typeof v?.value === "number" && (v?.name || v?.city) && (!date || String(input?.name || '').toLowerCase().includes('city'))) {
          // City list or undated value list with names
          const city = String(v.city || v.name);
          series.push({ total_value: Number(v.value) || 0, breakdown: { city } });
        } else if (typeof v?.value === "number") {
          series.push({ total_value: Number(v.value) || 0, date });
        } else if (v && typeof v?.value === 'object') {
          // value can be object with breakdown counts
          const breakdownKeys = Object.keys(v.value);
          breakdownKeys.forEach((key) => {
            const val = v.value[key];
            if (typeof val === 'number') {
              const breakdown: any = {};
              if (["post", "reel", "story", "video", "POST", "REEL", "STORY", "VIDEO", "CAROUSEL_CONTAINER", "IGTV"].includes(key)) breakdown.media_product_type = key;
              if (["follow", "unfollow", "follower", "non_follower", "FOLLOWER", "NON_FOLLOWER"].includes(key)) breakdown.follow_type = key;
              if (["email", "phone", "address", "text"].includes(key)) breakdown.contact_button_type = key;
              if (Object.keys(breakdown).length === 0) breakdown[key] = key;
              series.push({ total_value: Number(val) || 0, date, breakdown });
            }
          });
        }
      });
    }

    // Case: total_value on object (could be { value } or { breakdowns: [...] } or an array)
    const tv = input.total_value;
    if (tv && typeof tv === 'object' && !Array.isArray(tv)) {
      if (typeof tv.value === 'number') {
        series.push({ total_value: Number(tv.value) || 0 });
      }
      if (Array.isArray(tv.breakdowns) && tv.breakdowns.length > 0) {
        tv.breakdowns.forEach((bd: any) => {
          const dimKeys: string[] = Array.isArray(bd.dimension_keys) ? bd.dimension_keys : [];
          const results: any[] = Array.isArray(bd.results) ? bd.results : [];
          results.forEach((r) => {
            const breakdown: Record<string, any> = {};
            const vals: any[] = Array.isArray(r.dimension_values) ? r.dimension_values : [];
            dimKeys.forEach((key, idx) => {
              breakdown[key] = vals[idx];
            });
            series.push({ total_value: Number(r.value) || 0, breakdown });
          });
        });
      }
    } else if (Array.isArray(tv)) {
      tv.forEach((c: any) => {
        const city = c?.city || c?.name;
        const value = c?.value ?? c?.count;
        if (city && typeof value === 'number') {
          series.push({ total_value: Number(value) || 0, breakdown: { city: String(city) } });
        }
      });
    }

    if (series.length) return series;
  }

  // Instagram-like insights: [{ name, values: [{ end_time, value }] }]
  if (Array.isArray(input) && input.length > 0 && input[0]?.values) {
    const series: AnalyticsDataPoint[] = [];
    input.forEach((metric: any) => {
      if (Array.isArray(metric.values)) {
        metric.values.forEach((v: any) => {
          const date = (v.date || v.end_time) ? String(v.date || v.end_time).split("T")[0] : undefined;
          // value can be number or object (breakdown)
          if (typeof v.value === "number") {
            series.push({ total_value: v.value, date });
          } else if (v.value && typeof v.value === "object") {
            // Try common breakdown keys
            const breakdownKeys = Object.keys(v.value);
            breakdownKeys.forEach((key) => {
              const val = v.value[key];
              // When nested objects contain counts
              if (typeof val === "number") {
                // Map common breakdown dimensions
                const breakdown: any = {};
                if (["post", "reel", "story", "video", "POST", "REEL", "STORY", "VIDEO", "CAROUSEL_CONTAINER", "IGTV"].includes(key)) breakdown.media_product_type = key;
                if (["follow", "unfollow", "follower", "non_follower", "FOLLOWER", "NON_FOLLOWER"].includes(key)) breakdown.follow_type = key;
                if (["email", "phone", "address", "text"].includes(key)) breakdown.contact_button_type = key;
                // Fallback: store as generic type label when dimension unknown
                if (Object.keys(breakdown).length === 0) breakdown[key] = key;
                series.push({ total_value: val, date, breakdown });
              }
            });
          }
        });
      }
    });
    return series;
  }

  // Flat number array -> map to recent dates
  if (Array.isArray(input) && (typeof input[0] === "number")) {
    const days = input.length;
    return input.map((num: number, idx: number) => ({
      total_value: Number(num) || 0,
      date: new Date(Date.now() - (days - idx - 1) * ********).toISOString().split("T")[0],
    }));
  }

  // Unknown format -> empty
  return [];
}

export function mapApiToAnalytics(raw: any): AnalyticsData | null {
  if (!raw) return null;

  // Try various shapes
  const root = (raw.data?.analytics) || raw.data || raw.analytics_data || raw.analytics || raw;

  // Known metric keys expected by the UI
  const keys = [
    "accounts_engaged",
    "comments",
    "engaged_audience_demographics",
    "follows_and_unfollows",
    "follower_demographics",
    "likes",
    "profile_links_taps",
    "reach",
    "replies",
    "saves",
    "shares",
    "total_interactions",
    "views",
    "follower_count",
  ] as const;

  const result: any = {};
  let foundAny = false;
  keys.forEach((k) => {
    const series = normalizeSeries(root?.[k]);
    if (series && series.length) {
      foundAny = true;
    }
    result[k] = series || [];
  });

  // Carry over simple numeric counters if present on the payload root
  if (typeof (root as any)?.followers_count === 'number') {
    (result as any).followers_count = Number((root as any).followers_count);
    foundAny = true;
  }
  if (typeof (root as any)?.follows_count === 'number') {
    (result as any).follows_count = Number((root as any).follows_count);
    foundAny = true;
  }
  // Media count snapshot
  if (typeof (root as any)?.media_count === 'number') {
    (result as any).media_count = Number((root as any).media_count);
    foundAny = true;
  }

  // New: provider may split follower_demographics into multiple keys like
  // follower_demographics_age,gender / follower_demographics_city / follower_demographics_country
  // Merge all such variants into the unified follower_demographics bucket expected by the UI
  if (root && typeof root === 'object') {
    const followerVariantKeys = Object.keys(root).filter((rk) => rk.startsWith('follower_demographics'));
    if (followerVariantKeys.length > 0) {
      const merged: AnalyticsDataPoint[] = Array.isArray(result['follower_demographics']) ? [...result['follower_demographics']] : [];
      followerVariantKeys.forEach((vk) => {
        const series = normalizeSeries((root as any)[vk]);
        if (Array.isArray(series) && series.length > 0) {
          merged.push(...series);
        }
      });
      if (merged.length > 0) {
        result['follower_demographics'] = merged;
        foundAny = true;
      }
    }
  }

  // Additionally, try to map from a generic array of items: root.data: [{ name, values | total_value }]
  // Do this even if some metrics were already found, to backfill missing ones.
  if (Array.isArray(root?.data)) {
    const items = root.data as any[];
    items.forEach((item) => {
      const name: string | undefined = item?.name;
      const lowerName = (name || '').toLowerCase();
      const isKnown = !!name && (keys as readonly string[]).includes(name as any);

      const series: AnalyticsDataPoint[] = [];
      // Case 1: time series values
      if (Array.isArray(item.values)) {
        item.values.forEach((v: any) => {
          const date = (v.date || v.end_time) ? String(v.date || v.end_time).split("T")[0] : undefined;
          if (typeof v?.value === "number" && (v?.name || v?.city) && (lowerName.includes('city') || !date)) {
            // Values list representing city breakdowns: { name|city, value }
            const city = String(v.city || v.name);
            series.push({ total_value: Number(v.value) || 0, breakdown: { city } });
          } else if (typeof v?.value === "number") {
            series.push({ total_value: Number(v.value) || 0, date });
          } else if ((v?.city || v?.name) && typeof v?.count === 'number') {
            // Alternative shape: { city|name, count }
            const city = String(v.city || v.name);
            series.push({ total_value: Number(v.count) || 0, breakdown: { city } });
          }
        });
      }

      // Case 2: total_value possibly with breakdowns
      const tv = item.total_value;
      if (tv && typeof tv === "object") {
        if (typeof tv.value === "number") {
          series.push({ total_value: Number(tv.value) || 0 });
        }
        if (Array.isArray(tv.breakdowns) && tv.breakdowns.length > 0) {
          tv.breakdowns.forEach((bd: any) => {
            const dimKeys: string[] = Array.isArray(bd.dimension_keys) ? bd.dimension_keys : [];
            const results: any[] = Array.isArray(bd.results) ? bd.results : [];
            results.forEach((r) => {
              const breakdown: Record<string, any> = {};
              const vals: any[] = Array.isArray(r.dimension_values) ? r.dimension_values : [];
              dimKeys.forEach((key, idx) => {
                breakdown[key] = vals[idx];
              });
              series.push({ total_value: Number(r.value) || 0, breakdown });
            });
          });
        }
      } else if (Array.isArray(tv)) {
        // total_value could be a list of cities: [{ name|city, value }]
        tv.forEach((c: any) => {
          const city = c?.city || c?.name;
          const value = c?.value ?? c?.count;
          if (city && typeof value === 'number') {
            series.push({ total_value: Number(value) || 0, breakdown: { city: String(city) } });
          }
        });
      }

      if (isKnown && name) {
        // Merge/backfill into its named bucket
        const existing = result[name];
        const existingHasDates = Array.isArray(existing) && existing.some((it: any) => !!it?.date);
        const newHasDates = Array.isArray(series) && series.some((it: any) => !!it?.date);
        if (!Array.isArray(existing) || existing.length === 0) {
          result[name] = series;
        } else if (!existingHasDates && newHasDates) {
          // Prefer dated series over non-dated
          result[name] = series;
        } else {
          // Default: concatenate to preserve multiple sources (both dated or both undated)
          result[name] = [...existing, ...series];
        }
        if (series.length) foundAny = true;
      } else {
        // Unknown name: if this item is likely the city metric, merge into reach for cities section
        const hasCityBreakdown =
          lowerName.includes('city') ||
          (Array.isArray(item?.total_value?.breakdowns) && item.total_value.breakdowns.some((bd: any) => (bd?.dimension_keys || []).includes('city')));

        const looksLikeCityList = Array.isArray(item?.values) && item.values.some((v: any) => (v?.city || v?.name) && (typeof v?.value === 'number' || typeof v?.count === 'number'));

        if ((hasCityBreakdown || looksLikeCityList) && series.length) {
          const existingReach = result['reach'] || [];
          result['reach'] = Array.isArray(existingReach) ? [...existingReach, ...series] : series;
          foundAny = true;
        }
      }
    });
  }

  // If still nothing found but root has nested "insights" collections by name
  if (!foundAny && Array.isArray(root?.insights)) {
    const insights = root.insights as any[];
    insights.forEach((item) => {
      if (item?.name && (keys as readonly string[]).includes(item.name)) {
        result[item.name] = normalizeSeries(item.values);
        foundAny = foundAny || (result[item.name]?.length > 0);
      }
    });
  }

  return foundAny ? (result as AnalyticsData) : null;
}
