"use client";

import React, { forwardRef, CSSProperties } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { cn } from "@/lib/utils";
import { DraggableWidgetProps } from "@/types/drag-drop";

// Drag handle component
interface DragHandleProps {
  className?: string;
  isDragging?: boolean;
}

export function DragHandle({ className, isDragging }: DragHandleProps) {
  return (
    <div
      className={cn(
        "drag-handle cursor-grab active:cursor-grabbing",
        "flex items-center rounded-xl justify-center",
        "w-6 h-6 rounded  transition-colors",
        "opacity-0 group-hover:opacity-100",
        isDragging && "opacity-100 cursor-grabbing",
        className
      )}
      aria-label="Drag to reorder"
    >
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="text-gray-400"
      >
        <circle cx="3" cy="3" r="1" fill="currentColor" />
        <circle cx="9" cy="3" r="1" fill="currentColor" />
        <circle cx="3" cy="6" r="1" fill="currentColor" />
        <circle cx="9" cy="6" r="1" fill="currentColor" />
        <circle cx="3" cy="9" r="1" fill="currentColor" />
        <circle cx="9" cy="9" r="1" fill="currentColor" />
      </svg>
    </div>
  );
}

// Main draggable widget component
export const DraggableWidget = forwardRef<HTMLDivElement, DraggableWidgetProps>(
  (
    {
      id,
      children,
      isDragDisabled = false,
      dragHandle = true,
      onDragStart,
      onDragEnd,
      className,
      ...props
    },
    ref
  ) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
      isSorting,
    } = useSortable({
      id,
      disabled: isDragDisabled,
    });

    // Handle drag start
    React.useEffect(() => {
      if (isDragging && onDragStart) {
        onDragStart(id);
      }
    }, [isDragging, onDragStart, id]);

    // Handle drag end
    React.useEffect(() => {
      if (!isDragging && !isSorting && onDragEnd) {
        onDragEnd(id);
      }
    }, [isDragging, isSorting, onDragEnd, id]);

    // Transform styles for drag animation
    const style: CSSProperties = {
      transform: CSS.Transform.toString(transform),
      transition,
      zIndex: isDragging ? 1000 : 1,
    };

    // Drag handle props (only if dragHandle is true)
    const dragHandleProps = dragHandle
      ? {
          ...attributes,
          ...listeners,
        }
      : {};

    // Widget props (if dragHandle is false, entire widget is draggable)
    const widgetProps = !dragHandle
      ? {
          ...attributes,
          ...listeners,
        }
      : {};

    return (
      <div
        ref={(node) => {
          setNodeRef(node);
          if (typeof ref === "function") {
            ref(node);
          } else if (ref) {
            ref.current = node;
          }
        }}
        style={style}
        className={cn(
          "group relative",
          "transition-all duration-200 ease-in-out",
          isDragging && [
            "opacity-50",
            "scale-105",
            // remove wrapper shadow to avoid double shadow with chart container
            "rotate-2",
            "z-50",
          ],
          // no hover shadow on wrapper
          className
        )}
        {...widgetProps}
        {...props}
      >
        {/* Drag handle (if enabled) */}
        {dragHandle && !isDragDisabled && (
          <div className="absolute top-2 right-2 z-10" {...dragHandleProps}>
            <DragHandle isDragging={isDragging} />
          </div>
        )}

        {/* Widget content */}
        <div
          className={cn(
            "relative h-full w-full",
            isDragging && "pointer-events-none",
            !dragHandle &&
              !isDragDisabled &&
              "cursor-grab active:cursor-grabbing"
          )}
        >
          {children}
        </div>

        {/* Drag overlay indicator */}
        {isDragging && (
          <div className="absolute inset-0 bg-blue-500/10 border-2 border-blue-500 border-dashed rounded-lg pointer-events-none" />
        )}
      </div>
    );
  }
);

DraggableWidget.displayName = "DraggableWidget";

// Higher-order component for making any component draggable
export function withDraggable<P extends object>(
  Component: React.ComponentType<P>
) {
  const DraggableComponent = forwardRef<
    HTMLDivElement,
    P & DraggableWidgetProps
  >((props, ref) => {
    const {
      id,
      isDragDisabled,
      dragHandle,
      onDragStart,
      onDragEnd,
      className,
      ...componentProps
    } = props;

    return (
      <DraggableWidget
        ref={ref}
        id={id}
        isDragDisabled={isDragDisabled}
        dragHandle={dragHandle}
        onDragStart={onDragStart}
        onDragEnd={onDragEnd}
        className={className}
      >
        <Component {...(componentProps as P)} />
      </DraggableWidget>
    );
  });

  DraggableComponent.displayName = `withDraggable(${
    Component.displayName || Component.name
  })`;

  return DraggableComponent;
}

// Utility component for drag preview
interface DragPreviewProps {
  title: string;
  description?: string;
  className?: string;
}

export function DragPreview({
  title,
  description,
  className,
}: DragPreviewProps) {
  return (
    <div
      className={cn(
        "bg-white shadow-surface rounded-lg border-2 border-blue-500",
        "p-4 min-w-[200px] max-w-[300px]",
        "opacity-90 transform rotate-2",
        className
      )}
    >
      <h3 className="font-semibold text-sm text-gray-800 truncate">{title}</h3>
      {description && (
        <p className="text-xs text-gray-600 mt-1 line-clamp-2">{description}</p>
      )}
      <div className="mt-2 flex items-center text-xs text-blue-600">
        <svg
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="none"
          className="mr-1"
        >
          <path
            d="M6 1L7.5 4.5L11 4.5L8.25 7L9.5 10.5L6 8.5L2.5 10.5L3.75 7L1 4.5L4.5 4.5L6 1Z"
            fill="currentColor"
          />
        </svg>
        Moving...
      </div>
    </div>
  );
}

// Hook for drag state management
export function useDragState(id: string) {
  const { isDragging } = useSortable({ id });

  return {
    isDragging,
    dragClasses: cn(
      isDragging && [
        "opacity-50",
        "scale-105",
        "shadow-2xl",
        "rotate-2",
        "z-50",
      ]
    ),
  };
}
