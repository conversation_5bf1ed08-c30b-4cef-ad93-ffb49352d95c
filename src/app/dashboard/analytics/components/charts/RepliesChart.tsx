"use client";

import { useMemo } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { BaseChart } from '../shared/BaseChart';
import { demoAnalyticsData } from '../../utils/demoData';

interface RepliesChartProps {
  data: any[];
  timeRange: '7d' | '30d';
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
  mobileHeight?: string;
  bottomPadding?: number;
}

export const RepliesChart = ({ data, timeRange, selectedSocial, height, mobileHeight, bottomPadding }: RepliesChartProps) => {
  const chartData = useMemo(() => {
    const effectiveData = data && data.length > 0 ? data : demoAnalyticsData.replies;
    if (!effectiveData || effectiveData.length === 0) return [];
    
    return effectiveData.map((item, index) => ({
      day: timeRange === '7d' ? `Day ${index + 1}` : `Day ${index + 1}`,
      replies: item.total_value || 0,
      date: item.date || `2024-01-${String(index + 1).padStart(2, '0')}`
    }));
  }, [data, timeRange]);

  const totalReplies = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.replies, 0);
  }, [chartData]);

  const averageReplies = useMemo(() => {
    return chartData.length > 0 ? Math.round(totalReplies / chartData.length) : 0;
  }, [totalReplies, chartData.length]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="text-indigo-600 font-medium">
                Replies: {payload[0]?.value?.toLocaleString()}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <BaseChart
      title="Replies"
      selectedSocial={selectedSocial}
      height={height}
      mobileHeight={mobileHeight}
      bottomPadding={bottomPadding}
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="day"
            tick={{ fontSize: 12 }}
            interval="preserveStartEnd"
          />
          <YAxis
            tick={{ fontSize: 12 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Line
            type="monotone"
            dataKey="replies"
            stroke="#6366f1"
            strokeWidth={2}
            dot={{ fill: '#6366f1', strokeWidth: 1.5, r: 2 }}
            activeDot={{ r: 4, stroke: '#6366f1', strokeWidth: 1.5 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </BaseChart>
  );
};
