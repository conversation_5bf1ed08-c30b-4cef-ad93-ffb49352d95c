"use client";

import { useMemo } from "react";
import { BaseChart } from "../shared/BaseChart";
import { CHART_COLORS } from "../../utils/colors";

interface FollowersCountriesChartProps {
  data: any[]; // expect follower_demographics-like series
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
  // Total followers count used to compute percentage widths for bars
  totalFollowers?: number;
}

export const FollowersCountriesChart = ({
  data,
  selectedSocial,
  height = "320px",
  totalFollowers,
}: FollowersCountriesChartProps) => {
  // Build country list from breakdowns
  const countryList = useMemo(() => {
    const map: Record<string, number> = {};
    (Array.isArray(data) ? data : []).forEach((item: any) => {
      const b = item?.breakdown || {};
      const val = Number(item?.total_value ?? 0) || 0;
      const key = b.country || item?.country;
      if (key) map[String(key)] = (map[String(key)] || 0) + val;
    });
    const arr = Object.entries(map).map(([name, value]) => ({ name, value }));
    return arr.sort((a, b) => b.value - a.value);
  }, [data]);

  // Denominator for percentage widths: prefer provided totalFollowers, else sum of country values
  const totalFollowersValue = useMemo(() => {
    if (typeof totalFollowers === "number" && totalFollowers > 0) return totalFollowers;
    const sum = countryList.reduce((acc: number, d: any) => acc + (Number(d.value) || 0), 0);
    return sum > 0 ? sum : 1; // avoid divide-by-zero
  }, [countryList, totalFollowers]);

  const topCountry = countryList[0]?.name || "N/A";

  return (
    <BaseChart
      title="Followers / Countries"
      description={`Top country: ${topCountry}`}
      value={topCountry}
      minHeight={height}
      height={height}
      selectedSocial={selectedSocial}
    >
      <div
        className="space-y-2 p-2 overflow-y-auto pr-2"
        style={{ maxHeight: "220px" }}
      >
        {countryList.map((item: any, index: number) => (
          <div key={index} className="bg-white rounded-lg px-1 py-1">
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm font-medium">{item.name}</span>
              <span className="text-sm text-gray-600">
                {item.value.toLocaleString()}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="h-2.5 rounded-full"
                style={{
                  width: `${Math.min(100, Math.max(0, (Number(item.value) || 0) / totalFollowersValue * 100))}%`,
                  backgroundColor: CHART_COLORS.instagram,
                }}
              ></div>
            </div>
          </div>
        ))}
        {countryList.length === 0 && (
          <div className="text-sm text-gray-500">
            No country data available.
          </div>
        )}
      </div>
    </BaseChart>
  );
};
