"use client";

import { useMemo } from "react";
import { BaseChart } from "../shared/BaseChart";
import { CHART_COLORS } from "../../utils/colors";

interface AccountReachCitiesChartProps {
  data?: any[];
  timeRange?: "7d" | "30d";
  height?: string;
  // Max height for the bars list; overflow will scroll beyond this height
  listMaxHeight?: string;
  // Total followers count used to compute percentage widths for bars
  totalFollowers?: number;
}

export const AccountReachCitiesChart = ({
  data,
  timeRange,
  height = "450px",
  listMaxHeight = "220px",
  totalFollowers,
}: AccountReachCitiesChartProps) => {
  const chartData = useMemo(() => {
    if (data && data.length > 0) {
      const mapped = data.map((item, index) => ({
        name: item.city || item.name || `City ${index + 1}`,
        value: item.reach || item.value || 0,
      }));
      // Sort descending by value
      return mapped.sort((a: any, b: any) => b.value - a.value);
    }

    // Return empty array if no data
    return [];
  }, [data]);

  // Determine the denominator for percentage widths: prefer provided totalFollowers, otherwise sum of values
  const totalFollowersValue = useMemo(() => {
    if (typeof totalFollowers === "number" && totalFollowers > 0)
      return totalFollowers;
    const sum = chartData.reduce(
      (acc: number, d: any) => acc + (Number(d.value) || 0),
      0
    );
    return sum > 0 ? sum : 1; // avoid divide-by-zero
  }, [chartData, totalFollowers]);

  const topCity = useMemo(() => {
    return chartData.length > 0 ? chartData[0]?.name || "N/A" : "No data";
  }, [chartData]);

  return (
    <BaseChart
      title="Account Reach / Cities"
      description={`Top city: ${topCity}`}
      value={topCity}
      minHeight="320px"
      height={height}
      selectedSocial={{ platform: "instagram" }}
    >
      {chartData.length === 0 ? (
        <div className="flex items-center justify-center h-full min-h-[200px]">
          <div className="text-center">
            <p className="text-gray-500 text-lg mb-2">No data available</p>
            <p className="text-gray-400 text-sm">
              City reach data will appear here when available
            </p>
          </div>
        </div>
      ) : (
        <div
          className="space-y-2 p-2 overflow-y-auto pr-2"
          style={{ maxHeight: listMaxHeight }}
        >
          {chartData.map((item, index) => (
            <div key={index} className="bg-white rounded-lg px-1 py-1 ">
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium">{item.name}</span>
                <span className="text-sm text-gray-600">
                  {item.value.toLocaleString()}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="h-2.5 rounded-full"
                  style={{
                    width: `${Math.min(
                      100,
                      Math.max(
                        0,
                        ((Number(item.value) || 0) / totalFollowersValue) * 100
                      )
                    )}%`,
                    backgroundColor: CHART_COLORS.instagram,
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      )}
    </BaseChart>
  );
};
