"use client";

import { useMemo, useState } from "react";
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Sector,
} from "recharts";
import { BaseChart } from "../shared/BaseChart";
import { CHART_COLORS, getPlatformColors } from "../../utils/colors";

interface GenderDemographicsChartProps {
  data: any[];
  title?: string;
  description?: string;
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
}

// Gender icon mapping
const GENDER_ICONS = {
  male: "fa-mars",
  female: "fa-venus",
  other: "fa-genderless",
};

export const GenderDemographicsChart = ({
  data,
  title = "Gender Distribution",
  description = "Gender breakdown of your audience",
  selectedSocial,
  height = "400px",
}: GenderDemographicsChartProps) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const platformColors = getPlatformColors(selectedSocial?.platform);

  const genderData = useMemo(() => {
    let effectiveData = data && data.length > 0 ? data : [];
    if (effectiveData.length === 0) {
      return [];
    }

    // Aggregate by gender across all ages/cities/etc.
    const normalizeGender = (g: string): "male" | "female" | "other" => {
      const s = (g || "").toLowerCase();
      if (s === "m" || s === "male") return "male";
      if (s === "f" || s === "female") return "female";
      return "other"; // includes 'u'
    };

    const genderMap: Record<"male" | "female" | "other", number> = {
      male: 0,
      female: 0,
      other: 0,
    };

    effectiveData.forEach((item: any) => {
      const g = item?.breakdown?.gender;
      if (!g) return;
      const key = normalizeGender(String(g));
      const val = Number(item?.total_value ?? 0) || 0;
      genderMap[key] += val;
    });

    const total = Object.values(genderMap).reduce((s, v) => s + v, 0) || 1;
    const rows = [
      { key: "male" as const, icon: GENDER_ICONS.male },
      { key: "female" as const, icon: GENDER_ICONS.female },
      { key: "other" as const, icon: GENDER_ICONS.other },
    ];

    return rows
      .map(({ key, icon }) => {
        const value = genderMap[key] || 0;
        const percentage = (value / total) * 100;
        const name = key.charAt(0).toUpperCase() + key.slice(1);
        const color = CHART_COLORS.gender[key] || CHART_COLORS.gray500;
        return { name, value, percentage, color, icon };
      })
      .filter((r) => r.value > 0);
  }, [data]);

  const totalValue = useMemo(() => {
    return genderData.reduce((sum, item) => sum + item.value, 0);
  }, [genderData]);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <div className="space-y-1">
            <p className="text-sm">
              <span className="font-medium">{data.name}: </span>
              <span
                className="font-medium"
                style={{ color: data.payload.color }}
              >
                {data.value?.toLocaleString()} (
                {data.payload.percentage?.toFixed(1)}%)
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const CustomLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
    payload,
  }: any) => {
    // Always show labels INSIDE to avoid clipping during active slice expansion.
    const RADIAN = Math.PI / 180;
    const labelRadius = innerRadius + (outerRadius - innerRadius) * 0.55;
    const x = cx + labelRadius * Math.cos(-midAngle * RADIAN);
    const y = cy + labelRadius * Math.sin(-midAngle * RADIAN);

    // Contrast-aware text color for inside labels
    const getContrastColor = (hex: string) => {
      if (!hex) return "#111827"; // gray-900 fallback
      const c = hex.replace("#", "").trim();
      if (!(c.length === 3 || c.length === 6)) return "#111827";
      const rr = c.length === 3 ? c.charAt(0) + c.charAt(0) : c.substring(0, 2);
      const gg = c.length === 3 ? c.charAt(1) + c.charAt(1) : c.substring(2, 4);
      const bb = c.length === 3 ? c.charAt(2) + c.charAt(2) : c.substring(4, 6);
      const r = parseInt(rr, 16);
      const g = parseInt(gg, 16);
      const b = parseInt(bb, 16);
      // Relative luminance
      const lum = 0.2126 * r + 0.7152 * g + 0.0722 * b;
      return lum > 160 ? "#111827" : "#ffffff"; // dark text on light bg, white on dark
    };

    const textColor = getContrastColor(String(payload?.color || "#111827"));

    return (
      <text
        x={x}
        y={y}
        fill={textColor}
        stroke="#000"
        strokeWidth={2}
        strokeOpacity={0.3}
        paintOrder="stroke"
        pointerEvents="none"
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (genderData.length === 0) {
    return (
      <BaseChart
        title={title}
        selectedSocial={selectedSocial}
        // Ensure this card appears first on mobile and keeps grid span at xl
        className="order-1 xl:order-none xl:col-span-1"
        height={height}
        mobileHeight="320px"
      >
        <div className="flex items-center justify-center h-full text-gray-500">
          No gender data available
        </div>
      </BaseChart>
    );
  }

  // Removed JS-driven breakpoints. Use CSS responsive layout + percent radii
  // to avoid mismatch between mobile and desktop calculations.

  // Event handlers for pie chart interaction
  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  const onPieLeave = () => {
    setActiveIndex(null);
  };

  // Get the majority gender for header display
  const majorityGender = genderData.reduce((prev, current) =>
    prev.percentage > current.percentage ? prev : current
  );

  return (
    <BaseChart
      title={title}
      selectedSocial={selectedSocial}
      // Show first on mobile (before xl) while preserving layout at xl+
      className="order-1 xl:order-none xl:col-span-1"
      height={height}
      mobileHeight="350px"
    >
      <div className="h-full flex flex-col">
        {genderData.length === 0 ? (
          <div className="flex items-center justify-center h-full min-h-[200px]">
            <div className="text-center">
              <p className="text-gray-500 text-lg mb-2">No data available</p>
              <p className="text-gray-400 text-sm">
                Gender demographics data will appear here when available
              </p>
            </div>
          </div>
        ) : (
          <>
            {/* Mobile First Layout - Stack vertically on small screens */}
            <div className="flex-1 flex flex-col xl:flex-row gap-4 lg:gap-6 py-2">
              {/* Pie Chart Section */}
              <div className="flex-shrink-0 xl:w-1/2 order-1 xl:order-none flex-none w-full">
                {/* Center and make pie take full width on small screens; full height at lg+ */}
                <div className="h-42  lg:h-full w-full min-h-[185x] md:min-h-[200px] lg:min-h-[240px] flex items-center justify-center">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={genderData}
                        cx="50%"
                        cy="40%"
                        labelLine={false}
                        label={CustomLabel}
                        // Use percent-based radii so Recharts calculates sizing from
                        // the container and matches CSS breakpoints exactly.
                        outerRadius={"85%"}
                        fill="#8884d8"
                        dataKey="value"
                        onMouseEnter={onPieEnter}
                        onMouseLeave={onPieLeave}
                        {...({
                          activeIndex:
                            activeIndex === null ? undefined : activeIndex,
                        } as any)}
                        activeShape={(props: any) => (
                          <Sector
                            {...props}
                            innerRadius={Math.max(props.innerRadius - 2, 0)}
                            // Increase outerRadius proportionally for active slice
                            outerRadius={
                              props.outerRadius +
                              Math.max(8, Math.round(props.outerRadius * 0.06))
                            }
                            cornerRadius={4}
                          />
                        )}
                        paddingAngle={1.5}
                        isAnimationActive={false}
                      >
                        {genderData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={entry.color}
                            stroke={activeIndex === index ? "#fff" : "none"}
                            strokeWidth={activeIndex === index ? 2 : 0}
                          />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Gender Stats */}
              {/* Stats: place below pie on small screens and avoid growing (only grow at xl+) */}
              <div className="grid grid-cols-3 xl:grid-cols-1  xl:gap-2 gap-1 order-2 xl:order-2 xl:flex xl:flex-col xl:justify-between xl:h-full xl:flex-1 mt-auto ">
                {genderData.map((gender, index) => (
                  <div
                    key={gender.name}
                    className={`bg-white rounded-lg p-2 border-2 transition-all duration-200 cursor-pointer flex flex-col xl:flex-1 ${
                      activeIndex === index
                        ? "border-gray-300 shadow-md"
                        : "border-gray-200"
                    }`}
                    onMouseEnter={() => setActiveIndex(index)}
                    onMouseLeave={() => setActiveIndex(null)}
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2 md:gap-3 flex-1 min-w-0">
                        <div className="w-6 h-6 md:w-8 md:h-8 flex items-center justify-center shrink-0">
                          <i
                            className={`fas ${gender.icon} text-sm md:text-base`}
                            style={{ color: gender.color }}
                          ></i>
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="font-medium text-gray-800 truncate text-xs md:text-sm">
                            {gender.name}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            {gender.percentage.toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mt-1.5 md:mt-2 w-full">
                      <div className="w-full bg-gray-200 rounded-full h-1 md:h-1.5">
                        <div
                          className="h-1 md:h-1.5 rounded-full"
                          style={{
                            backgroundColor: gender.color,
                            width: `${gender.percentage}%`,
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </BaseChart>
  );
};
