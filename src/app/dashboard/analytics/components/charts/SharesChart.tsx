"use client";

import { useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import { BaseChart } from '../shared/BaseChart';
import { demoAnalyticsData } from '../../utils/demoData';

interface SharesChartProps {
  data: any[];
  timeRange: '7d' | '30d';
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
  bottomPadding?: number;
  mobileHeight?: string;
}

const MEDIA_COLORS = {
  post: '#3b82f6',
  reel: '#10b981',
  story: '#f59e0b',
  video: '#8b5cf6'
};

export const SharesChart = ({ data, timeRange, selectedSocial, height, bottomPadding, mobileHeight }: SharesChartProps) => {
  const [viewMode, setViewMode] = useState<'timeline' | 'breakdown'>('timeline');

  const chartData = useMemo(() => {
    const effectiveData = data && data.length > 0 ? data : demoAnalyticsData.shares;
    if (!effectiveData || effectiveData.length === 0) return [];

    const days = timeRange === '7d' ? 7 : 30;
    const byDate = new Map<string, number>();
    effectiveData.forEach((item: any) => {
      const date = item.date || '';
      const val = Number(item.total_value || 0);
      byDate.set(date, (byDate.get(date) || 0) + val);
    });
    const sortedDates = Array.from(byDate.keys()).sort();
    const lastNDates = sortedDates.slice(-days);
    return lastNDates.map((date, idx) => ({
      day: `Day ${idx + 1}`,
      shares: byDate.get(date) || 0,
      date,
    }));
  }, [data, timeRange]);

  const breakdownData = useMemo(() => {
    const effectiveData = data && data.length > 0 ? data : demoAnalyticsData.shares;
    if (!effectiveData || effectiveData.length === 0) return [];
    
    const breakdown = effectiveData.reduce((acc: any, item) => {
      const type = item.breakdown?.media_product_type || 'post';
      acc[type] = (acc[type] || 0) + (item.total_value || 0);
      return acc;
    }, {});

    return Object.entries(breakdown).map(([type, value]) => ({
      type: type.charAt(0).toUpperCase() + type.slice(1),
      shares: value,
      color: MEDIA_COLORS[type as keyof typeof MEDIA_COLORS] || '#6b7280'
    }));
  }, [data]);

  const totalShares = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.shares, 0);
  }, [chartData]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="text-emerald-600 font-medium">
                Shares: {payload[0]?.value?.toLocaleString()}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const PieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <div className="space-y-1">
            <p className="text-sm">
              <span className="font-medium">{payload[0]?.name}: </span>
              <span className="text-emerald-600 font-medium">
                {payload[0]?.value?.toLocaleString()} shares
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const actions = (
    <div className="flex bg-gray-100 rounded-lg p-1">
      <button
        onClick={() => setViewMode('timeline')}
        className={`px-2 md:px-3 py-1 rounded-md text-xs md:text-sm font-medium transition-all ${
          viewMode === 'timeline'
            ? 'bg-white text-emerald-600 shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
        }`}
      >
        Timeline
      </button>
      <button
        onClick={() => setViewMode('breakdown')}
        className={`px-2 md:px-3 py-1 rounded-md text-xs md:text-sm font-medium transition-all ${
          viewMode === 'breakdown'
            ? 'bg-white text-emerald-600 shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
        }`}
      >
        By Type
      </button>
    </div>
  );

  return (
    <BaseChart
      title="Shares"
      selectedSocial={selectedSocial}
      actions={actions}
      height={height}
      mobileHeight={mobileHeight}
      bottomPadding={bottomPadding}
    >
      <ResponsiveContainer width="100%" height="100%">
        {viewMode === 'timeline' ? (
          <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="day"
              tick={{ fontSize: 12 }}
              interval="preserveStartEnd"
            />
            <YAxis
              tick={{ fontSize: 12 }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="shares" fill="#10b981" radius={[4, 4, 0, 0]} />
          </BarChart>
        ) : (
          <PieChart>
            <Pie
              data={breakdownData}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={100}
              paddingAngle={5}
              dataKey="shares"
            >
              {breakdownData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<PieTooltip />} />
          </PieChart>
        )}
      </ResponsiveContainer>
    </BaseChart>
  );
};
