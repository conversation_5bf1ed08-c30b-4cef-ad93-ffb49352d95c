"use client";

import { useMemo } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  <PERSON>Axis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { BaseChart } from "../shared/BaseChart";
import { getPlatformColors, getAgeGroupColor } from "../../utils/colors";

interface FollowerDemographicsChartProps {
  data: any[];
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
}

export const FollowerDemographicsChart = ({
  data,
  selectedSocial,
  height = "400px",
}: FollowerDemographicsChartProps) => {
  const platformColors = getPlatformColors(selectedSocial?.platform);

  const demographicsData = useMemo(() => {
    // Use provided data if available, otherwise return empty
    const effectiveData = data && data.length > 0 ? data : [];
    if (effectiveData.length === 0) return { age: [], country: [] };

    // Aggregate maps
    const ageMap: Record<string, number> = {};
    const countryMap: Record<string, number> = {};

    effectiveData.forEach((item) => {
      const b = item?.breakdown || {};
      const val = Number(item?.total_value ?? 0) || 0;
      if (b.age) ageMap[String(b.age)] = (ageMap[String(b.age)] || 0) + val;
      if (b.country)
        countryMap[String(b.country)] =
          (countryMap[String(b.country)] || 0) + val;
    });

    // Convert to arrays and compute percentages for each selected breakdown
    const toArrayWithPct = (map: Record<string, number>, keyName: string) => {
      const entries = Object.entries(map);
      const total = entries.reduce((s, [, v]) => s + v, 0) || 1;
      return entries.map(([k, v]) => ({
        [keyName]: k,
        value: v,
        percentage: (v / total) * 100,
      }));
    };

    return {
      age: toArrayWithPct(ageMap, "range"),
      country: toArrayWithPct(countryMap, "country"),
    };
  }, [data]);

  // Countries list moved to dedicated FollowersCountriesChart component

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span
                className="font-medium"
                style={{ color: platformColors.primary }}
              >
                Followers: {data.value?.toLocaleString()}
              </span>
            </p>
            <p className="text-sm">
              <span className="text-gray-600">
                Percentage: {data.payload?.percentage?.toFixed(1)}%
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <>
      {/* Age Distribution Bar Chart */}
      <BaseChart
        title="Follower Demographics"
        selectedSocial={selectedSocial}
        className="lg:col-span-1"
        height={height}
      >
        {demographicsData.age.length === 0 ? (
          <div className="flex items-center justify-center h-full min-h-[200px]">
            <div className="text-center">
              <p className="text-gray-500 text-lg mb-2">No data available</p>
              <p className="text-gray-400 text-sm">
                Follower demographics data will appear here when available
              </p>
            </div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={demographicsData.age}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey={"range"} tick={{ fontSize: 12 }} height={60} />
              <YAxis
                tick={{ fontSize: 12 }}
                tickFormatter={(value) =>
                  value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value
                }
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="value" radius={[4, 4, 0, 0]}>
                {demographicsData.age.map((entry: any, index: number) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={getAgeGroupColor(index, selectedSocial?.platform)}
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        )}
      </BaseChart>
    </>
  );
};
