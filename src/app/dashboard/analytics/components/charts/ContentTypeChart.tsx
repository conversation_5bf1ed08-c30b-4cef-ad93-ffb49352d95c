"use client";

import { useMemo } from "react";
import { BaseChart } from "../shared/BaseChart";
import { CHART_COLORS } from "../../utils/colors";

interface ContentTypeChartProps {
  data?: any[];
  timeRange?: "7d" | "30d";
  selectedSocial?: any;
  height?: string;
  listMaxHeight?: string;
}

export const ContentTypeChart = ({
  data,
  timeRange,
  selectedSocial,
  height = "320px",
  listMaxHeight = "220px",
}: ContentTypeChartProps) => {
  const chartData = useMemo(() => {
    if (data && data.length > 0) {
      return data.map((item, index) => ({
        name: item.type || item.name || `Type ${index + 1}`,
        value: item.reach || item.value || 0,
      }));
    }

    // Demo data if no real data - matching the modal exactly
    return [
      { name: "Posts", value: 2628 },
      { name: "Reels", value: 1655 },
      { name: "Stories", value: 1209 },
      { name: "Others", value: 103 },
    ];
  }, [data]);

  const topContentType = useMemo(() => {
    const maxValue = Math.max(...chartData.map((item) => item.value));
    const top = chartData.find((item) => item.value === maxValue);
    return top?.name || "N/A";
  }, [chartData]);

  return (
    <BaseChart
      title="Account Reach / Content Type"
      description={`Top performing: ${topContentType}`}
      value={topContentType}
      selectedSocial={selectedSocial}
      height={height}
      minHeight="320px"
    >
      <div
        className="space-y-2 p-2 overflow-y-auto pr-2"
        style={{ maxHeight: listMaxHeight }}
      >
        {chartData.map((item, index) => (
          <div key={index} className="bg-white rounded-lg px-1 py-1">
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm font-medium">{item.name}</span>
              <span className="text-sm text-gray-600">
                {item.value.toLocaleString()}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="h-2.5 rounded-full"
                style={{
                  width: `${
                    (item.value / Math.max(...chartData.map((d) => d.value))) *
                    100
                  }%`,
                  backgroundColor: CHART_COLORS.instagram,
                }}
              ></div>
            </div>
          </div>
        ))}
      </div>
    </BaseChart>
  );
};
