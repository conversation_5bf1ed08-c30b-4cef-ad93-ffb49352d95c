"use client";

import { useMemo } from 'react';
import { <PERSON>Chart, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { BaseChart } from '../shared/BaseChart';
import { demoAnalyticsData } from '../../utils/demoData';

interface FollowsAndUnfollowsChartProps {
  data: any[];
  timeRange: '7d' | '30d';
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
  mobileHeight?: string;
  bottomPadding?: number;
}

export const FollowsAndUnfollowsChart = ({ data, timeRange, selectedSocial, height, mobileHeight, bottomPadding }: FollowsAndUnfollowsChartProps) => {
  const chartData = useMemo(() => {
    const effectiveData = data && data.length > 0 ? data : demoAnalyticsData.follows_and_unfollows;
    if (!effectiveData || effectiveData.length === 0) return [];
    
    return effectiveData.map((item, index) => {
      const followType = item.breakdown?.follow_type || 'follow';
      return {
        day: timeRange === '7d' ? `Day ${index + 1}` : `Day ${index + 1}`,
        follows: followType === 'follow' ? (item.total_value || 0) : 0,
        unfollows: followType === 'unfollow' ? (item.total_value || 0) : 0,
        net: followType === 'follow' ? (item.total_value || 0) : -(item.total_value || 0),
        date: item.date || `2024-01-${String(index + 1).padStart(2, '0')}`
      };
    });
  }, [data, timeRange]);

  const aggregatedData = useMemo(() => {
    const dayMap = new Map();
    
    chartData.forEach(item => {
      if (dayMap.has(item.day)) {
        const existing = dayMap.get(item.day);
        dayMap.set(item.day, {
          ...existing,
          follows: existing.follows + item.follows,
          unfollows: existing.unfollows + item.unfollows,
          net: existing.net + item.net
        });
      } else {
        dayMap.set(item.day, item);
      }
    });
    
    return Array.from(dayMap.values());
  }, [chartData]);

  const totalFollows = useMemo(() => {
    return aggregatedData.reduce((sum, item) => sum + item.follows, 0);
  }, [aggregatedData]);

  const totalUnfollows = useMemo(() => {
    return aggregatedData.reduce((sum, item) => sum + item.unfollows, 0);
  }, [aggregatedData]);

  const netGrowth = totalFollows - totalUnfollows;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="text-green-600 font-medium">
                Follows: {payload.find((p: any) => p.dataKey === 'follows')?.value || 0}
              </span>
            </p>
            <p className="text-sm">
              <span className="text-red-600 font-medium">
                Unfollows: {payload.find((p: any) => p.dataKey === 'unfollows')?.value || 0}
              </span>
            </p>
            <p className="text-sm">
              <span className="text-blue-600 font-medium">
                Net: {payload.find((p: any) => p.dataKey === 'net')?.value || 0}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <BaseChart
      title="Follows & Unfollows"
      selectedSocial={selectedSocial}
      height={height}
      mobileHeight={mobileHeight}
      bottomPadding={bottomPadding}
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={aggregatedData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="day"
            tick={{ fontSize: 12 }}
            interval="preserveStartEnd"
          />
          <YAxis
            tick={{ fontSize: 12 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="follows" fill="#10b981" radius={[2, 2, 0, 0]} />
          <Bar dataKey="unfollows" fill="#ef4444" radius={[2, 2, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </BaseChart>
  );
};
