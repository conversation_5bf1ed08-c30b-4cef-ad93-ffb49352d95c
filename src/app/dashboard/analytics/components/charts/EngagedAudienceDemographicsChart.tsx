"use client";

import { useMemo, useState } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
} from "recharts";
import { BaseChart } from "../shared/BaseChart";
import { getPlatformColors, getAgeGroupColor } from "../../utils/colors";
import { demoAnalyticsData } from "../../utils/demoData";

interface EngagedAudienceDemographicsChartProps {
  data: any[];
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
}

export const EngagedAudienceDemographicsChart = ({
  data,
  selectedSocial,
  height = "450px",
}: EngagedAudienceDemographicsChartProps) => {
  const [breakdown, setBreakdown] = useState<"age" | "country" | "city">("age");
  const platformColors = getPlatformColors(selectedSocial?.platform);

  const demographicsData = useMemo(() => {
    const effectiveData =
      data && data.length > 0
        ? data
        : demoAnalyticsData.engaged_audience_demographics;
    if (!effectiveData || effectiveData.length === 0)
      return { age: [], country: [], city: [] };

    const result: { age: any[], country: any[], city: any[] } = { age: [], country: [], city: [] };

    effectiveData.forEach((item) => {
      if (item.breakdown) {
        // Age breakdown
        if (item.breakdown.age && breakdown === "age") {
          result.age.push({
            range: item.breakdown.age,
            value: item.total_value || 0,
            percentage: item.percentage || 0,
          });
        }

        // Country breakdown
        if (item.breakdown.country && breakdown === "country") {
          result.country.push({
            country: item.breakdown.country,
            value: item.total_value || 0,
            percentage: item.percentage || 0,
          });
        }

        // City breakdown
        if (item.breakdown.city && breakdown === "city") {
          result.city.push({
            city: item.breakdown.city,
            value: item.total_value || 0,
            percentage: item.percentage || 0,
          });
        }
      }
    });

    return result;
  }, [data, breakdown]);

  const currentData = demographicsData[breakdown] || [];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="font-medium" style={{ color: platformColors.primary }}>
                Count: {data.value?.toLocaleString()}
              </span>
            </p>
            <p className="text-sm">
              <span className="text-gray-600">
                Percentage: {data.payload?.percentage?.toFixed(1)}%
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const PieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <div className="space-y-1">
            <p className="text-sm">
              <span className="font-medium">{payload[0]?.name}: </span>
              <span className="font-medium" style={{ color: platformColors.primary }}>
                {payload[0]?.value?.toLocaleString()} (
                {payload[0]?.payload?.percentage?.toFixed(1)}%)
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const actions = (
    <div className="flex flex-wrap gap-1 bg-gray-100 rounded-lg p-1">
      {["age", "country", "city"].map((type) => (
        <button
          key={type}
          onClick={() => setBreakdown(type as any)}
          className={`px-2 md:px-3 py-1 rounded-md text-xs md:text-sm font-medium transition-all ${
            breakdown === type
              ? "bg-white shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          }`}
          style={{ color: breakdown === type ? platformColors.primary : undefined }}
        >
          {type.charAt(0).toUpperCase() + type.slice(1)}
        </button>
      ))}
    </div>
  );

  return (
    <BaseChart
      title="Engaged Audience Demographics"
      selectedSocial={selectedSocial}
      actions={actions}
      className="lg:col-span-1"
      height={height}
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={currentData}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey={breakdown === "age" ? "range" : breakdown}
            tick={{ fontSize: 12 }}
            angle={breakdown === "city" || breakdown === "country" ? -45 : 0}
            textAnchor={
              breakdown === "city" || breakdown === "country" ? "end" : "middle"
            }
            height={breakdown === "city" || breakdown === "country" ? 80 : 60}
          />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={(value) =>
              value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value
            }
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" radius={[4, 4, 0, 0]}>
            {currentData.map((entry: any, index: number) => (
              <Cell
                key={`cell-${index}`}
                fill={getAgeGroupColor(index, selectedSocial?.platform)}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </BaseChart>
  );
};
