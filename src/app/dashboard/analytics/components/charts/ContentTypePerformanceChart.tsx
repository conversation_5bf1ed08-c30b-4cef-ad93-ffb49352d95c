"use client";

import { useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { BaseChart } from "../shared/BaseChart";
import { CHART_COLORS } from "../../utils/colors";

interface ContentTypePerformanceChartProps {
  data?: any[];
  timeRange?: "7d" | "30d";
}

export const ContentTypePerformanceChart = ({
  data,
  timeRange,
}: ContentTypePerformanceChartProps) => {
  const chartData = useMemo(() => {
    if (data && data.length > 0) {
      return data.map((item, index) => ({
        type: item.content_type || item.type || `Type ${index + 1}`,
        reach: item.reach || item.value || 0,
      }));
    }

    // Demo data if no real data
    return [
      { type: "Posts", reach: 2628 },
      { type: "Reels", reach: 1655 },
      { type: "Stories", reach: 1209 },
      { type: "IGTV", reach: 432 },
    ];
  }, [data]);

  const totalReach = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.reach, 0);
  }, [chartData]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span
                className="font-medium"
                style={{ color: CHART_COLORS.primary }}
              >
                Reach: {payload[0]?.value?.toLocaleString()}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const getBarColor = (index: number) => {
    const colors = [
      CHART_COLORS.primary,
      CHART_COLORS.secondary,
      CHART_COLORS.success,
      CHART_COLORS.warning,
    ];
    return colors[index % colors.length];
  };

  return (
    <BaseChart
      title="Content Type Performance"
      icon="fa-chart-bar"
      description="Reach by content type"
      value={totalReach.toLocaleString()}
      minHeight="320px"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis dataKey="type" tick={{ fontSize: 12 }} />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={(value) =>
              value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value
            }
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar
            dataKey="reach"
            fill={CHART_COLORS.primary}
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </BaseChart>
  );
};
