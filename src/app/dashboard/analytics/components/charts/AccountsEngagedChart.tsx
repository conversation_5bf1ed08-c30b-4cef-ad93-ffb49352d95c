"use client";

import { useMemo } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { BaseChart } from '../shared/BaseChart';
import { CHART_COLORS, getPlatformColors } from '../../utils/colors';
import { demoAnalyticsData } from '../../utils/demoData';

interface AccountsEngagedChartProps {
  data: any[];
  timeRange: '7d' | '30d';
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
  mobileHeight?: string;
  bottomPadding?: number;
}

export const AccountsEngagedChart = ({ data, timeRange, selectedSocial, height, mobileHeight, bottomPadding }: AccountsEngagedChartProps) => {
  const platformColors = getPlatformColors(selectedSocial?.platform);

  const chartData = useMemo(() => {
    const effectiveData = data && data.length > 0 ? data : demoAnalyticsData.accounts_engaged;
    if (!effectiveData || effectiveData.length === 0) return [];

    return effectiveData.map((item, index) => ({
      day: timeRange === '7d' ? `Day ${index + 1}` : `Day ${index + 1}`,
      accounts: item.total_value || 0,
      date: item.date || `2024-01-${String(index + 1).padStart(2, '0')}`
    }));
  }, [data, timeRange]);

  const totalAccounts = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.accounts, 0);
  }, [chartData]);

  const averageAccounts = useMemo(() => {
    return chartData.length > 0 ? Math.round(totalAccounts / chartData.length) : 0;
  }, [totalAccounts, chartData.length]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="font-medium" style={{ color: platformColors.primary }}>
                Accounts Engaged: {payload[0]?.value?.toLocaleString()}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <BaseChart
      title="Accounts Engaged"
      selectedSocial={selectedSocial}
      height={height}
      mobileHeight={mobileHeight}
      bottomPadding={bottomPadding}
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="day"
            tick={{ fontSize: 12 }}
            interval="preserveStartEnd"
          />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value}
          />
          <Tooltip content={<CustomTooltip />} />
          <Line
            type="monotone"
            dataKey="accounts"
            stroke={platformColors.primary}
            strokeWidth={3}
            dot={{ fill: platformColors.primary, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: platformColors.primary, strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </BaseChart>
  );
};
