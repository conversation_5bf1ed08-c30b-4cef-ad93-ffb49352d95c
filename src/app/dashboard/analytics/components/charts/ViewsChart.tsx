"use client";

import { useMemo, useState } from 'react';
import { ComposedChart, Area, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { BaseChart } from '../shared/BaseChart';
import { CHART_COLORS, getPlatformColors } from '../../utils/colors';
import { demoAnalyticsData } from '../../utils/demoData';

interface ViewsChartProps {
  data: any[];
  timeRange: '7d' | '30d';
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
  mobileHeight?: string;
  bottomPadding?: number;
}

export const ViewsChart = ({ data, timeRange, selectedSocial, height, mobileHeight, bottomPadding }: ViewsChartProps) => {
  const [viewMode, setViewMode] = useState<'timeline' | 'breakdown'>('timeline');
  const platformColors = getPlatformColors(selectedSocial?.platform);

  const chartData = useMemo(() => {
    const effectiveData = data && data.length > 0 ? data : demoAnalyticsData.views;
    if (!effectiveData || effectiveData.length === 0) return [];

    const days = timeRange === '7d' ? 7 : 30;
    // Aggregate by date to ensure exactly N daily points
    const byDate = new Map<string, number>();
    effectiveData.forEach((item: any) => {
      const date = item.date || '';
      const val = Number(item.total_value || 0);
      byDate.set(date, (byDate.get(date) || 0) + val);
    });
    const sortedDates = Array.from(byDate.keys()).sort();
    const lastNDates = sortedDates.slice(-days);
    return lastNDates.map((date, idx) => ({
      day: `Day ${idx + 1}`,
      views: byDate.get(date) || 0,
      date,
    }));
  }, [data, timeRange]);

  const breakdownData = useMemo(() => {
    const effectiveData = data && data.length > 0 ? data : demoAnalyticsData.views;
    if (!effectiveData || effectiveData.length === 0) return [];

    const breakdown = effectiveData.reduce((acc: any, item) => {
      const type = item.breakdown?.media_product_type || 'post';
      acc[type] = (acc[type] || 0) + (item.total_value || 0);
      return acc;
    }, {});

    return Object.entries(breakdown).map(([type, value]) => ({
      type: type.charAt(0).toUpperCase() + type.slice(1),
      views: value
    }));
  }, [data]);

  const totalViews = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.views, 0);
  }, [chartData]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="font-medium" style={{ color: platformColors.primary }}>
                Views: {payload[0]?.value?.toLocaleString()}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const actions = (
    <div className="flex bg-gray-100 rounded-lg p-1">
      <button
        onClick={() => setViewMode('timeline')}
        className={`px-2 md:px-3 py-1 rounded-md text-xs md:text-sm font-medium transition-all ${
          viewMode === 'timeline'
            ? 'bg-white shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
        }`}
      >
        Timeline
      </button>
      <button
        onClick={() => setViewMode('breakdown')}
        className={`px-2 md:px-3 py-1 rounded-md text-xs md:text-sm font-medium transition-all ${
          viewMode === 'breakdown'
            ? 'bg-white shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
        }`}
      >
        By Type
      </button>
    </div>
  );

  return (
    <BaseChart
      title="Views"
      selectedSocial={selectedSocial}
      actions={actions}
      height={height}
      mobileHeight={mobileHeight}
      bottomPadding={bottomPadding}
    >
      <ResponsiveContainer width="100%" height="100%">
        {viewMode === 'timeline' ? (
          <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
            <defs>
              <linearGradient id="viewsGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={platformColors.primary} stopOpacity={0.3}/>
                <stop offset="95%" stopColor={platformColors.primary} stopOpacity={0.05}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="day"
              tick={{ fontSize: 12 }}
              interval="preserveStartEnd"
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value}
            />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="views"
              fill="url(#viewsGradient)"
              stroke="none"
            />
            <Line
              type="monotone"
              dataKey="views"
              stroke={platformColors.primary}
              strokeWidth={2}
              dot={{ fill: platformColors.primary, strokeWidth: 1.5, r: 2 }}
              activeDot={{ r: 4, stroke: platformColors.primary, strokeWidth: 1.5 }}
            />
          </ComposedChart>
        ) : (
          <BarChart data={breakdownData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="type" tick={{ fontSize: 12 }} />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value}
            />
            <Tooltip
              formatter={(value, name) => [
                `${value?.toLocaleString()} views`,
                'Views'
              ]}
            />
            <Bar dataKey="views" fill={platformColors.primary} radius={[4, 4, 0, 0]} />
          </BarChart>
        )}
      </ResponsiveContainer>
    </BaseChart>
  );
};
