"use client";

import { useMemo, memo, useState, useCallback } from "react";
import { motion } from "framer-motion";
import { BaseChart } from "../shared/BaseChart";
import { CHART_COLORS, getPlatformColors } from "../../utils/colors";
import Skeleton from "~/components/skeleton";

interface TopPostsChartProps {
  data: any[];
  type: "posts" | "reels";
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
}

interface PostData {
  id: string;
  image: string;
  likes: number;
  comments: number;
  shares: number;
  reach: number;
  engagement_rate: string;
  posted_date: string;
}

// Demo post data - in real implementation, this would come from the API
const generateDemoTopPosts = (type: "posts" | "reels"): PostData[] => {
  const baseImages = [
    "https://images.unsplash.com/photo-1611262588024-d12430b98920?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1611605698335-8b1569810432?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1611262588019-db6935269049?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1611605698323-b1e99cfd37ea?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1611262588024-d12430b98920?w=400&h=400&fit=crop",
  ];

  return Array.from({ length: 5 }, (_, index) => ({
    id: `${type}_${index + 1}`,
    image: baseImages[index % baseImages.length],
    likes: Math.floor(Math.random() * 5000) + 1000,
    comments: Math.floor(Math.random() * 500) + 50,
    shares: Math.floor(Math.random() * 200) + 20,
    reach: Math.floor(Math.random() * 10000) + 2000,
    engagement_rate: (Math.random() * 8 + 2).toFixed(1),
    posted_date: new Date(
      Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
    ).toLocaleDateString(),
  }));
};

// Enhanced PostCard component with better design for grid layout
const PostCard = memo(
  ({
    post,
    index,
    platformColors,
  }: {
    post: PostData;
    index: number;
    platformColors: any;
  }) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    const handleImageLoad = useCallback(() => {
      setImageLoaded(true);
    }, []);

    const handleImageError = useCallback(() => {
      setImageError(true);
      setImageLoaded(true);
    }, []);

    return (
      <div className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 group">
        {/* Post Image */}
        <div className="relative aspect-square overflow-hidden bg-gray-50">
          {!imageLoaded && !imageError && (
            <motion.div
              className="absolute inset-0 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <Skeleton className="w-full h-full" animation="wave" />
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Skeleton
                  className="w-8 h-8"
                  variant="circular"
                  animation="pulse"
                />
              </motion.div>
            </motion.div>
          )}

          {!imageError ? (
            <motion.img
              src={post.image}
              alt={`Top ${index + 1}`}
              className={`w-full h-full object-cover transition-all duration-300 group-hover:scale-105 ${
                imageLoaded ? "opacity-100" : "opacity-0"
              }`}
              onLoad={handleImageLoad}
              onError={handleImageError}
              loading="lazy"
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: imageLoaded ? 1 : 0, scale: 1 }}
              transition={{ duration: 0.5 }}
            />
          ) : (
            <motion.div
              className="w-full h-full bg-gray-100 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <Skeleton className="w-8 h-8" variant="circular" />
            </motion.div>
          )}

          {/* Ranking Badge */}
          <div
            className="absolute top-3 left-3 w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg"
            style={{ backgroundColor: platformColors.primary }}
          >
            {index + 1}
          </div>

          {/* Engagement Badge */}
          <div className="absolute top-3 right-3 bg-white/95 backdrop-blur-sm px-3 py-1 rounded-full">
            <span
              className="text-sm font-bold"
              style={{ color: platformColors.primary }}
            >
              {post.engagement_rate}%
            </span>
          </div>
        </div>

        {/* Card Content */}
        <div className="p-4">
          {/* Primary Metrics */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center">
              <div className="text-lg font-bold text-gray-900 mb-1">
                {post.reach.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500 uppercase tracking-wide">
                Reach
              </div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-gray-900 mb-1">
                {post.likes.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500 uppercase tracking-wide">
                Likes
              </div>
            </div>
          </div>

          {/* Secondary Metrics */}
          <div className="grid grid-cols-2 gap-4 pt-3 border-t border-gray-100">
            <div className="text-center">
              <div className="text-sm font-medium text-gray-700">
                {post.comments.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">Comments</div>
            </div>
            <div className="text-center">
              <div className="text-sm font-medium text-gray-700">
                {post.shares.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">Shares</div>
            </div>
          </div>

          {/* Posted Date */}
          <div className="mt-3 pt-3 border-t border-gray-100">
            <div className="text-xs text-gray-500 text-center">
              Posted {post.posted_date}
            </div>
          </div>
        </div>
      </div>
    );
  }
);

PostCard.displayName = "PostCard";

export const TopPostsChart = ({
  data,
  type,
  selectedSocial,
  height = "450px",
}: TopPostsChartProps) => {
  const platformColors = getPlatformColors(selectedSocial?.platform);
  const topPosts = useMemo(() => {
    // For now, use demo data. In real implementation, process the actual data
    return generateDemoTopPosts(type);
  }, [type]);

  const summaryStats = useMemo(
    () => ({
      totalReach: topPosts.reduce((sum, post) => sum + post.reach, 0),
      avgEngagement: (
        topPosts.reduce(
          (sum, post) => sum + parseFloat(post.engagement_rate),
          0
        ) / topPosts.length
      ).toFixed(1),
      bestPost: topPosts.length > 0 ? topPosts[0] : null, // Assuming first post is the best performing
    }),
    [topPosts]
  );

  return (
    <BaseChart
      title={`Top ${type.charAt(0).toUpperCase() + type.slice(1)}`}
      icon={type === "posts" ? "fa-image" : "fa-video"}
      description={`Your best performing ${type} ranked by engagement`}
      dynamicHeight={true}
      minHeight="auto"
      height={height}
    >
      <div className="space-y-6 dashboard-shadow">
        {/* Summary Stats - Minimalist */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="text-center lg:col-span-2">
            <div
              className="text-2xl font-bold mb-1"
              style={{ color: platformColors.primary }}
            >
              {summaryStats.avgEngagement}%
            </div>
            <div className="text-sm text-gray-600">Average Engagement</div>
          </div>
          <div className="text-center lg:col-span-2">
            <div
              className="text-2xl font-bold mb-1"
              style={{ color: platformColors.primary }}
            >
              {summaryStats.totalReach.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Total Reach</div>
          </div>
        </div>

        {/* Posts Grid - Responsive Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
          {topPosts.map((post, index) => (
            <PostCard
              key={post.id}
              post={post}
              index={index}
              platformColors={platformColors}
            />
          ))}
        </div>

        {/* Best Performer Highlight */}
        <div
          className="mt-6 p-4 rounded-lg border-2 border-dashed"
          style={{ borderColor: platformColors.primary + "40" }}
        >
          <div className="flex items-center gap-3 text-sm">
            <div
              className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold"
              style={{ backgroundColor: platformColors.primary }}
            >
              ★
            </div>
            <span className="text-gray-600">
              Best performer:{" "}
              <span className="font-medium text-gray-900">
                {summaryStats.bestPost?.engagement_rate || "0"}% engagement rate
              </span>
            </span>
          </div>
        </div>
      </div>
    </BaseChart>
  );
};
