"use client";

import { useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { BaseChart } from "../shared/BaseChart";
import { CHART_COLORS } from "../../utils/colors";

interface MostActiveTimeChartProps {
  data?: any[];
  timeRange?: "7d" | "30d";
  selectedSocial?: any;
  height?: string;
  type?: "daily" | "weekly";
}

export const MostActiveTimeChart = ({
  data,
  timeRange,
  selectedSocial,
  height = "320px",
  type = "daily",
}: MostActiveTimeChartProps) => {
  const chartData = useMemo(() => {
    if (data && data.length > 0) {
      return data.map((item, index) => ({
        time: item.time || item.name || `Time ${index + 1}`,
        value: item.value || item.activity || 0,
      }));
    }

    // Demo data based on type
    if (type === "weekly") {
      return [
        { time: "Monday", value: 120 },
        { time: "Tuesday", value: 98 },
        { time: "Wednesday", value: 86 },
        { time: "Thursday", value: 99 },
        { time: "Friday", value: 135 },
        { time: "Saturday", value: 110 },
        { time: "Sunday", value: 85 },
      ];
    }

    // Daily time data
    return [
      { time: "12am", value: 45 },
      { time: "3am", value: 25 },
      { time: "6am", value: 65 },
      { time: "9am", value: 85 },
      { time: "12pm", value: 120 },
      { time: "3pm", value: 98 },
      { time: "6pm", value: 135 },
      { time: "9pm", value: 110 },
    ];
  }, [data, type]);

  const peakTime = useMemo(() => {
    const maxValue = Math.max(...chartData.map((item) => item.value));
    const peak = chartData.find((item) => item.value === maxValue);
    return peak?.time || "N/A";
  }, [chartData]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span
                className="font-medium"
                style={{ color: CHART_COLORS.primary }}
              >
                Activity: {payload[0]?.value?.toLocaleString()}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const title =
    type === "weekly"
      ? "Most Active Time (Weekly)"
      : "Most Active Time (Daily)";
  const icon = type === "weekly" ? "fa-calendar" : "fa-clock";

  return (
    <BaseChart
      title={title}
      icon={icon}
      description={`Peak activity: ${peakTime}`}
      value={peakTime}
      selectedSocial={selectedSocial}
      minHeight={height}
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="time"
            tick={{ fontSize: 12 }}
            angle={type === "daily" ? 0 : -45}
            textAnchor={type === "daily" ? "middle" : "end"}
            height={type === "daily" ? 30 : 60}
          />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={(value) =>
              value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value
            }
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar
            dataKey="value"
            fill={CHART_COLORS.primary}
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </BaseChart>
  );
};
