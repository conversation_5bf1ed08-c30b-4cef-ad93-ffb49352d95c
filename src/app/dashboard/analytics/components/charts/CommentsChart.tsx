"use client";

import { useMemo } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { BaseChart } from '../shared/BaseChart';
import { CHART_COLORS, getPlatformColors } from '../../utils/colors';
import { demoAnalyticsData } from '../../utils/demoData';

interface CommentsChartProps {
  data: any[];
  timeRange: '7d' | '30d';
  selectedSocial?: {
    platform: string;
    username?: string;
  };
  height?: string;
  mobileHeight?: string;
  bottomPadding?: number;
}

export const CommentsChart = ({ data, timeRange, selectedSocial, height, mobileHeight, bottomPadding }: CommentsChartProps) => {
  const platformColors = getPlatformColors(selectedSocial?.platform);

  const chartData = useMemo(() => {
    const effectiveData = data && data.length > 0 ? data : demoAnalyticsData.comments;
    if (!effectiveData || effectiveData.length === 0) return [];

    return effectiveData.map((item, index) => ({
      day: timeRange === '7d' ? `Day ${index + 1}` : `Day ${index + 1}`,
      comments: item.total_value || 0,
      date: item.date || `2024-01-${String(index + 1).padStart(2, '0')}`
    }));
  }, [data, timeRange]);

  const totalComments = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.comments, 0);
  }, [chartData]);

  const averageComments = useMemo(() => {
    return chartData.length > 0 ? Math.round(totalComments / chartData.length) : 0;
  }, [totalComments, chartData.length]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="font-medium" style={{ color: platformColors.primary }}>
                Comments: {payload[0]?.value?.toLocaleString()}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <BaseChart
      title="Comments"
      selectedSocial={selectedSocial}
      height={height}
      mobileHeight={mobileHeight}
      bottomPadding={bottomPadding}
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="day"
            tick={{ fontSize: 12 }}
            interval="preserveStartEnd"
          />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar
            dataKey="comments"
            fill={platformColors.primary}
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </BaseChart>
  );
};
