"use client";

interface TimeRangeFilterProps {
  timeRange: "7d" | "30d";
  onTimeRangeChange: (range: "7d" | "30d") => void;
}

export const TimeRangeFilter = ({
  timeRange,
  onTimeRangeChange,
}: TimeRangeFilterProps) => {
  const options = [
    { value: "7d" as const, label: "Last 7 Days", icon: "fa-calendar-week" },
    { value: "30d" as const, label: "Last 30 Days", icon: "fa-calendar-alt" },
  ];

  return (
    <div className="bg-white rounded-lg p-4 shadow-sm">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-medium text-gray-900">Time Range</h3>
          <p className="text-sm text-gray-600">
            Select the period for analytics data
          </p>
        </div>

        <div className="flex bg-gray-100 rounded-lg p-1">
          {options.map((option) => (
            <button
              key={option.value}
              onClick={() => onTimeRangeChange(option.value)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${
                timeRange === option.value
                  ? "bg-white text-blue-600 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              <i className={`fas ${option.icon} text-xs`}></i>
              <span>{option.label}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
