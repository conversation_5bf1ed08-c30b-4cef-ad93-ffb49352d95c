"use client";

import { motion } from "framer-motion";
import React from "react";
import { useUserStore } from "~/store/userStore";
import { SocialAccountSelectionSidebar } from "./SocialAccountSelectionSidebar";

// Lightweight shimmer block for improved glow effect
const Shimmer = ({ className = "" }: { className?: string }) => (
  <div className={`relative overflow-hidden bg-gray-200 ${className}`}>
    <div className="absolute inset-0 -translate-x-full bg-linear-to-r from-transparent via-white/50 to-transparent animate-[shimmer_1.6s_infinite]" />
  </div>
);

export const LoadingState = () => {
  const { selectedWorkspaceDetails, selectedSocial, setUser } = useUserStore();

  const handleSocialSelect = (account: any) => {
    setUser({
      selectedSocial: {
        platform: account.platform,
        social_id: account.social_id,
        social_name: account.social_name,
        username: account.username,
      },
    });
  };

  // Define skeleton variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  const pulseVariants = {
    pulse: {
      opacity: [0.6, 1, 0.6],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  return (
    <div className="flex min-h-screen bg-gray-50 md:mt-0 mt-[8vh]">
      {/* Sidebar: show real social account side menu during loading */}
      <SocialAccountSelectionSidebar
        socialAccounts={(selectedWorkspaceDetails as any)?.social_accounts || []}
        selectedSocial={selectedSocial as any}
        onSocialSelect={handleSocialSelect}
        onAddClick={() => {}}
      />

      {/* Main Content */}
      <div className="flex-1 p-4 md:p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header Skeleton */}
          <motion.div
            className="flex justify-between items-center mt-4 mb-6"
            variants={itemVariants}
            initial="hidden"
            animate="visible"
            transition={{ delay: 0.3 }}
          >
            <Shimmer className="h-8 w-40 rounded" />
            {/* omit status/debug widgets while loading */}
          </motion.div>

          {/* Metrics Overview Skeleton (no white wrapper; individual white cards) */}
          <motion.div
            variants={itemVariants}
            initial="hidden"
            animate="visible"
            transition={{ delay: 0.5 }}
          >
            <div className="mb-4">
              <Shimmer className="h-6 w-48 rounded" />
            </div>
            <div className="grid grid-cols-3 lg:grid-cols-5 mx-auto gap-3 md:gap-4 w-full">
              {[1, 2, 3, 4, 5].map((index) => (
                <motion.div
                  key={index}
                  className="bg-white rounded-lg p-3 md:p-4 text-center hover:shadow-md transition-shadow animate-soft-glow"
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                  transition={{ delay: 0.6 + index * 0.06 }}
                >
                  <div className="relative">
                    <div className="mb-1">
                      <Shimmer className="h-6 w-16 rounded mx-auto" />
                    </div>
                  </div>
                  <div>
                    <Shimmer className="h-3 w-20 rounded mx-auto" />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Main Charts Grid Skeleton */}
          <motion.div
            className="grid grid-cols-1 lg:grid-cols-5 gap-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Large Chart - 3/5 Width */}
            <motion.div className="lg:col-span-3" variants={itemVariants}>
              <div className="bg-white rounded-lg p-6 shadow-sm animate-soft-glow">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Shimmer className="w-10 h-10 rounded-lg" />
                    <div>
                      <Shimmer className="h-4 w-32 rounded mb-2" />
                    </div>
                  </div>
                  <Shimmer className="h-8 w-20 rounded" />
                </div>
                <Shimmer className="h-[300px] rounded-lg" />
              </div>
            </motion.div>

            {/* Medium Chart - 2/5 Width */}
            <motion.div className="lg:col-span-2" variants={itemVariants}>
              <div className="bg-white rounded-lg p-6 shadow-sm animate-soft-glow">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Shimmer className="w-10 h-10 rounded-lg" />
                    <div>
                      <Shimmer className="h-4 w-28 rounded mb-2" />
                    </div>
                  </div>
                </div>
                <Shimmer className="h-[300px] rounded-lg" />
              </div>
            </motion.div>
          </motion.div>

          {/* Regular Charts Grid Skeleton */}
          <motion.div
            className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {[1, 2, 3, 4, 5, 6].map((index) => (
              <motion.div key={index} variants={itemVariants}>
                <div className="bg-white rounded-lg p-6 shadow-sm animate-soft-glow">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Shimmer className="w-10 h-10 rounded-lg" />
                      <div>
                        <Shimmer className="h-4 w-24 rounded mb-2" />
                      </div>
                    </div>
                    <Shimmer className="h-8 w-16 rounded" />
                  </div>
                  <Shimmer className="h-[320px] rounded-lg" />
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Demographics Charts Skeleton */}
          <motion.div
            className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {[1, 2].map((index) => (
              <motion.div key={index} variants={itemVariants}>
                <div className="bg-white rounded-lg p-6 shadow-sm animate-soft-glow">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Shimmer className="w-10 h-10 rounded-lg" />
                      <div>
                        <Shimmer className="h-4 w-32 rounded mb-2" />
                      </div>
                    </div>
                  </div>
                  <Shimmer className="h-[400px] rounded-lg" />
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Account Reach Sections Skeleton */}
          <motion.div
            className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {[1, 2].map((index) => (
              <motion.div key={index} variants={itemVariants}>
                <div className="bg-white rounded-lg p-6 shadow-sm animate-soft-glow">
                  <div className="flex items-center gap-3 mb-6">
                    <Shimmer className="w-8 h-8 rounded-lg" />
                    <Shimmer className="h-5 w-48 rounded" />
                  </div>
                  <div className="space-y-4">
                    {[1, 2, 3, 4].map((item) => (
                      <div key={item}>
                        <div className="flex items-center justify-between mb-2">
                          <Shimmer className="h-4 w-16 rounded" />
                          <Shimmer className="h-4 w-12 rounded" />
                        </div>
                        <Shimmer className="w-full h-2 rounded-full" />
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Top Posts/Reels Sections Skeleton */}
          <motion.div
            className="space-y-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {[1, 2].map((section) => (
              <motion.div key={section} variants={itemVariants}>
                <div className="bg-white rounded-lg p-6 shadow-sm animate-soft-glow">
                  <div className="flex items-center gap-3 mb-6">
                    <Shimmer className="w-8 h-8 rounded-lg" />
                    <Shimmer className="h-6 w-32 rounded" />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[1, 2, 3].map((post) => (
                      <div key={post} className="space-y-3">
                        <Shimmer className="aspect-square rounded-lg" />
                        <div className="space-y-2">
                          <Shimmer className="h-4 w-full rounded" />
                          <Shimmer className="h-3 w-3/4 rounded" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Loading Status */}
          <motion.div
            className="text-center py-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
          >
            <motion.div
              className="w-8 h-8 border-3 border-pink-200 border-t-pink-500 rounded-full mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <motion.div
              className="text-gray-600"
              variants={pulseVariants}
              animate="pulse"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Loading Analytics
              </h3>
              <p>Fetching your Instagram performance data...</p>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

