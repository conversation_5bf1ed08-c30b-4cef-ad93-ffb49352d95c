"use client";

import { useMemo } from "react";
import { getPlatformColors } from "../../utils/colors";
import { useUserStore } from "~/store/userStore";

interface MetricsOverviewProps {
  data: any;
  timeRange: "7d" | "30d";
}

// Helper function to calculate change between day 1 and the last day
// Uses the earliest dated point as Day 1 and the latest dated point as Last Day
const calculateChange = (dataArray: any[], _timeRange: "7d" | "30d") => {
  if (!dataArray || dataArray.length === 0) return null;

  // Keep only dated points and sort asc
  const dated = dataArray
    .filter((item) => item?.date)
    .sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );

  if (dated.length < 2) return null; // need at least first and last

  const first = Number(dated[0]?.total_value ?? 0) || 0;
  const last = Number(dated[dated.length - 1]?.total_value ?? 0) || 0;

  if (first <= 0) return null; // avoid div-by-zero and meaningless % from zero baseline

  const percentageChange = ((last - first) / first) * 100;
  const absPct = Math.abs(percentageChange);
  const displayPct = absPct > 0 && absPct < 0.1 ? 0.1 : Number(absPct.toFixed(1));

  return {
    value: displayPct,
    valueStr: `${displayPct}%`,
    isPositive: percentageChange >= 0,
    rawChange: last - first,
  };
};

export const MetricsOverview = ({ data, timeRange }: MetricsOverviewProps) => {
  const { selectedSocial } = useUserStore();
  const platformColors = getPlatformColors(selectedSocial?.platform);

  const overviewMetrics = useMemo(() => {
    const metrics: any[] = [];

    // Followers: compute change using total followers at start vs end, where possible
    const computeFollowersChange = () => {
      const endFollowers =
        typeof data?.followers_count === "number" && isFinite(data.followers_count)
          ? Number(data.followers_count)
          : undefined;
      const gainsSeries: any[] = Array.isArray(data?.follower_count)
        ? data.follower_count.filter((it: any) => !!it?.date)
        : [];
      if (
        typeof endFollowers === "number" &&
        gainsSeries.length > 0
      ) {
        const sorted = [...gainsSeries].sort(
          (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
        );
        // Sum all gains in the available series (already scoped by selected range upstream)
        const sumGains = sorted.reduce(
          (s: number, d: any) => s + (Number(d?.total_value ?? 0) || 0),
          0
        );
        const startFollowers = endFollowers - sumGains;
        if (startFollowers > 0) {
          const pct = ((endFollowers - startFollowers) / startFollowers) * 100;
          const absPct = Math.abs(pct);
          const displayPct = absPct > 0 && absPct < 0.1 ? 0.1 : Number(absPct.toFixed(1));
          return {
            value: displayPct,
            valueStr: `${displayPct}%`,
            isPositive: pct >= 0,
            rawChange: endFollowers - startFollowers,
          };
        }
        return null;
      }
      // Fallback to generic day1 vs last-day change on gains if snapshot missing
      return calculateChange(
        Array.isArray(data?.follower_count) ? data.follower_count : [],
        timeRange
      );
    };

    // Helper: strictly use API aggregated total (no summing):
    // - If metric is an object with total_value.value, use it.
    // - If metric is a mapped series (array), pick the undated, no-breakdown entry (represents API total).
    const getTotalValue = (metric: any): number | undefined => {
      // Direct object shape
      const direct = metric?.total_value?.value;
      if (typeof direct === "number" && Number.isFinite(direct)) return direct;

      // Mapped series shape from mapApiToAnalytics
      if (Array.isArray(metric)) {
        const aggregate = metric.find(
          (it: any) =>
            (it?.date == null || it?.date === undefined) &&
            it?.breakdown == null
        );
        const val = aggregate?.total_value;
        if (typeof val === "number" && Number.isFinite(val)) return val;
      }

      return undefined;
    };

    // Followers count (snapshot) at the top
    const followersSnapshot: number | undefined =
      typeof data.followers_count === "number"
        ? data.followers_count
        : Array.isArray(data.follower_count) && data.follower_count.length > 0
        ? Number(
            data.follower_count[data.follower_count.length - 1]?.total_value ??
              0
          )
        : undefined;
    if (
      typeof followersSnapshot === "number" &&
      !Number.isNaN(followersSnapshot)
    ) {
      metrics.push({
        label: "Followers",
        value: followersSnapshot.toLocaleString(),
        icon: "fa-user-friends",
        color: platformColors.iconColor,
        bgColor: platformColors.metricIconBgColor,
        change: computeFollowersChange() || undefined,
      });
    }

    // Follows count (following) snapshot at the top
    const followsSnapshot: number | undefined =
      typeof data.follows_count === "number" ? data.follows_count : undefined;
    if (typeof followsSnapshot === "number" && !Number.isNaN(followsSnapshot)) {
      metrics.push({
        label: "Following",
        value: followsSnapshot.toLocaleString(),
        icon: "fa-user-plus",
        color: platformColors.iconColor,
        bgColor: platformColors.metricIconBgColor,
      });
    }

    // Media count (snapshot)
    const mediaSnapshot: number | undefined =
      typeof data.media_count === "number" ? data.media_count : undefined;
    if (typeof mediaSnapshot === "number" && !Number.isNaN(mediaSnapshot)) {
      metrics.push({
        label: "Media Count",
        value: mediaSnapshot.toLocaleString(),
        icon: "fa-photo-video",
        color: platformColors.iconColor,
        bgColor: platformColors.metricIconBgColor,
      });
    }

    // Follows and Unfollows (two cards: New Followers, Unfollowers)
    {
      const series = Array.isArray(data.follows_and_unfollows)
        ? data.follows_and_unfollows
        : [];
      if (series.length > 0) {
        const sumBy = (predicate: (it: any) => boolean) =>
          series.reduce(
            (acc: number, it: any) =>
              acc + (predicate(it) ? Number(it?.total_value) || 0 : 0),
            0
          );

        const isType = (it: any, types: string[]) => {
          const t = String(it?.breakdown?.follow_type || "").toUpperCase();
          return types.includes(t);
        };

        const newFollowers = sumBy((it) =>
          isType(it, ["FOLLOW", "FOLLOWER", "FOLLOWED"])
        );
        const unfollowers = sumBy((it) =>
          isType(it, ["UNFOLLOW", "NON_FOLLOWER", "UNFOLLOWED"])
        );

        metrics.push({
          label: "New Followers",
          value: newFollowers.toLocaleString(),
          icon: "fa-user-plus",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
        });

        metrics.push({
          label: "Unfollowers",
          value: unfollowers.toLocaleString(),
          icon: "fa-user-minus",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
        });
      }
    }

    // Profile Links Taps total
    {
      const total = getTotalValue(data.profile_links_taps);
      const change = calculateChange(
        Array.isArray(data.profile_links_taps) ? data.profile_links_taps : [],
        timeRange
      );
      if (typeof total === "number") {
        metrics.push({
          label: "Profile Links Taps",
          value: total.toLocaleString(),
          icon: "fa-link",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
          change,
        });
      }
    }

    // Replies total
    {
      const total = getTotalValue(data.replies);
      const change = calculateChange(
        Array.isArray(data.replies) ? data.replies : [],
        timeRange
      );
      if (typeof total === "number") {
        metrics.push({
          label: "Replies",
          value: total.toLocaleString(),
          icon: "fa-reply",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
          change,
        });
      }
    }

    // Saves total
    {
      const total = getTotalValue(data.saves);
      const change = calculateChange(
        Array.isArray(data.saves) ? data.saves : [],
        timeRange
      );
      if (typeof total === "number") {
        metrics.push({
          label: "Saves",
          value: total.toLocaleString(),
          icon: "fa-bookmark",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
          change,
        });
      }
    }

    // Total accounts engaged
    {
      const total = getTotalValue(data.accounts_engaged);
      const change = calculateChange(
        Array.isArray(data.accounts_engaged) ? data.accounts_engaged : [],
        timeRange
      );
      if (typeof total === "number") {
        metrics.push({
          label: "Accounts Engaged",
          value: total.toLocaleString(),
          icon: "fa-users",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
          change,
        });
      }
    }

    // Total reach
    {
      const total = getTotalValue(data.reach);
      const change = calculateChange(
        Array.isArray(data.reach) ? data.reach : [],
        timeRange
      );
      if (typeof total === "number") {
        metrics.push({
          label: "Total Reach",
          value: total.toLocaleString(),
          icon: "fa-eye",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
          change,
        });
      }
    }

    // Total likes
    {
      const total = getTotalValue(data.likes);
      const change = calculateChange(
        Array.isArray(data.likes) ? data.likes : [],
        timeRange
      );
      if (typeof total === "number") {
        metrics.push({
          label: "Total Likes",
          value: total.toLocaleString(),
          icon: "fa-heart",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
          change,
        });
      }
    }

    // Total comments
    {
      const total = getTotalValue(data.comments);
      const change = calculateChange(
        Array.isArray(data.comments) ? data.comments : [],
        timeRange
      );
      if (typeof total === "number") {
        metrics.push({
          label: "Total Comments",
          value: total.toLocaleString(),
          icon: "fa-comment",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
          change,
        });
      }
    }

    // Total interactions
    {
      const total = getTotalValue(data.total_interactions);
      const change = calculateChange(
        Array.isArray(data.total_interactions) ? data.total_interactions : [],
        timeRange
      );
      if (typeof total === "number") {
        metrics.push({
          label: "Total Interactions",
          value: total.toLocaleString(),
          icon: "fa-chart-line",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
          change,
        });
      }
    }

    // Total views
    {
      const total = getTotalValue(data.views);
      const change = calculateChange(
        Array.isArray(data.views) ? data.views : [],
        timeRange
      );
      if (typeof total === "number") {
        metrics.push({
          label: "Total Views",
          value: total.toLocaleString(),
          icon: "fa-play",
          color: platformColors.iconColor,
          bgColor: platformColors.metricIconBgColor,
          change,
        });
      }
    }

    return metrics;
  }, [data, timeRange, platformColors]);

  if (overviewMetrics.length === 0) {
    return null;
  }

  return (
    <div className="w-full max-w-full">
      <div className="mb-4 md:mb-6">
        <h3 className="text-base md:text-lg font-semibold text-gray-800">
          {timeRange === "30d" ? "Past 30 days metrics" : "Past 7 days metrics"}
        </h3>
      </div>

      <div className="grid grid-cols-3 lg:grid-cols-5 mx-auto gap-3 md:gap-4 w-full">
        {overviewMetrics.map((metric, index) => (
          <div
            key={metric.label}
            className="bg-white rounded-lg p-3 md:p-4  dashboard-shadow text-center min-w-0 shrink-0"
          >
            <div className="relative">
              <div className="text-lg md:text-xl font-bold text-gray-900 mb-1 truncate">
                {metric.value}
              </div>
              {/* Change indicator positioned at top right */}
              {metric.change && (
                <div
                  className={`absolute top-0 -right-1 md:-right-2 text-xs md:text-sm font-semibold ${
                    metric.change.isPositive ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {metric.change.isPositive ? "+" : "-"}
                  {metric.change.valueStr ?? `${metric.change.value}%`}
                </div>
              )}
            </div>
            <div className="text-xs md:text-sm text-gray-600 truncate">
              {metric.label}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
