"use client";

import React from "react";

interface ChartLoadingSkeletonProps {
  type?: "pie" | "line" | "bar" | "area" | "default";
  title?: string;
  showTitle?: boolean;
  showLegend?: boolean;
  className?: string;
}

// Base skeleton animation classes
const skeletonClasses =
  "animate-pulse bg-linear-to-r from-gray-200 via-gray-300 to-gray-200 bg-size-[200%_100%]";

// Default loading skeleton
export const ChartLoadingSkeleton = ({
  type = "default",
  title,
  showTitle = true,
  showLegend = false,
  className = "",
}: ChartLoadingSkeletonProps) => {
  return (
    <div
      className={`p-4 bg-white rounded-lg border border-gray-200 ${className}`}
    >
      {/* Title skeleton */}
      {showTitle && (
        <div className="mb-4">
          <div className={`h-6 w-48 rounded ${skeletonClasses}`}></div>
          {title && (
            <div className={`h-4 w-32 rounded mt-2 ${skeletonClasses}`}></div>
          )}
        </div>
      )}

      {/* Chart content skeleton based on type */}
      <div className="flex items-center justify-center h-64">
        {type === "pie" && <PieChartSkeleton />}
        {type === "line" && <LineChartSkeleton />}
        {type === "bar" && <BarChartSkeleton />}
        {type === "area" && <AreaChartSkeleton />}
        {type === "default" && <DefaultChartSkeleton />}
      </div>

      {/* Legend skeleton */}
      {showLegend && (
        <div className="mt-4 flex flex-wrap gap-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${skeletonClasses}`}></div>
              <div className={`h-4 w-16 rounded ${skeletonClasses}`}></div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Pie chart specific skeleton
const PieChartSkeleton = () => (
  <div className="relative">
    <div className={`w-48 h-48 rounded-full ${skeletonClasses}`}></div>
    <div className="absolute inset-0 flex items-center justify-center">
      <div className={`w-16 h-16 rounded-full bg-white`}></div>
    </div>
  </div>
);

// Line chart specific skeleton
const LineChartSkeleton = () => (
  <div className="w-full h-full flex flex-col justify-end space-y-2">
    {/* Y-axis labels */}
    <div className="flex items-end justify-between h-full">
      <div className="flex flex-col justify-between h-full py-2">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className={`h-3 w-8 rounded ${skeletonClasses}`}></div>
        ))}
      </div>

      {/* Chart area with wavy line simulation */}
      <div className="flex-1 h-full relative ml-4">
        <svg className="w-full h-full" viewBox="0 0 300 200">
          <path
            d="M10,150 Q50,100 100,120 T200,80 T290,100"
            stroke="currentColor"
            strokeWidth="3"
            fill="none"
            className={`text-gray-300 ${skeletonClasses}`}
          />
          <path
            d="M10,170 Q50,130 100,140 T200,110 T290,130"
            stroke="currentColor"
            strokeWidth="3"
            fill="none"
            className={`text-gray-300 ${skeletonClasses}`}
          />
        </svg>
      </div>
    </div>

    {/* X-axis labels */}
    <div className="flex justify-between mt-2 ml-12">
      {[1, 2, 3, 4, 5].map((i) => (
        <div key={i} className={`h-3 w-12 rounded ${skeletonClasses}`}></div>
      ))}
    </div>
  </div>
);

// Bar chart specific skeleton
const BarChartSkeleton = () => (
  <div className="w-full h-full flex flex-col justify-end">
    <div className="flex items-end justify-between h-full space-x-2">
      {[40, 80, 60, 90, 50, 70, 85].map((height, i) => (
        <div
          key={i}
          className={`w-8 rounded-t ${skeletonClasses}`}
          style={{ height: `${height}%` }}
        ></div>
      ))}
    </div>

    {/* X-axis labels */}
    <div className="flex justify-between mt-2">
      {[1, 2, 3, 4, 5, 6, 7].map((i) => (
        <div key={i} className={`h-3 w-6 rounded ${skeletonClasses}`}></div>
      ))}
    </div>
  </div>
);

// Area chart specific skeleton
const AreaChartSkeleton = () => (
  <div className="w-full h-full flex flex-col justify-end">
    <div className="flex-1 relative">
      <svg className="w-full h-full" viewBox="0 0 300 200">
        <defs>
          <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="currentColor" stopOpacity="0.3" />
            <stop offset="100%" stopColor="currentColor" stopOpacity="0.1" />
          </linearGradient>
        </defs>
        <path
          d="M10,150 Q50,100 100,120 T200,80 T290,100 L290,190 L10,190 Z"
          fill="url(#areaGradient)"
          className={`text-gray-300 ${skeletonClasses}`}
        />
        <path
          d="M10,150 Q50,100 100,120 T200,80 T290,100"
          stroke="currentColor"
          strokeWidth="2"
          fill="none"
          className={`text-gray-300 ${skeletonClasses}`}
        />
      </svg>
    </div>

    {/* X-axis labels */}
    <div className="flex justify-between mt-2">
      {[1, 2, 3, 4, 5].map((i) => (
        <div key={i} className={`h-3 w-12 rounded ${skeletonClasses}`}></div>
      ))}
    </div>
  </div>
);

// Default chart skeleton
const DefaultChartSkeleton = () => (
  <div className="w-full h-full flex items-center justify-center">
    <div className="text-center space-y-4">
      <div
        className={`w-16 h-16 rounded-full ${skeletonClasses} mx-auto`}
      ></div>
      <div className={`h-4 w-32 rounded ${skeletonClasses} mx-auto`}></div>
      <div className={`h-3 w-24 rounded ${skeletonClasses} mx-auto`}></div>
    </div>
  </div>
);

// Specific loading skeletons for different chart types
export const PieChartLoadingSkeleton = (
  props: Omit<ChartLoadingSkeletonProps, "type">
) => <ChartLoadingSkeleton {...props} type="pie" showLegend />;

export const LineChartLoadingSkeleton = (
  props: Omit<ChartLoadingSkeletonProps, "type">
) => <ChartLoadingSkeleton {...props} type="line" />;

export const BarChartLoadingSkeleton = (
  props: Omit<ChartLoadingSkeletonProps, "type">
) => <ChartLoadingSkeleton {...props} type="bar" />;

export const AreaChartLoadingSkeleton = (
  props: Omit<ChartLoadingSkeletonProps, "type">
) => <ChartLoadingSkeleton {...props} type="area" />;
