"use client";

import { useState } from "react";

interface AnalyticsHeaderProps {
  selectedMetrics: string[];
  onMetricsChange: (metrics: string[]) => void;
  onRefresh: () => void;
}

const AVAILABLE_METRICS = [
  { id: "followers_overview", label: "Followers Overview", icon: "fa-users" },
  { id: "follower_views", label: "Follower Views", icon: "fa-chart-pie" },
  { id: "accounts_engaged", label: "Accounts Engaged", icon: "fa-users" },
  { id: "comments", label: "Comments", icon: "fa-comment" },
  { id: "reach", label: "Reach", icon: "fa-eye" },
  { id: "likes", label: "Likes", icon: "fa-heart" },
  {
    id: "follows_and_unfollows",
    label: "Follows & Unfollows",
    icon: "fa-user-plus",
  },
  { id: "profile_links_taps", label: "Profile Links Taps", icon: "fa-link" },
  { id: "replies", label: "Replies", icon: "fa-reply" },
  { id: "saved", label: "Saved", icon: "fa-bookmark" },
  { id: "shares", label: "Shares", icon: "fa-share" },
  {
    id: "total_interactions",
    label: "Total Interactions",
    icon: "fa-chart-line",
  },
  { id: "views", label: "Views", icon: "fa-play" },
  {
    id: "engaged_audience_demographics",
    label: "Engaged Demographics",
    icon: "fa-chart-pie",
  },
  {
    id: "engaged_audience_gender",
    label: "Engaged Gender",
    icon: "fa-venus-mars",
  },
  {
    id: "follower_demographics",
    label: "Follower Demographics",
    icon: "fa-users",
  },
  { id: "follower_gender", label: "Follower Gender", icon: "fa-venus-mars" },
  { id: "top_posts", label: "Top Posts", icon: "fa-image" },
  { id: "top_reels", label: "Top Reels", icon: "fa-video" },
];

export const AnalyticsHeader = ({
  selectedMetrics,
  onMetricsChange,
  onRefresh,
}: AnalyticsHeaderProps) => {
  const [showMetricsSelector, setShowMetricsSelector] = useState(false);

  const toggleMetric = (metricId: string) => {
    if (selectedMetrics.includes(metricId)) {
      onMetricsChange(selectedMetrics.filter((id) => id !== metricId));
    } else {
      onMetricsChange([...selectedMetrics, metricId]);
    }
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        {/* Title */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Instagram Analytics
          </h1>
          <p className="text-gray-600 mt-1">
            Track your Instagram performance metrics
          </p>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-3">
          {/* Metrics Selector */}
          <div className="relative">
            <button
              onClick={() => setShowMetricsSelector(!showMetricsSelector)}
              className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <i className="fas fa-filter text-gray-600"></i>
              <span className="text-gray-700">
                Metrics ({selectedMetrics.length})
              </span>
              <i
                className={`fas fa-chevron-down text-gray-500 transition-transform ${
                  showMetricsSelector ? "rotate-180" : ""
                }`}
              ></i>
            </button>

            {/* Metrics Dropdown */}
            {showMetricsSelector && (
              <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 mb-3">
                    Select Metrics to Display
                  </h3>
                  <div className="space-y-2">
                    {AVAILABLE_METRICS.map((metric) => (
                      <label
                        key={metric.id}
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={selectedMetrics.includes(metric.id)}
                          onChange={() => toggleMetric(metric.id)}
                          className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                        />
                        <i
                          className={`fas ${metric.icon} text-gray-500 w-4`}
                        ></i>
                        <span className="text-gray-700">{metric.label}</span>
                      </label>
                    ))}
                  </div>
                  <div className="mt-4 pt-3 border-t border-gray-200 flex gap-2">
                    <button
                      onClick={() =>
                        onMetricsChange(AVAILABLE_METRICS.map((m) => m.id))
                      }
                      className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Select All
                    </button>
                    <button
                      onClick={() => onMetricsChange([])}
                      className="flex-1 px-3 py-2 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Refresh Button */}
          <button
            onClick={onRefresh}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <i className="fas fa-sync-alt"></i>
            <span className="hidden md:inline">Refresh</span>
          </button>
        </div>
      </div>

      {/* Selected Metrics Tags */}
      {selectedMetrics.length > 0 && (
        <div className="mt-4 flex flex-wrap gap-2">
          {selectedMetrics.map((metricId) => {
            const metric = AVAILABLE_METRICS.find((m) => m.id === metricId);
            if (!metric) return null;

            return (
              <div
                key={metricId}
                className="flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
              >
                <i className={`fas ${metric.icon} text-xs`}></i>
                <span>{metric.label}</span>
                <button
                  onClick={() => toggleMetric(metricId)}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <i className="fas fa-times text-xs"></i>
                </button>
              </div>
            );
          })}
        </div>
      )}

      {/* Click outside to close */}
      {showMetricsSelector && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowMetricsSelector(false)}
        />
      )}
    </div>
  );
};
