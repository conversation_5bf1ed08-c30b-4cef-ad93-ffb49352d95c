"use client";

import React, { Component, ReactNode } from "react";

interface ChartErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface ChartErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

// Default error fallback component
const DefaultErrorFallback = ({
  error,
  retry,
}: {
  error: Error;
  retry: () => void;
}) => (
  <div className="flex flex-col items-center justify-center h-full p-4 bg-gray-50 rounded-lg border border-gray-200">
    <div className="text-center space-y-3">
      <i className="fas fa-exclamation-triangle text-red-500 text-3xl"></i>
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">
          Chart Failed to Load
        </h3>
        <p className="text-sm text-gray-600 mb-3">
          {error.message ||
            "An unexpected error occurred while loading the chart."}
        </p>
      </div>
      <button
        onClick={retry}
        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
      >
        <i className="fas fa-redo mr-2"></i>
        Try Again
      </button>
    </div>
  </div>
);

export class ChartErrorBoundary extends Component<
  ChartErrorBoundaryProps,
  ChartErrorBoundaryState
> {
  constructor(props: ChartErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): ChartErrorBoundaryState {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error for debugging
    console.error("Chart Error Boundary caught an error:", error, errorInfo);
  }

  retry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error} retry={this.retry} />;
    }

    return this.props.children;
  }
}

// Specific error fallback components for different chart types
export const PieChartErrorFallback = ({
  error,
  retry,
}: {
  error: Error;
  retry: () => void;
}) => (
  <div className="flex flex-col items-center justify-center h-full p-4 bg-gray-50 rounded-lg border border-gray-200">
    <div className="text-center space-y-3">
      <i className="fas fa-chart-pie text-red-500 text-3xl"></i>
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">
          Pie Chart Error
        </h3>
        <p className="text-sm text-gray-600 mb-3">
          Unable to render the pie chart. Please check your data format.
        </p>
      </div>
      <button
        onClick={retry}
        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
      >
        <i className="fas fa-redo mr-2"></i>
        Retry
      </button>
    </div>
  </div>
);

export const LineChartErrorFallback = ({
  error,
  retry,
}: {
  error: Error;
  retry: () => void;
}) => (
  <div className="flex flex-col items-center justify-center h-full p-4 bg-gray-50 rounded-lg border border-gray-200">
    <div className="text-center space-y-3">
      <i className="fas fa-chart-line text-red-500 text-3xl"></i>
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">
          Line Chart Error
        </h3>
        <p className="text-sm text-gray-600 mb-3">
          Unable to render the line chart. Please verify your time series data.
        </p>
      </div>
      <button
        onClick={retry}
        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
      >
        <i className="fas fa-redo mr-2"></i>
        Retry
      </button>
    </div>
  </div>
);

export const BarChartErrorFallback = ({
  error,
  retry,
}: {
  error: Error;
  retry: () => void;
}) => (
  <div className="flex flex-col items-center justify-center h-full p-4 bg-gray-50 rounded-lg border border-gray-200">
    <div className="text-center space-y-3">
      <i className="fas fa-chart-bar text-red-500 text-3xl"></i>
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">
          Bar Chart Error
        </h3>
        <p className="text-sm text-gray-600 mb-3">
          Unable to render the bar chart. Please check your categorical data.
        </p>
      </div>
      <button
        onClick={retry}
        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
      >
        <i className="fas fa-redo mr-2"></i>
        Retry
      </button>
    </div>
  </div>
);
