"use client";

import React from "react";

interface SocialAccount {
  platform: string;
  social_id: string;
  social_name: string;
  username: string;
  profile_photo?: string;
}

interface SelectedSocial {
  platform: string;
  social_id: string;
  social_name: string;
  username: string;
  profile_photo?: string;
}

interface SocialAccountSelectionSidebarProps {
  socialAccounts: SocialAccount[];
  selectedSocial: SelectedSocial | null;
  onSocialSelect: (account: SocialAccount) => void;
  onAddClick: () => void;
  // Optional: allow rendering on mobile and pass custom classes
  showOnMobile?: boolean;
  className?: string;
}

export const SocialAccountSelectionSidebar = ({
  socialAccounts,
  selectedSocial,
  onSocialSelect,
  onAddClick,
  showOnMobile = false,
  className = "",
}: SocialAccountSelectionSidebarProps) => {
  // Debug: Log the social accounts to see what we're getting
  console.log("Social accounts:", socialAccounts);
  console.log("Selected social:", selectedSocial);

  const renderPlatformIcon = (
    account: SocialAccount,
    options?: { disabled?: boolean }
  ) => {
    console.log("Rendering icon for platform:", account.platform);
    const disabled = options?.disabled;

    // For Twitter/X, use modern X icon
    if (account.platform === "twitter" || account.platform === "x") {
      const isSelected =
        !disabled && selectedSocial?.platform === account.platform;
      return (
        <svg
          className={`w-8 h-8 transition-all duration-200 ${
            disabled
              ? "text-gray-300"
              : isSelected
              ? "text-black"
              : "text-gray-400"
          }`}
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
        </svg>
      );
    }

    // For other platforms, use existing SVG icons
    const iconPath =
      !disabled && selectedSocial?.platform === account.platform
        ? `/icons/performance/${account.platform}-on.svg`
        : `/icons/performance/${account.platform}-off.svg`;

    console.log("Icon path:", iconPath);

    return (
      <div className="relative w-8 h-8">
        <img
          src={iconPath}
          alt={account.platform}
          className={`w-8 h-8 transition-all duration-200 ${
            disabled ? "opacity-40" : ""
          }`}
          onError={(e) => {
            console.error("Failed to load icon:", iconPath);
            // Hide the broken image and show fallback
            e.currentTarget.style.display = "none";
            const fallback = e.currentTarget.nextElementSibling as HTMLElement;
            if (fallback) fallback.style.display = "flex";
          }}
        />
        {/* Fallback icon */}
        <div
          className={`absolute inset-0 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-xs font-bold hidden ${
            disabled ? "opacity-40" : ""
          }`}
          style={{ display: "none" }}
        >
          {account.platform.charAt(0).toUpperCase()}
        </div>
      </div>
    );
  };

  // Ensure X and Facebook are always shown (disabled if absent)
  const platformsToAlwaysShow = ["x", "facebook"];
  const accountByPlatform = (socialAccounts || []).reduce<
    Record<string, SocialAccount>
  >((acc, curr) => {
    acc[curr.platform] = curr;
    // Normalize X/Twitter
    if (curr.platform === "twitter") acc["x"] = curr;
    if (curr.platform === "x") acc["twitter"] = curr;
    return acc;
  }, {});

  // Build display list: include all existing platforms + required ones
  const displayPlatforms = Array.from(
    new Set([
      ...(socialAccounts || []).map((a) => a.platform),
      ...platformsToAlwaysShow,
    ])
  );

  // Sort: Instagram first, then Facebook, then X, then others
  const sortedPlatforms = displayPlatforms.sort((a, b) => {
    const order: Record<string, number> = { instagram: 1, facebook: 2, x: 3 };
    const aOrder = order[a] || 999;
    const bOrder = order[b] || 999;
    return aOrder - bOrder;
  });

  const baseClasses =
    "w-20 bg-gray-100 box-border  px-2 flex-col items-center sticky  md:top-0 self-start h-screen pt-[10vh] md:h-screen overflow-y-auto overflow-x-hidden z-10 shrink-0";
  const visibilityClasses = showOnMobile ? "flex" : "hidden md:flex";

  return (
    <div className={`${visibilityClasses} ${baseClasses} ${className}`.trim()}>
      {sortedPlatforms.length > 0 ? (
        sortedPlatforms.map((platform) => {
          const account = accountByPlatform[platform] || {
            platform,
            social_id: "",
            social_name: "",
            username: "",
          };
          const isDisabled = !accountByPlatform[platform];
          return (
            <div key={platform} className="flex flex-col items-center mb-4">
              <button
                className={`b-1 rounded-full p-2 transition-colors duration-200 ${
                  isDisabled ? "opacity-60" : "hover:bg-gray-200"
                }`}
                onClick={() =>
                  isDisabled
                    ? onAddClick()
                    : onSocialSelect(account as SocialAccount)
                }
                aria-disabled={isDisabled}
                title={
                  isDisabled
                    ? `Connect ${platform
                        .charAt(0)
                        .toUpperCase()}${platform.slice(1)}`
                    : undefined
                }
              >
                {renderPlatformIcon(account as SocialAccount, {
                  disabled: isDisabled,
                })}
              </button>
            </div>
          );
        })
      ) : (
        // Show a placeholder when no accounts
        <div className="text-xs text-gray-400 text-center mb-4">
          No social accounts
        </div>
      )}

      <div className="flex flex-col items-center">
        <button
          className="mb-1 bg-gray-200 rounded-full p-2 hover:bg-gray-300 transition-colors duration-200"
          onClick={onAddClick}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 text-gray-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4v16m8-8H4"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};
