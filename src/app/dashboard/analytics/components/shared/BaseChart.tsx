"use client";

import { ReactNode, memo, useEffect, useState } from "react";
import { getPlatformColors } from "../../utils/colors";

interface BaseChartProps {
  title: string;
  icon?: string; // Made optional since we're removing icons
  description?: string;
  value?: string | number;
  change?: {
    value: number;
    isPositive: boolean;
  };
  children: ReactNode;
  actions?: ReactNode;
  className?: string;
  dynamicHeight?: boolean;
  minHeight?: string;
  height?: string;
  mobileHeight?: string; // Height to use on small screens (<768px)
  contentOffset?: number; // Space reserved for header/actions (in px)
  bottomPadding?: number; // Extra space below the chart area (in px)
  selectedSocial?: {
    platform: string;
    username?: string;
  };
}

export const BaseChart = memo(
  ({
    title,
    icon,
    description,
    value,
    change,
    children,
    actions,
    className = "",
    dynamicHeight = false,
    minHeight = "200px",
    height = "450px",
    mobileHeight,
    contentOffset = 80,
    bottomPadding = 0,
    selectedSocial,
  }: BaseChartProps) => {
    // Get platform-specific colors
    const platformColors = getPlatformColors(selectedSocial?.platform);

    // Use CSS variable + media query to set mobile height instead of JS-driven breakpoints.
    // This avoids mismatches between server and client rendering.
    // Normalize height values to valid CSS (append px when missing)
    const normalizeHeight = (val?: string) => {
      if (!val) return undefined;
      // If already contains a unit, return as is
      if (/\d(px|%|vh|vw|rem|em)$/i.test(val.trim())) return val;
      // If purely numeric string, append px
      if (/^\d+(?:\.\d+)?$/.test(val.trim())) return `${val.trim()}px`;
      return val;
    };

    const containerHeight = normalizeHeight(height) || "450px";
    const mobileContainerHeight = normalizeHeight(mobileHeight) || undefined;

    // Get social media icon based on platform
    const getSocialIcon = (platform: string) => {
      switch (platform?.toLowerCase()) {
        case "instagram":
          return "fab fa-instagram";
        case "facebook":
          return "fab fa-facebook";
        case "twitter":
          return "fab fa-twitter";
        case "linkedin":
          return "fab fa-linkedin";
        case "youtube":
          return "fab fa-youtube";
        case "tiktok":
          return "fab fa-tiktok";
        default:
          return "fas fa-chart-line";
      }
    };

    const extraClass = (className || "").trim();
    const qualifier = extraClass ? `.${extraClass.split(" ").join(".")}` : "";

    return (
      <div
        className={`bg-white rounded-xl p-3 md:p-4 shadow-surface overflow-hidden bi-chart ${extraClass}`}
        style={{
          // Default desktop height; mobile override handled by media query below
          height: containerHeight,
          // expose CSS variable for potential descendant use
          ["--chart-height" as any]: containerHeight,
        }}
      >
        {/* Inline style tag to override height on small screens using mobileHeight when provided */}
        {mobileContainerHeight && (
          <style>{`
            @media (max-width: 767px) {
              .bi-chart${qualifier} { height: ${mobileContainerHeight} !important; }
            }
          `}</style>
        )}
        {/* Header */}
        <div className="flex items-center justify-between mb-4 gap-2 flex-wrap">
          <div className="flex items-center gap-3">
            {/* Social Media Platform Icon */}
            {selectedSocial?.platform && (
              <div className="w-12 h-12 flex items-center justify-center">
                <i
                  className={`${getSocialIcon(selectedSocial.platform)} ${
                    platformColors.iconColor
                  } text-2xl`}
                ></i>
              </div>
            )}

            {/* Title Only */}
            <div className="min-w-0 flex-1">
              <h3 className="text-base md:text-lg font-semibold text-gray-900 truncate">
                {title}
              </h3>
            </div>
          </div>

          {/* Actions */}
          {actions && (
            <div className="shrink-0 w-full sm:w-auto overflow-x-auto whitespace-nowrap pb-1">
              {actions}
            </div>
          )}
        </div>

        {/* Chart Content */}
        <div
          className="w-full"
          style={{
            height: `calc(${containerHeight} - ${contentOffset}px - ${bottomPadding}px)`, // Reserve extra bottom space without shrinking the outer container
            minHeight: 120,
          }}
        >
          {children}
        </div>
        {/* Extra bottom padding to keep axis labels within container */}
        {bottomPadding > 0 && <div style={{ height: `${bottomPadding}px` }} />}
      </div>
    );
  }
);
