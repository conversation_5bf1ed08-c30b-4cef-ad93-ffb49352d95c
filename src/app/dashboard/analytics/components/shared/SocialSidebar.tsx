"use client";

import React from "react";

interface SocialSidebarProps {
  onSocialSelect?: (platform: string) => void;
  onAddClick?: () => void;
  selectedPlatform?: string;
}

export const SocialSidebar = ({
  onSocialSelect,
  onAddClick,
  selectedPlatform,
}: SocialSidebarProps) => {
  const socialPlatforms = [
    {
      name: "instagram",
      icon: "fab fa-instagram",
      color: "text-pink-500",
      hoverColor: "hover:text-pink-600",
    },
    {
      name: "facebook",
      icon: "fab fa-facebook-f",
      color: "text-blue-600",
      hoverColor: "hover:text-blue-700",
    },
    {
      name: "twitter",
      icon: "fab fa-x-twitter",
      color: "text-gray-800",
      hoverColor: "hover:text-black",
    },
  ];

  return (
    <div
      className="w-20 shadow-sm border-r border-gray-200 flex flex-col items-center py-6 space-y-4"
      style={{ backgroundColor: "#2C3E5012" }}
    >
      {socialPlatforms.map((platform) => (
        <button
          key={platform.name}
          onClick={() => onSocialSelect?.(platform.name)}
          className={`w-12 h-12 rounded-lg flex items-center justify-center transition-all duration-200 ${
            selectedPlatform === platform.name
              ? "bg-gray-100 shadow-md"
              : "hover:bg-gray-50"
          }`}
        >
          <i
            className={`${platform.icon} text-2xl transition-colors ${
              selectedPlatform === platform.name
                ? platform.color // Keep the colorful styling when selected
                : `text-gray-400 ${platform.hoverColor}`
            }`}
          />
        </button>
      ))}

      {/* Add Button */}
      <button
        onClick={onAddClick}
        className="w-12 h-12 rounded-lg flex items-center justify-center transition-all duration-200 hover:bg-gray-50 border-2 border-dashed border-gray-300 hover:border-gray-400"
      >
        <i className="fas fa-plus text-xl text-gray-400 hover:text-gray-600 transition-colors" />
      </button>
    </div>
  );
};
