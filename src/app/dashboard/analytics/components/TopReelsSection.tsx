"use client";

import React, { useState } from "react";
import { getPlatformColors } from "../utils/colors";

interface TopReelsSectionProps {
  analyticsData: any;
  selectedSocial: any;
}

const TopReelsSection: React.FC<TopReelsSectionProps> = ({
  analyticsData,
  selectedSocial,
}) => {
  const [showAllReels, setShowAllReels] = useState(false);

  // Get platform-specific colors
  const platformColors = getPlatformColors(selectedSocial?.platform);

  // Filter for reels only
  const reelsData =
    analyticsData?.top_posts?.filter(
      (post: any) => post.media_product_type === "REELS"
    ) || [];

  return (
    <div className="bg-white rounded-lg p-6 dashboard-shadow ">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 flex items-center justify-center">
            <i
              className={`fas fa-video ${platformColors.iconColor} text-2xl`}
            ></i>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Top Reels</h3>
            <p className="text-sm text-gray-500">
              Best performing reels this period
            </p>
          </div>
        </div>

        {/* See All Button */}
        {reelsData.length > 4 && (
          <button
            onClick={() => setShowAllReels(!showAllReels)}
            className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            {showAllReels ? (
              <>
                <span>Show Less</span>
                <i className="fas fa-chevron-up text-xs"></i>
              </>
            ) : (
              <>
                <span>See All</span>
                <i className="fas fa-chevron-right text-xs"></i>
              </>
            )}
          </button>
        )}
      </div>

      {reelsData.length > 0 ? (
        <div className="overflow-hidden transition-all duration-500 ease-in-out">
          {/* First 4 reels - always visible */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {reelsData.slice(0, 4).map((reel: any) => {
              const reachValue =
                reel.insights?.data?.find(
                  (insight: any) => insight.name === "reach"
                )?.values?.[0]?.value || 0;
              const likesValue =
                reel.insights?.data?.find(
                  (insight: any) => insight.name === "likes"
                )?.values?.[0]?.value || 0;
              const commentsValue =
                reel.insights?.data?.find(
                  (insight: any) => insight.name === "comments"
                )?.values?.[0]?.value || 0;

              return (
                <div
                  key={reel.id}
                  className="cursor-pointer group"
                  onClick={() => {
                    if (reel.permalink) {
                      window.open(
                        reel.permalink,
                        "_blank",
                        "noopener,noreferrer"
                      );
                    }
                  }}
                >
                  {/* Reel Media - Vertical aspect ratio */}
                  <div className="relative w-full rounded-lg overflow-hidden">
                    <div className="aspect-9/16 bg-gray-200 flex items-center justify-center relative overflow-hidden">
                      {reel.thumbnail_url ? (
                        <img
                          src={reel.thumbnail_url}
                          alt="Reel thumbnail"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <i className="fas fa-video text-gray-400 text-4xl"></i>
                      )}

                      {/* Play button overlay */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="bg-black/50 rounded-full w-10 h-10 flex justify-center items-center">
                          <i className="fas fa-play text-white text-xl"></i>
                        </div>
                      </div>

                      {/* Name and Metrics Overlay */}
                      <div className="absolute bottom-0 left-0 right-0 bg-black/10 backdrop-blur-sm p-2">
                        <div className="grid grid-cols-3 gap-1 text-xs mb-1">
                          <div className="text-center">
                            <div className="font-semibold text-white">
                              {reachValue.toLocaleString()}
                            </div>
                            <div className="text-gray-300">Reach</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold text-white">
                              {likesValue.toLocaleString()}
                            </div>
                            <div className="text-gray-300">Likes</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold text-white">
                              {commentsValue.toLocaleString()}
                            </div>
                            <div className="text-gray-300">Comments</div>
                          </div>
                        </div>
                        <div className="text-center">
                          <h4 className="text-white text-xs font-medium truncate">
                            {reel.caption
                              ? reel.caption.split(" ").slice(0, 3).join(" ") +
                                (reel.caption.split(" ").length > 3
                                  ? "..."
                                  : "")
                              : "Untitled Reel"}
                          </h4>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Date Only */}
                  <div className="mt-2 text-center">
                    <span className="text-xs text-gray-500">
                      {new Date(reel.timestamp).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Additional reels - animated expand/collapse */}
          {reelsData.length > 4 && (
            <div
              className={`transition-all duration-500 ease-in-out overflow-hidden ${
                showAllReels
                  ? "max-h-[2000px] opacity-100 mt-4"
                  : "max-h-0 opacity-0 mt-0"
              }`}
            >
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {reelsData.slice(4).map((reel: any, index: number) => {
                  const reachValue =
                    reel.insights?.data?.find(
                      (insight: any) => insight.name === "reach"
                    )?.values?.[0]?.value || 0;
                  const likesValue =
                    reel.insights?.data?.find(
                      (insight: any) => insight.name === "likes"
                    )?.values?.[0]?.value || 0;
                  const commentsValue =
                    reel.insights?.data?.find(
                      (insight: any) => insight.name === "comments"
                    )?.values?.[0]?.value || 0;

                  return (
                    <div
                      key={reel.id}
                      className={`cursor-pointer group transition-all duration-300 ease-in-out ${
                        showAllReels
                          ? "transform translate-y-0 opacity-100"
                          : "transform translate-y-4 opacity-0"
                      }`}
                      style={{
                        transitionDelay: showAllReels
                          ? `${index * 50}ms`
                          : "0ms",
                      }}
                      onClick={() => {
                        if (reel.permalink) {
                          window.open(
                            reel.permalink,
                            "_blank",
                            "noopener,noreferrer"
                          );
                        }
                      }}
                    >
                      {/* Reel Media - Vertical aspect ratio */}
                      <div className="relative w-full rounded-lg overflow-hidden">
                        <div className="aspect-9/16 bg-gray-200 flex items-center justify-center relative overflow-hidden">
                          {reel.thumbnail_url ? (
                            <img
                              src={reel.thumbnail_url}
                              alt="Reel thumbnail"
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <i className="fas fa-video text-gray-400 text-4xl"></i>
                          )}

                          {/* Play button overlay */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="bg-black/50 rounded-full flex justify-center items-center w-10 h-10">
                              <i className="fas fa-play text-white text-xl"></i>
                            </div>
                          </div>

                          {/* Name and Metrics Overlay */}
                          <div className="absolute bottom-0 left-0 right-0 bg-black/10 backdrop-blur-sm p-2">
                            <div className="grid grid-cols-3 gap-1 text-xs mb-1">
                              <div className="text-center">
                                <div className="font-semibold text-white">
                                  {reachValue.toLocaleString()}
                                </div>
                                <div className="text-gray-300">Reach</div>
                              </div>
                              <div className="text-center">
                                <div className="font-semibold text-white">
                                  {likesValue.toLocaleString()}
                                </div>
                                <div className="text-gray-300">Likes</div>
                              </div>
                              <div className="text-center">
                                <div className="font-semibold text-white">
                                  {commentsValue.toLocaleString()}
                                </div>
                                <div className="text-gray-300">Comments</div>
                              </div>
                            </div>
                            <div className="text-center">
                              <h4 className="text-white text-xs font-medium truncate">
                                {reel.caption
                                  ? reel.caption
                                      .split(" ")
                                      .slice(0, 3)
                                      .join(" ") +
                                    (reel.caption.split(" ").length > 3
                                      ? "..."
                                      : "")
                                  : "Untitled Reel"}
                              </h4>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Date Only */}
                      <div className="mt-2 text-center">
                        <span className="text-xs text-gray-500">
                          {new Date(reel.timestamp).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <i className="fas fa-video text-gray-300 text-4xl mb-4"></i>
          <h4 className="text-lg font-medium text-gray-500 mb-2">
            No Reels Available
          </h4>
          <p className="text-sm text-gray-400">
            No reels data is available for the selected period.
          </p>
        </div>
      )}
    </div>
  );
};

export default TopReelsSection;
