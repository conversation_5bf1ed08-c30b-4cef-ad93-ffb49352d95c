"use client";

import React, { useState } from "react";
import { getPlatformColors } from "../utils/colors";

interface TopPostsSectionProps {
  analyticsData: any;
  selectedSocial: any;
}

const TopPostsSection: React.FC<TopPostsSectionProps> = ({
  analyticsData,
  selectedSocial,
}) => {
  const [showAllPosts, setShowAllPosts] = useState(false);

  // Get platform-specific colors
  const platformColors = getPlatformColors(selectedSocial?.platform);

  // Filter out reels - only show non-reel content
  const postsData =
    analyticsData?.top_posts?.filter(
      (post: any) => post.media_product_type !== "REELS"
    ) || [];

  return (
    <div className="bg-white rounded-lg p-6 dashboard-shadow">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 flex items-center justify-center">
            <i
              className={`fas fa-images ${platformColors.iconColor} text-2xl`}
            ></i>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Top Posts</h3>
            <p className="text-sm text-gray-500">
              Best performing posts this period
            </p>
          </div>
        </div>

        {/* See All Button */}
        {postsData.length > 4 && (
          <button
            onClick={() => setShowAllPosts(!showAllPosts)}
            className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            {showAllPosts ? (
              <>
                <span>Show Less</span>
                <i className="fas fa-chevron-up text-xs"></i>
              </>
            ) : (
              <>
                <span>See All</span>
                <i className="fas fa-chevron-right text-xs"></i>
              </>
            )}
          </button>
        )}
      </div>

      {postsData.length > 0 ? (
        <div className="overflow-hidden transition-all duration-500 ease-in-out">
          {/* First 4 posts - always visible */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {postsData.slice(0, 4).map((post: any) => {
              const reachValue =
                post.insights?.data?.find(
                  (insight: any) => insight.name === "reach"
                )?.values?.[0]?.value || 0;
              const likesValue =
                post.insights?.data?.find(
                  (insight: any) => insight.name === "likes"
                )?.values?.[0]?.value || 0;
              const commentsValue =
                post.insights?.data?.find(
                  (insight: any) => insight.name === "comments"
                )?.values?.[0]?.value || 0;

              return (
                <div
                  key={post.id}
                  className="cursor-pointer group"
                  onClick={() => {
                    if (post.permalink) {
                      window.open(
                        post.permalink,
                        "_blank",
                        "noopener,noreferrer"
                      );
                    }
                  }}
                >
                  {/* Full Size Media */}
                  <div className="relative w-full rounded-lg overflow-hidden">
                    {post.media_type === "VIDEO" ? (
                      <div className="aspect-square bg-gray-200 flex items-center justify-center relative overflow-hidden">
                        {post.thumbnail_url ? (
                          <img
                            src={post.thumbnail_url}
                            alt="Post thumbnail"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <i className="fas fa-video text-gray-400 text-4xl"></i>
                        )}
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="bg-black/50 rounded-full w-10 h-10 flex items-center justify-center">
                            <i className="fas fa-play text-white text-xl"></i>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="aspect-square bg-gray-200 flex items-center justify-center overflow-hidden">
                        {post.media_url ? (
                          <img
                            src={post.media_url}
                            alt="Post image"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <i className="fas fa-image text-gray-400 text-4xl"></i>
                        )}
                      </div>
                    )}

                    {/* Media Type Badge */}
                    <div className="absolute top-3 left-3">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full backdrop-blur-sm ${
                          post.media_product_type === "REELS"
                            ? "bg-purple-100/90 text-purple-700"
                            : "bg-blue-100/90 text-blue-700"
                        }`}
                      >
                        {post.media_product_type}
                      </span>
                    </div>

                    {/* Name and Metrics Overlay */}
                    <div className="absolute bottom-0 left-0 right-0 bg-black/40 backdrop-blur-sm p-3">
                      <div className="grid grid-cols-3 gap-2 text-xs mb-2">
                        <div className="text-center">
                          <div className="font-semibold text-white">
                            {reachValue.toLocaleString()}
                          </div>
                          <div className="text-gray-300">Reach</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-white">
                            {likesValue.toLocaleString()}
                          </div>
                          <div className="text-gray-300">Likes</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-white">
                            {commentsValue.toLocaleString()}
                          </div>
                          <div className="text-gray-300">Comments</div>
                        </div>
                      </div>
                      <div className="text-center">
                        <span className="text-white !text-xs font-medium truncate">
                          {post.caption
                            ? post.caption.split(" ").slice(0, 4).join(" ") +
                              (post.caption.split(" ").length > 4 ? "..." : "")
                            : "Untitled Post"}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Date Only */}
                  <div className="mt-2 text-center">
                    <span className="text-xs text-gray-500">
                      {new Date(post.timestamp).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Additional posts - animated expand/collapse */}
          {postsData.length > 4 && (
            <div
              className={`transition-all duration-500 ease-in-out overflow-hidden ${
                showAllPosts
                  ? "max-h-[2000px] opacity-100 mt-4"
                  : "max-h-0 opacity-0 mt-0"
              }`}
            >
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {postsData.slice(4).map((post: any, index: number) => {
                  const reachValue =
                    post.insights?.data?.find(
                      (insight: any) => insight.name === "reach"
                    )?.values?.[0]?.value || 0;
                  const likesValue =
                    post.insights?.data?.find(
                      (insight: any) => insight.name === "likes"
                    )?.values?.[0]?.value || 0;
                  const commentsValue =
                    post.insights?.data?.find(
                      (insight: any) => insight.name === "comments"
                    )?.values?.[0]?.value || 0;

                  return (
                    <div
                      key={post.id}
                      className={`cursor-pointer group transition-all duration-300 ease-in-out ${
                        showAllPosts
                          ? "transform translate-y-0 opacity-100"
                          : "transform translate-y-4 opacity-0"
                      }`}
                      style={{
                        transitionDelay: showAllPosts
                          ? `${index * 50}ms`
                          : "0ms",
                      }}
                      onClick={() => {
                        if (post.permalink) {
                          window.open(
                            post.permalink,
                            "_blank",
                            "noopener,noreferrer"
                          );
                        }
                      }}
                    >
                      {/* Full Size Media */}
                      <div className="relative w-full rounded-lg overflow-hidden">
                        {post.media_type === "VIDEO" ? (
                          <div className="aspect-square bg-gray-200 flex items-center justify-center relative overflow-hidden">
                            {post.thumbnail_url ? (
                              <img
                                src={post.thumbnail_url}
                                alt="Post thumbnail"
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <i className="fas fa-video text-gray-400 text-4xl"></i>
                            )}
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="bg-black/50 rounded-full w-10 h-10 flex items-center justify-center">
                                <i className="fas fa-play text-white text-xl"></i>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="aspect-square bg-gray-200 flex items-center justify-center overflow-hidden">
                            {post.media_url ? (
                              <img
                                src={post.media_url}
                                alt="Post image"
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <i className="fas fa-image text-gray-400 text-4xl"></i>
                            )}
                          </div>
                        )}

                        {/* Media Type Badge */}
                        <div className="absolute top-3 left-3">
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full backdrop-blur-sm ${
                              post.media_product_type === "REELS"
                                ? "bg-purple-100/90 text-purple-700"
                                : "bg-blue-100/90 text-blue-700"
                            }`}
                          >
                            {post.media_product_type}
                          </span>
                        </div>

                        {/* Name and Metrics Overlay */}
                        <div className="absolute bottom-0 left-0 right-0 bg-black/40 backdrop-blur-sm p-3">
                          <div className="grid grid-cols-3 gap-2 text-xs mb-2">
                            <div className="text-center">
                              <div className="font-semibold text-white">
                                {reachValue.toLocaleString()}
                              </div>
                              <div className="text-gray-300">Reach</div>
                            </div>
                            <div className="text-center">
                              <div className="font-semibold text-white">
                                {likesValue.toLocaleString()}
                              </div>
                              <div className="text-gray-300">Likes</div>
                            </div>
                            <div className="text-center">
                              <div className="font-semibold text-white">
                                {commentsValue.toLocaleString()}
                              </div>
                              <div className="text-gray-300">Comments</div>
                            </div>
                          </div>
                          <div className="text-center">
                            <h4 className="text-white text-xs font-medium truncate">
                              {post.caption
                                ? post.caption
                                    .split(" ")
                                    .slice(0, 4)
                                    .join(" ") +
                                  (post.caption.split(" ").length > 4
                                    ? "..."
                                    : "")
                                : "Untitled Post"}
                            </h4>
                          </div>
                        </div>
                      </div>

                      {/* Date Only */}
                      <div className="mt-2 text-center">
                        <span className="text-xs text-gray-500">
                          {new Date(post.timestamp).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <i className="fas fa-images text-gray-300 text-4xl mb-4"></i>
          <h4 className="text-lg font-medium text-gray-500 mb-2">
            No Data Available
          </h4>
          <p className="text-sm text-gray-400">
            Top posts data is not available for the selected period.
          </p>
        </div>
      )}
    </div>
  );
};

export default TopPostsSection;
