import React from "react";
import { WidgetPreviewProps } from "../../types/widget-registry";
import { BasePreview } from "./BasePreview";

interface GenericChartPreviewProps extends WidgetPreviewProps {
  chartType: "line" | "bar" | "pie" | "area" | "table" | "metric";
  icon: string;
  primaryColor?: string;
}

export const GenericChartPreview: React.FC<GenericChartPreviewProps> = ({
  chartType,
  icon,
  primaryColor = "#3B82F6",
  ...props
}) => {
  const renderChart = () => {
    switch (chartType) {
      case "line":
        return renderLineChart();
      case "bar":
        return renderBarChart();
      case "pie":
        return renderPieChart();
      case "area":
        return renderAreaChart();
      case "table":
        return renderTableChart();
      case "metric":
        return renderMetricChart();
      default:
        return renderDefaultChart();
    }
  };

  const renderLineChart = () => {
    const points = [
      { x: 5, y: 35 },
      { x: 15, y: 25 },
      { x: 25, y: 45 },
      { x: 35, y: 30 },
      { x: 45, y: 50 },
      { x: 55, y: 40 },
      { x: 65, y: 20 },
    ];

    const pathData = points
      .map((point, index) => `${index === 0 ? "M" : "L"} ${point.x} ${point.y}`)
      .join(" ");

    return (
      <svg viewBox="0 0 70 60" className="w-full h-full max-w-16 max-h-12">
        <g stroke="#E5E7EB" strokeWidth="0.5" opacity="0.5">
          <line x1="5" y1="15" x2="65" y2="15" />
          <line x1="5" y1="30" x2="65" y2="30" />
          <line x1="5" y1="45" x2="65" y2="45" />
        </g>
        <path
          d={pathData}
          fill="none"
          stroke={primaryColor}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        {points.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="1"
            fill={primaryColor}
          />
        ))}
      </svg>
    );
  };

  const renderBarChart = () => {
    const bars = [20, 35, 25, 45, 30, 50, 40];

    return (
      <svg viewBox="0 0 70 60" className="w-full h-full max-w-16 max-h-12">
        <g stroke="#E5E7EB" strokeWidth="0.5" opacity="0.5">
          <line x1="5" y1="10" x2="65" y2="10" />
          <line x1="5" y1="25" x2="65" y2="25" />
          <line x1="5" y1="40" x2="65" y2="40" />
          <line x1="5" y1="55" x2="65" y2="55" />
        </g>
        {bars.map((height, index) => {
          const x = 8 + index * 8;
          const y = 55 - height;
          return (
            <rect
              key={index}
              x={x}
              y={y}
              width="6"
              height={height}
              fill={primaryColor}
              rx="1"
            />
          );
        })}
      </svg>
    );
  };

  const renderAreaChart = () => {
    const points = [
      { x: 5, y: 35 },
      { x: 15, y: 25 },
      { x: 25, y: 45 },
      { x: 35, y: 30 },
      { x: 45, y: 50 },
      { x: 55, y: 40 },
      { x: 65, y: 25 },
    ];

    const pathData = points
      .map((point, index) => `${index === 0 ? "M" : "L"} ${point.x} ${point.y}`)
      .join(" ");

    return (
      <svg viewBox="0 0 70 60" className="w-full h-full max-w-16 max-h-12">
        <defs>
          <linearGradient
            id={`gradient-${primaryColor.replace("#", "")}`}
            x1="0%"
            y1="0%"
            x2="0%"
            y2="100%"
          >
            <stop offset="0%" stopColor={primaryColor} stopOpacity="0.3" />
            <stop offset="100%" stopColor={primaryColor} stopOpacity="0.1" />
          </linearGradient>
        </defs>
        <g stroke="#E5E7EB" strokeWidth="0.5" opacity="0.5">
          <line x1="5" y1="15" x2="65" y2="15" />
          <line x1="5" y1="30" x2="65" y2="30" />
          <line x1="5" y1="45" x2="65" y2="45" />
        </g>
        <path
          d={`${pathData} L 65 55 L 5 55 Z`}
          fill={`url(#gradient-${primaryColor.replace("#", "")})`}
        />
        <path
          d={pathData}
          fill="none"
          stroke={primaryColor}
          strokeWidth="2"
          strokeLinecap="round"
        />
      </svg>
    );
  };

  const renderPieChart = () => {
    return (
      <div className="relative w-12 h-12">
        <svg viewBox="0 0 42 42" className="w-full h-full transform -rotate-90">
          <circle
            cx="21"
            cy="21"
            r="15.915"
            fill="transparent"
            stroke={primaryColor}
            strokeWidth="3"
            strokeDasharray="40 60"
            strokeDashoffset="0"
          />
          <circle
            cx="21"
            cy="21"
            r="15.915"
            fill="transparent"
            stroke="#10B981"
            strokeWidth="3"
            strokeDasharray="35 65"
            strokeDashoffset="-40"
          />
          <circle
            cx="21"
            cy="21"
            r="15.915"
            fill="transparent"
            stroke="#F59E0B"
            strokeWidth="3"
            strokeDasharray="25 75"
            strokeDashoffset="-75"
          />
        </svg>
      </div>
    );
  };

  const renderTableChart = () => {
    return (
      <div className="w-full h-full flex flex-col justify-center space-y-1 px-2">
        {[1, 2, 3, 4].map((_, index) => (
          <div key={index} className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            <div className="flex-1 h-1 bg-gray-200 rounded"></div>
            <div className="w-3 h-1 bg-gray-300 rounded"></div>
          </div>
        ))}
      </div>
    );
  };

  const renderMetricChart = () => {
    return (
      <div className="w-full h-full flex flex-col items-center justify-center">
        <div className="text-lg font-bold text-gray-700">1.2K</div>
        <div className="text-xs text-gray-500">Total</div>
        <div className="flex items-center mt-1">
          <i className="fas fa-arrow-up text-green-500 text-xs mr-1"></i>
          <span className="text-xs text-green-500">+12%</span>
        </div>
      </div>
    );
  };

  const renderDefaultChart = () => {
    return (
      <div className="text-center">
        <i className="fas fa-chart-bar text-gray-400 text-lg mb-1"></i>
        <p className="text-xs text-gray-500">Chart</p>
      </div>
    );
  };

  return (
    <BasePreview {...props} icon={icon}>
      <div className="w-full h-full flex items-center justify-center p-2">
        {renderChart()}
      </div>
    </BasePreview>
  );
};
