// Export base components
export { BasePreview } from "./BasePreview";
export { GenericChartPreview } from "./GenericChartPreview";

// Export specific preview components
export { GenderDemographicsPreview } from "./GenderDemographicsPreview";
export { ReachPreview } from "./ReachPreview";
export { LikesPreview } from "./LikesPreview";

// Create and export all other preview components using GenericChartPreview
import React from "react";
import { GenericChartPreview } from "./GenericChartPreview";
import { WidgetPreviewProps } from "../../types/widget-registry";

// Engagement category previews
export const AccountsEngagedPreview: React.FC<WidgetPreviewProps> = (props) => (
  <GenericChartPreview
    {...props}
    chartType="bar"
    icon="fa-users"
    primaryColor="#8B5CF6"
  />
);

export const CommentsPreview: React.FC<WidgetPreviewProps> = (props) => (
  <GenericChartPreview
    {...props}
    chartType="line"
    icon="fa-comments"
    primaryColor="#06B6D4"
  />
);

export const RepliesPreview: React.FC<WidgetPreviewProps> = (props) => (
  <GenericChartPreview
    {...props}
    chartType="area"
    icon="fa-reply"
    primaryColor="#10B981"
  />
);

export const SavedPreview: React.FC<WidgetPreviewProps> = (props) => (
  <GenericChartPreview
    {...props}
    chartType="line"
    icon="fa-bookmark"
    primaryColor="#F59E0B"
  />
);

export const SharesPreview: React.FC<WidgetPreviewProps> = (props) => (
  <GenericChartPreview
    {...props}
    chartType="bar"
    icon="fa-share"
    primaryColor="#EF4444"
  />
);

// Demographics category previews
export const EngagedAudienceDemographicsPreview: React.FC<
  WidgetPreviewProps
> = (props) => (
  <GenericChartPreview
    {...props}
    chartType="pie"
    icon="fa-chart-pie"
    primaryColor="#EC4899"
  />
);

export const FollowerDemographicsPreview: React.FC<WidgetPreviewProps> = (
  props
) => (
  <GenericChartPreview
    {...props}
    chartType="pie"
    icon="fa-user-friends"
    primaryColor="#8B5CF6"
  />
);

export const AccountReachCitiesPreview: React.FC<WidgetPreviewProps> = (
  props
) => (
  <GenericChartPreview
    {...props}
    chartType="bar"
    icon="fa-map-marker-alt"
    primaryColor="#06B6D4"
  />
);

// Performance category previews
export const ContentTypePerformancePreview: React.FC<WidgetPreviewProps> = (
  props
) => (
  <GenericChartPreview
    {...props}
    chartType="bar"
    icon="fa-chart-column"
    primaryColor="#10B981"
  />
);

export const TopPostsPreview: React.FC<WidgetPreviewProps> = (props) => (
  <GenericChartPreview
    {...props}
    chartType="table"
    icon="fa-trophy"
    primaryColor="#F59E0B"
  />
);

// Reach category previews
export const FollowerViewsPreview: React.FC<WidgetPreviewProps> = (props) => (
  <GenericChartPreview
    {...props}
    chartType="area"
    icon="fa-eye"
    primaryColor="#3B82F6"
  />
);

export const ViewsPreview: React.FC<WidgetPreviewProps> = (props) => (
  <GenericChartPreview
    {...props}
    chartType="area"
    icon="fa-chart-area"
    primaryColor="#06B6D4"
  />
);

// Followers category previews
export const FollowsAndUnfollowsPreview: React.FC<WidgetPreviewProps> = (
  props
) => (
  <GenericChartPreview
    {...props}
    chartType="line"
    icon="fa-user-plus"
    primaryColor="#10B981"
  />
);

export const FollowersOverviewPreview: React.FC<WidgetPreviewProps> = (
  props
) => (
  <GenericChartPreview
    {...props}
    chartType="metric"
    icon="fa-users"
    primaryColor="#8B5CF6"
  />
);

// Interactions category previews
export const ProfileLinksTapsPreview: React.FC<WidgetPreviewProps> = (
  props
) => (
  <GenericChartPreview
    {...props}
    chartType="bar"
    icon="fa-link"
    primaryColor="#EC4899"
  />
);

export const TotalInteractionsPreview: React.FC<WidgetPreviewProps> = (
  props
) => (
  <GenericChartPreview
    {...props}
    chartType="metric"
    icon="fa-hand-pointer"
    primaryColor="#F59E0B"
  />
);

// Fallback preview for unsupported widgets
export const FallbackPreview: React.FC<WidgetPreviewProps> = ({
  config,
  isSelected,
  onClick,
  className = "",
}) => (
  <div
    className={`
      flex flex-col items-center justify-center p-4 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer
      ${isSelected ? "border-blue-500 bg-blue-50" : "hover:border-gray-400"}
      ${className}
    `}
    onClick={onClick}
  >
    <i className="fas fa-puzzle-piece text-gray-400 text-xl mb-2"></i>
    <p className="text-gray-600 text-xs font-medium text-center">
      {config.title}
    </p>
    <p className="text-gray-500 text-xs text-center mt-1">
      Preview not available
    </p>
  </div>
);
