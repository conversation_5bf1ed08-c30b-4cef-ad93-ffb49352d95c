import React from "react";
import { WidgetPreviewProps } from "../../types/widget-registry";
import { BasePreview } from "./BasePreview";

export const LikesPreview: React.FC<WidgetPreviewProps> = (props) => {
  // Mock data for bar chart
  const bars = [
    { height: 20, color: "#EF4444" },
    { height: 35, color: "#F97316" },
    { height: 25, color: "#EAB308" },
    { height: 45, color: "#22C55E" },
    { height: 30, color: "#3B82F6" },
    { height: 50, color: "#8B5CF6" },
    { height: 40, color: "#EC4899" },
  ];

  return (
    <BasePreview {...props} icon="fa-heart">
      <div className="w-full h-full flex items-center justify-center p-2">
        <svg viewBox="0 0 70 60" className="w-full h-full max-w-16 max-h-12">
          {/* Grid lines */}
          <g stroke="#E5E7EB" strokeWidth="0.5" opacity="0.5">
            <line x1="5" y1="10" x2="65" y2="10" />
            <line x1="5" y1="25" x2="65" y2="25" />
            <line x1="5" y1="40" x2="65" y2="40" />
            <line x1="5" y1="55" x2="65" y2="55" />
          </g>

          {/* Bars */}
          {bars.map((bar, index) => {
            const x = 8 + index * 8;
            const y = 55 - bar.height;
            return (
              <rect
                key={index}
                x={x}
                y={y}
                width="6"
                height={bar.height}
                fill={bar.color}
                rx="1"
              />
            );
          })}
        </svg>
      </div>
    </BasePreview>
  );
};
