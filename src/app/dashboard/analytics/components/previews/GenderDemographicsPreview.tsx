import React from "react";
import { WidgetPreviewProps } from "../../types/widget-registry";
import { BasePreview } from "./BasePreview";

export const GenderDemographicsPreview: React.FC<WidgetPreviewProps> = (
  props
) => {
  const mockData = [
    { name: "Female", value: 45, color: "#EC4899" },
    { name: "Male", value: 35, color: "#3B82F6" },
    { name: "Other", value: 20, color: "#10B981" },
  ];

  return (
    <BasePreview {...props} icon="fa-venus-mars">
      <div className="w-full h-full flex items-center justify-center">
        {/* Simple pie chart representation */}
        <div className="relative w-16 h-16">
          <svg
            viewBox="0 0 42 42"
            className="w-full h-full transform -rotate-90"
          >
            <circle
              cx="21"
              cy="21"
              r="15.915"
              fill="transparent"
              stroke="#EC4899"
              strokeWidth="3"
              strokeDasharray="45 55"
              strokeDashoffset="0"
            />
            <circle
              cx="21"
              cy="21"
              r="15.915"
              fill="transparent"
              stroke="#3B82F6"
              strokeWidth="3"
              strokeDasharray="35 65"
              strokeDashoffset="-45"
            />
            <circle
              cx="21"
              cy="21"
              r="15.915"
              fill="transparent"
              stroke="#10B981"
              strokeWidth="3"
              strokeDasharray="20 80"
              strokeDashoffset="-80"
            />
          </svg>
        </div>

        {/* Legend */}
        <div className="ml-3 space-y-1">
          {mockData.map((item, index) => (
            <div key={index} className="flex items-center gap-1">
              <div
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-xs text-gray-600">{item.value}%</span>
            </div>
          ))}
        </div>
      </div>
    </BasePreview>
  );
};
