import React from "react";
import { WidgetPreviewProps } from "../../types/widget-registry";
import { BasePreview } from "./BasePreview";

export const ReachPreview: React.FC<WidgetPreviewProps> = (props) => {
  // Mock data points for area chart
  const points = [
    { x: 5, y: 35 },
    { x: 15, y: 25 },
    { x: 25, y: 45 },
    { x: 35, y: 30 },
    { x: 45, y: 50 },
    { x: 55, y: 40 },
    { x: 65, y: 60 },
  ];

  const pathData = points
    .map((point, index) => `${index === 0 ? "M" : "L"} ${point.x} ${point.y}`)
    .join(" ");

  return (
    <BasePreview {...props} icon="fa-chart-area">
      <div className="w-full h-full flex items-center justify-center p-2">
        <svg viewBox="0 0 70 70" className="w-full h-full max-w-16 max-h-16">
          {/* Grid lines */}
          <defs>
            <linearGradient
              id="reachGradient"
              x1="0%"
              y1="0%"
              x2="0%"
              y2="100%"
            >
              <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.3" />
              <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.1" />
            </linearGradient>
          </defs>

          {/* Grid */}
          <g stroke="#E5E7EB" strokeWidth="0.5" opacity="0.5">
            <line x1="5" y1="15" x2="65" y2="15" />
            <line x1="5" y1="35" x2="65" y2="35" />
            <line x1="5" y1="55" x2="65" y2="55" />
          </g>

          {/* Area fill */}
          <path d={`${pathData} L 65 65 L 5 65 Z`} fill="url(#reachGradient)" />

          {/* Line */}
          <path
            d={pathData}
            fill="none"
            stroke="#3B82F6"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />

          {/* Data points */}
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="1.5"
              fill="#3B82F6"
            />
          ))}
        </svg>
      </div>
    </BasePreview>
  );
};
