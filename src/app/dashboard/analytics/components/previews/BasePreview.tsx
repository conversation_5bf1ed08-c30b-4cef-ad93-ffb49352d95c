import React from "react";
import { WidgetPreviewProps } from "../../types/widget-registry";

interface BasePreviewProps extends WidgetPreviewProps {
  icon?: string;
  children?: React.ReactNode;
  showDescription?: boolean;
}

export const BasePreview: React.FC<BasePreviewProps> = ({
  config,
  isSelected = false,
  onClick,
  className = "",
  icon,
  children,
  showDescription = true,
}) => {
  return (
    <div
      className={`
        relative group cursor-pointer rounded-lg border-2 transition-all duration-200 overflow-hidden
        ${
          isSelected
            ? "border-blue-500 bg-blue-50 shadow-md"
            : "border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm"
        }
        ${className}
      `}
      onClick={onClick}
    >
      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute top-2 right-2 z-10">
          <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
            <i className="fas fa-check text-white text-xs"></i>
          </div>
        </div>
      )}

      {/* Preview content */}
      <div className="p-3 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center gap-2 mb-2">
          {icon && (
            <div className="w-6 h-6 flex items-center justify-center text-gray-600">
              <i className={`fas ${icon} text-sm`}></i>
            </div>
          )}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {config.title}
            </h4>
            <div className="flex items-center gap-1 mt-0.5">
              <span className="inline-block px-1.5 py-0.5 text-xs bg-gray-100 text-gray-600 rounded">
                {config.category}
              </span>
            </div>
          </div>
        </div>

        {/* Preview visualization */}
        <div className="flex-1 flex items-center justify-center bg-gray-50 rounded border border-gray-200 mb-2 min-h-[80px]">
          {children || (
            <div className="text-center">
              <i className="fas fa-chart-bar text-gray-400 text-lg mb-1"></i>
              <p className="text-xs text-gray-500">Preview</p>
            </div>
          )}
        </div>

        {/* Description */}
        {showDescription && (
          <p className="text-xs text-gray-600 line-clamp-2 leading-relaxed">
            {config.description}
          </p>
        )}

        {/* Size info */}
        <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-100">
          <span className="text-xs text-gray-500">
            Size: {config.defaultSize.width}×{config.defaultSize.height}
          </span>
          {config.tags && config.tags.length > 0 && (
            <div className="flex items-center gap-1">
              <i className="fas fa-tags text-gray-400 text-xs"></i>
              <span className="text-xs text-gray-500">
                {config.tags.slice(0, 2).join(", ")}
                {config.tags.length > 2 && "..."}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Hover effect */}
      <div
        className={`
        absolute inset-0 bg-linear-to-t from-black/5 to-transparent opacity-0 transition-opacity duration-200
        ${!isSelected ? "group-hover:opacity-100" : ""}
      `}
      />
    </div>
  );
};
