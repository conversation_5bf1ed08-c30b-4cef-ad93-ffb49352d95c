"use client";

import React, { useMemo, useCallback, useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import {
  WidgetLayout,
  GridConfig,
  GridBreakpoint,
  ResponsiveWidgetSize,
} from "@/types/drag-drop";
import { GridSystem } from "./GridSystem";
import { GridItem } from "./GridItem";
import { useResponsiveGrid } from "../../hooks/useResponsiveGrid";
import {
  DEFAULT_RESPONSIVE_COLUMNS,
  generateResponsiveGridClasses,
} from "../../utils/grid-utils";

interface ResponsiveGridContainerProps {
  widgets: WidgetLayout[];
  onLayoutChange?: (widgets: WidgetLayout[]) => void;
  gridConfig?: Partial<GridConfig>;
  className?: string;
  children?: React.ReactNode;
  enableMobileOptimization?: boolean;
  mobileStackingOrder?: "original" | "priority" | "size";
}

// Default responsive configuration
const DEFAULT_RESPONSIVE_CONFIG = {
  breakpoints: DEFAULT_RESPONSIVE_COLUMNS,
  gap: "1rem",
  minRowHeight: 200,
  mobileOptimization: {
    singleColumn: true,
    stackingOrder: "original" as const,
    minHeight: 150,
  },
};

export const ResponsiveGridContainer: React.FC<
  ResponsiveGridContainerProps
> = ({
  widgets,
  onLayoutChange,
  gridConfig,
  className,
  children,
  enableMobileOptimization = true,
  mobileStackingOrder = "original",
}) => {
  // Merge configuration
  const config = useMemo(
    () => ({
      ...DEFAULT_RESPONSIVE_CONFIG,
      ...gridConfig,
    }),
    [gridConfig]
  );

  // Use responsive grid hook
  const {
    currentBreakpoint,
    columns,
    windowSize,
    isMobile,
    isTablet,
    isDesktop,
    adaptLayoutForBreakpoint,
  } = useResponsiveGrid({
    gridConfig: config,
  });

  // State for adapted widgets
  const [adaptedWidgets, setAdaptedWidgets] = useState<WidgetLayout[]>(widgets);

  // Adapt widgets for current breakpoint
  useEffect(() => {
    const adapted = adaptLayoutForBreakpoint(widgets);
    setAdaptedWidgets(adapted);

    if (onLayoutChange) {
      onLayoutChange(adapted);
    }
  }, [widgets, adaptLayoutForBreakpoint, onLayoutChange]);

  // Generate mobile-optimized layout
  const mobileLayout = useMemo(() => {
    if (!isMobile || !enableMobileOptimization) {
      return adaptedWidgets;
    }

    let sortedWidgets = [...adaptedWidgets];

    // Apply mobile stacking order
    switch (mobileStackingOrder) {
      case "priority":
        // Sort by widget priority (if available) or area
        sortedWidgets.sort((a, b) => {
          const priorityA = (a.config as any).priority || 0;
          const priorityB = (b.config as any).priority || 0;

          if (priorityA !== priorityB) {
            return priorityB - priorityA; // Higher priority first
          }

          // Fallback to area
          const areaA = a.size.width * a.size.height;
          const areaB = b.size.width * b.size.height;
          return areaB - areaA;
        });
        break;

      case "size":
        // Sort by widget size (larger first)
        sortedWidgets.sort((a, b) => {
          const areaA = a.size.width * a.size.height;
          const areaB = b.size.width * b.size.height;
          return areaB - areaA;
        });
        break;

      case "original":
      default:
        // Keep original order (sort by position)
        sortedWidgets.sort((a, b) => {
          if (a.position.row !== b.position.row) {
            return a.position.row - b.position.row;
          }
          return a.position.col - b.position.col;
        });
        break;
    }

    // Stack widgets vertically in single column
    return sortedWidgets.map((widget, index) => ({
      ...widget,
      position: { row: index, col: 0 },
      size: { width: 1, height: widget.size.height },
    }));
  }, [adaptedWidgets, isMobile, enableMobileOptimization, mobileStackingOrder]);

  // Generate responsive CSS classes
  const containerClasses = useMemo(() => {
    const baseClasses = ["w-full", "h-full", "relative", "overflow-hidden"];

    // Add responsive grid classes
    const responsiveClasses = generateResponsiveGridClasses(config.breakpoints);

    return cn(
      ...baseClasses,
      responsiveClasses,

      // Mobile-specific classes
      isMobile &&
        enableMobileOptimization && [
          "grid-cols-1", // Force single column on mobile
          "gap-2", // Smaller gap on mobile
        ],

      // Tablet-specific classes
      isTablet && ["gap-3"],

      // Desktop-specific classes
      isDesktop && ["gap-4"],

      className
    );
  }, [
    config.breakpoints,
    isMobile,
    isTablet,
    isDesktop,
    enableMobileOptimization,
    className,
  ]);

  // Custom CSS properties for responsive behavior
  const containerStyles = useMemo(() => {
    return {
      "--current-breakpoint": currentBreakpoint,
      "--grid-columns": columns,
      "--window-width": windowSize.width,
      "--window-height": windowSize.height,
      "--is-mobile": isMobile ? "1" : "0",
      "--is-tablet": isTablet ? "1" : "0",
      "--is-desktop": isDesktop ? "1" : "0",
    } as React.CSSProperties;
  }, [currentBreakpoint, columns, windowSize, isMobile, isTablet, isDesktop]);

  // Handle layout changes
  const handleLayoutChange = useCallback(
    (newWidgets: WidgetLayout[]) => {
      if (onLayoutChange) {
        onLayoutChange(newWidgets);
      }
    },
    [onLayoutChange]
  );

  // Render widgets based on current layout
  const renderWidgets = () => {
    const widgetsToRender =
      isMobile && enableMobileOptimization ? mobileLayout : adaptedWidgets;

    return widgetsToRender.map((widget) => (
      <GridItem
        key={widget.id}
        id={widget.id}
        position={widget.position}
        size={widget.size}
        className={cn(
          // Base widget styles
          "bg-white",
          "rounded-lg",
          "shadow-sm",
          "border border-gray-200",
          "overflow-hidden",

          // Mobile-specific styles
          isMobile &&
            enableMobileOptimization && [
              "mb-4", // Add margin bottom for stacked layout
              "min-h-[150px]", // Minimum height on mobile
            ],

          // Tablet-specific styles
          isTablet && ["min-h-[180px]"],

          // Desktop-specific styles
          isDesktop && ["min-h-[200px]"]
        )}
      >
        {/* Widget content would be rendered here */}
        <div className="p-4 h-full">
          <h3 className="text-sm font-medium text-gray-900 mb-2">
            {widget.config.title}
          </h3>
          <div className="text-xs text-gray-500">
            {widget.config.description || `Widget ${widget.id}`}
          </div>

          {/* Debug info in development */}
          {process.env.NODE_ENV === "development" && (
            <div className="mt-2 text-xs text-gray-400 font-mono">
              <div>Breakpoint: {currentBreakpoint}</div>
              <div>
                Position: {widget.position.row},{widget.position.col}
              </div>
              <div>
                Size: {widget.size.width}x{widget.size.height}
              </div>
              <div>Columns: {columns}</div>
            </div>
          )}
        </div>
      </GridItem>
    ));
  };

  return (
    <div
      className={containerClasses}
      style={containerStyles}
      data-testid="responsive-grid-container"
      data-breakpoint={currentBreakpoint}
      data-columns={columns}
      data-mobile={isMobile}
      data-tablet={isTablet}
      data-desktop={isDesktop}
      role="grid"
      aria-label={`Dashboard grid - ${currentBreakpoint} breakpoint`}
    >
      <GridSystem
        columns={columns}
        gap={config.gap}
        onLayoutChange={handleLayoutChange}
        className="grid auto-rows-min"
      >
        {renderWidgets()}
        {children}
      </GridSystem>

      {/* Breakpoint indicator for development */}
      {process.env.NODE_ENV === "development" && (
        <div className="fixed top-4 right-4 z-50 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs font-mono">
          {currentBreakpoint} ({windowSize.width}x{windowSize.height})
        </div>
      )}
    </div>
  );
};

ResponsiveGridContainer.displayName = "ResponsiveGridContainer";

// Hook for responsive widget sizing
export const useResponsiveWidgetSize = (
  baseSize: ResponsiveWidgetSize,
  currentBreakpoint: GridBreakpoint
) => {
  return useMemo(() => {
    return baseSize[currentBreakpoint] || baseSize.md;
  }, [baseSize, currentBreakpoint]);
};

// Utility component for responsive widget wrapper
export const ResponsiveWidget: React.FC<{
  widget: WidgetLayout;
  children: React.ReactNode;
  className?: string;
}> = ({ widget, children, className }) => {
  const { currentBreakpoint, isMobile } = useResponsiveGrid();

  const responsiveClasses = useMemo(() => {
    return cn(
      // Base styles
      "w-full h-full",
      "transition-all duration-300 ease-in-out",

      // Mobile optimizations
      isMobile && [
        "text-sm", // Smaller text on mobile
        "p-3", // Smaller padding
      ],

      // Desktop styles
      !isMobile && ["text-base", "p-4"],

      className
    );
  }, [isMobile, className]);

  return (
    <div
      className={responsiveClasses}
      data-widget-id={widget.id}
      data-breakpoint={currentBreakpoint}
    >
      {children}
    </div>
  );
};
