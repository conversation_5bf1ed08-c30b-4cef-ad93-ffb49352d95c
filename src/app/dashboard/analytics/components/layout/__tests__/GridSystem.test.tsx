import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { GridSystem } from "../GridSystem";
import { GridItem } from "../GridItem";
import { ResponsiveGridContainer } from "../ResponsiveGridContainer";
import { WidgetLayout, GridConfig } from "@/types/drag-drop";

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

describe("GridSystem", () => {
  const defaultProps = {
    columns: 12,
    gap: "1rem",
  };

  it("renders with default props", () => {
    render(<GridSystem {...defaultProps} />);

    const gridElement = screen.getByTestId("grid-system");
    expect(gridElement).toBeInTheDocument();
    expect(gridElement).toHaveAttribute("role", "grid");
  });

  it("applies custom CSS properties", () => {
    render(<GridSystem {...defaultProps} />);

    const gridElement = screen.getByTestId("grid-system");
    expect(gridElement).toHaveStyle({
      "--grid-columns": "12",
      "--grid-gap": "1rem",
    });
  });

  it("handles layout changes", () => {
    const onLayoutChange = jest.fn();
    render(
      <GridSystem {...defaultProps} onLayoutChange={onLayoutChange}>
        <div>Test content</div>
      </GridSystem>
    );

    expect(screen.getByText("Test content")).toBeInTheDocument();
  });

  it("applies responsive classes", () => {
    render(<GridSystem {...defaultProps} />);

    const gridElement = screen.getByTestId("grid-system");
    expect(gridElement).toHaveClass("grid", "grid-cols-1", "sm:grid-cols-2");
  });
});

describe("GridItem", () => {
  const defaultProps = {
    id: "test-widget",
    position: { row: 0, col: 0 },
    size: { width: 2, height: 1 },
  };

  it("renders with correct grid positioning", () => {
    render(
      <GridItem {...defaultProps}>
        <div>Widget content</div>
      </GridItem>
    );

    const gridItem = screen.getByTestId("grid-item-test-widget");
    expect(gridItem).toBeInTheDocument();
    expect(gridItem).toHaveAttribute("data-widget-id", "test-widget");
    expect(gridItem).toHaveAttribute("data-position", "0,0");
    expect(gridItem).toHaveAttribute("data-size", "2x1");
  });

  it("applies dragging styles when isDragging is true", () => {
    render(
      <GridItem {...defaultProps} isDragging={true}>
        <div>Widget content</div>
      </GridItem>
    );

    const gridItem = screen.getByTestId("grid-item-test-widget");
    expect(gridItem).toHaveClass("opacity-50", "scale-95");
  });

  it("applies drop target styles when isDropTarget is true", () => {
    render(
      <GridItem {...defaultProps} isDropTarget={true}>
        <div>Widget content</div>
      </GridItem>
    );

    const gridItem = screen.getByTestId("grid-item-test-widget");
    expect(gridItem).toHaveClass("ring-2", "ring-blue-500");
  });

  it("calculates correct grid area", () => {
    render(
      <GridItem {...defaultProps}>
        <div>Widget content</div>
      </GridItem>
    );

    const gridItem = screen.getByTestId("grid-item-test-widget");
    expect(gridItem).toHaveStyle({
      gridArea: "1 / 1 / 2 / 3",
    });
  });
});

describe("ResponsiveGridContainer", () => {
  const mockWidgets: WidgetLayout[] = [
    {
      id: "widget-1",
      type: "chart",
      position: { row: 0, col: 0 },
      size: { width: 4, height: 2 },
      config: {
        type: "chart",
        title: "Test Chart",
        description: "A test chart widget",
        props: {},
        minSize: { width: 2, height: 1 },
        maxSize: { width: 12, height: 4 },
        defaultSize: { width: 4, height: 2 },
        category: "analytics",
      },
    },
    {
      id: "widget-2",
      type: "metric",
      position: { row: 0, col: 4 },
      size: { width: 2, height: 1 },
      config: {
        type: "metric",
        title: "Test Metric",
        description: "A test metric widget",
        props: {},
        minSize: { width: 1, height: 1 },
        maxSize: { width: 4, height: 2 },
        defaultSize: { width: 2, height: 1 },
        category: "analytics",
      },
    },
  ];

  beforeEach(() => {
    // Mock window dimensions
    Object.defineProperty(window, "innerWidth", {
      writable: true,
      configurable: true,
      value: 1280,
    });
    Object.defineProperty(window, "innerHeight", {
      writable: true,
      configurable: true,
      value: 720,
    });
  });

  it("renders widgets in responsive grid", () => {
    render(<ResponsiveGridContainer widgets={mockWidgets} />);

    const container = screen.getByTestId("responsive-grid-container");
    expect(container).toBeInTheDocument();
    expect(container).toHaveAttribute("data-breakpoint", "xl");
    expect(container).toHaveAttribute("data-columns", "12");
  });

  it("adapts layout for mobile breakpoint", () => {
    // Mock mobile viewport
    Object.defineProperty(window, "innerWidth", {
      writable: true,
      configurable: true,
      value: 375,
    });

    render(
      <ResponsiveGridContainer
        widgets={mockWidgets}
        enableMobileOptimization={true}
      />
    );

    const container = screen.getByTestId("responsive-grid-container");
    expect(container).toHaveAttribute("data-mobile", "true");
  });

  it("calls onLayoutChange when layout changes", () => {
    const onLayoutChange = jest.fn();

    render(
      <ResponsiveGridContainer
        widgets={mockWidgets}
        onLayoutChange={onLayoutChange}
      />
    );

    // Layout change should be called during initial render
    expect(onLayoutChange).toHaveBeenCalled();
  });

  it("applies mobile stacking order", () => {
    Object.defineProperty(window, "innerWidth", {
      writable: true,
      configurable: true,
      value: 375,
    });

    render(
      <ResponsiveGridContainer
        widgets={mockWidgets}
        enableMobileOptimization={true}
        mobileStackingOrder="size"
      />
    );

    const container = screen.getByTestId("responsive-grid-container");
    expect(container).toHaveClass("grid-cols-1");
  });

  it("shows development breakpoint indicator", () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = "development";

    render(<ResponsiveGridContainer widgets={mockWidgets} />);

    // Should show breakpoint indicator in development
    expect(screen.getByText(/xl \(1280x720\)/)).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });
});

describe("Grid Utilities", () => {
  it("validates grid positions correctly", async () => {
    const { isValidGridPosition } = await import("../GridItem");

    expect(
      isValidGridPosition({ row: 0, col: 0 }, { width: 2, height: 1 }, 12)
    ).toBe(true);
    expect(
      isValidGridPosition({ row: 0, col: 11 }, { width: 2, height: 1 }, 12)
    ).toBe(false);
    expect(
      isValidGridPosition({ row: -1, col: 0 }, { width: 2, height: 1 }, 12)
    ).toBe(false);
  });

  it("finds next available position", async () => {
    const { findNextAvailablePosition } = await import("../GridItem");

    const occupiedPositions = [
      { row: 0, col: 0 },
      { row: 0, col: 2 },
    ];

    const nextPosition = findNextAvailablePosition(
      { width: 2, height: 1 },
      occupiedPositions,
      12
    );

    expect(nextPosition).toEqual({ row: 0, col: 4 });
  });
});

describe("Responsive Behavior", () => {
  it("handles window resize events", async () => {
    render(<ResponsiveGridContainer widgets={mockWidgets} />);

    // Simulate window resize
    Object.defineProperty(window, "innerWidth", {
      writable: true,
      configurable: true,
      value: 768,
    });

    fireEvent(window, new Event("resize"));

    await waitFor(() => {
      const container = screen.getByTestId("responsive-grid-container");
      expect(container).toHaveAttribute("data-breakpoint", "md");
    });
  });

  it("debounces resize events", async () => {
    const onLayoutChange = jest.fn();

    render(
      <ResponsiveGridContainer
        widgets={mockWidgets}
        onLayoutChange={onLayoutChange}
      />
    );

    // Clear initial calls
    onLayoutChange.mockClear();

    // Trigger multiple resize events quickly
    for (let i = 0; i < 5; i++) {
      fireEvent(window, new Event("resize"));
    }

    // Should debounce and only call once after delay
    await waitFor(
      () => {
        expect(onLayoutChange).toHaveBeenCalledTimes(1);
      },
      { timeout: 200 }
    );
  });
});
