"use client";

import React, { useMemo } from "react";
import { cn } from "@/lib/utils";
import { DropZoneProps, GridPosition, WidgetSize } from "@/types/drag-drop";
import { generateWidgetSpanClasses } from "../../utils/grid-utils";

export const DropZone: React.FC<DropZoneProps> = ({
  id,
  position,
  size,
  isActive,
  isOver,
  canDrop,
  onDrop,
  className,
}) => {
  // Generate responsive grid classes
  const gridClasses = useMemo(() => {
    return generateWidgetSpanClasses(size);
  }, [size]);

  // Calculate grid area for precise positioning
  const gridArea = useMemo(() => {
    const { row, col } = position;
    const { width, height } = size;

    const startRow = row + 1;
    const endRow = startRow + height;
    const startCol = col + 1;
    const endCol = startCol + width;

    return `${startRow} / ${startCol} / ${endRow} / ${endCol}`;
  }, [position, size]);

  // Combine all classes
  const dropZoneClasses = useMemo(() => {
    return cn(
      // Base styles
      "relative",
      "border-2 border-dashed",
      "rounded-lg",
      "transition-all duration-200 ease-in-out",
      "pointer-events-auto",

      // Grid positioning
      gridClasses,

      // State-based styling
      isActive && ["border-blue-400", "bg-blue-50 bg-opacity-30", "shadow-sm"],

      isOver &&
        canDrop && [
          "border-green-400",
          "bg-green-50 bg-opacity-40",
          "shadow-md",
          "scale-105",
        ],

      isOver &&
        !canDrop && ["border-red-400", "bg-red-50 bg-opacity-40", "shadow-md"],

      !isActive && !isOver && ["border-gray-200", "bg-transparent"],

      // Hover effects when not dragging
      !isActive && "hover:border-gray-300",

      className
    );
  }, [gridClasses, isActive, isOver, canDrop, className]);

  // Custom styles for grid positioning
  const dropZoneStyles = useMemo(() => {
    return {
      gridArea,
      "--drop-zone-id": id,
      "--drop-zone-row": position.row,
      "--drop-zone-col": position.col,
      "--drop-zone-width": size.width,
      "--drop-zone-height": size.height,
    } as React.CSSProperties;
  }, [gridArea, id, position, size]);

  // Handle drop events
  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();

    if (canDrop) {
      const draggedId = event.dataTransfer.getData("text/plain");
      if (draggedId) {
        onDrop(draggedId, position);
      }
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDragEnter = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
  };

  return (
    <div
      id={`drop-zone-${id}`}
      className={dropZoneClasses}
      style={dropZoneStyles}
      data-testid={`drop-zone-${id}`}
      data-position={`${position.row},${position.col}`}
      data-size={`${size.width}x${size.height}`}
      data-can-drop={canDrop}
      role="button"
      tabIndex={0}
      aria-label={`Drop zone at row ${position.row + 1}, column ${
        position.col + 1
      }`}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
    >
      {/* Visual indicator */}
      <div className="absolute inset-0 flex items-center justify-center">
        {isActive && (
          <div className="flex flex-col items-center justify-center text-center p-4">
            {canDrop ? (
              <>
                <div className="w-8 h-8 mb-2 rounded-full bg-blue-100 flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                </div>
                <span className="text-xs text-blue-600 font-medium">
                  Drop here
                </span>
              </>
            ) : (
              <>
                <div className="w-8 h-8 mb-2 rounded-full bg-red-100 flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
                <span className="text-xs text-red-600 font-medium">
                  Cannot drop
                </span>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

DropZone.displayName = "DropZone";

// Collision highlight component for showing conflicts
export const CollisionHighlight: React.FC<{
  position: GridPosition;
  size: WidgetSize;
  severity: "warning" | "error";
  className?: string;
}> = ({ position, size, severity, className }) => {
  const gridClasses = useMemo(() => {
    return generateWidgetSpanClasses(size);
  }, [size]);

  const gridArea = useMemo(() => {
    const { row, col } = position;
    const { width, height } = size;

    const startRow = row + 1;
    const endRow = startRow + height;
    const startCol = col + 1;
    const endCol = startCol + width;

    return `${startRow} / ${startCol} / ${endRow} / ${endCol}`;
  }, [position, size]);

  const highlightClasses = useMemo(() => {
    return cn(
      // Base styles
      "absolute inset-0",
      "border-2",
      "rounded-lg",
      "pointer-events-none",
      "z-10",
      "animate-pulse",

      // Grid positioning
      gridClasses,

      // Severity-based styling
      severity === "warning" && [
        "border-yellow-400",
        "bg-yellow-100 bg-opacity-30",
      ],

      severity === "error" && ["border-red-400", "bg-red-100 bg-opacity-30"],

      className
    );
  }, [gridClasses, severity, className]);

  return (
    <div
      className={highlightClasses}
      style={{ gridArea }}
      data-testid="collision-highlight"
      data-severity={severity}
      role="alert"
      aria-label={`${
        severity === "error" ? "Collision" : "Warning"
      } at position ${position.row + 1}, ${position.col + 1}`}
    />
  );
};

CollisionHighlight.displayName = "CollisionHighlight";
