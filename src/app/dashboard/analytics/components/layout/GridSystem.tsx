"use client";

import React, { useMemo, useCallback } from "react";
import { cn } from "@/lib/utils";
import {
  GridSystemProps,
  WidgetLayout,
  GridConfig,
  GridBreakpoint,
} from "@/types/drag-drop";

// Default grid configuration
const DEFAULT_GRID_CONFIG: GridConfig = {
  columns: 12,
  gap: "1rem",
  breakpoints: {
    xs: 1,
    sm: 2,
    md: 4,
    lg: 8,
    xl: 12,
  },
  minRowHeight: 200,
};

// Breakpoint pixel values for media queries
const BREAKPOINT_VALUES = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
};

interface GridSystemState {
  currentBreakpoint: GridBreakpoint;
  columns: number;
  gap: string;
}

export const GridSystem: React.FC<GridSystemProps> = ({
  columns = DEFAULT_GRID_CONFIG.columns,
  gap = DEFAULT_GRID_CONFIG.gap,
  children,
  onLayoutChange,
  className,
}) => {
  // Calculate responsive columns based on screen size
  const gridConfig = useMemo(() => {
    return {
      ...DEFAULT_GRID_CONFIG,
      columns,
      gap,
    };
  }, [columns, gap]);

  // Generate CSS custom properties for the grid
  const gridStyles = useMemo(() => {
    return {
      "--grid-columns": gridConfig.columns,
      "--grid-gap": gridConfig.gap,
      "--grid-min-row-height": `${gridConfig.minRowHeight}px`,
    } as React.CSSProperties;
  }, [gridConfig]);

  // Generate responsive CSS classes
  const gridClasses = useMemo(() => {
    return cn(
      // Base grid styles
      "grid w-full h-full",
      "grid-cols-1", // Default to single column on mobile

      // Responsive grid columns
      "sm:grid-cols-2",
      "md:grid-cols-4",
      "lg:grid-cols-8",
      "xl:grid-cols-12",

      // Grid gap
      "gap-4",

      // Auto-fit rows with minimum height
      "auto-rows-min",

      // Additional styling
      "relative",
      "overflow-hidden",

      className
    );
  }, [className]);

  // Handle layout changes
  const handleLayoutChange = useCallback(
    (newLayout: WidgetLayout[]) => {
      if (onLayoutChange) {
        onLayoutChange(newLayout);
      }
    },
    [onLayoutChange]
  );

  return (
    <div
      className={gridClasses}
      style={gridStyles}
      data-testid="grid-system"
      role="grid"
      aria-label="Dashboard widget grid"
    >
      {children}
    </div>
  );
};

GridSystem.displayName = "GridSystem";
