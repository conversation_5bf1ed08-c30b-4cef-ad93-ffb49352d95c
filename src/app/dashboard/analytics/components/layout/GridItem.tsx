"use client";

import React, { useMemo, forwardRef } from "react";
import { cn } from "@/lib/utils";
import {
  GridItemProps,
  GridPosition,
  WidgetSize,
  GridBreakpoint,
} from "@/types/drag-drop";

// Utility function to calculate grid area CSS
const calculateGridArea = (
  position: GridPosition,
  size: WidgetSize,
  maxColumns: number = 12
): string => {
  const { row, col } = position;
  const { width, height } = size;

  // Ensure we don't exceed grid boundaries
  const startCol = Math.max(1, Math.min(col + 1, maxColumns));
  const endCol = Math.min(startCol + width, maxColumns + 1);
  const startRow = Math.max(1, row + 1);
  const endRow = startRow + height;

  return `${startRow} / ${startCol} / ${endRow} / ${endCol}`;
};

// Utility function to generate responsive grid classes
const generateResponsiveClasses = (
  size: WidgetSize,
  position: GridPosition
): string => {
  const { width, height } = size;

  // Base classes for different breakpoints
  const classes = [
    // Mobile (xs) - always full width
    "col-span-1",

    // Small screens (sm) - limit to 2 columns max
    `sm:col-span-${Math.min(width, 2)}`,

    // Medium screens (md) - limit to 4 columns max
    `md:col-span-${Math.min(width, 4)}`,

    // Large screens (lg) - limit to 8 columns max
    `lg:col-span-${Math.min(width, 8)}`,

    // Extra large screens (xl) - full 12 columns available
    `xl:col-span-${Math.min(width, 12)}`,

    // Row span (consistent across breakpoints)
    `row-span-${height}`,
  ];

  return classes.join(" ");
};

export const GridItem = forwardRef<HTMLDivElement, GridItemProps>(
  (
    {
      id,
      position,
      size,
      isDragging = false,
      isDropTarget = false,
      children,
      className,
      ...props
    },
    ref
  ) => {
    // Calculate grid positioning
    const gridArea = useMemo(() => {
      return calculateGridArea(position, size);
    }, [position, size]);

    // Generate responsive classes
    const responsiveClasses = useMemo(() => {
      return generateResponsiveClasses(size, position);
    }, [size, position]);

    // Combine all classes
    const itemClasses = useMemo(() => {
      return cn(
        // Base grid item styles
        "relative",
        "min-h-0", // Allow shrinking
        "min-w-0", // Allow shrinking

        // Responsive grid positioning
        responsiveClasses,

        // Drag and drop states
        isDragging && [
          "opacity-50",
          "scale-95",
          "z-50",
          "shadow-2xl",
          "rotate-2",
          "transition-all duration-200 ease-in-out",
        ],

        // Drop target highlighting
        isDropTarget && [
          "ring-2 ring-blue-500 ring-opacity-50",
          "bg-blue-50 bg-opacity-30",
          "border-2 border-dashed border-blue-300",
          "transition-all duration-150 ease-in-out",
        ],

        // Default transition for smooth animations
        !isDragging && "transition-all duration-300 ease-in-out",

        className
      );
    }, [responsiveClasses, isDragging, isDropTarget, className]);

    // Custom CSS properties for precise grid positioning
    const itemStyles = useMemo(() => {
      return {
        gridArea,
        "--widget-id": id,
        "--widget-row": position.row,
        "--widget-col": position.col,
        "--widget-width": size.width,
        "--widget-height": size.height,
      } as React.CSSProperties;
    }, [gridArea, id, position, size]);

    return (
      <div
        ref={ref}
        id={`grid-item-${id}`}
        className={itemClasses}
        style={itemStyles}
        data-testid={`grid-item-${id}`}
        data-widget-id={id}
        data-position={`${position.row},${position.col}`}
        data-size={`${size.width}x${size.height}`}
        role="gridcell"
        aria-label={`Widget at row ${position.row + 1}, column ${
          position.col + 1
        }`}
        {...props}
      >
        {children}
      </div>
    );
  }
);

GridItem.displayName = "GridItem";

// Utility function to validate grid position
export const isValidGridPosition = (
  position: GridPosition,
  size: WidgetSize,
  maxColumns: number = 12,
  maxRows: number = 100
): boolean => {
  const { row, col } = position;
  const { width, height } = size;

  // Check boundaries
  if (row < 0 || col < 0) return false;
  if (col + width > maxColumns) return false;
  if (row + height > maxRows) return false;

  return true;
};

// Utility function to find next available position
export const findNextAvailablePosition = (
  size: WidgetSize,
  occupiedPositions: GridPosition[],
  maxColumns: number = 12
): GridPosition => {
  const { width, height } = size;

  // Start from top-left and find first available position
  for (let row = 0; row < 100; row++) {
    for (let col = 0; col <= maxColumns - width; col++) {
      const position: GridPosition = { row, col };

      // Check if this position conflicts with any occupied positions
      const hasConflict = occupiedPositions.some((occupied) => {
        return (
          position.row < occupied.row + height &&
          position.row + height > occupied.row &&
          position.col < occupied.col + width &&
          position.col + width > occupied.col
        );
      });

      if (!hasConflict) {
        return position;
      }
    }
  }

  // Fallback to bottom of grid
  return { row: 100, col: 0 };
};
