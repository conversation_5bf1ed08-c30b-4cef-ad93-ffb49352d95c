"use client";

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  lazy,
  Suspense,
} from "react";
import { motion } from "framer-motion";
import { useUserStore } from "~/store/userStore";
import { useWebSocket } from "~/hooks/useWebSocket";
import AddSocialAccountModal from "../../../components/addsocialaccountmodal";
import Skeleton from "~/components/skeleton";
import { getPlatformColors } from "./utils/colors";

// Lazy load chart components for better performance
const AccountsEngagedChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.AccountsEngagedChart,
  }))
);
const CommentsChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.CommentsChart,
  }))
);
const EngagedAudienceDemographicsChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.EngagedAudienceDemographicsChart,
  }))
);
const FollowsAndUnfollowsChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.FollowsAndUnfollowsChart,
  }))
);
const FollowerDemographicsChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.FollowerDemographicsChart,
  }))
);
const FollowersOverviewChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.FollowersOverviewChart,
  }))
);
const FollowerViewsChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.FollowerViewsChart,
  }))
);
const GenderDemographicsChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.GenderDemographicsChart,
  }))
);
const LikesChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.LikesChart,
  }))
);
const ProfileLinksTapsChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.ProfileLinksTapsChart,
  }))
);
const ReachChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.ReachChart,
  }))
);
const RepliesChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.RepliesChart,
  }))
);
const SavedChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.SavedChart,
  }))
);
const SharesChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.SharesChart,
  }))
);
const TotalInteractionsChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.TotalInteractionsChart,
  }))
);

const ViewsChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.ViewsChart,
  }))
);

const ContentTypeChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.ContentTypeChart,
  }))
);

const AccountReachCitiesChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.AccountReachCitiesChart,
  }))
);

const FollowersCountriesChart = lazy(() =>
  import("./components/charts").then((module) => ({
    default: module.FollowersCountriesChart,
  }))
);

// Import shared components (keep these as regular imports since they're always needed)
import {
  MetricsOverview,
  LoadingState,
  ErrorState,
  SocialAccountSelectionSidebar,
} from "./components/shared";

// Import TopPostsSection and TopReelsSection components
import TopPostsSection from "./components/TopPostsSection";
import TopReelsSection from "./components/TopReelsSection";

// Import types
import { AnalyticsData, TimeRange } from "./types";

// Import utilities
import { mapApiToAnalytics } from "./utils/mapApiToAnalytics";

// Add interface for social accounts
interface SocialAccount {
  platform: string;
  social_id: string;
  social_name: string;
  username: string;
  profile_photo?: string;
}

// Modern Chart loading skeleton component with Framer Motion
const ChartSkeleton = ({
  iconColor = "bg-blue-100",
  iconClass = "fa-chart-line",
  iconTextColor = "text-blue-600",
  selectedSocial,
  height = "320px",
}: {
  iconColor?: string;
  iconClass?: string;
  iconTextColor?: string;
  selectedSocial?: any;
  height?: string;
}) => {
  // Framer Motion animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut",
      },
    },
  };

  const platformColors = getPlatformColors(selectedSocial?.platform);

  return (
    <motion.div
      className="bg-white rounded-xl p-3 md:p-4 shadow-sm transition-shadow hover:shadow-md"
      style={{ height }}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header with Instagram icon and title skeleton */}
      <motion.div
        className="flex items-center justify-between mb-3 md:mb-4"
        variants={itemVariants}
      >
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 md:w-12 md:h-12 flex items-center justify-center">
            <i
              className={`fab fa-instagram ${platformColors.iconColor} text-xl md:text-2xl`}
            />
          </div>
          <div className="min-w-0 flex-1">
            <Skeleton
              className="h-3 md:h-4 w-24 md:w-32 mb-1 md:mb-2"
              animation="wave"
            />
          </div>
        </div>
        <Skeleton
          className="h-6 md:h-8 w-16 md:w-20 rounded-lg"
          animation="wave"
        />
      </motion.div>

      {/* Chart area with sophisticated shimmer effect */}
      <motion.div variants={itemVariants}>
        <Skeleton
          className="w-full rounded-lg"
          style={{ height: `calc(${height} - 70px)` }}
          animation="wave"
        />
      </motion.div>
    </motion.div>
  );
};

const Analytics = () => {
  const {
    selectedWorkspace,
    selectedSocial,
    selectedWorkspaceDetails,
    setUser,
  } = useUserStore();

  // Get platform-specific colors
  const platformColors = getPlatformColors(selectedSocial?.platform);

  const { getAnalytics } = useWebSocket();

  // Storage helpers to align with dashboard behavior
  const getAnalyticsStorageKey = (
    ws?: string | null,
    socialId?: string | null
  ) => (ws && socialId ? `analytics_${ws}_${socialId}` : null);

  const initialCachedAnalytics = (() => {
    try {
      if (typeof window === "undefined") return null;
      const scopedKey = getAnalyticsStorageKey(
        selectedWorkspace ?? null,
        selectedSocial?.social_id ?? null
      );
      const scoped = scopedKey ? localStorage.getItem(scopedKey) : null;
      if (scoped) return JSON.parse(scoped)?.data ?? JSON.parse(scoped);
      const generic = localStorage.getItem("analytics_data");
      if (generic) return JSON.parse(generic);
      const keys = Object.keys(localStorage).filter((k) =>
        /^analytics_[^_]+_/.test(k)
      );
      if (keys.length === 1) {
        const lone = localStorage.getItem(keys[0]!);
        if (lone) return JSON.parse(lone)?.data ?? JSON.parse(lone);
      }
      return null;
    } catch {
      return null;
    }
  })();

  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(
    initialCachedAnalytics
  );
  // Disable flags to hard-toggle specific sections
  const DISABLE_ENGAGED_DEMOGRAPHICS = true;
  const DISABLE_ENGAGED_GENDER = true;
  const [isLoading, setIsLoading] = useState(initialCachedAnalytics ? false : true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<TimeRange>("30d"); // Default to 1 month as per API requirement
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([
    "followers_overview",
    "follower_views",
    "accounts_engaged",
    "comments",
    "follower_demographics",
    "follower_gender",
    "follows_and_unfollows",
    "likes",
    "profile_links_taps",
    "reach",
    "replies",
    "saves",
    "shares",
    "total_interactions",
    "views",
  ]);
  const [showAddSocialModal, setShowAddSocialModal] = useState(false);

  // Build and persist a compact summary to reduce memory usage across the app
  const computeAnalyticsSummary = useCallback((data: any) => {
    if (!data) return null;
    const sumValues = (obj: Record<string, number>) =>
      Object.values(obj).reduce((s, v) => s + (Number(v) || 0), 0);

    // Helper to aggregate by a breakdown key (e.g., 'age', 'gender', 'country', 'city')
    const aggregateBy = (arr: any[] | undefined, key: string) => {
      const map: Record<string, number> = {};
      if (!Array.isArray(arr)) return map;
      arr.forEach((dp: any) => {
        const k = dp?.breakdown?.[key] ?? dp?.[key];
        if (!k) return;
        const val = Number(dp?.total_value ?? dp?.value ?? 0) || 0;
        map[String(k)] = (map[String(k)] || 0) + val;
      });
      return map;
    };

    // Prefer follower_demographics as the primary demographics source
    const demo = Array.isArray(data?.follower_demographics)
      ? data.follower_demographics
      : [];

    const ageMap = aggregateBy(demo, "age");
    const genderMap = aggregateBy(demo, "gender");
    // Cities/countries can also be present in reach
    const reach = Array.isArray(data?.reach) ? data.reach : [];
    const countryMap = Object.keys(aggregateBy(demo, "country")).length
      ? aggregateBy(demo, "country")
      : aggregateBy(reach, "country");
    const cityMap = Object.keys(aggregateBy(reach, "city")).length
      ? aggregateBy(reach, "city")
      : aggregateBy(demo, "city");

    // Convert to percentages where appropriate
    const toPercent = (map: Record<string, number>) => {
      const total = sumValues(map) || 0;
      if (!total) return {} as Record<string, number>;
      const out: Record<string, number> = {};
      Object.entries(map).forEach(([k, v]) => {
        out[k] = Math.round(((Number(v) || 0) / total) * 100);
      });
      // Adjust rounding error on the largest entry
      const sumPct = Object.values(out).reduce((s, v) => s + v, 0);
      const entries = Object.entries(out) as [string, number][];
      if (sumPct !== 100 && entries.length) {
        const [largestKey] = entries.reduce((max, cur) =>
          cur[1] > max[1] ? cur : max
        );
        out[largestKey] = (out[largestKey] || 0) + (100 - sumPct);
      }
      return out;
    };

    const agePct = toPercent(ageMap);
    const genderPct = toPercent(genderMap);
    const countryPct = toPercent(countryMap);
    const cityPct = toPercent(cityMap);

    // Top lists (limit to keep storage small)
    const toTop = (map: Record<string, number>, limit = 25) =>
      Object.entries(map)
        .sort((a, b) => (Number(b[1]) || 0) - (Number(a[1]) || 0))
        .slice(0, limit)
        .map(([name, value]) => ({ name, value }));

    return {
      age: agePct,
      gender: genderPct,
      country: countryPct,
      city: cityPct,
      topCountries: toTop(countryMap, 50),
      topCities: toTop(cityMap, 50),
    };
  }, []);

  // Memoized fetch function to prevent unnecessary re-renders
  const fetchAnalyticsData = useCallback(async () => {
    if (!selectedWorkspace || !selectedSocial) {
      console.log("Missing workspace or social account");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Calculate time range - default to 30 days (1 month)
      const endTime = new Date();
      const startTime = new Date();
      const daysToSubtract = timeRange === "7d" ? 7 : 30; // Default 30 days (1 month)
      startTime.setDate(endTime.getDate() - daysToSubtract);

      // Format dates as YYYY-MM-DD (required format)
      const formatDate = (date: Date) => {
        return date.toISOString().split("T")[0];
      };

      const startDateStr = formatDate(startTime);
      const endDateStr = formatDate(endTime);

      console.log("Fetching analytics data for:", {
        workspace: selectedWorkspace,
        social_id: selectedSocial.social_id,
        timeRange,
        start_time: startDateStr,
        end_time: endDateStr,
      });

      // Make real API call with correct format
      const response = await getAnalytics({
        workspace_name: selectedWorkspace,
        social_id: selectedSocial.social_id,
        start_time: startDateStr,
        end_time: endDateStr,
      });

      console.log("Analytics API response:", response);

      // Extract top_posts early so we can preserve it in all branches
      const topPosts =
        (response as any)?.data?.top_posts ??
        (response as any)?.data?.analytics?.top_posts ??
        (response as any)?.analytics_data?.top_posts ??
        (response as any)?.analytics?.top_posts ??
        (response as any)?.top_posts;

      // Normalize the response into AnalyticsData shape
      const mapped = mapApiToAnalytics(response);
      if (mapped) {
        const merged: any = Array.isArray(topPosts)
          ? { ...mapped, top_posts: topPosts }
          : mapped;

        setAnalyticsData(merged);
        // Save to generic key for compatibility
        localStorage.setItem("analytics_data", JSON.stringify(merged));
        // Save to scoped key for workspace/social
        const scopedKey = getAnalyticsStorageKey(
          selectedWorkspace,
          selectedSocial?.social_id ?? null
        );
        if (scopedKey) {
          localStorage.setItem(
            scopedKey,
            JSON.stringify({ timestamp: Date.now(), data: merged })
          );
        }
        console.log(
          "Successfully mapped analytics data from API (with top_posts if available)"
        );
      } else {
        console.log("No mappable analytics data from API. Response:", response);
        // Set empty data instead of demo data
        setAnalyticsData(null);
        localStorage.removeItem("analytics_data");
        const scopedKey = getAnalyticsStorageKey(
          selectedWorkspace,
          selectedSocial?.social_id ?? null
        );
        if (scopedKey) localStorage.removeItem(scopedKey);
      }
    } catch (err) {
      console.error("Analytics fetch error:", err);

      // More specific error handling
      if (err instanceof Error) {
        setError(`Failed to fetch analytics data: ${err.message}`);
      } else {
        setError("Failed to fetch analytics data. Please try again.");
      }

      // Set empty data instead of demo data on error
      console.log("API error occurred, setting empty data");
      setAnalyticsData(null);
      localStorage.removeItem("analytics_data");
      const scopedKey = getAnalyticsStorageKey(
        selectedWorkspace,
        selectedSocial?.social_id ?? null
      );
      if (scopedKey) localStorage.removeItem(scopedKey);
    } finally {
      setIsLoading(false);
    }
  }, [selectedWorkspace, selectedSocial, timeRange, getAnalytics]);

  // Sidebar handlers
  const handleSocialSelect = useCallback(
    (account: SocialAccount) => {
      setUser({
        selectedSocial: {
          platform: account.platform,
          social_id: account.social_id,
          social_name: account.social_name,
          username: account.username,
        },
      });
    },
    [setUser]
  );

  const handleAddClick = useCallback(() => {
    setShowAddSocialModal(true);
  }, []);

  // Time range handler
  const handleTimeRangeChange = useCallback((newTimeRange: TimeRange) => {
    setTimeRange(newTimeRange);
  }, []);

  // Metrics selection handler
  const handleMetricsChange = useCallback((newMetrics: string[]) => {
    setSelectedMetrics(newMetrics);
  }, []);

  // Keep analyticsData in sync with selection using scoped cache for instant render
  useEffect(() => {
    try {
      const key = getAnalyticsStorageKey(
        selectedWorkspace ?? null,
        selectedSocial?.social_id ?? null
      );
      const saved = key ? localStorage.getItem(key) : null;
      if (saved) {
        const parsed = JSON.parse(saved);
        setAnalyticsData(parsed?.data ?? parsed);
        setIsLoading(false);
      } else {
        const generic = localStorage.getItem("analytics_data");
        if (generic) {
          setAnalyticsData(JSON.parse(generic));
          setIsLoading(false);
        }
      }
    } catch (e) {
      console.warn("Failed to load cached analytics for selection:", e);
    }
  }, [selectedWorkspace, selectedSocial?.social_id]);

  // Ensure loading is not blocking on initial cached data
  useEffect(() => {
    if (analyticsData && isLoading) setIsLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Persist a compact summary whenever analyticsData changes
  useEffect(() => {
    if (!analyticsData) return;
    try {
      const summary = computeAnalyticsSummary(analyticsData);
      if (summary) {
        localStorage.setItem("analytics_summary", JSON.stringify(summary));
      }
    } catch (e) {
      // Avoid crashing if storage quota exceeded or serialization fails
      console.warn("Failed to persist analytics_summary:", e);
    }
  }, [analyticsData, computeAnalyticsSummary]);

  // Fetch data when dependencies change
  useEffect(() => {
    if (selectedWorkspace && selectedSocial) {
      fetchAnalyticsData();
    } else {
      console.log("Waiting for workspace and social account selection");
    }
  }, [fetchAnalyticsData]);

  // Derived datasets for content type and cities from reach breakdown
  const contentTypeData = useMemo(() => {
    const map: Record<string, number> = {};
    const reachArr = Array.isArray(analyticsData?.reach)
      ? (analyticsData as any).reach
      : [];
    reachArr.forEach((dp: any) => {
      const t =
        dp?.breakdown?.media_product_type ||
        dp?.breakdown?.media_type ||
        dp?.media_product_type;
      const val = Number(dp?.total_value ?? 0) || 0;
      if (typeof t === "string") {
        map[t] = (map[t] || 0) + val;
      }
    });
    const label = (k: string) => {
      const s = k.toLowerCase();
      if (s.includes("reel")) return "Reels";
      if (s.includes("story")) return "Stories";
      if (s.includes("video")) return "Videos";
      if (s.includes("post") || s.includes("carousel")) return "Posts";
      return k;
    };
    return Object.entries(map).map(([k, v]) => ({ name: label(k), value: v }));
  }, [analyticsData]);

  const cityData = useMemo(() => {
    const map: Record<string, number> = {};

    // 1) Prefer reach-by-city if present (can include undated breakdowns)
    const reachArr = Array.isArray(analyticsData?.reach)
      ? (analyticsData as any).reach
      : [];
    reachArr.forEach((dp: any) => {
      const city = dp?.breakdown?.city || dp?.city;
      const val = Number(dp?.total_value ?? 0) || 0;
      if (city) map[String(city)] = (map[String(city)] || 0) + val;
    });

    // 2) If still empty, scan follower_demographics
    if (Object.keys(map).length === 0) {
      const demoArr = Array.isArray(analyticsData?.follower_demographics)
        ? (analyticsData as any).follower_demographics
        : [];
      demoArr.forEach((dp: any) => {
        const city = dp?.breakdown?.city || dp?.city;
        const val = Number(dp?.total_value ?? 0) || 0;
        if (city) map[String(city)] = (map[String(city)] || 0) + val;
      });
    }

    // 3) As a final fallback, scan all metrics for any city breakdowns (covers engaged audience, etc.)
    if (Object.keys(map).length === 0 && analyticsData) {
      Object.values(analyticsData as any).forEach((arr: any) => {
        if (!Array.isArray(arr)) return;
        arr.forEach((dp: any) => {
          const city = dp?.breakdown?.city || dp?.city;
          const val = Number(dp?.total_value ?? 0) || 0;
          if (city) map[String(city)] = (map[String(city)] || 0) + val;
        });
      });
    }

    return Object.entries(map).map(([name, value]) => ({ name, value }));
  }, [analyticsData]);

  // Build cumulative followers series from daily follower_count (gains) and overall followers_count snapshot
  const followersOverviewData = useMemo(() => {
    const gainsSeries: any[] = Array.isArray(analyticsData?.follower_count)
      ? [...(analyticsData as any).follower_count]
      : [];

    // Normalize and sort by date ascending
    const normalized = gainsSeries
      .map((dp: any) => {
        const dateStr: string | undefined = dp?.date
          ? String(dp.date).split("T")[0]
          : dp?.end_time
          ? String(dp.end_time).split("T")[0]
          : undefined;
        const value = Number(dp?.total_value ?? dp?.value ?? 0) || 0;
        const ts = dateStr ? new Date(dateStr).getTime() : 0;
        return { date: dateStr, total_value: value, _ts: ts };
      })
      .filter((d: any) => !!d.date)
      .sort((a: any, b: any) => a._ts - b._ts);

    if (!normalized.length) return gainsSeries; // fallback to raw if nothing to compute

    const latestTotal = (analyticsData as any)?.followers_count;
    if (typeof latestTotal !== "number" || !isFinite(latestTotal)) {
      // If no snapshot available, fallback to raw daily gains (chart will show gains)
      return normalized;
    }

    const sumGains = normalized.reduce(
      (s: number, d: any) => s + (Number(d.total_value) || 0),
      0
    );
    let running = Number(latestTotal) - sumGains;

    return normalized.map((d: any) => {
      running += Number(d.total_value) || 0;
      return {
        date: d.date,
        total_value: running < 0 ? 0 : running,
      };
    });
  }, [analyticsData]);

  // Only block initial render if loading and no cached data yet
  if (isLoading && !analyticsData) {
    return <LoadingState />;
  }

  // Only block entire page on error when there's no data to show
  if (error && !analyticsData) {
    return <ErrorState error={error} onRetry={fetchAnalyticsData} />;
  }

  return (
    <div className="flex min-h-screen bg-gray-50 md:pt-0 pt-[8vh]">
      <SocialAccountSelectionSidebar
        socialAccounts={
          (selectedWorkspaceDetails as any)?.social_accounts || []
        }
        selectedSocial={selectedSocial}
        onSocialSelect={handleSocialSelect}
        onAddClick={handleAddClick}
      />
      <div className="flex-1 p-4 md:p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="flex justify-between items-center mt-4 mb-6">
            <h2 className="text-2xl font-bold ">Analytics</h2>

            {/* Status indicator */}
            <div className="flex items-center gap-4 text-sm">
              {selectedWorkspace && selectedSocial ? (
                <div className="flex items-center gap-2 text-green-600">
                  <i className="fas fa-check-circle"></i>
                  <span>
                    {selectedSocial.username} ({selectedSocial.platform})
                  </span>
                </div>
              ) : (
                <div className="flex items-center gap-2 text-amber-600">
                  <i className="fas fa-exclamation-triangle"></i>
                  <span>Select a social account</span>
                </div>
              )}
            </div>
          </div>

          {/* Error display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-2 text-red-800">
                <i className="fas fa-exclamation-triangle"></i>
                <span className="font-medium">Error</span>
              </div>
              <p className="text-red-700 mt-1">{error}</p>
              <button
                onClick={() => {
                  setError(null);
                  fetchAnalyticsData();
                }}
                className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Try Again
              </button>
            </div>
          )}

          {/* Manual refresh button for testing */}
          <div className="flex justify-end  items-center mb-6">
            <div className="flex items-center gap-3">
              {/* API status indicator */}
              <div className="text-xs text-gray-500">
                {selectedWorkspace && selectedSocial ? (
                  <span className="text-green-600">✓ Ready for API call</span>
                ) : (
                  <span className="text-amber-600">
                    ⚠ Missing workspace/social
                  </span>
                )}
              </div>
              <button
                onClick={fetchAnalyticsData}
                disabled={isLoading || !selectedWorkspace || !selectedSocial}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <i className="fas fa-spinner fa-spin"></i>
                    Loading...
                  </>
                ) : (
                  <>
                    <i className="fas fa-refresh"></i>
                    Refresh
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Metrics Overview */}
          {analyticsData && (
            <MetricsOverview data={analyticsData} timeRange={timeRange} />
          )}

          {/* New Charts Section - Followers Overview and Views in Same Row */}
          {(selectedMetrics.includes("followers_overview") ||
            selectedMetrics.includes("follower_views")) && (
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
              {/* Followers Overview - 3/5 Width */}
              {selectedMetrics.includes("followers_overview") && (
                <div className="lg:col-span-3">
                  <Suspense
                    fallback={
                      <ChartSkeleton
                        iconColor="bg-blue-100"
                        iconClass="fa-users"
                        iconTextColor="text-blue-600"
                        selectedSocial={selectedSocial}
                      />
                    }
                  >
                    <FollowersOverviewChart
                      data={followersOverviewData || []}
                      timeRange={timeRange}
                      selectedSocial={selectedSocial}
                    />
                  </Suspense>
                </div>
              )}

              {/* Follower vs Non-Follower Views - 2/5 Width */}
              {selectedMetrics.includes("follower_views") && (
                <div className="lg:col-span-2">
                  <Suspense
                    fallback={
                      <ChartSkeleton
                        iconColor="bg-green-100"
                        iconClass="fa-eye"
                        iconTextColor="text-green-600"
                        selectedSocial={selectedSocial}
                      />
                    }
                  >
                    <FollowerViewsChart
                      data={Array.isArray(analyticsData?.views) ? (analyticsData as any).views : []}
                      timeRange={timeRange}
                      selectedSocial={selectedSocial}
                    />
                  </Suspense>
                </div>
              )}
            </div>
          )}

          {/* Demographics Charts */}
          <div className="space-y-6">
            {/* Follower Demographics + Follower Gender (same row) */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {selectedMetrics.includes("follower_demographics") && (
                <div>
                    <FollowerDemographicsChart
                    data={Array.isArray(analyticsData?.follower_demographics) ? (analyticsData as any).follower_demographics : []}
                    selectedSocial={selectedSocial}
                    height="400px"
                  />
                </div>
              )}
              {selectedMetrics.includes("follower_gender") && (
                <div>
                  <GenderDemographicsChart
                    data={Array.isArray(analyticsData?.follower_demographics) ? (analyticsData as any).follower_demographics : []}
                    title="Follower Gender"
                    description="Gender distribution of followers"
                    selectedSocial={selectedSocial}
                  />
                </div>
              )}
            </div>

            {/* Engaged Demographics + Engaged Gender (same row, guarded by flags) */}
            {(!DISABLE_ENGAGED_DEMOGRAPHICS || !DISABLE_ENGAGED_GENDER) && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {!DISABLE_ENGAGED_DEMOGRAPHICS &&
                  selectedMetrics.includes("engaged_audience_demographics") && (
                    <div>
                      <EngagedAudienceDemographicsChart
                        data={Array.isArray((analyticsData as any)?.engaged_audience_demographics) ? (analyticsData as any).engaged_audience_demographics : []}
                        selectedSocial={selectedSocial}
                      />
                    </div>
                  )}
                {!DISABLE_ENGAGED_GENDER &&
                  selectedMetrics.includes("engaged_audience_gender") && (
                    <div>
                      <GenderDemographicsChart
                        data={Array.isArray((analyticsData as any)?.engaged_audience_demographics) ? (analyticsData as any).engaged_audience_demographics : []}
                        title="Engaged Audience Gender"
                        description="Gender distribution of engaged users"
                        selectedSocial={selectedSocial}
                      />
                    </div>
                  )}
              </div>
            )}
          </div>

          {/* Account Reach Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Reach (moved here to be in the same row as Cities) */}
            {selectedMetrics.includes("reach") && (
              <div>
                <Suspense
                  fallback={
                    <ChartSkeleton
                      iconColor="bg-green-100"
                      iconClass="fa-eye"
                      iconTextColor="text-green-600"
                      selectedSocial={selectedSocial}
                      height="320px"
                    />
                  }
                >
                  <ReachChart
                    data={Array.isArray(analyticsData?.reach) ? (analyticsData as any).reach : []}
                    timeRange={timeRange}
                    selectedSocial={selectedSocial}
                    height="320px"
                    mobileHeight="300"
                    bottomPadding={40}
                  />
                </Suspense>
              </div>
            )}

            {/* Countries (side-by-side with Reach) */}
            {selectedMetrics.includes("follower_demographics") && (
              <div>
                <Suspense
                  fallback={
                    <ChartSkeleton
                      iconColor="bg-teal-100"
                      iconClass="fa-flag"
                      iconTextColor="text-teal-600"
                      selectedSocial={selectedSocial}
                      height="320px"
                    />
                  }
                >
                  <FollowersCountriesChart
                    data={Array.isArray(analyticsData?.follower_demographics) ? (analyticsData as any).follower_demographics : []}
                    selectedSocial={selectedSocial}
                    height="320px"
                    totalFollowers={(analyticsData as any)?.followers_count}
                  />
                </Suspense>
              </div>
            )}
          </div>

          {/* Account Reach Details: Cities and Posts */}
          {selectedMetrics.includes("reach") && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Account Reach / Cities */}
              <div>
                <Suspense
                  fallback={
                    <ChartSkeleton
                      iconColor="bg-teal-100"
                      iconClass="fa-map-marker-alt"
                      iconTextColor="text-teal-600"
                      selectedSocial={selectedSocial}
                      height="320px"
                    />
                  }
                >
                  <AccountReachCitiesChart
                    data={cityData}
                    timeRange={timeRange}
                    height="320px"
                    totalFollowers={(analyticsData as any)?.followers_count}
                  />
                </Suspense>
              </div>

              {/* Account Reach / Posts (Content Types) */}
              <div>
                <Suspense
                  fallback={
                    <ChartSkeleton
                      iconColor="bg-indigo-100"
                      iconClass="fa-th-large"
                      iconTextColor="text-indigo-600"
                      selectedSocial={selectedSocial}
                      height="320px"
                    />
                  }
                >
                  <ContentTypeChart
                    data={contentTypeData}
                    selectedSocial={selectedSocial}
                    height="320px"
                  />
                </Suspense>
              </div>
            </div>
          )}

          {/* Engagement, Comments, Likes (temporarily hidden) */}

          {/* Secondary Metrics Grid - moved down (temporarily hidden) */}

          {/* Top Posts Section */}
          <TopPostsSection
            analyticsData={analyticsData}
            selectedSocial={selectedSocial}
          />

          {/* Top Reels Section */}
          <TopReelsSection
            analyticsData={analyticsData}
            selectedSocial={selectedSocial}
          />
        </div>
      </div>

      {/* Add Social Account Modal */}
      {showAddSocialModal && (
        <AddSocialAccountModal
          onClose={() => setShowAddSocialModal(false)}
          onAddAccount={(platform) => {
            console.log(`Adding ${platform} account`);
            setShowAddSocialModal(false);
          }}
        />
      )}
    </div>
  );
};

export default Analytics;
