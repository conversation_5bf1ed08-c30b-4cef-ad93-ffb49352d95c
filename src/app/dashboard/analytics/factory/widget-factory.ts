import React from "react";
import {
  WidgetFactory,
  WidgetComponentProps,
  WidgetPreviewProps,
  WidgetConfig,
  WidgetCategory,
  CreateWidgetOptions,
} from "../types/widget-registry";
import {
  WIDGET_REGISTRY,
  getWidgetsByCategory,
  getEnabledWidgets,
  getWidgetConfig,
  isWidgetSupported,
} from "../registry/widget-registry";
import { WidgetSize } from "../../../../types/drag-drop";

// Fallback component for unsupported widgets
const UnsupportedWidget: React.FC<WidgetComponentProps> = ({
  data,
  selectedSocial,
  ...props
}) => (
  <div className="flex flex-col items-center justify-center h-full p-4 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg">
    <i className="fas fa-exclamation-triangle text-gray-400 text-2xl mb-2"></i>
    <p className="text-gray-600 text-sm font-medium mb-1">
      Widget Not Supported
    </p>
    <p className="text-gray-500 text-xs text-center">
      This widget type is not available or has been disabled.
    </p>
  </div>
);

// Fallback preview component for unsupported widgets
const UnsupportedPreview: React.FC<WidgetPreviewProps> = ({
  config,
  isSelected,
  onClick,
  className = "",
}) => (
  <div
    className={`
      flex flex-col items-center justify-center p-4 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer
      ${isSelected ? "border-blue-500 bg-blue-50" : "hover:border-gray-400"}
      ${className}
    `}
    onClick={onClick}
  >
    <i className="fas fa-puzzle-piece text-gray-400 text-xl mb-2"></i>
    <p className="text-gray-600 text-xs font-medium text-center">
      {config.title}
    </p>
    <p className="text-gray-500 text-xs text-center mt-1">
      Preview not available
    </p>
  </div>
);

// Widget factory implementation
class WidgetFactoryImpl implements WidgetFactory {
  /**
   * Create a widget component instance
   */
  createWidget(type: string, props: WidgetComponentProps = {}): JSX.Element {
    const registryEntry = WIDGET_REGISTRY[type];

    if (!registryEntry || !registryEntry.config.isEnabled) {
      console.warn(`Widget type "${type}" is not supported or disabled`);
      return React.createElement(UnsupportedWidget, props);
    }

    const { component: Component } = registryEntry;

    try {
      return React.createElement(Component, {
        ...props,
        key: `widget-${type}-${Date.now()}`, // Ensure unique keys
      });
    } catch (error) {
      console.error(`Error creating widget "${type}":`, error);
      return React.createElement(UnsupportedWidget, props);
    }
  }

  /**
   * Create a widget preview component instance
   */
  createPreview(
    type: string,
    props: WidgetPreviewProps = {} as WidgetPreviewProps
  ): JSX.Element {
    const registryEntry = WIDGET_REGISTRY[type];

    if (!registryEntry || !registryEntry.config.isEnabled) {
      const config = registryEntry?.config || {
        type,
        title: "Unknown Widget",
        description: "Widget not found",
        category: "performance" as WidgetCategory,
        minSize: { width: 2, height: 2 },
        maxSize: { width: 4, height: 4 },
        defaultSize: { width: 3, height: 3 },
      };

      return React.createElement(UnsupportedPreview, {
        ...props,
        config,
        key: `preview-${type}-${Date.now()}`,
      });
    }

    const { preview: PreviewComponent, config } = registryEntry;

    try {
      return React.createElement(PreviewComponent, {
        ...props,
        config,
        key: `preview-${type}-${Date.now()}`,
      });
    } catch (error) {
      console.error(`Error creating preview for widget "${type}":`, error);
      return React.createElement(UnsupportedPreview, {
        ...props,
        config,
        key: `preview-${type}-${Date.now()}`,
      });
    }
  }

  /**
   * Get widget configuration
   */
  getWidgetConfig(type: string): WidgetConfig | null {
    return getWidgetConfig(type);
  }

  /**
   * Get all available (enabled) widgets
   */
  getAvailableWidgets(): WidgetConfig[] {
    return getEnabledWidgets().map((entry) => entry.config);
  }

  /**
   * Get widgets by category
   */
  getWidgetsByCategory(category: WidgetCategory): WidgetConfig[] {
    return getWidgetsByCategory(category).map((entry) => entry.config);
  }

  /**
   * Check if widget type is supported
   */
  isWidgetSupported(type: string): boolean {
    return (
      isWidgetSupported(type) &&
      WIDGET_REGISTRY[type]?.config.isEnabled !== false
    );
  }

  /**
   * Create a complete widget with layout information
   */
  createWidgetWithLayout(
    type: string,
    options: CreateWidgetOptions = {},
    componentProps: WidgetComponentProps = {}
  ) {
    const config = this.getWidgetConfig(type);
    if (!config) {
      throw new Error(`Widget type "${type}" is not supported`);
    }

    const {
      id = `widget-${type}-${Date.now()}`,
      position = { row: 0, col: 0 },
      size = config.defaultSize,
      props: layoutProps = {},
    } = options;

    // Validate size constraints
    const validatedSize = this.validateWidgetSize(size, config);

    return {
      id,
      type,
      position,
      size: validatedSize,
      config,
      component: this.createWidget(type, { ...componentProps, ...layoutProps }),
      props: { ...componentProps, ...layoutProps },
    };
  }

  /**
   * Validate widget size against constraints
   */
  private validateWidgetSize(
    size: WidgetSize,
    config: WidgetConfig
  ): WidgetSize {
    return {
      width: Math.max(
        config.minSize.width,
        Math.min(config.maxSize.width, size.width)
      ),
      height: Math.max(
        config.minSize.height,
        Math.min(config.maxSize.height, size.height)
      ),
    };
  }

  /**
   * Get widget categories with counts
   */
  getCategoriesWithCounts(): Array<{
    category: WidgetCategory;
    count: number;
    widgets: WidgetConfig[];
  }> {
    const categories = new Map<WidgetCategory, WidgetConfig[]>();

    this.getAvailableWidgets().forEach((config) => {
      if (!categories.has(config.category)) {
        categories.set(config.category, []);
      }
      categories.get(config.category)!.push(config);
    });

    return Array.from(categories.entries()).map(([category, widgets]) => ({
      category,
      count: widgets.length,
      widgets,
    }));
  }

  /**
   * Search widgets by title, description, or tags
   */
  searchWidgets(query: string): WidgetConfig[] {
    const searchTerm = query.toLowerCase().trim();
    if (!searchTerm) return this.getAvailableWidgets();

    return this.getAvailableWidgets().filter((config) => {
      const titleMatch = config.title.toLowerCase().includes(searchTerm);
      const descriptionMatch = config.description
        .toLowerCase()
        .includes(searchTerm);
      const tagMatch =
        config.tags?.some((tag) => tag.toLowerCase().includes(searchTerm)) ||
        false;

      return titleMatch || descriptionMatch || tagMatch;
    });
  }

  /**
   * Get recommended widgets based on category popularity or usage
   */
  getRecommendedWidgets(limit: number = 6): WidgetConfig[] {
    // For now, return most commonly used widget types
    const priorityTypes = [
      "reach",
      "likes",
      "gender-demographics",
      "followers-overview",
      "total-interactions",
      "content-type-performance",
    ];

    const recommended: WidgetConfig[] = [];

    // Add priority widgets first
    for (const type of priorityTypes) {
      const config = this.getWidgetConfig(type);
      if (config && recommended.length < limit) {
        recommended.push(config);
      }
    }

    // Fill remaining slots with other available widgets
    if (recommended.length < limit) {
      const remaining = this.getAvailableWidgets()
        .filter((config) => !priorityTypes.includes(config.type))
        .slice(0, limit - recommended.length);

      recommended.push(...remaining);
    }

    return recommended;
  }
}

// Export singleton instance
export const widgetFactory = new WidgetFactoryImpl();

// Export the class for testing or custom instances
export { WidgetFactoryImpl };

// Export utility functions
export const createWidget = (type: string, props?: WidgetComponentProps) =>
  widgetFactory.createWidget(type, props);

export const createPreview = (type: string, props?: WidgetPreviewProps) =>
  widgetFactory.createPreview(type, props);

export const getAvailableWidgets = () => widgetFactory.getAvailableWidgets();

export const searchWidgets = (query: string) =>
  widgetFactory.searchWidgets(query);

export const getRecommendedWidgets = (limit?: number) =>
  widgetFactory.getRecommendedWidgets(limit);
