import {
  Widget<PERSON><PERSON><PERSON><PERSON>,
  Widget<PERSON>onfig,
  WidgetCategory,
} from "../types/widget-registry";

// Import all chart components
import { AccountsEngagedChart } from "../components/charts/AccountsEngagedChart";
import { CommentsChart } from "../components/charts/CommentsChart";
import { ContentTypePerformanceChart } from "../components/charts/ContentTypePerformanceChart";
import { EngagedAudienceDemographicsChart } from "../components/charts/EngagedAudienceDemographicsChart";
import { FollowsAndUnfollowsChart } from "../components/charts/FollowsAndUnfollowsChart";
import { FollowerDemographicsChart } from "../components/charts/FollowerDemographicsChart";
import { FollowersOverviewChart } from "../components/charts/FollowersOverviewChart";
import { FollowerViewsChart } from "../components/charts/FollowerViewsChart";
import { GenderDemographicsChart } from "../components/charts/GenderDemographicsChart";
import { Likes<PERSON><PERSON> } from "../components/charts/LikesChart";
import { ProfileLinksTapsChart } from "../components/charts/ProfileLinksTapsChart";
import { ReachChart } from "../components/charts/ReachChart";
import { RepliesChart } from "../components/charts/RepliesChart";
import { SavedChart } from "../components/charts/SavedChart";
import { SharesChart } from "../components/charts/SharesChart";
import { TotalInteractionsChart } from "../components/charts/TotalInteractionsChart";
import { TopPostsChart } from "../components/charts/TopPostsChart";
import { ViewsChart } from "../components/charts/ViewsChart";
import { AccountReachCitiesChart } from "../components/charts/AccountReachCitiesChart";

// Import preview components
import {
  AccountsEngagedPreview,
  CommentsPreview,
  ContentTypePerformancePreview,
  EngagedAudienceDemographicsPreview,
  FollowsAndUnfollowsPreview,
  FollowerDemographicsPreview,
  FollowersOverviewPreview,
  FollowerViewsPreview,
  GenderDemographicsPreview,
  LikesPreview,
  ProfileLinksTapsPreview,
  ReachPreview,
  RepliesPreview,
  SavedPreview,
  SharesPreview,
  TotalInteractionsPreview,
  TopPostsPreview,
  ViewsPreview,
  AccountReachCitiesPreview,
} from "../components/previews";

// Widget configurations
const WIDGET_CONFIGS: Record<string, WidgetConfig> = {
  "accounts-engaged": {
    type: "accounts-engaged",
    title: "Accounts Engaged",
    description: "Track unique accounts that engaged with your content",
    category: "engagement",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["engagement", "accounts", "interactions"],
    isEnabled: true,
  },

  comments: {
    type: "comments",
    title: "Comments",
    description: "Monitor comments on your posts over time",
    category: "engagement",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["comments", "engagement", "timeline"],
    isEnabled: true,
  },

  "content-type-performance": {
    type: "content-type-performance",
    title: "Content Type Performance",
    description: "Compare performance across different content types",
    category: "performance",
    minSize: { width: 3, height: 2 },
    maxSize: { width: 8, height: 5 },
    defaultSize: { width: 4, height: 3 },
    tags: ["content", "performance", "comparison"],
    isEnabled: true,
  },

  "engaged-audience-demographics": {
    type: "engaged-audience-demographics",
    title: "Engaged Audience Demographics",
    description: "Demographic breakdown of your engaged audience",
    category: "demographics",
    minSize: { width: 3, height: 3 },
    maxSize: { width: 6, height: 5 },
    defaultSize: { width: 4, height: 4 },
    tags: ["demographics", "audience", "engagement"],
    isEnabled: true,
  },

  "follows-and-unfollows": {
    type: "follows-and-unfollows",
    title: "Follows & Unfollows",
    description: "Track follower growth and churn over time",
    category: "followers",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["followers", "growth", "timeline"],
    isEnabled: true,
  },

  "follower-demographics": {
    type: "follower-demographics",
    title: "Follower Demographics",
    description: "Demographic breakdown of your followers",
    category: "demographics",
    minSize: { width: 3, height: 3 },
    maxSize: { width: 6, height: 5 },
    defaultSize: { width: 4, height: 4 },
    tags: ["demographics", "followers", "audience"],
    isEnabled: true,
  },

  "followers-overview": {
    type: "followers-overview",
    title: "Followers Overview",
    description: "Comprehensive overview of your follower metrics",
    category: "followers",
    minSize: { width: 3, height: 2 },
    maxSize: { width: 8, height: 4 },
    defaultSize: { width: 4, height: 3 },
    tags: ["followers", "overview", "metrics"],
    isEnabled: true,
  },

  "follower-views": {
    type: "follower-views",
    title: "Follower Views",
    description: "Track profile views from followers",
    category: "reach",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["views", "followers", "profile"],
    isEnabled: true,
  },

  "gender-demographics": {
    type: "gender-demographics",
    title: "Gender Distribution",
    description: "Gender breakdown of your audience",
    category: "demographics",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 4, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["gender", "demographics", "pie-chart"],
    isEnabled: true,
  },

  likes: {
    type: "likes",
    title: "Likes",
    description: "Track likes on your content over time",
    category: "engagement",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["likes", "engagement", "timeline"],
    isEnabled: true,
  },

  "profile-links-taps": {
    type: "profile-links-taps",
    title: "Profile Link Taps",
    description: "Monitor taps on links in your profile",
    category: "interactions",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["links", "profile", "taps"],
    isEnabled: true,
  },

  reach: {
    type: "reach",
    title: "Reach",
    description: "Track the reach of your content",
    category: "reach",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["reach", "audience", "timeline"],
    isEnabled: true,
  },

  replies: {
    type: "replies",
    title: "Replies",
    description: "Monitor replies to your content",
    category: "engagement",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["replies", "engagement", "timeline"],
    isEnabled: true,
  },

  saved: {
    type: "saved",
    title: "Saved",
    description: "Track how often your content is saved",
    category: "engagement",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["saved", "engagement", "timeline"],
    isEnabled: true,
  },

  shares: {
    type: "shares",
    title: "Shares",
    description: "Monitor shares of your content",
    category: "engagement",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["shares", "engagement", "timeline"],
    isEnabled: true,
  },

  "total-interactions": {
    type: "total-interactions",
    title: "Total Interactions",
    description: "Overview of all interactions with your content",
    category: "interactions",
    minSize: { width: 3, height: 2 },
    maxSize: { width: 8, height: 4 },
    defaultSize: { width: 4, height: 3 },
    tags: ["interactions", "total", "overview"],
    isEnabled: true,
  },

  "top-posts": {
    type: "top-posts",
    title: "Top Posts",
    description: "Your best performing posts",
    category: "performance",
    minSize: { width: 3, height: 3 },
    maxSize: { width: 8, height: 6 },
    defaultSize: { width: 4, height: 4 },
    tags: ["posts", "performance", "top"],
    isEnabled: true,
  },

  views: {
    type: "views",
    title: "Views",
    description: "Track views on your content",
    category: "reach",
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 3 },
    tags: ["views", "reach", "timeline"],
    isEnabled: true,
  },

  "account-reach-cities": {
    type: "account-reach-cities",
    title: "Account Reach by Cities",
    description: "Geographic distribution of your account reach",
    category: "demographics",
    minSize: { width: 3, height: 3 },
    maxSize: { width: 6, height: 5 },
    defaultSize: { width: 4, height: 4 },
    tags: ["geography", "cities", "reach"],
    isEnabled: true,
  },
};

// Create the widget registry
export const WIDGET_REGISTRY: WidgetRegistry = {
  "accounts-engaged": {
    component: AccountsEngagedChart,
    config: WIDGET_CONFIGS["accounts-engaged"],
    preview: AccountsEngagedPreview,
    category: "engagement",
  },

  comments: {
    component: CommentsChart,
    config: WIDGET_CONFIGS["comments"],
    preview: CommentsPreview,
    category: "engagement",
  },

  "content-type-performance": {
    component: ContentTypePerformanceChart,
    config: WIDGET_CONFIGS["content-type-performance"],
    preview: ContentTypePerformancePreview,
    category: "performance",
  },

  "engaged-audience-demographics": {
    component: EngagedAudienceDemographicsChart,
    config: WIDGET_CONFIGS["engaged-audience-demographics"],
    preview: EngagedAudienceDemographicsPreview,
    category: "demographics",
  },

  "follows-and-unfollows": {
    component: FollowsAndUnfollowsChart,
    config: WIDGET_CONFIGS["follows-and-unfollows"],
    preview: FollowsAndUnfollowsPreview,
    category: "followers",
  },

  "follower-demographics": {
    component: FollowerDemographicsChart,
    config: WIDGET_CONFIGS["follower-demographics"],
    preview: FollowerDemographicsPreview,
    category: "demographics",
  },

  "followers-overview": {
    component: FollowersOverviewChart,
    config: WIDGET_CONFIGS["followers-overview"],
    preview: FollowersOverviewPreview,
    category: "followers",
  },

  "follower-views": {
    component: FollowerViewsChart,
    config: WIDGET_CONFIGS["follower-views"],
    preview: FollowerViewsPreview,
    category: "reach",
  },

  "gender-demographics": {
    component: GenderDemographicsChart,
    config: WIDGET_CONFIGS["gender-demographics"],
    preview: GenderDemographicsPreview,
    category: "demographics",
  },

  likes: {
    component: LikesChart,
    config: WIDGET_CONFIGS["likes"],
    preview: LikesPreview,
    category: "engagement",
  },

  "profile-links-taps": {
    component: ProfileLinksTapsChart,
    config: WIDGET_CONFIGS["profile-links-taps"],
    preview: ProfileLinksTapsPreview,
    category: "interactions",
  },

  reach: {
    component: ReachChart,
    config: WIDGET_CONFIGS["reach"],
    preview: ReachPreview,
    category: "reach",
  },

  replies: {
    component: RepliesChart,
    config: WIDGET_CONFIGS["replies"],
    preview: RepliesPreview,
    category: "engagement",
  },

  saved: {
    component: SavedChart,
    config: WIDGET_CONFIGS["saved"],
    preview: SavedPreview,
    category: "engagement",
  },

  shares: {
    component: SharesChart,
    config: WIDGET_CONFIGS["shares"],
    preview: SharesPreview,
    category: "engagement",
  },

  "total-interactions": {
    component: TotalInteractionsChart,
    config: WIDGET_CONFIGS["total-interactions"],
    preview: TotalInteractionsPreview,
    category: "interactions",
  },

  "top-posts": {
    component: TopPostsChart,
    config: WIDGET_CONFIGS["top-posts"],
    preview: TopPostsPreview,
    category: "performance",
  },

  views: {
    component: ViewsChart,
    config: WIDGET_CONFIGS["views"],
    preview: ViewsPreview,
    category: "reach",
  },

  "account-reach-cities": {
    component: AccountReachCitiesChart,
    config: WIDGET_CONFIGS["account-reach-cities"],
    preview: AccountReachCitiesPreview,
    category: "demographics",
  },
};

// Export widget categories for filtering
export const WIDGET_CATEGORIES: WidgetCategory[] = [
  "engagement",
  "demographics",
  "performance",
  "reach",
  "content",
  "followers",
  "interactions",
];

// Export helper functions
export const getWidgetsByCategory = (category: WidgetCategory) => {
  return Object.values(WIDGET_REGISTRY).filter(
    (widget) => widget.category === category
  );
};

export const getEnabledWidgets = () => {
  return Object.values(WIDGET_REGISTRY).filter(
    (widget) => widget.config.isEnabled !== false
  );
};

export const getWidgetConfig = (type: string) => {
  return WIDGET_REGISTRY[type]?.config || null;
};

export const isWidgetSupported = (type: string) => {
  return type in WIDGET_REGISTRY;
};
