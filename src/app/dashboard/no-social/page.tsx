"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useUserStore } from "~/store/userStore";
import AddSocialAccountModal from "~/components/addsocialaccountmodal";
import { showSuccessToast } from "~/components/toasts";
import { usePathname } from "next/navigation";

interface WorkspaceDetails {
  social_accounts: any[];
  // add other workspace detail properties as needed
}

const NoSocialPage = () => {
  const { selectedWorkspace, workspacesDetails, selectedWorkspaceDetails } =
    useUserStore();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (typeof window === "undefined") return;
    // Prefer selectedWorkspaceDetails when available; fallback to list lookup
    const details =
      selectedWorkspaceDetails &&
      (selectedWorkspaceDetails as any).workspace_name
        ? (selectedWorkspaceDetails as any)
        : (workspacesDetails as any[]).find(
            (workspace: any) => workspace.workspace_name === selectedWorkspace
          );

    const hasSocial = Boolean(
      details?.social_accounts && details.social_accounts.length > 0
    );
    if (hasSocial) {
      router.replace("/dashboard");
    }
  }, [
    selectedWorkspace,
    selectedWorkspaceDetails,
    workspacesDetails,
    pathname,
    router,
  ]);

  const handleClose = () => {
    if (typeof window === "undefined") return;
    const details =
      selectedWorkspaceDetails &&
      (selectedWorkspaceDetails as any).workspace_name
        ? (selectedWorkspaceDetails as any)
        : (workspacesDetails as any[]).find(
            (workspace: any) => workspace.workspace_name === selectedWorkspace
          );
    if (details?.social_accounts?.length > 0) {
      router.replace("/dashboard");
    }
  };

  const handleAddAccount = (platform: string) => {
    // This will be handled by the AddSocialAccountModal internally
    // After successful addition, the useEffect above will redirect to dashboard
    showSuccessToast("Connecting to " + platform, "no-social-toast");
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      {/* <div className="text-center mb-8">
                <h1 className="text-2xl font-bold mb-4">Connect Your Social Media</h1>
                <p className="text-gray-600">
                    To start using our platform, please connect at least one social media account.
                </p>
            </div> */}
      <AddSocialAccountModal
        onClose={handleClose}
        onAddAccount={handleAddAccount}
      />
      {/* <ToastWrapper containerId="no-social-toast"/> */}
    </div>
  );
};

export default NoSocialPage;
