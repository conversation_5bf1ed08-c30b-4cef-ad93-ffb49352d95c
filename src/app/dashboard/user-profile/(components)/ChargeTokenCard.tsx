"use client";
import React from "react";
import { motion } from "framer-motion";
import { useUserStore } from "~/store/userStore";
import { FiVideo, FiImage, FiFileText, FiHash } from "react-icons/fi";
import { AiOutlinePlus, AiOutlineMinus } from "react-icons/ai";

const MotionDiv = motion.div;
const MotionButton = motion.button;

interface ChargeTokenCardProps {
  chargeValue: string;
  tokenRate: string;
  onDecrement: () => void;
  onIncrement: () => void;
  onPay: () => void;
  isPayDisabled: boolean;
}

export const ChargeTokenCard: React.FC<ChargeTokenCardProps> = ({
  chargeValue,
  tokenRate,
  onDecrement,
  onIncrement,
  onPay,
  isPayDisabled,
}) => {
  const { videoCredit = 0, imageCredit = 0, contentCredit = 0, hashtagCredit = 0 } =
    useUserStore();
  const rate = parseFloat(tokenRate || "0");
  const total = (Number(chargeValue || 0) * rate) || 0;

  return (
    <MotionDiv
      className="bg-white rounded-2xl shadow-sm p-4 py-6 flex flex-col items-center gap-4 h-full mb-4 md:mb-0 border border-gray-100 w-full overflow-hidden box-border"
      variants={{ hidden: { opacity: 0, y: 10 }, show: { opacity: 1, y: 0 } }}
      transition={{ type: "spring", stiffness: 250, damping: 24 }}
    >
      <div className="title w-full flex items-center justify-center flex-col">
        <h2 className="text-xl font-bold mb-1">Charge Tokens</h2>
        <p className="text-gray-600 text-center text-sm">
          Choose The Amount You Wanted To Charge
        </p>
      </div>
      {/* Enhanced Controls Section with Better Styling and Responsiveness */}
      <div className="w-full space-y-4">
        {/* Token Amount Control */}
        <div className="relative">
          <MotionDiv
            className="flex items-center justify-between gap-4 bg-gradient-to-r from-[#99b356] to-[#8ba34a] rounded-xl p-5 shadow-lg border border-[#7a8f42]/20"
            whileHover={{ scale: 1.01 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
          >
            <div className="flex flex-col">
              <span className="text-xs text-white/80 font-medium uppercase tracking-wide mb-1">
                Tokens
              </span>
              <span className="text-3xl md:text-4xl text-white font-bold tabular-nums">
                {chargeValue}
              </span>
            </div>
            <div className="flex flex-row gap-3 shrink-0">
              <MotionButton
                whileHover={{ scale: 1.05, backgroundColor: "#f8fafc" }}
                whileTap={{ scale: 0.95 }}
                className="bg-white/95 backdrop-blur-sm text-[#99b356] rounded-full w-12 h-12 flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-200 border border-white/50"
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  onDecrement();
                }}
                aria-label="Decrease by 10"
                title="Decrease by 10"
              >
                <AiOutlineMinus size={22} className="font-bold" />
              </MotionButton>
              <MotionButton
                whileHover={{ scale: 1.05, backgroundColor: "#f8fafc" }}
                whileTap={{ scale: 0.95 }}
                className="bg-white/95 backdrop-blur-sm text-[#99b356] rounded-full w-12 h-12 flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-200 border border-white/50"
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  onIncrement();
                }}
                aria-label="Increase by 10"
                title="Increase by 10"
              >
                <AiOutlinePlus size={22} className="font-bold" />
              </MotionButton>
            </div>
          </MotionDiv>
        </div>

        {/* Price and Pay Section */}
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Price Display */}
          <MotionDiv
            className="flex-1 flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200 rounded-xl px-6 py-4 shadow-sm"
            whileHover={{ scale: 1.01 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
          >
            <div className="text-center">
              <div className="text-xs text-slate-500 font-medium uppercase tracking-wide mb-1">
                Total Price
              </div>
              <div className="text-2xl md:text-3xl font-bold text-slate-800 tabular-nums">
                ${total.toFixed(2)}
              </div>
            </div>
          </MotionDiv>

          {/* Pay Button */}
          <div className="flex-shrink-0">
            <MotionButton
              whileHover={!isPayDisabled ? { scale: 1.02, y: -1 } : {}}
              whileTap={!isPayDisabled ? { scale: 0.98 } : {}}
              onClick={onPay}
              disabled={isPayDisabled}
              className={`
                relative overflow-hidden px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 w-full sm:w-auto min-w-[120px]
                ${isPayDisabled
                  ? "bg-slate-300 text-slate-500 cursor-not-allowed shadow-none"
                  : "bg-gradient-to-r from-[#99b356] to-[#8ba34a] text-white shadow-lg hover:shadow-xl hover:from-[#8ba34a] hover:to-[#7a8f42] active:shadow-md"
                }
                before:absolute before:inset-0 before:bg-white/20 before:translate-x-[-100%] before:transition-transform before:duration-700
                ${!isPayDisabled ? "hover:before:translate-x-[100%]" : ""}
              `}
            >
              <span className="relative z-10 flex items-center justify-center gap-2">
                Pay Now
                {!isPayDisabled && (
                  <svg
                    className="w-5 h-5 transition-transform group-hover:translate-x-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                )}
              </span>
            </MotionButton>
          </div>
        </div>
      </div>

      {/* Optional small rate hint under row for clarity on mobile */}
      {/* Rate Information */}
      <div className="flex items-center justify-center w-full">
        <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2">
          <span className="text-sm text-blue-700 font-medium">
            Rate: ${rate.toFixed(2)} per token
          </span>
        </div>
      </div>

      {/* Enhanced Credits Overview */}
      <div className="w-full mt-auto">
        <h3 className="text-sm font-semibold text-gray-700 mb-3 text-center">Current Credits</h3>
        <div className="grid grid-cols-2 gap-3 w-full">
          <MotionDiv
            className="flex items-center gap-3 bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200"
            whileHover={{ scale: 1.02, y: -1 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
          >
            <div className="bg-red-100 p-2 rounded-lg">
              <FiVideo className="text-red-600 text-xl" />
            </div>
            <div className="flex flex-col">
              <span className="text-xs text-gray-500 font-medium">Video</span>
              <span className="text-lg font-bold text-gray-800 tabular-nums">{videoCredit}</span>
            </div>
          </MotionDiv>

          <MotionDiv
            className="flex items-center gap-3 bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200"
            whileHover={{ scale: 1.02, y: -1 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
          >
            <div className="bg-blue-100 p-2 rounded-lg">
              <FiImage className="text-blue-600 text-xl" />
            </div>
            <div className="flex flex-col">
              <span className="text-xs text-gray-500 font-medium">Image</span>
              <span className="text-lg font-bold text-gray-800 tabular-nums">{imageCredit}</span>
            </div>
          </MotionDiv>

          <MotionDiv
            className="flex items-center gap-3 bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200"
            whileHover={{ scale: 1.02, y: -1 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
          >
            <div className="bg-green-100 p-2 rounded-lg">
              <FiFileText className="text-green-600 text-xl" />
            </div>
            <div className="flex flex-col">
              <span className="text-xs text-gray-500 font-medium">Content</span>
              <span className="text-lg font-bold text-gray-800 tabular-nums">{contentCredit}</span>
            </div>
          </MotionDiv>

          <MotionDiv
            className="flex items-center gap-3 bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200"
            whileHover={{ scale: 1.02, y: -1 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
          >
            <div className="bg-purple-100 p-2 rounded-lg">
              <FiHash className="text-purple-600 text-xl" />
            </div>
            <div className="flex flex-col">
              <span className="text-xs text-gray-500 font-medium">Hashtag</span>
              <span className="text-lg font-bold text-gray-800 tabular-nums">{hashtagCredit}</span>
            </div>
          </MotionDiv>
        </div>
      </div>
    </MotionDiv>
  );
};

export default ChargeTokenCard;
