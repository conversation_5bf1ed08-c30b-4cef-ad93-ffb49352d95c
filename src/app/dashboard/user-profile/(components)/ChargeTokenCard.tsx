"use client";
import React from "react";
import { motion } from "framer-motion";
import Dashboardbtn from "~/components/dashboardbtn";
import { useUserStore } from "~/store/userStore";
import { FiVideo, FiImage, FiFileText, FiHash } from "react-icons/fi";
import { AiOutlinePlus, AiOutlineMinus } from "react-icons/ai";

const MotionDiv = motion.div;
const MotionButton = motion.button;

interface ChargeTokenCardProps {
  chargeValue: string;
  tokenRate: string;
  onDecrement: () => void;
  onIncrement: () => void;
  onPay: () => void;
  isPayDisabled: boolean;
}

export const ChargeTokenCard: React.FC<ChargeTokenCardProps> = ({
  chargeValue,
  tokenRate,
  onDecrement,
  onIncrement,
  onPay,
  isPayDisabled,
}) => {
  const { videoCredit = 0, imageCredit = 0, contentCredit = 0, hashtagCredit = 0 } =
    useUserStore();
  const rate = parseFloat(tokenRate || "0");
  const total = (Number(chargeValue || 0) * rate) || 0;

  return (
    <MotionDiv
      className="bg-white rounded-2xl shadow-sm p-4 py-6 flex flex-col items-center gap-4 h-full mb-4 md:mb-0 border border-gray-100 w-full overflow-hidden box-border"
      variants={{ hidden: { opacity: 0, y: 10 }, show: { opacity: 1, y: 0 } }}
      transition={{ type: "spring", stiffness: 250, damping: 24 }}
    >
      <div className="title w-full flex items-center justify-center flex-col">
        <h2 className="text-xl font-bold mb-1">Charge Tokens</h2>
        <p className="text-gray-600 text-center text-sm">
          Choose The Amount You Wanted To Charge
        </p>
      </div>
      {/* Controls row with Price and Pay button in same row; equal heights */}
      <div className="w-full flex flex-col sm:flex-row items-stretch gap-3">
        <div className="flex p-2 items-center justify-between gap-4 bg-[#99b356] rounded-lg w-full sm:max-w-none flex-1 min-w-0 sm:h-12 pointer-events-none">
          <span className="text-2xl text-white font-extrabold tabular-nums truncate">
            {chargeValue}
          </span>
          <div className="flex flex-row gap-2 shrink-0 w-20 justify-between pointer-events-auto">
            <MotionButton
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-gray-800 rounded-full w-10 h-10 flex items-center justify-center shadow-sm hover:shadow transition-all"
              onClick={(e) => {
                e.stopPropagation();
                onDecrement();
              }}
              aria-label="Decrease by 10"
              title="Decrease by 10"
            >
              <AiOutlineMinus size={20} />
            </MotionButton>
            <MotionButton
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-gray-800 rounded-full w-10 h-10 flex items-center justify-center shadow-sm hover:shadow transition-all"
              onClick={(e) => {
                e.stopPropagation();
                onIncrement();
              }}
              aria-label="Increase by 10"
              title="Increase by 10"
            >
              <AiOutlinePlus size={20} />
            </MotionButton>
          </div>
        </div>

        <div className="w-full sm:w-auto flex items-stretch">
          <div className="flex items-center justify-center bg-gray-200 text-gray-900 rounded-lg px-4 w-full sm:w-[160px] sm:h-12 font-semibold tabular-nums">
            ${total.toFixed(2)}
          </div>
        </div>

        <div className="w-full sm:w-auto flex justify-center sm:justify-end items-stretch">
          <Dashboardbtn
            onClick={onPay}
            variant="default"
            className={`${
              isPayDisabled ? "opacity-50 cursor-not-allowed" : ""
            } w-full sm:w-auto whitespace-nowrap px-6 sm:h-12`}
            disabled={isPayDisabled}
          >
            Pay
          </Dashboardbtn>
        </div>
      </div>

      {/* Optional small rate hint under row for clarity on mobile */}
      <div className="flex items-center justify-center w-full text-xs text-gray-500">
        Rate: ${rate.toFixed(2)} per token
      </div>

      {/* Credits overview at bottom */}
      <div className="grid grid-cols-2 gap-3 w-full mt-auto">
        <div className="flex items-center gap-2 bg-slate-100 rounded-md p-4">
          <FiVideo className="text-slate-800 text-2xl" />
          <span className="text-sm text-slate-800">Video: <span className="font-semibold tabular-nums">{videoCredit}</span></span>
        </div>
        <div className="flex items-center gap-2 bg-slate-100 rounded-md p-4">
          <FiImage className="text-slate-800 text-2xl" />
          <span className="text-sm text-slate-800">Image: <span className="font-semibold tabular-nums">{imageCredit}</span></span>
        </div>
        <div className="flex items-center gap-2 bg-slate-100 rounded-md p-4">
          <FiFileText className="text-slate-800 text-2xl" />
          <span className="text-sm text-slate-800">Content: <span className="font-semibold tabular-nums">{contentCredit}</span></span>
        </div>
        <div className="flex items-center gap-2 bg-slate-100 rounded-md p-4">
          <FiHash className="text-slate-800 text-2xl" />
          <span className="text-sm text-slate-800">Hashtag: <span className="font-semibold tabular-nums">{hashtagCredit}</span></span>
        </div>
      </div>
    </MotionDiv>
  );
};

export default ChargeTokenCard;
