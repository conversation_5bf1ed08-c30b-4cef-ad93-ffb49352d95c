"use client";
import React from "react";
import { AnimatePresence, motion } from "framer-motion";

const MotionDiv = motion.div;
const MotionButton = motion.button;

export type PaymentStatus = "idle" | "loading" | "success" | "canceled" | "error";

interface PaymentStatusModalProps {
  status: PaymentStatus;
  onContinue: () => void;
}

export const PaymentStatusModal: React.FC<PaymentStatusModalProps> = ({ status, onContinue }) => {
  if (status === "idle") return null;

  const modalConfig = (
    {
      loading: {
        icon: (
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4" />
        ),
        title: "Processing Payment",
        message: "Please wait while we verify your payment...",
        bgColor: "bg-white",
        textColor: "text-gray-800",
      },
      success: {
        icon: (
          <div className="rounded-full bg-green-100 p-2 mb-4">
            <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        ),
        title: "Payment Successful",
        message: "Your payment has been processed successfully.",
        bgColor: "bg-white",
        textColor: "text-gray-800",
      },
      canceled: {
        icon: (
          <div className="rounded-full bg-yellow-100 p-2 mb-4">
            <svg className="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01" />
            </svg>
          </div>
        ),
        title: "Payment Canceled",
        message: "Your payment was canceled.",
        bgColor: "bg-white",
        textColor: "text-gray-800",
      },
      error: {
        icon: (
          <div className="rounded-full bg-red-100 p-2 mb-4">
            <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
        ),
        title: "Payment Error",
        message: "There was an error processing your payment. Please try again or contact support.",
        bgColor: "bg-white",
        textColor: "text-gray-800",
      },
    } as const
  )[status];

  return (
    <AnimatePresence>
      <MotionDiv
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <MotionDiv
          className={`${modalConfig.bgColor} rounded-lg shadow-xl max-w-md w-full mx-4 p-6`}
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 10, opacity: 0 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        >
          <div className="flex flex-col items-center text-center">
            {modalConfig.icon}
            <h3 className={`${modalConfig.textColor} text-lg font-semibold mb-2`}>{modalConfig.title}</h3>
            <p className={`${modalConfig.textColor} mb-4`}>{modalConfig.message}</p>
            {status !== 'loading' && (
              <MotionButton
                whileTap={{ scale: 0.98 }}
                onClick={onContinue}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-gray-800 transition-colors"
              >
                Continue
              </MotionButton>
            )}
          </div>
        </MotionDiv>
      </MotionDiv>
    </AnimatePresence>
  );
};

export default PaymentStatusModal;
