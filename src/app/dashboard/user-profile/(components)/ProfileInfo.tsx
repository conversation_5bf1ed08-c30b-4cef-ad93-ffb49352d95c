"use client";
import React from "react";
import { motion } from "framer-motion";

const MotionDiv = motion.div;
const MotionButton = motion.button;

interface ProfileInfoProps {
  userEmail: string;
  userFirstName: string;
  userLastName: string;
  userRegisterDate: string | number | Date;
  profilePhoto?: string | null;
  isEditingName: boolean;
  newFirstName: string;
  newLastName: string;
  onFirstNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onLastNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onToggleEditName: () => void;
  onSaveName: () => void;
  onChangePassword: () => void;
  onUploadPicture: () => void;
  onDeleteAccountClick: () => void;
}

export const ProfileInfo: React.FC<ProfileInfoProps> = ({
  userEmail,
  userFirstName,
  userLastName,
  userRegisterDate,
  profilePhoto,
  isEditingName,
  newFirstName,
  newLastName,
  onFirstNameChange,
  onLastNameChange,
  onToggleEditName,
  onSaveName,
  onChangePassword,
  onUploadPicture,
  onDeleteAccountClick,
}) => {
  return (
    <MotionDiv
      className="bg-white rounded-lg shadow-md p-3 flex flex-col items-start justify-center h-full"
      variants={{ hidden: { opacity: 0, y: 10 }, show: { opacity: 1, y: 0 } }}
      transition={{ type: "spring", stiffness: 250, damping: 24 }}
    >
      <div className="flex flex-col w-full md:flex-row items-center md:justify-between justify-center mb-2 gap-2">
        <div className="flex md:flex-row flex-col items-center  gap-2 ">
          <div className="w-12 h-12 relative">
            <img
              src={profilePhoto || "/icons/user1.svg"}
              alt="Profile"
              className="profile-photo rounded-full object-cover aspect-square"
            />
            <MotionDiv
              onClick={onUploadPicture}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="absolute bottom-0 right-0 bg-[#2d394b] rounded-full p-1 cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-[#2d394b]"
              role="button"
              tabIndex={0}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                />
              </svg>
            </MotionDiv>
          </div>
          <div className="text-center md:text-left">
            <h2 className="text-lg font-bold">{`${userFirstName} ${userLastName}`}</h2>
            <p className="text-gray-500 text-xs">
              Since {new Date(userRegisterDate).getFullYear()}
            </p>
          </div>
        </div>
        <div className="delete-account-btn">
          <MotionButton
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={onDeleteAccountClick}
            className="bg-[#8B0000] text-white px-4 py-2 rounded-md hover:bg-[#a00000] transition-transform transition-colors ease-out cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-[#8B0000]"
          >
            Delete Account
          </MotionButton>
        </div>
      </div>
      <div className="space-y-2 w-full">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            User Email Address
          </label>
          <div className="flex items-center">
            <p className="mt-1 block w-full rounded-md text-gray-500 p-2 break-words">
              {userEmail}
            </p>
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Full Name
          </label>
          <div className="flex flex-col md:flex-row items-end justify-between w-full">
            {isEditingName ? (
              <div className="flex flex-col md:flex-row w-full mr-2 py-4 px-2 md:p-0">
                <div className="w-full md:w-[45%] mb-4 md:mb-0 md:mr-2">
                  <label
                    htmlFor="firstName"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    First Name
                  </label>
                  <input
                    id="firstName"
                    type="text"
                    value={newFirstName}
                    onChange={onFirstNameChange}
                    className="mt-1 block w-full rounded-md border-gray-300 bg-gray-200 p-2"
                  />
                </div>
                <div className="w-full md:w-[45%]">
                  <label
                    htmlFor="lastName"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Last Name
                  </label>
                  <input
                    id="lastName"
                    type="text"
                    value={newLastName}
                    onChange={onLastNameChange}
                    className="mt-1 block w-full rounded-md border-gray-300 bg-gray-200 p-2"
                  />
                </div>
              </div>
            ) : (
              <p className="mt-1 block w-full md:w-1/2 rounded-md text-gray-500 border-gray-300 p-2 break-words">{`${userFirstName} ${userLastName}`}</p>
            )}
            <div className="mt-2 md:mt-0 w-full md:w-auto">
              {isEditingName ? (
                <MotionButton
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onSaveName}
                  className="w-full md:w-full px-4 py-2 text-gray-500 border-2 border-[#2C3E50] rounded-md cursor-pointer transition-transform ease-out focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-[#2C3E50]"
                >
                  Save
                </MotionButton>
              ) : (
                <MotionButton
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onToggleEditName}
                  className="md:w-full w-full px-4 py-2  border-2 text-gray-500 border-[#2C3E50] rounded-md cursor-pointer transition-transform ease-out focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-[#2C3E50]"
                >
                  Change Name
                </MotionButton>
              )}
            </div>
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Password
          </label>
          <div className="flex flex-col md:flex-row items-center justify-between w-full">
            <p className="mt-1 rounded-md border-gray-300 text-gray-500 p-2 w-full md:w-auto">
              ********
            </p>
            <MotionButton
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="mt-2 md:mt-0 w-full md:w-auto px-4 py-2 text-gray-500 border-2 border-[#2C3E50] rounded-md cursor-pointer transition-transform ease-out focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-[#2C3E50]"
              onClick={onChangePassword}
            >
              Change Password
            </MotionButton>
          </div>
        </div>
      </div>
    </MotionDiv>
  );
};

export default ProfileInfo;
