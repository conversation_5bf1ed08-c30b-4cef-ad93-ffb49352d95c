"use client";
import React from "react";
import { motion } from "framer-motion";
import Dashboardbtn from "~/components/dashboardbtn";

const MotionDiv = motion.div;
const MotionButton = motion.button;
const MotionUl = motion.ul;
const MotionLi = motion.li;

interface SubscriptionCardProps {
  userPlan: string;
  userPlanExpireDate: string | number | Date;
  onChangePlan: () => void;
}

export const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
  userPlan,
  userPlanExpireDate,
  onChangePlan,
}) => {
  const cardVariants = {
    hidden: { opacity: 0, y: 12, scale: 0.98 },
    show: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { type: "spring", stiffness: 260, damping: 26 },
    },
    hover: {
      y: -2,
      transition: { type: "spring", stiffness: 300, damping: 22 },
    },
  } as const;

  const listVariants = {
    hidden: {},
    show: { transition: { staggerChildren: 0.06, delayChildren: 0.05 } },
  } as const;

  const itemVariants = {
    hidden: { opacity: 0, y: 6 },
    show: {
      opacity: 1,
      y: 0,
      transition: { type: "spring", stiffness: 300, damping: 20 },
    },
  } as const;

  return (
    <MotionDiv
      className="relative overflow-hidden bg-white rounded-lg shadow-md p-3 flex flex-col justify-between items-center h-full"
      variants={cardVariants}
      initial="hidden"
      animate="show"
    >
      <h2 className="text-lg font-bold mb-2">Subscription</h2>
      <MotionDiv
        className="relative rounded-lg p-6 mb-3 w-full bg-linear-to-r from-[#C6D59F]/90 via-[#E4EDD2]/60 to-[#C6D59F]/15 ring-1 ring-[#B2BFA5]/40 bg-size-[220%_220%] bg-position-[0%_50%] mx-6"

      >
        <div className="subscription flex flex-col md:flex-row md:justify-between md:items-center items-stretch justify-center">
          <MotionUl
            className="space-y-2 text-sm text-[#6B7280] mb-4 md:mb-0"
            variants={listVariants}
            initial="hidden"
            animate="show"
          >
            <div className="mb-3">

            <span className="text-lg font-semibold text-gray-800 ">
              {userPlan}
            </span></div>
            <MotionLi className="flex items-center" variants={itemVariants}>
              <svg
                className="w-4 h-4 mr-2 text-gray-800"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
              2 Active Workspace
            </MotionLi>
            <MotionLi className="flex items-center" variants={itemVariants}>
              <svg
                className="w-4 h-4 mr-2 text-gray-800"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
              2 Team Members
            </MotionLi>
            <MotionLi className="flex items-center" variants={itemVariants}>
              <svg
                className="w-4 h-4 mr-2 text-gray-800"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
              Free Usage Of AI
            </MotionLi>
            <MotionLi className="flex items-center" variants={itemVariants}>
              <svg
                className="w-4 h-4 mr-2 text-gray-800"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
              Content Library
            </MotionLi>
          </MotionUl>
          <div className="data flex-col flex justify-center items-center mt-2">
            <span className="text-lg font-semibold text-gray-800">
              {userPlan}
            </span>
            <MotionDiv
              className="bg-[#B2BFA5] text-white text-md font-medium px-3 py-2 rounded-[6px] mb-2 mt-[0.4rem]"
              initial={{ boxShadow: "0 0 0px rgba(178,191,165,0.0)" }}
              animate={{
                boxShadow: [
                  "0 0 0px rgba(178,191,165,0.0)",
                  "0 0 18px rgba(178,191,165,0.75)",
                  "0 0 0px rgba(178,191,165,0.0)",
                ],
              }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            >
              Activated
            </MotionDiv>
            <p className="text-xs text-[#6B7280] mb-2 text-center">
              {Math.ceil(
                (new Date(userPlanExpireDate).getTime() -
                  new Date().getTime()) /
                  (1000 * 60 * 60 * 24)
              ) < 0
                ? "Plan expired"
                : `${Math.ceil(
                    (new Date(userPlanExpireDate).getTime() -
                      new Date().getTime()) /
                      (1000 * 60 * 60 * 24)
                  )} days left until ${new Date(
                    userPlanExpireDate
                  ).toLocaleDateString("en-CA")}`}
            </p>
          </div>
        </div>
      </MotionDiv>

      <div className="flex flex-col items-center w-full">
        <div className="relative w-full md:w-auto">
          {/* Glow layer */}
          <MotionDiv
            aria-hidden
            className="pointer-events-none absolute inset-0 rounded-full blur-md"
            style={{
              background:
                "radial-gradient(60% 60% at 50% 50%, rgba(198,213,159,0.7), rgba(226,237,206,0.0))",
            }}
            initial={{ opacity: 0.35, scale: 0.98 }}
            animate={{ opacity: [0.35, 0.75, 0.35], scale: [0.98, 1.02, 0.98] }}
            transition={{ duration: 3.2, repeat: Infinity, ease: "easeInOut" }}
          />
          <MotionButton
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.98 }}
            className="relative z-10 w-full md:w-auto"
            onClick={onChangePlan}
          >
            <Dashboardbtn
              variant="default"
              className="w-full md:w-auto bg-[#2d394b] hover:bg-[#303c50] shadow-[0_8px_24px_rgba(45,57,75,0.25)]"
            >
              Change Plan
            </Dashboardbtn>
          </MotionButton>
        </div>
      </div>
    </MotionDiv>
  );
};

export default SubscriptionCard;
