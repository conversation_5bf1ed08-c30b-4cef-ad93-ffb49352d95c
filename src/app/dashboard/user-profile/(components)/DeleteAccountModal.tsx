"use client";
import React from "react";
import { motion } from "framer-motion";

const MotionDiv = motion.div;
const MotionButton = motion.button;

interface DeleteAccountModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}

export const DeleteAccountModal: React.FC<DeleteAccountModalProps> = ({ open, onCancel, onConfirm }) => {
  if (!open) return null;
  return (
    <MotionDiv className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4" initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
      <MotionDiv className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6" initial={{ y: 20, opacity: 0 }} animate={{ y: 0, opacity: 1 }}>
        <div className="flex flex-col items-center text-center">
          <div className="rounded-full bg-red-100 p-2 mb-4">
            <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Account</h3>
          <p className="text-gray-500 mb-6">Are you sure you want to delete your account? This action cannot be undone.</p>
          <div className="flex flex-col sm:flex-row gap-3 w-full">
            <MotionButton whileTap={{ scale: 0.98 }} onClick={onCancel} className="w-full px-4 py-2 text-gray-500 border-2 border-gray-300 rounded-md hover:bg-gray-50">Cancel</MotionButton>
            <MotionButton whileTap={{ scale: 0.98 }} onClick={onConfirm} className="w-full px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700">Delete Account</MotionButton>
          </div>
        </div>
      </MotionDiv>
    </MotionDiv>
  );
};

export default DeleteAccountModal;
