"use client"
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Dashboardbtn from '~/components/dashboardbtn';
import {useUserStore} from '../../../store/userStore';
import { showSuccessToast, ToastWrapper, showErrorToast } from '~/components/toasts';
import { useWebSocket } from '~/hooks/useWebSocket'; // Adjust the import path as needed
import dynamic from 'next/dynamic';
import InstagramPreview from './components/InstagramPreview';
import TwitterPreview from './components/TwitterPreview';
import FacebookPreview from './components/FacebookPreview';
import AdvancedPromptInput from './components/AdvancedPromptInput';
import BasicPromptBar from './components/BasicPromptBar';
import TokenUsageIndicator from './components/TokenUsageIndicator';
import ImagePreviewGrid from './components/ImagePreviewGrid';
import VideoPreviewGrid from './components/VideoPreviewGrid';
import CaptionSection from './components/CaptionSection';
import AddImagesSection from './components/AddImagesSection';
import PostPreviewSection from './components/PostPreviewSection';
import BasicAdvancedToggle from './components/BasicAdvancedToggle';
import CaptionGeneratedDisplay from './components/CaptionGeneratedDisplay';
import CaptionActions from './components/CaptionActions';
import HashtagsDisplay from './components/HashtagsDisplay';
import HashtagsActions from './components/HashtagsActions';
import AIContentModal from './components/AIContentModal';

// Use custom VideoPreview for reliable thumbnails and playback
const ReactPlayer = null as any;
import VideoPreview from './components/VideoPreview';
import AddSocialAccountModal from "../../../components/addsocialaccountmodal";
import { useRouter, useSearchParams } from 'next/navigation';
import devtoolsDetect from 'devtools-detect';
import axios from 'axios';
import { TrendingUpDown } from 'lucide-react';
import { SocialAccountSelectionSidebar } from "~/app/dashboard/analytics/components/shared/SocialAccountSelectionSidebar";

// Add this type for media
interface MediaFile {
  url: string;
  type: 'image' | 'video';
}

interface MobileNewPostProps {
  captionRef: React.RefObject<string>;
  setCaption: (caption: string) => void;
  selectedImages: File[];
  handleImageUpload: () => void;
  handlePublish: () => void;
  handleSchedule: () => void;
  handleDraft: () => void;
  handleGenerateAICaption: () => void;
  handleTagPeople: () => void;
  handleAudience: () => void;
  handleLocation: () => void;
  handleGenerateAIImage: () => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  removeImage: (index: number) => void;
}


const NewPost: React.FC = () => {
  const {sendMessage, generateHashtags, generateCaption, generateVideo, createPost, createSchedule, generateImage} = useWebSocket();
  const captionRef = useRef<{ value: string }>({ value: '' });
  const selectedImagesRef = useRef<File[]>([]);
  const selectedImages64Ref = useRef<string[]>([]);
  const selectedSocialMediaRef = useRef('instagram');
  const {selectedSocial, generatedCaption, generatedHashtags, isAdvancedMode, keywords} = useUserStore();
  const isAIModalOpenDesktopRef = useRef(false);
  const [isSchedulePopupOpen, setIsSchedulePopupOpen] = useState(false);
  const [scheduledDate, setScheduledDate] = useState(new Date());
  const [ai_generated, setAiGenerated] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [hours2, setHours2] = useState(new Date().getHours());
  const [minutes2, setMinutes2] = useState(new Date().getMinutes());
  const scheduleButtonRef = useRef<HTMLButtonElement>(null);
  const schedulePopupRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const {selectedWorkspaceDetails, setUser, workspaceLogo, activePage, countDown, imageLoading, videoLoading} = useUserStore();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [videos, setVideos] = useState<string[]>(["", "", ""]);
  const [images, setImages] = useState<string[]>(["", "", ""]);
  const [showAddSocialModal, setShowAddSocialModal] = useState(false);
  const searchParams = useSearchParams();
  const postId = searchParams.get('postId');
  const social = searchParams.get('social');
  const aiGenerated = searchParams.get('ai_generated');
  const dateParam = searchParams.get('date');
  const editMode = Boolean(postId);
  const currentWorkspace = useUserStore(state => 
    state.workspaces?.find(w => w.workspace_name === state.selectedWorkspace)
  );

  const router = useRouter();

  // Add state for image loading
  const [loadingImages, setLoadingImages] = useState<boolean[]>([]);
  const [isLoadingEdit, setIsLoadingEdit] = useState(false);

  useEffect(() => {
    if (!selectedWorkspaceDetails) {
      router.push('/dashboard');
      showErrorToast("Please create or select a workspace first", "newpost-toast");
    }

    // Handle date parameter for scheduling
    if (dateParam) {
      const date = new Date(parseInt(dateParam));
      if (!isNaN(date.getTime())) {
        setScheduledDate(date);
        setHours2(date.getHours());
        setMinutes2(date.getMinutes());
      }
    }
  }, [selectedWorkspaceDetails, router, dateParam]);

  // Modify the title based on mode
  const pageTitle = editMode ? 'Edit Post' : 'New Post';

  // Image generation type fixed to 'generative'; dropdown removed

  // Add helper functions to update refs and force UI updates when needed
  const [, forceUpdate] = useState({});

  // Removed image type dropdown state/handlers

  // Add useEffect to load post data when in edit mode
  useEffect(() => {
    const loadPostData = async () => {
      if (postId) {
        setIsLoadingEdit(true);
        try {
          const postCaption = searchParams.get('caption') || '';
          const postMediaStr = searchParams.get('media');
          
          captionRef.current.value = postCaption;
          
          if (postMediaStr) {
            try {
              const mediaData = JSON.parse(postMediaStr);
              const mediaUrls = Array.isArray(mediaData) ? mediaData : [mediaData];
              
              // Initialize loading states for each media item
              setLoadingImages(new Array(mediaUrls.length).fill(true));

              const mediaFiles = await Promise.all(
                mediaUrls.map(async (mediaItem: any, index) => {
                  const url = typeof mediaItem === 'object' ? mediaItem.url : mediaItem;
                  const response = await axios.get(url, { responseType: 'blob' });
                  const blob = response.data;
                  const filename = url.split('/').pop() || 'file';
                  const type = url.toLowerCase().endsWith('.mp4') ? 'video/mp4' : 'image/jpeg';
                  
                  // Update loading state for this specific media item
                  setLoadingImages(prev => {
                    const newState = [...prev];
                    newState[index] = false;
                    return newState;
                  });

                  return new File([blob], filename, { type });
                })
              );

              selectedImagesRef.current = mediaFiles;
              
              const base64Results = await Promise.all(
                mediaFiles.map(file => 
                  new Promise<string>((resolve) => {
                    const reader = new FileReader();
                    reader.onloadend = () => {
                      const base64 = reader.result?.toString().split(',')[1] || '';
                      resolve(base64);
                    };
                    reader.readAsDataURL(file);
                  })
                )
              );

              selectedImages64Ref.current = base64Results;
            } catch (e) {
              console.error('Error processing media:', e);
            }
          }
        } catch (error) {
          const result = error.response.data;
          console.error('Error loading post data:', result);
          showErrorToast(result.message || "An unexpected error occurred. Please try again.", "verify-toast");
        } finally {
          setIsLoadingEdit(false);
          setLoadingImages([]);
        }
      }
    };

    loadPostData();
  }, [postId, searchParams]);

  const handleImageUpload = (files: FileList) => {
    const newImages = Array.from(files).filter(file => file.type.startsWith('image/') || file.type.startsWith('video/'));
    selectedImagesRef.current = [...selectedImagesRef.current, ...newImages];

    // Convert new images to base64
    const convertToBase64 = async (files: File[]) => {
      const base64Promises = files.map((file) => {
        return new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            const result = reader.result as string;
            const base64 = result?.split(',')[1] || ''; // Get only the base64 part
            resolve(base64);
          };
          reader.readAsDataURL(file);
        });
      });

      const base64Results = await Promise.all(base64Promises);
      selectedImages64Ref.current = base64Results
      forceUpdate({});

    };

    convertToBase64(newImages);
    forceUpdate({});

  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
    forceUpdate({});

  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    forceUpdate({});

  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    forceUpdate({});

  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleImageUpload(e.dataTransfer.files);
      forceUpdate({});

    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleImageUpload(e.target.files);
      forceUpdate({});

    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
    forceUpdate({});

  };

  const removeImage = (index: number) => {
    selectedImagesRef.current = selectedImagesRef.current.filter((_, i) => i !== index);
    selectedImages64Ref.current = selectedImages64Ref.current.filter((_, i) => i !== index);
    forceUpdate({});

  };

  const handlePublish = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      const mediaPromises = selectedImagesRef.current.map(async (file) => {
        return new Promise((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64String = reader.result?.toString().split(',')[1] || '';
            resolve(base64String);
          };
          reader.readAsDataURL(file);
        });
      });

      const mediaFiles = await Promise.all(mediaPromises);  

      await createPost({
        workspace_name: selectedWorkspaceDetails.workspace_name,
        type: "publish",
        images: editMode ?  mediaFiles : mediaFiles,
        socials: [social ? social : selectedSocial.social_id],
        caption: captionRef.current.value,
        ...(postId && { post_id: postId }),
        ai_generated: (aiGenerated === 'true' ? true : false) || ai_generated,
      }).then((response: any) => {
        if(response.success){
          showSuccessToast(editMode ? "Post updated successfully" : "Your post has been created successfully" ,  "newpost-toast");
          setTimeout(() => {
            window.location.href = '/dashboard/planner';
          }, 1000);
        } else {
          showErrorToast(response.message,  "newpost-toast");
        }
      });
    } catch (error) {
      showErrorToast("An error occurred while publishing", "newpost-toast");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDraft = async () => {
    const mediaPromises = selectedImagesRef.current.map(async (file) => {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64String = reader.result?.toString().split(',')[1] || '';
          resolve(
            base64String
          );
        };
        reader.readAsDataURL(file);
      });
    });

    const mediaFiles = await Promise.all(mediaPromises);


    if (editMode) {
      await createPost({
        workspace_name: selectedWorkspaceDetails.workspace_name,
        type: "draft", 
        images: mediaFiles,
        socials: [social ? social : selectedSocial.social_id],
        caption: captionRef.current.value,
        ...(postId && { post_id: postId }),
        ai_generated: (aiGenerated === 'true' ? true : false) || ai_generated,
      }).then((response) => {
        if(response.success){
          showSuccessToast(editMode ? "Draft updated successfully" : "Your post has been saved as draft" ,  "newpost-toast");
          setTimeout(() => {
            window.location.href = '/dashboard/planner';
          }, 1000);
        } else {
            showErrorToast(response.message,  "newpost-toast");
        }
      });
    } else {
      await createPost({
        workspace_name: selectedWorkspaceDetails.workspace_name,
        type: "draft",
        images: mediaFiles,
        socials: [social ? social : selectedSocial.social_id],
        caption: captionRef.current.value,
        ...(postId && { post_id: postId }),
        ai_generated: (aiGenerated === 'true' ? true : false) || ai_generated,
      }).then((response) => {
        if(response.success){
          showSuccessToast(editMode ? "Draft updated successfully" : "Your post has been saved as draft" ,  "newpost-toast");
          setTimeout(() => {
            window.location.href = '/dashboard/planner';
          }, 1000);
        } else {
          showErrorToast(response.message,  "newpost-toast");
        }
      });
    }
  };

  const handleSchedule = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      const date = new Date(scheduledDate);
      date.setHours(hours2 ? parseInt(hours2.toString()) : 0, minutes2 ? parseInt(minutes2.toString()) : 0, 0);
      
      // Check if selected date is in the past
      const now = new Date();
      if (date <= now) {
        showErrorToast("Cannot schedule posts in the past", "newpost-toast");
        return;
      }

      const formattedScheduleDate = date.toISOString().slice(0, 19).replace('T', ' ');
      
      // Convert selectedImages64 array to numbered object format

       // Convert all images to base64
       const mediaPromises = selectedImagesRef.current.map(async (file) => {
        return new Promise((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64String = reader.result?.toString().split(',')[1] || '';
            resolve(base64String);
          };
          reader.readAsDataURL(file);
        });
      });

      const mediaFiles = await Promise.all(mediaPromises);

      if (editMode) {
        await createSchedule({
          workspace_name: selectedWorkspaceDetails.workspace_name,
          type: "schedule",
          datetime: formattedScheduleDate,
          images: mediaFiles,
          socials: [social ? social : selectedSocial.social_id],
          caption: captionRef.current.value,
          ai_generated: (aiGenerated === 'true' ? true : false) || ai_generated,
        }).then((response: any) => {
          if(response.success){
            showSuccessToast(editMode ? "Schedule updated successfully" : "Your post has been scheduled" ,  "newpost-toast");
            setTimeout(() => {
              window.location.href = '/dashboard/planner';
            }, 1000);
          } else {
            showErrorToast(response.message,  "newpost-toast");
          }
        });
    
        setIsSchedulePopupOpen(false);
      } else {
        await createSchedule({
          workspace_name: selectedWorkspaceDetails.workspace_name,
          type: "schedule", 
          datetime: formattedScheduleDate,
          images: mediaFiles,
          socials: [social ? social : selectedSocial.social_id],
          caption: captionRef.current.value,
          ...(postId && { post_id: postId }),
          ai_generated: (aiGenerated === 'true' ? true : false) || ai_generated,
        }).then((response: any) => {
          if(response.success){
            showSuccessToast(editMode ? "Schedule updated successfully" : "Your post has been scheduled" ,  "newpost-toast" );
            setTimeout(() => {
              window.location.href = '/dashboard/planner';
            }, 1000);
          } else {
            showErrorToast(response.message,  "newpost-toast" );
          }
        });
    
        setIsSchedulePopupOpen(false);
      }
    } catch (error) {
      showErrorToast("An error occurred while scheduling", "newpost-toast");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add useEffect for handling click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isSchedulePopupOpen && 
        schedulePopupRef.current && 
        !schedulePopupRef.current.contains(event.target as Node) &&
        scheduleButtonRef.current && 
        !scheduleButtonRef.current.contains(event.target as Node)
      ) {
        setIsSchedulePopupOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isSchedulePopupOpen]);

  const advancedPromptRef = useRef('');

  const handleAdvancedPromptChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    advancedPromptRef.current = e.target.value;
  };

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const renderPreview = () => {
    const avatarUrl = (selectedSocial as any)?.profile_photo ||
      workspaceLogo ||
      `https://ui-avatars.com/api/?name=${encodeURIComponent(selectedWorkspaceDetails?.workspace_name || 'User')}`;

    const files = selectedImagesRef.current as File[];
    const caption = captionRef.current.value || '';
    const displayName = selectedWorkspaceDetails.workspace_name;
    const username = (selectedSocial as any)?.username || 'username';

    const platform = (selectedSocial as any)?.platform || selectedSocialMediaRef.current || 'instagram';

    switch (platform) {
      case 'twitter':
      case 'x':
        return (
          <TwitterPreview
            avatarUrl={avatarUrl}
            displayName={displayName}
            username={username}
            caption={caption || "Just like a photograph, life is all about capturing the right moments each moment is like a frame in a movie, telling a unique story that shapes who we are 📸✨"}
            files={files}
            currentIndex={currentImageIndex}
            onChangeIndex={(i) => setCurrentImageIndex(i)}
          />
        );
      case 'instagram':
        return (
          <InstagramPreview
            avatarUrl={avatarUrl}
            displayName={displayName}
            caption={caption}
            files={files}
            currentIndex={currentImageIndex}
            onChangeIndex={(i) => setCurrentImageIndex(i)}
          />
        );
      case 'facebook':
        return (
          <FacebookPreview
            avatarUrl={avatarUrl}
            displayName={displayName}
            caption={caption}
            files={files}
            currentIndex={currentImageIndex}
            onChangeIndex={(i) => setCurrentImageIndex(i)}
          />
        );
      default:
        return <div>Select a social media platform to see preview</div>;
    }
  };

  // First, modify the AIImageModal component to accept props
  const AIImageModal = ({ caption,ai_generated, setAiGenerated, setActivePage }: { caption: string;  ai_generated: boolean, setAiGenerated: (ai_generated: boolean) => void, setActivePage: string}) => {
    const { activePage, setUser } = useUserStore();
    const inputRef = useRef('');
    const generationProgressRef = useRef(0);

    if (setActivePage) {
      setUser({ activePage: setActivePage });
    }

    const getTokenUsage = (type: string) => {
      switch (type) {
        case 'Video':
          return 30;
        case 'Image':
          return 20;
        case 'Caption':
        case 'Hashtags':
          return 5;
        default:
          return 0;
      }
    };

    const [isDevToolsOpen, setIsDevToolsOpen] = useState(false);

    // useEffect(() => {
    //   const handleChange = (isOpen: boolean) => {
    //     setIsDevToolsOpen(isOpen);
    //   };

    //   // Add listener and launch detector
    //   addListener(handleChange);
    //   launch();

    //   return () => {
    //     // Clean up by removing listener and stopping detector
    //     removeListener(handleChange);
    //     stop();
    //   };
    // }, [window.innerWidth]);


    const [isGeneratingCaption, setIsGeneratingCaption] = useState(false);
    const [isGeneratingHashtags, setIsGeneratingHashtags] = useState(false);

    const [isGenerating, setIsGenerating] = useState(false);
    const [estimatedTime, setEstimatedTime] = useState(0);
  
    
    const isAdvancedModeRef = useRef<boolean>(false);
    
    const [isLoading, setIsLoading] = useState(false);

    // Separate states for different content types
    const [imageKeywords, setImageKeywords] = useState<string[]>([]);
    const [videoKeywords, setVideoKeywords] = useState<string[]>([]);
    const captionKeywordsRef = useRef<string[]>([]);
    const hashtagKeywordsRef = useRef<string[]>([]);
    const [keywordUpdateTrigger, setKeywordUpdateTrigger] = useState(0); // Add this to force render when needed


    const [imagePrompt, setImagePrompt] = useState('');
    const [videoPrompt, setVideoPrompt] = useState('');
    const [captionPrompt, setCaptionPrompt] = useState('');
    const [hashtagPrompt, setHashtagPrompt] = useState('');

    const [hashtags, setHashtags] = useState<string[]>([]);
    const generatedCaptionRef = useRef<string>('');
    const hashtagsRef = useRef<string[]>([]);
    const [captionUpdateTrigger, setCaptionUpdateTrigger] = useState(0);
    const [hashtagUpdateTrigger, setHashtagUpdateTrigger] = useState(0);

    console.log(generatedCaptionRef.current)

    // Add these state variables near the top of your component
    const [isGeneratingImage, setIsGeneratingImage] = useState(false);
    const [isGeneratingVideo, setIsGeneratingVideo] = useState(false);

    const EmptyPlaceholder = ({ type, isGenerating }: { type: 'image' | 'video', isGenerating: boolean }) => (
      <div className="relative aspect-square w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
        {isGenerating ? (
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 mb-2">
              <svg className="animate-spin text-gray-400" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
            </div>
            <div className="text-sm text-gray-500">Generating {type}...</div>
            <div className="text-xs text-gray-400 mt-1">Time remaining: {countDown}s</div>
            <div className="w-32 h-1 bg-gray-200 rounded-full mt-2">
              <div 
                className="h-full bg-blue-500 rounded-full transition-all duration-200"
                style={{ width: `${generationProgressRef.current}%` }}
              />
            </div>
          </div>
        ) : (
          <div className="w-12 h-12 rounded-full bg-gray-200/80 flex items-center justify-center 
                        backdrop-blur-sm transition-all duration-200 group-hover:bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
        )}
      </div>
    );

    const SkeletonLoader = ({ type }: { type: string }) => {
      return (
        <div className="w-full h-full rounded-lg overflow-hidden">
          <div className="w-full h-full bg-gray-200 animate-pulse-skeleton">
            <div className="flex items-center justify-center h-full">
              {type === 'image' ? (
                <svg className="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              ) : (
                <svg className="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              )}
            </div>
          </div>
        </div>
      );
    };

    // Replace inputValue state with ref usage
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      inputRef.current = e.target.value;
    };

    // Update handleKeyDown to use inputRef
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if ( e.key === 'Enter' && inputRef.current?.trim()) {
        const trimmedInput = inputRef.current.trim();
        
        switch (activePage) {
          case 'Image':
            setImageKeywords(prev => [...prev, trimmedInput]);
            break;
          case 'Video':
            setVideoKeywords(prev => [...prev, trimmedInput]); 
            break;
          case 'Caption':
            captionKeywordsRef.current = [...captionKeywordsRef.current, trimmedInput];
            setKeywordUpdateTrigger(prev => prev + 1); // Force render

            break;
          case 'Hashtags':
            hashtagKeywordsRef.current = [...hashtagKeywordsRef.current, trimmedInput];
            setKeywordUpdateTrigger(prev => prev + 1); // Force render

            break;
        }

        setUser({keywords: [...keywords, trimmedInput]});
        inputRef.current = '';
        e.currentTarget.value = '';
      }
    };

    const removeKeyword = (indexToRemove: number) => {
      // Remove keyword from both the current keywords and the page-specific keywords
      const newKeywords = keywords.filter((_, index) => index !== indexToRemove);
      setUser({keywords: newKeywords});

      switch (activePage) {
        case 'Image':
          const newImageKeywords = imageKeywords.filter((_, index) => index !== indexToRemove);
          setImageKeywords(newImageKeywords);
          break;
        case 'Video':
          const newVideoKeywords = videoKeywords.filter((_, index) => index !== indexToRemove);
          setVideoKeywords(newVideoKeywords);
          break;
        case 'Caption':
          captionKeywordsRef.current = captionKeywordsRef.current.filter((_, index) => index !== indexToRemove);
          setKeywordUpdateTrigger(prev => prev + 1); // Force render
          break;
        case 'Hashtags':
          hashtagKeywordsRef.current = hashtagKeywordsRef.current.filter((_, index) => index !== indexToRemove);
          setKeywordUpdateTrigger(prev => prev + 1); // Force render
          break;
      }
    };

    // // Reset states when changing pages
    const handlePageChange = (newPage: string) => {
      setUser({ activePage: newPage });
    
      // Reset keywords based on the new page
      switch (newPage) {
        case 'Image':
          setUser({keywords: imageKeywords});
          break;
        case 'Video':
          setUser({keywords: videoKeywords});
          break;
        case 'Caption':
          setUser({keywords: captionKeywordsRef.current});
          break;
        case 'Hashtags':
          setUser({keywords: hashtagKeywordsRef.current});
          break;
      }
    };

    // useEffect(() => {
    //   switch (activePage) {
    //     case 'Image':
    //       setUser({keywords: imageKeywords});
    //       break;
    //     case 'Video':
    //       setUser({keywords: videoKeywords});
    //       break;
    //     case 'Caption':
    //       setUser({keywords: captionKeywordsRef.current});
    //       break;
    //     case 'Hashtags':
    //       setUser({keywords: hashtagKeywordsRef.current});
    //       break;
    //   }
    // }, [activePage, imageKeywords, videoKeywords, captionKeywordsRef, hashtagKeywordsRef]);
  
    const handleGenerateHashtag = async (edit: string, type: string) => {
      if (type === "basic" && keywords.length === 0) {
        showErrorToast("please add at least three keywords",  "newpost-toast");
        return;
      }
      if (type === "advanced" && !advancedPromptRef.current?.trim()) {
        showErrorToast("Please enter a prompt",  "newpost-toast");
        return;
      }

      try {
        setIsGeneratingHashtags(true); // Set loading state

        let prompt = "";
        if (type === "basic") {
          prompt = keywords.join(', ');
          if (!prompt) {
            showErrorToast("Invalid prompt", "newpost-toast");
            return;
          }
        }

        const requestData = {
          prompt: type === "basic" ? prompt : advancedPromptRef.current,
          type: type,
          ...(edit !== "default" && { edit }),
          ...(edit === "recreate" && { previous_answer: generatedHashtags.join(' ') })
        };

        const response = await generateHashtags(requestData);

        if (!response.success) {
          showErrorToast(response.message || "Error generating hashtags", "newpost-toast");
          return;
        }

      

      } catch (error) {
        console.error("Error generating hashtags:", error);
        showErrorToast(error?.message || "Failed to generate hashtags", "newpost-toast");
      } finally {
        setIsGeneratingHashtags(false);
      }
    };




  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      );
    }

    switch (activePage) {
      case 'Image':
        const handleGenerateImage = async (edit: string, type: string) => {
          if (type === "basic" && keywords.length === 0) {
            showErrorToast("please add at least three keywords",  "newpost-toast");
            return;
          }
          if (type === "advanced" && !advancedPromptRef.current.trim()) {
            showErrorToast("Please enter a prompt",  "newpost-toast");
            return;
          }

          setIsGeneratingImage(true);
          generationProgressRef.current = 0;
          
          try {
            let prompt = "";
            if (type === "basic") {
              prompt = keywords.join(', ');
              if (!prompt) {
                setUser({
                        imageLoading: false,
                        countDown: 0,
                      });
                return;
              }
            }

            switch (type) {
              case "basic":
                switch(edit) {
                  case "default": {
                    const generatedUrl = await generateImage({
                      prompt: prompt,
                      type: 'generative'
                    });
                    
                    if (typeof generatedUrl === 'string' ) {
                      console.log("Image generation complete. URL:", generatedUrl);
                      const emptyIndex = images.findIndex(image => image === "");
                      if (emptyIndex !== -1) {
                        const newImages = [...images];
                        newImages[emptyIndex] = generatedUrl;
                        setImages(newImages);
                      }
                      setUser({
                        imageLoading: false,
                        countDown: 0,
                      });
                    } else if (generatedUrl && typeof generatedUrl === 'object' && generatedUrl.status === 'inProgress') {
                      // Image generation started, backend will update when ready
                      showSuccessToast("Image generation started. It will appear once ready.",  "newpost-toast");
                      setUser({ imageLoading: false, countDown: 0 });
                    } else {
                      setUser({
                        imageLoading: false,
                        countDown: 0,
                      })

                      console.log(generatedUrl)
                      showErrorToast("Failed to generate image",  "newpost-toast");
                    }
                    break;
                  }
                  case "recreate": {
                    const generatedUrl = await generateImage({
                      prompt: prompt,
                      type: 'generative',
                      edit: "recreate"
                    });

                    if (typeof generatedUrl === 'string') {
                      console.log("Image generation complete. URL:", generatedUrl);
                      const emptyIndex = images.findIndex(image => image === "");
                      if (emptyIndex !== -1) {
                        const newImages = [...images];
                        newImages[emptyIndex] = generatedUrl;
                        setImages(newImages);
                      }
                    } else if (generatedUrl && typeof generatedUrl === 'object' && generatedUrl.status === 'inProgress') {
                      showSuccessToast("Image generation started. It will appear once ready.",  "newpost-toast");
                      setUser({ imageLoading: false, countDown: 0 });
                    } else {
                      setUser({
                        imageLoading: false,
                        countDown: 0,
                      });
                      showErrorToast("Failed to generate image",  "newpost-toast");
                    }
                    break;
                  }
                }
                break;
              case "advanced":
                switch(edit) {
                  case "default": {
                    const generatedUrl = await generateImage({
                      prompt: advancedPromptRef.current,
                      type: 'generative'
                    });
                    
                    if (typeof generatedUrl === 'string') {
                      console.log("Image generation complete. URL:", generatedUrl);
                      const emptyIndex = images.findIndex(image => image === "");
                      if (emptyIndex !== -1) {
                        const newImages = [...images];
                        newImages[emptyIndex] = generatedUrl;
                        setImages(newImages);
                      }
                    } else if (generatedUrl && typeof generatedUrl === 'object' && generatedUrl.status === 'inProgress') {
                      showSuccessToast("Image generation started. It will appear once ready.",  "newpost-toast");
                      setUser({ imageLoading: false, countDown: 0 });
                    } else {
                      setUser({
                        imageLoading: false,
                        countDown: 0,
                      });
                      console.log(generatedUrl)

                      showErrorToast(generatedUrl?.message || "Failed to generate image",  "newpost-toast");
                    }
                    break;
                  }
                  case "recreate": {
                    const generatedUrl = await generateImage({
                      prompt: advancedPromptRef.current,
                      type: 'generative',
                      edit: "recreate"
                    });

                    if (typeof generatedUrl === 'string') {
                      console.log("Image generation complete. URL:", generatedUrl);
                      const emptyIndex = images.findIndex(image => image === "");
                      if (emptyIndex !== -1) {
                        const newImages = [...images];
                        newImages[emptyIndex] = generatedUrl;
                        setImages(newImages);
                      }
                    } else {
                      setUser({
                        imageLoading: false,
                        countDown: 0,
                      });
                      showErrorToast("Failed to generate image",  "newpost-toast");
                    }
                    break;
                  }
                }
                break;
            }
          } catch (error) {
            console.error("Error during image generation:", error);
            showErrorToast("Failed to generate image. Please try again.",  "newpost-toast");
          } finally {
            setUser({
                        imageLoading: false,
                        countDown: 0,
                      });
          }
        };

       

        const handleInsertImage = async (url: string) => {
          await downloadImage(url);
          setAiGenerated(true)
          isAIModalOpenDesktopRef.current = false;
          forceUpdate({});

        }
        const downloadAndConvertToBase64 = async (url: string): Promise<string> => {
          try {
            // Fetch the file
            const response = await axios.get(url, { responseType: 'blob' });
            const blob = response.data;
            
            // Convert blob to base64
            return new Promise<string>((resolve, reject) => {
              const reader = new FileReader();
              reader.onloadend = () => {
                const result = reader.result as string | ArrayBuffer | null;
                if (typeof result !== 'string') {
                  reject(new Error('Failed to read file as data URL'));
                  return;
                }
                const base64String = result
                  .replace('data:', '')
                  .replace(/^.+,/, '');
                resolve(base64String);
              };
              reader.onerror = reject;
              reader.readAsDataURL(blob);
            });
          } catch (error) {
            if (axios.isAxiosError(error)) {
              const result = error.response?.data as { message?: string } | undefined;
              console.error('Error downloading and converting file:', result);
              showErrorToast(result?.message || "An unexpected error occurred. Please try again.", "verify-toast");
            } else {
              console.error('Error downloading and converting file:', error);
              showErrorToast("An unexpected error occurred. Please try again.", "verify-toast");
            }
            throw error;
          }
        };

      

        const downloadImage = async (url: string) => {
          
            let blob;
            if (url.startsWith('blob:')) {
              const response = await axios.get(url, { responseType: 'blob' });
              blob = response.data;
            } else {
              const response = await axios.get(url, { responseType: 'blob' });
              blob = response.data;
            }
            const filename = url.split('/').pop() ?? 'image.jpg';
            const file = new File([blob], filename, { type: 'image/jpeg' });
            selectedImagesRef.current = [...selectedImagesRef.current, file];
            
            // Also update base64 version
            const base64 = await downloadAndConvertToBase64(url);
            selectedImages64Ref.current = [...selectedImages64Ref.current, base64];
          
        }

        

        return (
          <div className="flex flex-col w-full gap-3 md:gap-4 h-full min-h-0 animate-fadeIn">
            {/* Token Usage Indicator */}
            <TokenUsageIndicator tokensLabel="20 tokens per generation" />
            
            {/* Image Preview Grid */}
            <div className="flex-1 min-h-0 overflow-y-auto pr-1">
              <ImagePreviewGrid
                images={images}
                imageLoading={imageLoading}
                loadingIndex={imageLoading ? images.findIndex((img) => !img || img === "") : undefined}
                countDown={countDown}
                onInsert={(url) => handleInsertImage(url)}
                onDelete={(index) => {
                  const newImages = [...images];
                  newImages[index] = "";
                  setImages(newImages);
                }}
              />
            </div>

            {/* Input Section */}
            <div className="flex flex-col gap-3 md:gap-4 mt-auto">
              {isAdvancedMode ? (
                <BasicPromptBar
                  keywords={keywords}
                  onRemoveKeyword={removeKeyword}
                  onInputChange={handleInputChange}
                  onInputKeyDown={handleKeyDown}
                  onRecreate={() => handleGenerateImage('recreate', 'advance')}
                  onGenerate={() => handleGenerateImage('default', 'basic')}
                  
                />
              ) : (
                <div className="relative">
                  <AdvancedPromptInput
                    value={advancedPromptRef.current}
                    onChange={handleAdvancedPromptChange}
                    onGenerateDefault={() => handleGenerateImage('default', 'advanced')}
                    onGenerateRecreate={() => handleGenerateImage('recreate', 'advanced')}
                    
                  />
                </div>
              )}
            </div>
          </div>
        );
// ...
      case 'Video':



        const handleGenerateVideo = async (edit: string, type: string) => {
          if (type === "basic" && keywords.length === 0) {
            showErrorToast("please add at least three keywords",  "newpost-toast");
            return;
          }
          if (type === "advanced" && !advancedPromptRef.current.trim()) {
            showErrorToast("Please enter a prompt",  "newpost-toast");
            return;
          }

          setIsGeneratingVideo(true);
          generationProgressRef.current = 0;
          
          try {
            let prompt = "";
            if (type === "basic") {
              prompt = keywords.join(', ');
              if (!prompt) {
                setIsGeneratingVideo(false);
                return;
              }
            }

            switch (type) {
              case "basic":
                switch(edit) {
                  case "default": {
                    const generatedUrl = await generateVideo({
                      prompt: prompt,
                      type: "basic"
                    });
                    
                    if (typeof generatedUrl === 'string') {
                      console.log("Video generation complete. URL:", generatedUrl);
                      const emptyIndex = videos.findIndex(video => video === "");
                      if (emptyIndex !== -1) {
                        const newVideos = [...videos];
                        newVideos[emptyIndex] = generatedUrl;
                        setVideos(newVideos);
                      }
                    }
                    break;
                  }
                  case "recreate": {
                    const generatedUrl = await generateVideo({
                      prompt: prompt,
                      type: "basic",
                      edit: "recreate"
                    });

                    if (typeof generatedUrl === 'string') {
                      console.log("Video generation complete. URL:", generatedUrl);
                      const emptyIndex = videos.findIndex(video => video === "");
                      if (emptyIndex !== -1) {
                        const newVideos = [...videos];
                        newVideos[emptyIndex] = generatedUrl;
                        setVideos(newVideos);
                      }
                    }
                    break;
                  }
                }
                break;
              case "advanced":
                switch(edit) {
                  case "default": {
                    const generatedUrl = await generateVideo({
                      prompt: advancedPromptRef.current,
                      type: "advanced"
                    });

                    if (!generatedUrl?.success) {
                      showErrorToast(generatedUrl?.message || "Failed to generate video. Please try again.",  "newpost-toast");
                      return;
                    }
                    
                    if (typeof generatedUrl === 'string') {
                      console.log("Video generation complete. URL:", generatedUrl);
                      const emptyIndex = videos.findIndex(video => video === "");
                      if (emptyIndex !== -1) {
                        const newVideos = [...videos];
                        newVideos[emptyIndex] = generatedUrl;
                        setVideos(newVideos);
                      }
                    }
                    break;
                  }
                  case "recreate": {
                    const generatedUrl = await generateVideo({
                      prompt: advancedPromptRef.current,
                      type: "advanced",
                      edit: "recreate"
                    });

                    console.log(generatedUrl)

                    if (!generatedUrl.success) {
                      showErrorToast(generatedUrl.message,  "newpost-toast");
                      return;
                    }

                    if (typeof generatedUrl === 'string') {
                      console.log("Video generation complete. URL:", generatedUrl);
                      const emptyIndex = videos.findIndex(video => video === "");
                      if (emptyIndex !== -1) {
                        const newVideos = [...videos];
                        newVideos[emptyIndex] = generatedUrl;
                        setVideos(newVideos);
                      }
                    }
                    break;
                  }
                }
                break;
            }
          } catch (error) {
            console.error("Error during video generation:", error);
            showErrorToast(error?.message || "Failed to generate video. Please try again.",  "newpost-toast");
          } finally {
            setIsGeneratingVideo(false);
          }
        };

        const handleInsertVideo = async (url: string) => {
          downloadVideo(url)
          setAiGenerated(true)
          isAIModalOpenDesktopRef.current = false;
          forceUpdate({});

        }

        const downloadVideo = async (url: string) => {
          try {
            const response = await axios.get(url, { responseType: 'blob' });
            const blob = response.data;
            const filename = url.split('/').pop() || 'video.mp4';
            const file = new File([blob], filename, { type: 'video/mp4' });
            selectedImagesRef.current = [...selectedImagesRef.current, file];
            
            // Also update base64 version
            const base64 = await downloadAndConvertToBase64(url);
            selectedImages64Ref.current = [...selectedImages64Ref.current, base64];
          } catch (error) {
            const result = error.response.data;
            console.error('Error downloading video:', result);
            showErrorToast(result.message || "An unexpected error occurred. Please try again.", "verify-toast");
          }
        }


 


        return (
          <div className="flex flex-col gap-3 md:gap-4 animate-fadeIn w-full h-full min-h-0">
            {/* Token Usage Indicator */}
            <TokenUsageIndicator tokensLabel="30 tokens per generation" />

            {/* Video Preview Grid */}
            <div className="flex-1 min-h-0 overflow-y-auto pr-1">
              <VideoPreviewGrid
                videos={videos}
                videoLoading={videoLoading}
                countDown={countDown}
                onInsert={(url) => handleInsertVideo(url)}
                onDelete={(index) => {
                  const newVideos = [...videos];
                  newVideos[index] = "";
                  setVideos(newVideos);
                }}
              />
            </div>

            {/* Input Section */}
            <div className="flex flex-col gap-3 md:gap-4 mt-auto">
              {isAdvancedMode ? (
                <BasicPromptBar
                  keywords={keywords}
                  onRemoveKeyword={removeKeyword}
                  onInputChange={handleInputChange}
                  onInputKeyDown={handleKeyDown}
                  onRecreate={() => handleGenerateVideo('recreate', 'basic')}
                  onGenerate={() => handleGenerateVideo('default', 'basic')}
                />
              ) : (
                <div className="relative">
                  <AdvancedPromptInput
                    value={advancedPromptRef.current}
                    onChange={handleAdvancedPromptChange}
                    onGenerateDefault={() => handleGenerateVideo('default', 'advanced')}
                    onGenerateRecreate={() => handleGenerateVideo('recreate', 'advanced')}
                  />
                </div>
              )}
            </div>
          </div>
        );
      case 'Caption':
        const handleGenerateCaption = async (edit: string, type: string) => {
          try {
            setIsGeneratingCaption(true);

            // Build and validate prompt consistently (avoid empty payloads)
            let prompt = "";
            if (type === "basic") {
              // Accept either added keywords (chips) or the raw input text
              const fromChips = keywords.join(', ');
              const fromInput = inputRef.current || "";
              prompt = (fromChips || fromInput).trim();
              if (!prompt) {
                showErrorToast("Please type at least one keyword", "newpost-toast");
                return;
              }
            } else {
              prompt = advancedPromptRef.current || "";
              if (!prompt.trim()) {
                showErrorToast("Please enter a prompt", "newpost-toast");
                return;
              }
            }

            await generateCaption({
              prompt,
              type,
              ...(edit !== "default" && { edit }),
              ...(edit === "recreate" && { previous_answer: generatedCaption })
            }).then((response) => {
              if (!response.success) {
                showErrorToast(response.message || "Failed to generate caption", "newpost-toast");
              }
            });
          } catch (error) {
            showErrorToast(error?.message || "Failed to generate caption", "newpost-toast");
          } finally {
            setIsGeneratingCaption(false);
          }
        };
        return (
          <div className="flex w-full flex-col gap-8 animate-fadeIn justify-between h-full">
            {/* Token Usage Indicator */}
            <TokenUsageIndicator tokensLabel="5 tokens per generation" />
            {/* Generated Caption Display */}
            <CaptionGeneratedDisplay
              isGenerating={isGeneratingCaption}
              isDevToolsOpen={isDevToolsOpen}
              generatedCaption={generatedCaption}
            />

            {/* Caption Actions */}
            <CaptionActions
              onAddToPost={() => {
                const spacer = captionRef.current.value.endsWith(' ') ? '' : ' ';
                captionRef.current.value = captionRef.current.value + spacer + generatedCaption;
                isAIModalOpenDesktopRef.current = false;
                showSuccessToast('Caption added to post' ,  "newpost-toast");
                setAiGenerated(true);
                forceUpdate({});
              }}
              onExpand={() => handleGenerateCaption('expand', isAdvancedMode ? 'advanced' : 'basic')}
              onShorten={() => handleGenerateCaption('shorten', isAdvancedMode ? 'advanced' : 'basic')}
            />

            {/* Mode Toggle */}
            <BasicAdvancedToggle 
              isAdvanced={isAdvancedMode}
              onBasic={() => setUser({isAdvancedMode: false})}
              onAdvanced={() => setUser({isAdvancedMode: true})}
            />

            {/* Input Section */}
            <div className="flex flex-col gap-4">
              {!isAdvancedMode ? (
                <BasicPromptBar
                  keywords={keywords}
                  onRemoveKeyword={removeKeyword}
                  onInputChange={handleInputChange}
                  onInputKeyDown={(e) => {
                    handleKeyDown(e);
                    if (e.key === 'Enter' && e.shiftKey) {
                      e.preventDefault();
                      handleGenerateCaption('default', isAdvancedMode ? 'advanced' : 'basic');
                    }
                  }}
                  onRecreate={() => handleGenerateCaption('recreate', isAdvancedMode ? 'advanced' : 'basic')}
                  onGenerate={() => handleGenerateCaption('default', 'basic')}
                />
              ) : (
                <div className="relative">
                  <AdvancedPromptInput
                    value={advancedPromptRef.current}
                    onChange={handleAdvancedPromptChange}
                    onGenerateDefault={() => handleGenerateCaption('default', 'advanced')}
                    onGenerateRecreate={() => handleGenerateCaption('recreate', 'advanced')}
                  />
                </div>
              )}
            </div>
          </div>
        );
      case 'Hashtags':


        return (
          <div className="flex flex-col justify-between w-full gap-8 animate-fadeIn h-full">
            {/* Token Usage Indicator */}
            <TokenUsageIndicator tokensLabel="5 tokens per generation" />

            {/* Hashtags Display */}
            <HashtagsDisplay
              isGenerating={isGeneratingHashtags}
              isDevToolsOpen={isDevToolsOpen}
              hashtags={generatedHashtags}
            />

            {/* Hashtags Actions */}
            <HashtagsActions
              onAddToPost={() => {
                const hashtagString = generatedHashtags.join(' ');
                captionRef.current.value = captionRef.current.value + (captionRef.current.value.endsWith(' ') ? '' : ' ') + hashtagString;
                isAIModalOpenDesktopRef.current = false;
                setAiGenerated(true);
                showSuccessToast('Hashtags added to post',  "newpost-toast");
                forceUpdate({});
              }}
              onMore={() => handleGenerateHashtag('more', isAdvancedMode ? 'advanced' : 'basic')}
              onFewer={() => handleGenerateHashtag('fewer', isAdvancedMode ? 'advanced' : 'basic')}
            />

            {/* Mode Toggle */}
            <BasicAdvancedToggle 
              isAdvanced={isAdvancedMode}
              onBasic={() => setUser({isAdvancedMode: false})}
              onAdvanced={() => setUser({isAdvancedMode: true})}
            />

            {/* Input Section */}
            <div className="flex flex-col gap-4">
              {!isAdvancedMode ? (
                <BasicPromptBar
                  keywords={keywords}
                  onRemoveKeyword={removeKeyword}
                  onInputChange={handleInputChange}
                  onInputKeyDown={(e) => {
                    handleKeyDown(e);
                    if (e.key === 'Enter' && e.shiftKey) {
                      e.preventDefault();
                      handleGenerateHashtag('default', isAdvancedMode ? 'advanced' : 'basic');
                    }
                  }}
                  onRecreate={() => handleGenerateHashtag('recreate', 'basic')}
                  onGenerate={() => handleGenerateHashtag('default', 'basic')}
                />
              ) : (
                <div className="relative">
                  <AdvancedPromptInput
                    value={advancedPromptRef.current}
                    onChange={handleAdvancedPromptChange}
                    onGenerateDefault={() => handleGenerateHashtag('default', 'advanced')}
                    onGenerateRecreate={() => handleGenerateHashtag('recreate', 'advanced')}
                  />
                </div>
              )}
            </div>
          </div>
        );
      default:
        return <div>Select a social media platform to see preview</div>;
    }
  };

  return (
    <AIContentModal
      contentKey={activePage}
      headerTabs={(
        <>
          {['Image', 'Video', 'Caption', 'Hashtags'].map((page) => (
            <motion.button
              key={page}
              className={`text-xs sm:text-sm md:text-base relative transition-all duration-200 whitespace-nowrap md:whitespace-normal flex-1 text-center px-2 py-2 rounded ${
                activePage === page 
                  ? 'text-blue-600 font-medium' 
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              onClick={() => handlePageChange(page)}
              whileHover={{ scale: 1.02, transition: { type: 'tween', duration: 0.12, ease: 'easeOut' } }}
              whileTap={{ scale: 0.98, transition: { type: 'tween', duration: 0.08, ease: 'easeOut' } }}
            >
              <div className="flex items-center justify-center gap-2">
                {page}
              </div>
              {activePage === page && (
                <motion.div
                  layoutId="aiTabUnderline"
                  className="absolute bottom-0 left-2 right-2 h-0.5 bg-blue-600"
                  transition={{ duration: 0.18, ease: 'easeOut' }}
                />
              )}
            </motion.button>
          ))}
        </>
      )}
      onClose={() => {
        isAIModalOpenDesktopRef.current = false;
        forceUpdate({});
      }}
    >
      {renderContent()}
    </AIContentModal>
  );
};

{/* <div className="flex items-center gap-2 text-blue-600 bg-blue-50 w-fit px-3 py-1.5 rounded-lg mb-4">
<svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
</svg>
<span className="text-sm font-medium">2 tokens for schedule and publish</span>
</div> */}

  

  const SchedulePopup = () => {
    // Add useEffect for click outside handling
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          schedulePopupRef.current && 
          !schedulePopupRef.current.contains(event.target as Node)
        ) {
          setIsSchedulePopupOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    return (
      <div ref={schedulePopupRef} className="absolute bottom-full mb-2 right-[10%] md:right-0 bg-white rounded-lg shadow-lg p-4 w-80 z-99">
        <h2 className="text-xl font-bold mb-4">Schedule post</h2>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
          <input
            type="date"
            className="w-full p-2 border rounded"
            value={scheduledDate.toISOString().split('T')[0]} // Already in YYYY-MM-DD format
            onChange={(e) => setScheduledDate(new Date(e.target.value))}
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Manually set time</label>
          <div className="flex space-x-2">
            <input
              type="number"
              className="w-1/3 p-2 border rounded"
              value={hours2 || new Date().getHours()}
              onChange={(e) => setHours2(e.target.value)}
              min="0"
              max="23"
            />
            <input
              type="number"
              className="w-1/3 p-2 border rounded"
              value={minutes2 || new Date().getMinutes()}
              onChange={(e) => setMinutes2(e.target.value)}
              min="0"
              max="59"
            />
            <input
              type="number"
              className="w-1/3 p-2 border rounded"
              value="00"
              readOnly
            />
          </div>
          <div className="flex flex-row gap-4">
            <button
              disabled={!isScheduleDateValid()}
              className={`px-4 py-2 mt-4 rounded transition-all duration-200 ${
                isScheduleDateValid() 
                  ? 'bg-gray-200 text-gray-700 hover:bg-gray-300' 
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
              onClick={handleSchedule}
            >
              {isSubmitting ? 'Scheduling...' : 'Schedule'}
            </button>
            <button 
              className={`px-4 py-2 mt-4 rounded transition-all duration-200 ${
                isSubmitting ? 'bg-blue-300 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600'
              } text-white`}
              onClick={handlePublish}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Publishing...' : 'Publish Now'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Add this helper function to check if selected date/time is valid
  const isScheduleDateValid = () => {
    const date = new Date(scheduledDate);
    date.setHours(hours2 ? parseInt(hours2.toString()) : 0, minutes2 ? parseInt(minutes2.toString()) : 0, 0);
    const now = new Date();
    return date > now;
  };

  return (
    <>
      {/* Mobile view */}
      <div className="md:hidden h-screen overflow-x-hidden">
        <div className="md:hidden w-full h-dvh bg-white flex flex-col overflow-x-hidden">
          {/* Header */}
          <div className="p-3 border-b border-[#cccccc]">
            <h2 className="text-base font-semibold text-[#0d3e4f] text-center">New post</h2>
          </div>

          {showAddSocialModal && (
            <AddSocialAccountModal
              onClose={() => setShowAddSocialModal(false)}
              dim={true}
              onAddAccount={(platform) => {
                console.log(`Adding ${platform} account`);
                setShowAddSocialModal(false);
              }}
            />
          )}

          {isAIModalOpenDesktopRef.current && (
            <AIImageModal 
              caption={captionRef.current.value} 
              ai_generated={ai_generated}
              setAiGenerated={setAiGenerated}
            />
          )}

          {/* <ToastWrapper containerId="newpost-toast"/> */}

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto py-5 overflow-x-hidden p-3 mt-4 lg:mt-0 no-scrollbar">
            {/* Image Preview */}
            <div className="flex overflow-x-auto gap-3 mb-5 snap-x mx-auto snap-mandatory w-full">
              {selectedImagesRef.current.map((image, index) => (
                <div key={index} className="flex-none w-48 h-60 relative rounded-md overflow-hidden snap-center">
                  {image.type === 'video/mp4' ? (
                    <VideoPreview src={URL.createObjectURL(image)} className="rounded-lg" />
                  ) : (
                    <Image
                      src={URL.createObjectURL(image)}
                      alt={`Preview ${index + 1}`}
                      layout="fill"
                      objectFit="cover"
                      className="rounded-md"
                    />
                  )}
                  <button
                    onClick={() => removeImage(index)}
                    className="absolute top-2 right-2 bg-white rounded-full p-1 shadow"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M18 6L6 18M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              ))}

              {/* Add More Button - Always visible */}
              <div 
                className="flex-none w-48 h-60 border-2 border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center gap-2 snap-center"
                onClick={() => fileInputRef.current?.click()}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-7 h-7 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 5v14M5 12h14" />
                </svg>
                <span className="text-xs font-medium text-gray-600">Add Media</span>
              </div>
            </div>

            {/* Caption Input (same component as desktop for consistency) */}
            <div className="mb-4">
              <CaptionSection
                value={captionRef.current.value}
                onChange={(val) => { captionRef.current.value = val; forceUpdate({}); }}
                onClear={() => { captionRef.current.value = ''; forceUpdate({}); }}
                onOpenAI={() => { isAIModalOpenDesktopRef.current = true; setUser({ activePage: 'Caption' }); }}
              />
            </div>

            {/* Instagram Preview (mobile) */}
            <div className="mb-4 py-3">
              <h3 className="text-lg font-semibold text-[#0d3e4f] tracking-tight mb-3">Instagram preview</h3>
              {(() => {
                const avatarUrl = (selectedSocial as any)?.profile_photo ||
                  workspaceLogo ||
                  `https://ui-avatars.com/api/?name=${encodeURIComponent(selectedWorkspaceDetails?.workspace_name || 'User')}`;
                const files = selectedImagesRef.current as File[];
                const caption = captionRef.current.value || '';
                const displayName = selectedWorkspaceDetails?.workspace_name || 'Post';
                return (
                  <div className="border border-gray-200 rounded-md overflow-hidden bg-white h-[60vh]">
                    <InstagramPreview
                      avatarUrl={avatarUrl}
                      displayName={displayName}
                      caption={caption}
                      files={files}
                      currentIndex={currentImageIndex}
                      onChangeIndex={(i) => setCurrentImageIndex(i)}
                    />
                  </div>
                );
              })()}
            </div>

            {/* Action Items */}
            {[
              {
                icon: (
                  <img src="/icons/ai.svg" alt="AI"  className="w-8 h-8"/>
                ),
                text: "Create Image by AI",
                onClick: () => {
                  setUser({ activePage: 'Image' });
                  setTimeout(() => isAIModalOpenDesktopRef.current = true, 200);
              
                }
              }
            ].map((item, index) => (
              <button 
                key={index}
                onClick={item.onClick}
                className="w-full flex items-center gap-3 p-3 border-t border-[#cccccc] last:border-b"
              >
                {item.icon}
                <span className="text-[0.813rem] text-[#474747]">{item.text}</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 ml-auto" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M9 18l6-6-6-6"/>
                </svg>
              </button>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="p-3 space-y-2">
            <button 
              onClick={() => selectedImagesRef.current.length > 0 && setIsSchedulePopupOpen(!isSchedulePopupOpen)}
              disabled={selectedImagesRef.current.length === 0 || isSubmitting}
              className={`w-full py-3 rounded-lg text-white! text-sm  ${
                selectedImagesRef.current.length === 0 || isSubmitting
                  ? 'bg-[#007aff]/50 cursor-not-allowed text-white' 
                  : 'bg-[#007aff] hover:bg-[#0056b3] font-bold text-white'
              }`}
            >
              {isSubmitting ? 'Publishing...' : 'Publish'}
            </button>

            {isSchedulePopupOpen && (
              <div className="relative">
                <SchedulePopup />
              </div>
            )}
            <button 
              onClick={handleDraft}
              disabled={selectedImagesRef.current.length === 0}
              className={`w-full text-sm py-3 border border-[#cccccc]/80 rounded-lg text-[#1d1d1d] text-[0.688rem] ${
                selectedImagesRef.current.length === 0 
                  ? 'bg-gray-50 cursor-not-allowed text-gray-400' 
                  : 'hover:bg-gray-100'
              }`}
            >
              Save draft
            </button>
          </div>
        </div>
      </div>

      {/* Desktop view */}
      <div className="hidden md:flex w-full">
        {isAIModalOpenDesktopRef.current && (
          <AIImageModal 
            caption={captionRef.current.value} 
            ai_generated={ai_generated}
            setAiGenerated={setAiGenerated}
          />
        )}

        <div className="flex w-full">
          {/* Left section with social media options */}
          <SocialAccountSelectionSidebar
            socialAccounts={selectedWorkspaceDetails?.social_accounts || []}
            selectedSocial={selectedSocial || null}
            onSocialSelect={(account) =>
              setUser({
                selectedSocial: {
                  platform: account.platform,
                  social_id: account.social_id,
                  social_name: account.social_name,
                  username: account.username,
                  profile_photo: account.profile_photo,
                },
              })
            }
            onAddClick={() => setShowAddSocialModal(true)}
            showOnMobile={false}
            className="shrink-0"
          />
    
          {/* Main content */}
          <div className="flex-1 h-screen md:h-full overflow-hidden bg-gray-100 md:mt-0 mt-[8vh]">
            {!selectedSocial ? (
              <NoSocialSelected />
            ) : (
              <div className="flex-1 w-full mx-auto p-6 bg-gray-100">
                <h1 className="text-2xl font-bold mb-6 text-gray-800">
                  {pageTitle}
                  {editMode && (
                    <span className="ml-2 text-sm text-gray-500">
                      (ID: {postId})
                    </span>
                  )}
                </h1>
                
                <div className="flex flex-col lg:flex-row gap-4 h-[60vh] md:h-[72vh] lg:h-[75vh] min-h-0">
                  <div className="w-full lg:w-7/12 xl:w-7/12 flex flex-col gap-4 h-full min-h-0">
                    <div className="shrink-0 border border-gray-200 rounded-md">
                      <CaptionSection
                        value={captionRef.current.value}
                        onChange={(val) => { captionRef.current.value = val; forceUpdate({}); }}
                        onClear={() => { captionRef.current.value = ''; forceUpdate({}); }}
                        onOpenAI={() => { isAIModalOpenDesktopRef.current = true; setUser({ activePage: 'Caption' }); }}
                      />
                    </div>

                    <div className="flex-1 min-h-0 border border-gray-200 rounded-md">
                      <AddImagesSection
                      files={selectedImagesRef.current}
                      isDragging={isDragging}
                      onDragEnter={handleDragEnter}
                      onDragLeave={handleDragLeave}
                      onDragOver={handleDragOver}
                      onDrop={handleDrop}
                      fileInputRef={fileInputRef}
                      onFileInputChange={handleFileInputChange}
                      onOpenPicker={() => { if (fileInputRef.current) fileInputRef.current.click(); }}
                      onRemove={(index) => removeImage(index)}
                      onOpenAI={() => { isAIModalOpenDesktopRef.current = true; setUser({ activePage: 'Image' }); }}
                      />
                    </div>
                  </div>

                  <div className="w-full lg:w-5/12 xl:w-5/12 h-full min-h-0 border border-gray-200 rounded-md">
                    <PostPreviewSection renderPreview={renderPreview} />
                  </div>
                </div>

                <div className="flex justify-end space-x-2 mt-4 relative">
                  <button 
                    disabled={selectedImagesRef.current.length === 0}
                    className={`px-4 py-2 rounded ${
                      selectedImagesRef.current.length === 0 
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                    onClick={handleDraft}
                  >
                    Save draft
                  </button>
                  <button 
                    disabled={selectedImagesRef.current.length === 0}
                    className={`pb-btn px-4 py-2 rounded text-white! ${
                      selectedImagesRef.current.length === 0 
                        ? 'bg-blue-300 cursor-not-allowed' 
                        : 'bg-blue-500 hover:bg-blue-600'
                    }`}
                    style={{
                      color: "white !important"
                    }}
                    onClick={() => {
                      setIsSchedulePopupOpen(true);
                    }}
                  >
                    Publish
                  </button>

                  {isSchedulePopupOpen && <SchedulePopup />}
                </div>
              </div>
            )}
          </div> 
        </div>
      </div>

      {showAddSocialModal && (
        <AddSocialAccountModal
          onClose={() => setShowAddSocialModal(false)}
          dim={true}
          onAddAccount={(platform) => {
            console.log(`Adding ${platform} account`);
            setShowAddSocialModal(false);
          }}
        />
      )}

      {/* <ToastWrapper containerId="newpost2-toast"/> */}
    </>
  );
  
}

export default NewPost;
