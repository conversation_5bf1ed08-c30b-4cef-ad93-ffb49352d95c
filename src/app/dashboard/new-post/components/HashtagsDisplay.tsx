"use client";
import React from "react";

interface HashtagsDisplayProps {
  isGenerating: boolean;
  isDevToolsOpen: boolean;
  hashtags: string[];
}

const HashtagsDisplay: React.FC<HashtagsDisplayProps> = ({ isGenerating, isDevToolsOpen, hashtags }) => {
  return (
    <div className="flex flex-wrap flex-col gap-2 max-h-60 md:max-h-48 overflow-y-auto justify-center p-4 md:p-6">
      {isGenerating ? (
        <div className="animate-pulse flex flex-wrap gap-2 justify-center">
          <div className="h-4 bg-gray-200 rounded w-24 animate-[blink_1s_ease-in-out_infinite]"></div>
          <div className="h-4 bg-gray-200 rounded w-32 animate-[blink_1s_ease-in-out_infinite]"></div>
          <div className="h-4 bg-gray-200 rounded w-28 animate-[blink_1s_ease-in-out_infinite]"></div>
          <div className="h-4 bg-gray-200 rounded w-20 animate-[blink_1s_ease-in-out_infinite]"></div>
          <div className="h-4 bg-gray-200 rounded w-36 animate-[blink_1s_ease-in-out_infinite]"></div>
          <div className="h-4 bg-gray-200 rounded w-24 animate-[blink_1s_ease-in-out_infinite]"></div>
        </div>
      ) : (
        <div className="flex flex-wrap gap-2 w-full justify-center max-h-[10vh] overflow-y-auto select-none" onMouseDown={(e) => e.preventDefault()}>
          {isDevToolsOpen ? (
            <div className="text-center p-4" role="alert">Please close developer tools to view content</div>
          ) : (
            <>
              {Array.isArray(hashtags) && hashtags.length > 0 ? hashtags.map((tag, index) => (
                <p key={index} className="px-4 py-2 text-lg hover:bg-gray-50 transition-colors duration-200 select-none">
                  {tag}
                </p>
              )) : <p className="text-gray-800 text-lg text-center">Please insert prompt and generate hashtags</p>}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default HashtagsDisplay;
