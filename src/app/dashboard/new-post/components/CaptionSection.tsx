"use client";
import React from 'react';

interface CaptionSectionProps {
  value: string;
  onChange: (value: string) => void;
  onClear: () => void;
  onOpenAI: () => void;
}

const CaptionSection: React.FC<CaptionSectionProps> = ({ value, onChange, onClear, onOpenAI }) => {
  return (
    <div className="bg-white rounded-lg shadow max-h-[28vh] md:max-h-[30vh] flex flex-col">
      <div className="flex justify-between items-center p-4 border-b">
        <h2 className="text-[17px] md:text-lg font-semibold text-slate-900 tracking-tight">Caption</h2>
      <button
        className="text-gray-500 hover:text-gray-700"
        onClick={onClear}
        aria-label="Clear caption"
        type="button"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
      <textarea
        className="w-full p-4 text-gray-700 resize-none focus:outline-none overflow-y-auto"
        rows={4}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Write caption..."
      />
      <div className='w-full p-4 border-t'>
        <button 
          className=" px-3 py-2 bg-white text-gray-700 rounded border flex items-center justify-center gap-2 border-gray-300 hover:bg-gray-50 text-sm"
          onClick={onOpenAI}
          type="button"
        >
          <span>Create Caption by AI</span>
          <img src="/icons/ai.svg" alt="AI" />
        </button>
      </div>
    </div>
  );
};

export default CaptionSection;
