"use client";
import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";

interface ImagePreviewGridProps {
  images: (string | Blob)[];
  imageLoading: boolean;
  loadingIndex?: number; // which card should show loading skeleton
  countDown?: number;
  onInsert: (imageUrl: string) => void;
  onDelete: (index: number) => void;
}

const SkeletonLoader: React.FC<{ type: 'image' | 'video' }> = ({ type }) => (
  <div className="w-full h-full rounded-xl overflow-hidden relative bg-gray-200/80 ring-1 ring-gray-200">
    <div className="absolute inset-0 -translate-x-full animate-shimmer bg-linear-to-r from-transparent via-white/60 to-transparent" />
    <div className="flex items-center justify-center h-full">
      {type === 'image' ? (
        <svg className="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ) : (
        <svg className="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      )}
    </div>
  </div>
);

const EmptyPlaceholder: React.FC<{ type: 'image' | 'video'; isGenerating: boolean; countDown?: number; generationPercent?: number }>
  = ({ type, isGenerating, countDown, generationPercent }) => (
  <div className="relative aspect-square w-full h-full bg-gray-50 rounded-xl flex items-center justify-center ring-1 ring-gray-200">
    {isGenerating ? (
      <div className="flex flex-col items-center">
        <div className="w-12 h-12 mb-2">
          <svg className="animate-spin text-gray-400" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
        </div>
        <div className="text-sm text-gray-500">Generating {type}...</div>
        {typeof countDown === 'number' && (
          <div className="text-xs text-gray-400 mt-1">Time remaining: {countDown}s</div>
        )}
        {typeof generationPercent === 'number' && (
          <div className="w-32 h-1 bg-gray-200 rounded-full mt-2">
            <div className="h-full bg-blue-500 rounded-full transition-all duration-200" style={{ width: `${generationPercent}%` }} />
          </div>
        )}
      </div>
    ) : (
      <div className="w-12 h-12 rounded-full bg-gray-200/80 flex items-center justify-center backdrop-blur-sm transition-all duration-200 group-hover:bg-gray-100">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      </div>
    )}
  </div>
);

const ImagePreviewGrid: React.FC<ImagePreviewGridProps> = ({ images, imageLoading, loadingIndex, countDown, onInsert, onDelete }) => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 md:gap-3 py-1">
      {images.map((image, index) => (
        <div key={index} className="relative aspect-square w-full">
          {(!image || image === "") ? (
            imageLoading && typeof loadingIndex === 'number' && loadingIndex === index ? (
              <SkeletonLoader type="image" />
            ) : (
              <EmptyPlaceholder type="image" isGenerating={false} />
            )
          ) : (
            <>
              <Image
                src={typeof image === 'string' ? image : URL.createObjectURL(image)}
                alt={`Preview ${index + 1}`}
                fill
                className="rounded-lg object-cover"
              />
              <div className="absolute w-full h-full z-50 bottom-0 left-0 right-0 flex justify-center items-end opacity-0 hover:opacity-100 transition-opacity duration-200 p-2">
                <div className="flex gap-2">
                  <motion.button
                    whileTap={{ scale: 0.98 }}
                    className="bg-[#2c3e50] text-white px-4 py-2 rounded-lg shadow backdrop-blur-sm transition-all duration-200 hover:bg-[#1f2d3a]"
                    onClick={() => onInsert(typeof image === 'string' ? image : URL.createObjectURL(image))}
                  >
                    Insert
                  </motion.button>
                  <motion.button
                    whileTap={{ scale: 0.98 }}
                    className="bg-red-500 text-white px-4 py-2 rounded-lg shadow backdrop-blur-sm transition-all duration-200 hover:bg-red-600"
                    onClick={() => onDelete(index)}
                  >
                    Delete
                  </motion.button>
                </div>
              </div>
            </>
          )}
        </div>
      ))}
    </div>
  );
};

export default React.memo(
  ImagePreviewGrid,
  (prev, next) => {
    // Only re-render when visual props change
    if (prev.imageLoading !== next.imageLoading) return false;
    if (prev.loadingIndex !== next.loadingIndex) return false;
    if (prev.countDown !== next.countDown) return false;
    // Shallow compare images array contents (strings/Blobs)
    if (prev.images.length !== next.images.length) return false;
    for (let i = 0; i < prev.images.length; i++) {
      if (prev.images[i] !== next.images[i]) return false;
    }
    // Ignore handler identity changes (onInsert, onDelete)
    return true;
  }
);
