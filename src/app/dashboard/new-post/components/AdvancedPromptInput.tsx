"use client";
import React from "react";
import { motion } from "framer-motion";

interface AdvancedPromptInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onGenerateDefault: () => void;
  onGenerateRecreate: () => void;
}

const AdvancedPromptInput: React.FC<AdvancedPromptInputProps> = ({
  value,
  onChange,
  onGenerateDefault,
  onGenerateRecreate,
}) => {
  return (
    <div className="relative">
      <div className="flex flex-row items-stretch gap-2 bg-white rounded-lg shadow w-full p-2 border border-gray-300">
        <div className="relative w-full flex-1 min-w-0">
          <input
            className="w-full h-12 px-4 text-gray-700 rounded-lg bg-white focus:outline-none text-sm md:text-base"
            placeholder="Write a detailed description of the image you want to generate..."
            defaultValue={value}
            onChange={onChange}
            onKeyDown={(e) => {
              if (e.key === "Enter" && e.shiftKey) {
                e.preventDefault();
                onGenerateDefault();
              }
            }}
          />
        </div>
        <motion.button
          whileTap={{ scale: 0.98 }}
          className="h-12 px-3 flex items-center gap-1 text-gray-500 hover:text-gray-700 shrink-0 rounded-lg"
          onClick={onGenerateRecreate}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          <span className="text-sm">Recreate</span>
        </motion.button>
        <motion.button
          whileTap={{ scale: 0.98 }}
          className="h-12 px-3 md:px-4 bg-[#2c3e50] text-white rounded-lg flex flex-row items-center justify-center gap-2
                                      transition-all duration-200 hover:bg-[#1a252f] hover:shadow-md active:scale-95
                                      group relative shrink-0"
          onClick={onGenerateDefault}
        >
          <span className="text-sm md:text-base">Generate</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 md:h-5 md:w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        </motion.button>
      </div>
    </div>
  );
};

export default React.memo(AdvancedPromptInput);
