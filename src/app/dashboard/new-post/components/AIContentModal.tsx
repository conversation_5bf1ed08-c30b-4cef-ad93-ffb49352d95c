"use client";
import React, { useEffect } from "react";
import { motion, useAnimation } from "framer-motion";

interface AIContentModalProps {
  headerTabs: React.ReactNode;
  onClose: () => void;
  children: React.ReactNode;
  /**
   * A stable key representing the active tab/content.
   * When this changes, the modal content will crossfade.
   */
  contentKey?: string | number;
}

const AIContentModal: React.FC<AIContentModalProps> = ({
  headerTabs,
  onClose,
  children,
  contentKey,
}) => {
  const controls = useAnimation();

  // When contentKey changes, fade the content without unmounting children
  useEffect(() => {
    // Start from 0 then fade to 1 for a smooth crossfade effect
    controls.set({ opacity: 0 });
    controls.start({
      opacity: 1,
      transition: { duration: 0.45, ease: "easeInOut" },
    });
  }, [contentKey, controls]);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 md:p-4">
      <div className="bg-white rounded-md md:rounded-lg w-full md:w-3/4 flex flex-col h-[90vh] md:h-[80vh]">
        {/* Header */}
        <div className="flex flex-col md:flex-row p-3 md:p-4 border-b gap-2 md:gap-4">
          {/* Top bar: title + close */}
          <div className="flex flex-row items-center justify-between md:justify-start md:gap-4">
            <div className="flex items-center justify-center gap-2">
              <h2 className="text-base md:text-xl font-semibold">AI</h2>
            </div>
            <div className="md:hidden" />
            <motion.button
              whileTap={{ scale: 0.98 }}
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 transition-colors duration-200"
              aria-label="Close AI modal"
              type="button"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </motion.button>
          </div>

          {/* Tabs row */}
          <div className="tabs mt-2 md:mt-0 w-full grid grid-cols-4 gap-1 md:gap-6 md:flex md:flex-row md:w-auto pb-1.5 md:pb-0 no-scrollbar">
            {headerTabs}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-3 md:p-6 min-h-0 overflow-y-auto">
          <motion.div
            // Keep mounted; only animate opacity when contentKey changes
            initial={false}
            animate={controls}
            className="h-full"
            style={{ willChange: "opacity" }}
          >
            {children}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default AIContentModal;
