"use client";
import React from "react";
import Image from "next/image";
import Carousel from "./Carousel";
import { MediaFileLike } from "./MediaRenderer";

interface TwitterPreviewProps {
  avatarUrl: string;
  displayName: string;
  username: string;
  caption: string;
  files: MediaFileLike[];
  currentIndex: number;
  onChangeIndex: (index: number) => void;
}

const TwitterPreview: React.FC<TwitterPreviewProps> = ({
  avatarUrl,
  displayName,
  username,
  caption,
  files,
  currentIndex,
  onChangeIndex,
}) => {
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex items-center mb-4">
        <Image src={avatarUrl} alt="Profile" width={40} height={40} className="rounded-full mr-3" />
        <div>
          <h3 className="font-bold">{displayName}</h3>
          <p className="text-sm text-gray-500">@{username || "username"}</p>
        </div>
      </div>
      <p className="mb-2">{caption}</p>
      {/* Example hashtag line retained for look-and-feel */}
      <p className="text-blue-500 mb-2">#travel</p>

      {files.length > 0 && (
        <Carousel files={files} currentIndex={currentIndex} onChangeIndex={onChangeIndex} />
      )}

      <div className="text-sm text-gray-500 mb-2">
        <span>11:30 PM - 21/03/2023 - 987k view</span>
      </div>
      <div className="flex justify-between text-sm text-gray-500 mb-4">
        <span>82k Retweets</span>
        <span>45k Quotes</span>
        <span>91k Like</span>
        <span>97k Bookmarks</span>
      </div>
      <div className="flex justify-between">
        <button className="text-gray-500" aria-label="Comment">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </button>
        <button className="text-gray-500" aria-label="Retweet">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
        <button className="text-red-500" aria-label="Like">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
          </svg>
        </button>
        <button className="text-gray-500" aria-label="Bookmark">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
          </svg>
        </button>
        <button className="text-gray-500" aria-label="Share">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default TwitterPreview;
