"use client";
import React, { useState } from "react";
import Carousel from "./Carousel";
import { MediaFileLike } from "./MediaRenderer";

interface InstagramPreviewProps {
  avatarUrl: string;
  displayName: string;
  caption: string;
  files: MediaFileLike[];
  currentIndex: number;
  onChangeIndex: (index: number) => void;
}

const InstagramPreview: React.FC<InstagramPreviewProps> = ({
  avatarUrl,
  displayName,
  caption,
  files,
  currentIndex,
  onChangeIndex,
}) => {
  const [liked, setLiked] = useState(false);

  return (
    <div className="bg-white rounded-md md:rounded-lg shadow py-5 p-3 md:p-5 md:py-6 h-full flex flex-col overflow-hidden">
      <div className="flex items-center mb-3 md:mb-4 shrink-0">
        <img src={avatarUrl} alt="Profile" className="rounded-full mr-2.5 md:mr-3 w-8 h-8 md:w-10 md:h-10" />
        <h3 className="font-semibold text-sm md:text-base">{displayName}</h3>
      </div>

      <div className="grow min-h-0 flex items-start justify-center">
        {files.length > 0 ? (
          <div className="relative w-full max-w-full max-h-full aspect-square rounded-lg overflow-hidden border border-gray-200 bg-black">
            <Carousel files={files} currentIndex={currentIndex} onChangeIndex={onChangeIndex} />
          </div>
        ) : (
          <div className="relative w-full max-w-full max-h-full aspect-square rounded-lg overflow-hidden border border-dashed border-gray-200 bg-white flex items-center justify-center">
            <p className="text-xs md:text-sm text-gray-600 text-center px-4">
              Drag and drop images here, or click to select files
            </p>
          </div>
        )}
      </div>

      <div className="flex justify-between items-center mt-3 md:mt-4 pt-3 md:pt-4 border-t shrink-0">
        <div className="flex space-x-3 md:space-x-4">
          <button
            className={`transition-colors ${liked ? "text-red-500" : "text-gray-500 hover:text-red-500"}`}
            aria-label="Like"
            onClick={() => setLiked((v) => !v)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-6 w-6 lg:h-7 lg:w-7 heart-icon ${liked ? "fill-red-500" : ""}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
              />
            </svg>
          </button>
          <button className="text-gray-800" aria-label="Comment">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 lg:h-7 lg:w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </button>
          <button className="text-gray-800" aria-label="Share">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 lg:h-7 lg:w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
            </svg>
          </button>
        </div>
        <button className="text-gray-800" aria-label="Bookmark">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 lg:h-7 lg:w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
          </svg>
        </button>
      </div>

      <div className="mt-3 md:mt-4 text-xs md:text-sm text-gray-700 shrink-0">
        <p>{caption || "Your Instagram caption here"}</p>
      </div>
    </div>
  );
};

export default React.memo(InstagramPreview);
