"use client";
import React, { useEffect, useRef, useState } from "react";

interface VideoPreviewProps {
  src: string;
  className?: string;
  posterUrl?: string;
  interactive?: boolean; // when false, show only thumbnail without playback
}

// Capture a frame from the video as a data URL to use as a poster
async function capturePoster(src: string): Promise<string | undefined> {
  return new Promise((resolve) => {
    try {
      const video = document.createElement("video");
      video.preload = "auto";
      video.src = src;
      video.crossOrigin = "anonymous";
      video.muted = true;
      const onError = () => resolve(undefined);

      const seekTo = 0.1;
      const capture = () => {
        try {
          const canvas = document.createElement("canvas");
          canvas.width = video.videoWidth || 720;
          canvas.height = video.videoHeight || 720;
          const ctx = canvas.getContext("2d");
          if (!ctx) return resolve(undefined);
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
          resolve(canvas.toDataURL("image/jpeg", 0.8));
        } catch {
          resolve(undefined);
        }
      };

      video.addEventListener("loadedmetadata", () => {
        if (isFinite(video.duration) && video.duration > seekTo) {
          video.currentTime = seekTo;
        } else {
          // Some blobs report NaN, try capturing right away
          capture();
        }
      });

      video.addEventListener("seeked", capture, { once: true });
      video.addEventListener("error", onError, { once: true });
      // iOS needs a play-pause to get a frame sometimes
      video.play().then(() => video.pause()).catch(() => {/* ignore */});
    } catch {
      resolve(undefined);
    }
  });
}

const VideoPreview: React.FC<VideoPreviewProps> = ({ src, className, posterUrl, interactive = true }) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [showPlayer, setShowPlayer] = useState(false);
  const [poster, setPoster] = useState<string | undefined>(posterUrl);

  // Capture poster from the video source if none provided
  useEffect(() => {
    let active = true;
    (async () => {
      if (!posterUrl && src) {
        const p = await capturePoster(src);
        if (active) setPoster(p);
      }
    })();
    return () => { active = false; };
  }, [src, posterUrl]);

  // If the user clicks play, show the native video element with controls
  return (
    <div className={`relative w-full h-full overflow-hidden ${className ?? ""}`}>
      {!showPlayer ? (
        interactive ? (
          <button
            type="button"
            className="group w-full h-full"
            onClick={() => setShowPlayer(true)}
            aria-label="Play video"
          >
            {poster ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img src={poster} alt="Video thumbnail" className="w-full h-full object-contain bg-black" />
            ) : (
              <div className="w-full h-full bg-black" />
            )}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-14 h-14 rounded-full bg-white/90 group-hover:bg-white shadow-md flex items-center justify-center">
                <svg className="w-7 h-7 text-black" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z" />
                </svg>
              </div>
            </div>
          </button>
        ) : (
          <div className="w-full h-full relative">
            {poster ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img src={poster} alt="Video thumbnail" className="w-full h-full object-contain bg-black" />
            ) : (
              <div className="w-full h-full bg-black" />
            )}
            {/* Non-interactive play badge centered to indicate video */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-10 h-10 rounded-full bg-white/90 shadow flex items-center justify-center">
                <svg className="w-5 h-5 text-black" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z" />
                </svg>
              </div>
            </div>
          </div>
        )
      ) : (
        <video
          ref={videoRef}
          src={src}
          controls
          playsInline
          autoPlay
          className="w-full h-full object-contain bg-black rounded"
        />
      )}
    </div>
  );
};

export default VideoPreview;
