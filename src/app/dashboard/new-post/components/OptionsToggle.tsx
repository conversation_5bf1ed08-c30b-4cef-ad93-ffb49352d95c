"use client";
import React from "react";

interface OptionsToggleProps {
  selected: string | null;
  isOpen: boolean;
  onSelect: (option: "Generative" | "Generic" | "Meme" | "Quotes") => void;
  onToggle: () => void;
}

const OptionsToggle: React.FC<OptionsToggleProps> = ({ selected, isOpen, onSelect, onToggle }) => {
  return (
    <div className="flex gap-2 mb-4">
      {/* Desktop */}
      <div className="hidden md:flex flex-row justify-center items-center gap-2">
        {/* Stage 1 Button (kept commented to mirror current behavior) */}
        {/* <button onClick={onToggle} className={`h-[2.8rem] px-4 rounded-lg transition-all duration-200 bg-white text-gray-700 border z-50`}>
          <span className="text-[#1d1d1d] text-sm">{selected || 'Options'}</span>
          <svg className={`w-4 h-4 ml-1 inline-block transition-transform duration-300 ease-in-out ${isOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button> */}

        {/* Stage 2 Expanded Options */}
        <div 
          className={`flex items-center transition-all duration-500 ease-in-out overflow-hidden z-10 transform ${
            isOpen ? 'w-101 -ml-20 opacity-100 scale-100' : 'w-0 opacity-0 scale-95'
          }`}
        >
          <div className={`w-auto h-[2.8rem] bg-[#f4f4f4] rounded-[2.0625rem] border border-[#dfdfdf] overflow-hidden transform transition-transform duration-500 ease-in-out ${selected === 'Generative' ? 'pl-[14%]' : 'pl-[14%]'}`}>
            <div className="h-full w-full flex justify-center items-center px-[20px] gap-[0.3rem]">
              {(["Generative","Generic","Meme","Quotes"] as const).map((opt) => (
                <button
                  key={opt}
                  onClick={() => onSelect(opt)}
                  className={`text-center w-full h-full text-[#1d1d1d] text-sm px-2 py-1 border border-transparent hover:bg-white hover:border-[#cacaca] hover:rounded-md transition-all duration-300 ${selected === opt ? 'border-[#cacaca]! border rounded-md bg-white' : ''}`}
                >
                  {opt}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Options Dropdown */}
      <div className="md:hidden relative flex flex-col w-full z-50">
        {/* <button 
          onClick={onToggle}
          className="h-[2.8rem] px-4 rounded-lg transition-all duration-200 bg-white text-gray-700 border w-full flex items-center justify-between"
        >
          <span className="text-[#1d1d1d] text-sm">{selected || 'Options'}</span>
          <svg 
            className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button> */}

        <div 
          className={`absolute top-full left-0 right-0 bg-white border rounded-lg mt-1 shadow-lg transition-all duration-300 overflow-hidden ${
            isOpen ? 'max-h-48 opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          {(["Generative","Generic","Meme","Quotes"] as const).map((opt) => (
            <button
              key={opt}
              onClick={() => onSelect(opt)}
              className={`w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors duration-200 ${selected === opt ? 'bg-gray-50' : ''}`}
            >
              {opt}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OptionsToggle;
