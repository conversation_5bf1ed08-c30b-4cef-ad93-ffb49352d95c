"use client";
import React from "react";
import { motion } from "framer-motion";

interface BasicPromptBarProps {
  keywords: string[];
  onRemoveKeyword: (index: number) => void;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onInputKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onRecreate: () => void;
  onGenerate: () => void;
}

const BasicPromptBar: React.FC<BasicPromptBarProps> = ({
  keywords,
  onRemoveKeyword,
  onInputChange,
  onInputKeyDown,
  onRecreate,
  onGenerate,
}) => {
  return (
    <div className="bg-white rounded-lg shadow p-2 w-full border border-gray-300">
      <div className="flex flex-row items-stretch gap-2 w-full">
        <div className="relative max-w-[45%] md:max-w-[320px] overflow-hidden shrink-0">
          <div className="absolute left-[-13px] top-0 bottom-0 w-8 bg-gradient-to-r from-white to-transparent z-10"></div>
          <div className="flex overflow-x-auto gap-2 no-scrollbar">
            {keywords.map((keyword, index) => (
                <span
                  key={index}
                  className="shrink-0 px-2 py-1 bg-gray-200 rounded flex items-center text-xs md:text-sm transition-all duration-200 hover:bg-gray-300"
                >
                  {keyword}
                  <button
                    className="ml-1 text-gray-500 hover:text-gray-700 transition-colors duration-200"
                    onClick={() => onRemoveKeyword(index)}
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          </div>
          <div className="relative grow min-w-0">
            <input
              type="text"
              className="w-full h-12 px-4 text-gray-700 rounded-lg bg-white focus:outline-none text-sm md:text-base"
              placeholder="Type a keyword and press Enter..."
              onChange={onInputChange}
              onKeyDown={onInputKeyDown}
            />
          </div>
          <div className="flex items-center gap-2 shrink-0">
            <motion.button whileTap={{ scale: 0.98 }} className="h-12 px-3 flex items-center gap-1 text-gray-500 hover:text-gray-700 shrink-0 rounded-lg" onClick={onRecreate}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span className="text-sm">Recreate</span>
            </motion.button>
            <motion.button
              whileTap={{ scale: 0.98 }}
              className="h-12 px-3 md:px-4 bg-[#2c3e50] text-white rounded-lg flex flex-row items-center justify-center gap-2 transition-all duration-200 hover:bg-[#1a252f] hover:shadow-md active:scale-95 shrink-0"
              onClick={onGenerate}
            >
              <span className="text-sm md:text-base">Generate</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </motion.button>
          </div>
        </div>
    </div>
  );
};

export default React.memo(BasicPromptBar);
