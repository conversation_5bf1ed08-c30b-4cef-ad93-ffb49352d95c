"use client";
import React from "react";
import { MediaFileLike } from "./MediaRenderer";
import Carousel from "./Carousel";

interface FacebookPreviewProps {
  avatarUrl: string;
  displayName: string;
  caption: string;
  files: MediaFileLike[];
  currentIndex: number;
  onChangeIndex: (index: number) => void;
}

const FacebookPreview: React.FC<FacebookPreviewProps> = ({
  avatarUrl,
  displayName,
  caption,
  files,
  currentIndex,
  onChangeIndex,
}) => {
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex items-center mb-4">
        <img src={avatarUrl} alt="Profile" className="rounded-full mr-3 w-10 h-10" />
        <div>
          <h3 className="font-bold">{displayName}</h3>
          <p className="text-sm text-gray-500">1h · Public</p>
        </div>
      </div>

      {files.length > 0 ? (
        <Carousel files={files} currentIndex={currentIndex} onChangeIndex={onChangeIndex} />
      ) : (
        <p className="mt-2 text-sm text-gray-600 text-center">
          Drag and drop images here, or click to select files
        </p>
      )}

      <div className="mt-4 text-sm text-gray-700">
        <p>{caption || "Your Facebook post content here"}</p>
      </div>

      <div className="flex justify-between text-gray-500 border-t pt-4 mt-4">
        <button className="flex items-center" aria-label="Like">
          <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
          Like
        </button>
        <button className="flex items-center" aria-label="Comment">
          <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          Comment
        </button>
        <button className="flex items-center" aria-label="Share">
          <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
          </svg>
          Share
        </button>
      </div>
    </div>
  );
};

export default FacebookPreview;
