"use client";
import React, { useMemo } from "react";
import Image from "next/image";
import VideoPreview from "./VideoPreview";

interface AddImagesSectionProps {
  files: File[];
  isDragging: boolean;
  onDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;
  onDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;
  onDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (e: React.DragEvent<HTMLDivElement>) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  onFileInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onOpenPicker: () => void;
  onRemove: (index: number) => void;
  onOpenAI: () => void;
}

const AddImagesSection: React.FC<AddImagesSectionProps> = ({
  files,
  isDragging,
  onDragEnter,
  onDragLeave,
  onDragOver,
  onDrop,
  fileInputRef,
  onFileInputChange,
  onOpenPicker,
  onRemove,
  onOpenAI,
}) => {
  return (
    <div className="bg-white rounded-lg shadow h-full min-h-0 flex flex-col justify-between">
      <div className="p-4 border-b">
        <h2 className="text-[17px] md:text-lg font-semibold text-slate-900 tracking-tight">Add images</h2>
      </div>
      <div
        className={`p-4 flex-1 overflow-y-auto ${isDragging ? "bg-blue-100" : ""}`}
        onDragEnter={onDragEnter}
        onDragLeave={onDragLeave}
        onDragOver={onDragOver}
        onDrop={onDrop}
      >
        <input
          type="file"
          accept="image/jpeg,video/mp4"
          multiple
          ref={fileInputRef}
          className="hidden"
          onChange={onFileInputChange}
        />
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4 auto-rows-fr">
          {files.map((file, index) => (
            <MediaThumbItem
              key={(file as any).name ?? index}
              file={file}
              index={index}
              onRemove={onRemove}
            />
          ))}
          <div
            onClick={onOpenPicker}
            className="border-2 border-dashed border-gray-300 rounded-lg aspect-square flex items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
          </div>
        </div>
      </div>
      <div className="p-4 border-t flex flex-row gap-4 justify-start">
        <button
          type="button"
          className="px-3 py-1 bg-white text-gray-700 rounded border border-gray-300 hover:bg-gray-50 flex items-center justify-center text-sm"
          onClick={onOpenPicker}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
              clipRule="evenodd"
            />
          </svg>
          Drag & drop or select
        </button>
        <button
          type="button"
          className="px-3 py-1 bg-white text-gray-700 flex-row gap-2 items-center rounded border border-gray-300 hover:bg-gray-50 flex items-center justify-center text-sm"
          onClick={onOpenAI}
        >
          <span>Create Image by AI</span>
          <img src="/icons/ai.svg" alt="AI" />
        </button>
      </div>
    </div>
  );
};

export default AddImagesSection;

// Memoized individual thumbnail to avoid re-renders and re-creating object URLs
const MediaThumbItem = React.memo(function MediaThumbItem({
  file,
  index,
  onRemove,
}: {
  file: File;
  index: number;
  onRemove: (i: number) => void;
}) {
  const objectUrl = useMemo(() => URL.createObjectURL(file), [file]);

  return (
    <div className="relative aspect-square w-full">
      {file.type === "video/mp4" ? (
        <VideoPreview
          src={objectUrl}
          className="rounded-lg"
          interactive={false}
        />
      ) : (
        <div className="relative w-full h-full">
          <Image
            src={objectUrl}
            alt={`Uploaded image ${index + 1}`}
            fill
            className="rounded-lg object-cover"
          />
        </div>
      )}
      <button
        onClick={() => onRemove(index)}
        className="absolute top-2 right-2 bg-white text-gray-700 rounded-full p-1.5 shadow hover:bg-gray-100 transition-colors"
        type="button"
        aria-label="Remove image"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>
    </div>
  );
});
