"use client";
import * as React from "react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { FiChevronDown } from "react-icons/fi";
import { motion } from "framer-motion";

const OPTIONS = ["Generative", "Generic", "Meme", "Quotes"] as const;

export interface ImageTypeDropdownProps {
  value?: string; // optional: if omitted, component manages its own label internally
  onChange: (next: string) => void;
  className?: string;
}

export default React.memo(ImageTypeDropdown);

function ImageTypeDropdown({ value, onChange, className }: ImageTypeDropdownProps) {
  // Internal state for uncontrolled usage; initialize from prop or default
  const [internal, setInternal] = React.useState<string>(() => (OPTIONS.includes(value as any) ? (value as string) : "Generative"));
  // Keep internal state in sync if parent controls value
  React.useEffect(() => {
    if (typeof value === 'string' && OPTIONS.includes(value as any)) {
      setInternal(value);
    }
  }, [value]);
  const label = OPTIONS.includes((value ?? internal) as any) ? (value ?? internal) : "Generative";

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <motion.button
          whileTap={{ scale: 0.98 }}
          type="button"
          className={
            "inline-flex items-center justify-between gap-1 rounded-md  bg-white px-2 py-3 text-sm font-medium shadow-sm transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-32 overflow-hidden" +
            (className ? " " + className : "")
          }
        >
          <span className="text-gray-700 truncate">{label}</span>
          <span className="inline-flex items-center justify-center rounded-sm  p-1">
            <FiChevronDown className="h-5 w-5  text-black" />
          </span>
        </motion.button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          align="start"
          sideOffset={8}
          className="z-50 min-w-48 overflow-hidden rounded-md border border-gray-200 bg-white p-1 shadow-lg outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[side=bottom]:slide-in-from-top-1 data-[side=top]:slide-in-from-bottom-1"
        >
          {OPTIONS.map((opt) => (
            <DropdownMenu.Item
              key={opt}
              onSelect={(e) => {
                e.preventDefault();
                setInternal(opt); // update internal label immediately without requiring parent re-render
                onChange(opt);
              }}
              className={
                "flex cursor-pointer select-none items-center rounded-sm px-2 py-2 text-sm outline-none transition-colors " +
                (opt === label
                  ? " bg-[#2c3e50] text-white"
                  : " text-gray-700 hover:bg-gray-100 focus:bg-gray-100")
              }
            >
              {opt}
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
