"use client";
import React from "react";

interface TokenUsageIndicatorProps {
  tokensLabel: string; // e.g., "20 tokens per generation"
}

const TokenUsageIndicator: React.FC<TokenUsageIndicatorProps> = ({ tokensLabel }) => (
  <div className="flex items-center gap-2 text-blue-600 bg-blue-50 w-fit px-3 py-1.5 rounded-lg">
    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
    </svg>
    <span className="text-sm font-medium">{tokensLabel}</span>
  </div>
);

export default TokenUsageIndicator;
