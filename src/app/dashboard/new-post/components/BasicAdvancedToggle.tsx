"use client";
import React from "react";

interface BasicAdvancedToggleProps {
  isAdvanced: boolean;
  onBasic: () => void;
  onAdvanced: () => void;
}

const BasicAdvancedToggle: React.FC<BasicAdvancedToggleProps> = ({ isAdvanced, onBasic, onAdvanced }) => {
  return (
    <div className="flex gap-2 mb-4">
      <button
        className={`px-4 py-2 rounded-lg transition-all duration-200 ${!isAdvanced ? 'bg-[#2c3e50] text-white' : 'bg-white text-gray-700 border'}`}
        onClick={onBasic}
      >
        Basic
      </button>
      <button
        className={`px-4 py-2 rounded-lg transition-all duration-200 ${isAdvanced ? 'bg-[#2c3e50] text-white' : 'bg-white text-gray-700 border'}`}
        onClick={onAdvanced}
      >
        Advanced
      </button>
    </div>
  );
};

export default BasicAdvancedToggle;
