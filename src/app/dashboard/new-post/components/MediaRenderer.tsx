"use client";
import React, { useEffect, useMemo } from "react";
import Image from "next/image";
import VideoPreview from "./VideoPreview";

export type MediaFileLike = File | { type: string; url: string };

interface MediaRendererProps {
  file: MediaFileLike;
  className?: string;
}

function isVideo(file: MediaFileLike) {
  return file.type?.startsWith("video/");
}

function useObjectUrl(file: MediaFileLike) {
  return useMemo(() => {
    if (file instanceof File) {
      const url = URL.createObjectURL(file);
      return url;
    }
    return file.url;
  }, [file]);
}

const MediaRenderer: React.FC<MediaRendererProps> = ({ file, className }) => {
  const objectUrl = useObjectUrl(file);

  if (isVideo(file)) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <VideoPreview src={objectUrl} className={className} />
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      <Image
        src={objectUrl}
        alt="Media preview"
        fill
        className={className ?? "rounded-lg object-contain"}
        unoptimized
        sizes="100vw"
      />
    </div>
  );
};

export default React.memo(MediaRenderer);
