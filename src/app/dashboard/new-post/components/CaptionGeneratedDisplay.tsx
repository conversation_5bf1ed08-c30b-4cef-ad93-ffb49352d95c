"use client";
import React from "react";

interface CaptionGeneratedDisplayProps {
  isGenerating: boolean;
  isDevToolsOpen: boolean;
  generatedCaption: string | undefined;
}

const CaptionGeneratedDisplay: React.FC<CaptionGeneratedDisplayProps> = ({ isGenerating, isDevToolsOpen, generatedCaption }) => {
  return (
    <div className="flex flex-wrap flex-col gap-8 max-h-60 md:max-h-48 overflow-y-auto justify-center p-4 md:p-6">
      <div className="text-center text-gray-800 text-lg max-h-[10vh] overflow-y-auto " onCopy={(e) => e.preventDefault()}>
        {isGenerating ? (
          <div className="animate-pulse flex flex-wrap gap-2 justify-center">
            <div className="h-4 bg-gray-200 rounded w-24 animate-[blink_1s_ease-in-out_infinite]"></div>
            <div className="h-4 bg-gray-200 rounded w-32 animate-[blink_1s_ease-in-out_infinite]"></div>
            <div className="h-4 bg-gray-200 rounded w-28 animate-[blink_1s_ease-in-out_infinite]"></div>
          </div>
        ) : (
          <div className="select-none" onCopy={(e) => e.preventDefault()}>
            {isDevToolsOpen ? (
              <div className="text-center p-4" role="alert">Please close developer tools to view content</div>
            ) : (
              <div className="text-lg text-center">{generatedCaption || "Please insert prompt and generate caption"}</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CaptionGeneratedDisplay;
