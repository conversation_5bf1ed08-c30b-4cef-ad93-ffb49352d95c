"use client";
import React from "react";

interface CaptionActionsProps {
  onAddToPost: () => void;
  onExpand: () => void;
  onShorten: () => void;
}

const CaptionActions: React.FC<CaptionActionsProps> = ({ onAddToPost, onExpand, onShorten }) => {
  return (
    <div className="flex flex-wrap justify-center gap-2 md:gap-4 mt-6">
      <button onClick={onAddToPost} className="flex items-center gap-2 px-4 py-2 rounded-full border hover:bg-gray-50 transition-colors">
        <span>Add to Post</span>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      </button>
      <button className="flex items-center gap-2 px-4 py-2 rounded-full border hover:bg-gray-50 transition-colors" onClick={onExpand}>
        <span>Expand</span>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      <button className="flex items-center gap-2 px-4 py-2 rounded-full border hover:bg-gray-50 transition-colors" onClick={onShorten}>
        <span>Shorten</span>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
        </svg>
      </button>
    </div>
  );
};

export default CaptionActions;
