"use client";
import React from "react";
import clsx from "clsx";

export type AITabKey = "Image" | "Video" | "Caption" | "Hashtags";

export interface AITab {
  key: AITabKey;
  label: string;
}

interface AIModalTabsProps {
  activeKey: AITabKey;
  onChange: (key: AITabKey) => void;
  panes: Partial<Record<AITabKey, React.ReactNode>>;
  tabs?: AITab[];
  className?: string;
}

const DEFAULT_TABS: AITab[] = [
  { key: "Image", label: "Image" },
  { key: "Video", label: "Video" },
  { key: "Caption", label: "Caption" },
  { key: "Hashtags", label: "Hashtags" },
];

const AIModalTabs: React.FC<AIModalTabsProps> = ({
  activeKey,
  onChange,
  panes,
  tabs = DEFAULT_TABS,
  className,
}) => {
  return (
    <div className={clsx("flex h-full flex-col", className)}>
      {/* Tabs header */}
      <div className="flex items-center gap-2 rounded-xl bg-gray-50 p-1">
        {tabs.map((t) => {
          const selected = activeKey === t.key;
          return (
            <button
              key={t.key}
              type="button"
              onClick={() => onChange(t.key)}
              className={clsx(
                "flex-1 min-w-24 px-4 py-2 text-sm rounded-lg transition-all",
                selected
                  ? "bg-[#2c3e50] text-white shadow-sm"
                  : "bg-white text-gray-700 border hover:bg-gray-50"
              )}
            >
              {t.label}
            </button>
          );
        })}
      </div>

      {/* Tab content */}
      <div className="mt-4 flex-1 min-h-0">
        {panes[activeKey] ?? null}
      </div>
    </div>
  );
};

export default AIModalTabs;
