"use client";
import React from "react";
import MediaRender<PERSON>, { MediaFileLike } from "./MediaRenderer";

interface CarouselProps {
  files: MediaFileLike[];
  currentIndex: number;
  onChangeIndex: (index: number) => void;
}

const Carousel: React.FC<CarouselProps> = ({ files, currentIndex, onChangeIndex }) => {
  if (!files.length) return null;

  return (
    <div className="relative w-full h-full min-h-0 mb-4 overflow-hidden">
      <div
        className="flex h-full transition-transform duration-300 ease-in-out"
        style={{
          width: `${files.length * 100}%`,
          transform: `translateX(-${(currentIndex * 100) / files.length}%)`,
        }}
      >
        {files.map((file, index) => (
          <div
            key={index}
            className="relative w-full h-full shrink-0"
            style={{ width: `${100 / files.length}%` }}
          >
            <MediaRenderer file={file} className="object-cover" />
          </div>
        ))}
      </div>

      {files.length > 1 && (
        <>
          {currentIndex > 0 && (
            <button
              className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 p-2 rounded-full shadow-md hover:bg-white/90 transition-colors z-10"
              onClick={(e) => {
                e.stopPropagation();
                onChangeIndex(currentIndex - 1);
              }}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}

          {currentIndex < files.length - 1 && (
            <button
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 p-2 rounded-full shadow-md hover:bg-white/90 transition-colors z-10"
              onClick={(e) => {
                e.stopPropagation();
                onChangeIndex(currentIndex + 1);
              }}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}

          <div className="absolute bottom-2 left-0 right-0 flex justify-center gap-1">
            {files.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${index === currentIndex ? "bg-white" : "bg-white/50"}`}
                onClick={() => onChangeIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default Carousel;
