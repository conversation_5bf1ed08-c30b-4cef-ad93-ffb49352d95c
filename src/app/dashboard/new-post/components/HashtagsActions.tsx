"use client";
import React from "react";

interface HashtagsActionsProps {
  onAddToPost: () => void;
  onMore: () => void;
  onFewer: () => void;
}

const HashtagsActions: React.FC<HashtagsActionsProps> = ({ onAddToPost, onMore, onFewer }) => {
  return (
    <div className="flex flex-wrap justify-center gap-2 md:gap-4 mt-6">
      <button onClick={onAddToPost} className="flex items-center gap-2 px-4 py-2 rounded-full border hover:bg-gray-50 transition-colors">
        <span>Add to Post</span>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      </button>
      <button className="flex items-center gap-2 px-4 py-2 rounded-full border hover:bg-gray-50 transition-colors" onClick={onMore}>
        <span>More</span>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      </button>
      <button className="flex items-center gap-2 px-4 py-2 rounded-full border hover:bg-gray-50 transition-colors" onClick={onFewer}>
        <span>fewer</span>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
        </svg>
      </button>
    </div>
  );
};

export default HashtagsActions;
