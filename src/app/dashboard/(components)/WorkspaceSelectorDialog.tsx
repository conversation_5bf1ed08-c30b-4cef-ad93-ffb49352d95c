"use client";

import React from "react";
import RadixDialog from "~/components/ui/RadixDialog";
import { motion, AnimatePresence } from "framer-motion";
import FallbackImage from "~/components/ui/FallbackImage";

interface DashboardSocialAccount {
  platform: string;
  social_id: string;
  social_name: string;
  username?: string;
  profile_photo?: string;
}

interface DashboardWorkspaceMember {
  first_name: string;
  last_name: string;
  profile_photo?: string;
  workspaces?: Array<{
    workspace_name: string;
    role: string;
  }>;
}

export interface DashboardWorkspaceDetails {
  workspace_name: string;
  logo?: string;
  social_accounts?: DashboardSocialAccount[];
  members?: DashboardWorkspaceMember[];
  owner?: string;
}

interface WorkspaceSelectorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  workspacesDetails: DashboardWorkspaceDetails[] | undefined;
  onSelect: (workspace: DashboardWorkspaceDetails) => void;
  onAddMore: () => void;
}

const listVariants = {
  hidden: { opacity: 0, scale: 0.98 },
  visible: { opacity: 1, scale: 1, transition: { duration: 0.15 } },
};

const itemVariants = {
  hidden: { opacity: 0, y: 6 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.15 } },
};

export const WorkspaceSelectorDialog: React.FC<
  WorkspaceSelectorDialogProps
> = ({ open, onOpenChange, workspacesDetails, onSelect, onAddMore }) => {
  return (
    <RadixDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Select Active Workspace"
    >
      <p className="text-sm text-gray-600 mb-4">
        Please select the desired workspace to manage using business insight
      </p>
      <h3 className="font-medium mb-3">Choose a Workspace to continue</h3>
      <AnimatePresence initial={false}>
        <motion.div
          className="grid md:grid-cols-4 grid-cols-2 gap-4"
          variants={listVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          {workspacesDetails && workspacesDetails.length > 0 ? (
            <>
              {workspacesDetails.map((workspace, index) => (
                <motion.button
                  key={workspace.workspace_name + index}
                  onClick={() => onSelect(workspace)}
                  className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-100"
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <FallbackImage
                    src={
                      workspace.logo
                        ? workspace.logo
                        : "/default-workspace-logo.svg"
                    }
                    alt={workspace.workspace_name || "Workspace"}
                    width={48}
                    height={48}
                    className="w-12 h-12 mb-2 rounded-full aspect-square object-cover"
                  />
                  <span className="text-sm text-center">
                    {workspace.workspace_name || "Unnamed Workspace"}
                  </span>
                </motion.button>
              ))}
              {[...Array(Math.max(0, 4 - workspacesDetails.length))].map(
                (_, i) => (
                  <motion.button
                    key={`empty-${i}`}
                    onClick={onAddMore}
                    className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-100"
                    variants={itemVariants}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg
                      className="w-12 h-12 mb-2 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    <span>Add More</span>
                  </motion.button>
                )
              )}
            </>
          ) : (
            [...Array(4)].map((_, i) => (
              <motion.button
                key={`empty-${i}`}
                onClick={onAddMore}
                className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-100"
                variants={itemVariants}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <svg
                  className="w-12 h-12 mb-2 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                <span>Add More</span>
              </motion.button>
            ))
          )}
        </motion.div>
      </AnimatePresence>
    </RadixDialog>
  );
};

export default WorkspaceSelectorDialog;
