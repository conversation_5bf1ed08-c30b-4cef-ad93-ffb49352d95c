"use client";

import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";

import { FallbackImage } from "~/components/ui/FallbackImage";
import { CreatePostBtn } from "~/components/createpostbtn";

interface SidebarProps {
  isMobileMenuOpen: boolean;
  setIsMobileMenuOpen: (open: boolean) => void;
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  currentPath: string;
  hasSocialAccounts: boolean;
  unreadMessagesCount: number;
  userPic: string;
  userName: string;
  userTokens: string | number;
  onLogout: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  isMobileMenuOpen,
  setIsMobileMenuOpen,
  isExpanded,
  setIsExpanded,
  currentPath,
  hasSocialAccounts,
  unreadMessagesCount,
  userPic,
  userName,
  userTokens,
  onLogout,
}) => {
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // Track if viewport is desktop (md and up) to control animation behavior
  const [isDesktop, setIsDesktop] = useState(false);
  useEffect(() => {
    const mq = window.matchMedia("(min-width: 768px)");
    const update = () => setIsDesktop(mq.matches);
    update();
    if (mq.addEventListener) mq.addEventListener("change", update);
    else mq.addListener(update);
    return () => {
      if (mq.removeEventListener) mq.removeEventListener("change", update);
      else mq.removeListener(update);
    };
  }, []);

  return (
    <motion.aside
      ref={mobileMenuRef}
      className={`md:fixed fixed left-0 top-0 h-dvh bg-[#2c3e50] text-white overflow-hidden overflow-x-hidden box-border flex flex-col rounded-tr-2xl rounded-br-2xl px-6 md:px-6 py-2 gap-2 z-75`}
      // Desktop: animate width between collapsed/expanded; Mobile: fixed width, slide in/out
      initial={false}
      animate={{
        width: isDesktop ? (isExpanded ? 230 : 96) : 256,
        x: isDesktop ? 0 : isMobileMenuOpen ? 0 : -256,
        boxShadow: isDesktop || isMobileMenuOpen ? "0px 10px 30px rgba(0,0,0,0.3)" : "0px 0px 0px rgba(0,0,0,0)",
      }}
      transition={{ type: "spring", stiffness: 360, damping: 30, mass: 0.6 }}
      onMouseEnter={() => setIsExpanded(true)}
      onMouseLeave={() => setIsExpanded(false)}
    >
      <div
        className={`logo flex flex-row items-center overflow-hidden h-[12%] justify-start`}
      >
        <div className="flex items-center justify-center rounded-xl aspect-square  w-12 h-12 md:w-14 md:h-14 border border-transparent">
          <img
            src="/logo-sidebar.svg"
            alt=""
            className="w-8 h-8 md:w-12 md:h-12 2xl:w-14 2xl:h-14"
          />
        </div>
        <h1
          className={`font-bold text-base transition-opacity duration-300 ${
            isExpanded || isMobileMenuOpen
              ? "ml-3 md:ml-2 w-[110px] opacity-100"
              : "ml-0 md:ml-0 w-0 opacity-0"
          } text-white whitespace-nowrap overflow-hidden`}
        >
          <span className="whitespace-nowrap">
            Business <br /> Insight
          </span>
        </h1>
      </div>

      <nav className="my-auto h-auto overflow-y-auto md:overflow-y-visible">
        <ul className="flex flex-col justify-start space-y-3 md:space-y-1">
          {[
            {
              icon: "/icons/element-3.svg",
              label: "Dashboard",
              href: "/dashboard",
            },
            {
              icon: "/icons/Frame 59.svg",
              label: "Inbox",
              href: "/dashboard/inbox",
              requiresSocial: true,
              badge: unreadMessagesCount > 0,
            },
            {
              icon: "/icons/user-square.svg",
              label: "User Profile",
              href: "/dashboard/user-profile",
            },
            {
              icon: "/icons/analytics.svg",
              label: "Analytics",
              href: "/dashboard/analytics",
              requiresSocial: true,
            },
            {
              icon: "/goal.svg",
              label: "Goals",
              href: "/dashboard/goals",
              requiresSocial: true,
            },
            {
              icon: "/icons/Team.svg",
              label: "Team",
              href: "/dashboard/team",
              requiresSocial: true,
            },
            {
              icon: "/icons/planner.svg",
              label: "Planner",
              href: "/dashboard/planner",
              requiresSocial: true,
            },
            {
              icon: "/icons/setting-2.svg",
              label: "Workspace",
              href: "/dashboard/workspaces",
            },
          ]
            .filter((item) => item.href !== null)
            .map((item, index) => {
              const isActive =
                currentPath === item.href ||
                (item.href !== "/dashboard" &&
                  currentPath.startsWith(item.href));
              const isDisabled = item.requiresSocial && !hasSocialAccounts;

              return (
                <li key={index} className="relative group">
                  <motion.div
                    whileTap={{ scale: 0.96 }}
                    transition={{
                      type: "spring",
                      stiffness: 600,
                      damping: 40,
                      duration: 0.05,
                    }}
                    className={`flex items-center text-white hover:bg-[#34495e] py-1 md:py-1  rounded-lg transition-colors duration-200 ${
                      isDisabled ? "opacity-50 cursor-pointer" : ""
                    }`}
                  >
                    <Link
                      href={isDisabled ? "#" : item.href}
                      onClick={(e) => {
                        if (isDisabled) {
                          // Disabled; show tooltip on hover only
                        } else {
                          setIsMobileMenuOpen(false);
                        }
                      }}
                      className={`flex items-center w-full justify-start`}
                    >
                      <div className="relative">
                        <div
                          className={`flex items-center justify-center rounded-xl aspect-square p-1.5 md:p-1 w-11 h-11 md:w-11 md:h-11 2xl:w-12 2xl:h-12 border ${
                            isActive
                              ? "bg-gray-300/40 border-[#CCCCCC]"
                              : "border-transparent"
                          }`}
                        >
                          <img
                            src={item.icon}
                            alt={item.label}
                            className="w-7 h-7 md:w-7 md:h-7 2xl:w-8 2xl:h-8 text-white"
                          />
                        </div>
                        {item.badge && (
                          <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                      <span
                        className={`${
                          isExpanded || isMobileMenuOpen
                            ? "ml-3 md:ml-2 w-[110px] opacity-100"
                            : "ml-0 md:ml-0 w-0 opacity-0"
                        } text-base md:text-base md:leading-normal overflow-hidden transition-all duration-300 ease-in-out whitespace-nowrap flex items-center`}
                      >
                        {item.href === "/dashboard/inbox" &&
                        unreadMessagesCount > 0 ? (
                          <>
                            <span className="mr-2">Inbox</span>
                            <span className="bg-blue-500 text-white text-xs rounded-full px-2 py-0.5 flex items-center justify-center min-w-[20px] h-[20px]">
                              {unreadMessagesCount}
                            </span>
                          </>
                        ) : (
                          item.label
                        )}
                      </span>
                    </Link>
                  </motion.div>
                  {isDisabled && (
                    <div className="absolute left-full translate-x-2 translate-y-[-75%] z-50 hidden group-hover:block bg-gray-800 text-white text-sm p-2 rounded-md min-w-[200px]">
                      <div className="absolute right-full top-1/2 -rotate-90 w-0 h-0 border-l-8  border-b-8 border-l-transparent border-r-transparent border-b-gray-800"></div>
                      You should have social accounts to access this page <br />
                      <Link
                        href="/dashboard/no-social"
                        className="text-blue-400 hover:underline"
                      >
                        Add social account
                      </Link>
                    </div>
                  )}
                </li>
              );
            })}
        </ul>
      </nav>

      {/* Bottom Section */}
      <div className="shrink-0 mt-auto">
        {/* Mobile Create New Post Button */}
        <div
          className={`md:hidden mb-4 transition-opacity duration-300 ${
            isMobileMenuOpen ? "opacity-100" : "opacity-0"
          }`}
        >
          <CreatePostBtn
            link="/dashboard/new-post"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        </div>

        {/* (Removed) User Profile Section - now moved to bottom with Logout */}

        {/* Profile + Logout - Bottom Container */}
        <div className="border-t border-gray-600/30 pt-2 min-h-[120px] md:min-h-0">
          {/* Profile (Desktop) */}
          <div className="md:flex hidden items-center rounded-lg px-0 md:px-0 py-1 md:py-1 transition-colors duration-200 justify-start">
            <div className="flex items-center w-full">
              <div className="relative shrink-0">
                <div className="w-11 h-11 md:w-11 md:h-11 min-w-[44px] min-h-[44px] flex items-center aspect-square justify-center rounded-full overflow-hidden p-0 border border-transparent">
                  <FallbackImage
                    src={userPic}
                    alt="User"
                    width={72}
                    height={72}
                    className="w-full h-full object-cover rounded-full"
                  />
                </div>
              </div>
              <div
                className={`${
                  isExpanded || isMobileMenuOpen
                    ? "ml-3 md:ml-2 w-[140px] opacity-100"
                    : "ml-0 md:ml-0 w-0 opacity-0 max-h-0"
                } overflow-hidden transition-all duration-300 ease-in-out flex flex-col text-white`}
              >
                <AnimatePresence initial={false}>
                  {(isExpanded || isMobileMenuOpen) && (
                    <motion.div
                      key="profile-text-desktop"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="flex flex-col"
                    >
                      <span className="text-sm font-semibold truncate whitespace-nowrap">{userName}</span>
                      <span className="text-xs opacity-90 whitespace-nowrap">{Number(userTokens).toLocaleString()} Tokens</span>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>

          {/* Profile (Mobile) */}
          <div className="flex md:hidden items-center flex-row justify-start gap-2 rounded-lg mb-2 min-h-[48px]">
            <FallbackImage
              src={userPic}
              alt="User"
              width={40}
              height={40}
              className="w-10 h-10 min-w-[40px] min-h-[40px] shrink-0 rounded-full profile-photo aspect-square object-cover"
            />
            <div
              className={`transition-opacity duration-300 ease-in-out overflow-hidden flex flex-col w-[150px] ${
                isMobileMenuOpen ? "opacity-100" : "opacity-0 pointer-events-none"
              }`}
            >
              <AnimatePresence initial={false}>
                {isMobileMenuOpen && (
                  <motion.div
                    key="profile-text-mobile"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex flex-col"
                  >
                    <p className="font-semibold text-white truncate max-w-[150px] whitespace-nowrap">
                      {userName}
                    </p>
                    <p className="text-sm font-bold text-white whitespace-nowrap">
                      {Number(userTokens).toLocaleString()} Tokens
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Logout Button - sits higher when collapsed, moves down when expanded due to profile text block above */}
          <button
            onClick={onLogout}
            className={`flex items-center w-full text-white py-1 md:py-1 px-0 md:px-0 rounded-lg hover:bg-[#34495e] transition-colors duration-200 justify-start focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2 focus-visible:ring-offset-[#2c3e50] min-h-[44px]`}
          >
            <div className="relative">
              <div
                className={`flex items-center justify-center rounded-xl aspect-square p-1.5 md:p-1 w-11 h-11 md:w-11 md:h-11 2xl:w-12 2xl:h-12 border border-transparent`}
              >
                <img
                  src="/icons/logout.svg"
                  alt="Log Out"
                  className="w-7 h-7 md:w-7 md:h-7 2xl:w-8 2xl:h-8"
                />
              </div>
            </div>
            <span
              className={`${
                isExpanded || isMobileMenuOpen
                  ? "ml-3 md:ml-2 w-[110px] opacity-100"
                  : "ml-0 md:ml-0 w-0 opacity-0"
              } whitespace-nowrap overflow-hidden transition-all duration-300`}
            >
              Log Out
            </span>
          </button>
        </div>
      </div>
    </motion.aside>
  );
};

export default Sidebar;
