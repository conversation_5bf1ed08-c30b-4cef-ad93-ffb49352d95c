"use client";

import React from "react";
import Link from "next/link";
import { AnimatedTooltip } from "~/components/ui/animated-tooltip";
import { CreatePostBtn } from "~/components/createpostbtn";
import { WorkspaceDropdown } from "./WorkspaceDropdown";
import type { DashboardWorkspaceDetails } from "./WorkspaceSelectorDialog";

interface TooltipItem {
  id: number;
  name: string;
  designation: string;
  image: string;
}

interface SocialAccount {
  social_id: string;
  platform: string;
  social_name: string;
  profile_photo?: string;
  username?: string;
}

interface HeaderBarProps {
  selectedWorkspace?: string | null;
  workspacesDetails?: DashboardWorkspaceDetails[] | null;
  onSelectWorkspace: (ws: DashboardWorkspaceDetails) => void;
  onAddWorkspace: () => void;
  socialAccounts: SocialAccount[];
  hasSocialAccounts: boolean;
  tooltipItems: TooltipItem[];
  onGoGoals: () => void;
  onOpenTeam: () => void;
  onCreatePost: () => void;
}

const HeaderBar: React.FC<HeaderBarProps> = ({
  selectedWorkspace,
  workspacesDetails,
  onSelectWorkspace,
  onAddWorkspace,
  socialAccounts,
  hasSocialAccounts,
  tooltipItems,
  onGoGoals,
  onOpenTeam,
  onCreatePost,
}) => {
  return (
    <div className="header flex-row z-50 w-full h-[8%] items-center justify-between hidden md:flex px-2 border-b border-gray-300/50 py-8 bg-white">
      <div className="left flex-row flex items-center gap-4">
        <WorkspaceDropdown
          selectedWorkspace={selectedWorkspace ?? undefined}
          workspacesDetails={workspacesDetails ?? undefined}
          onSelectWorkspace={onSelectWorkspace}
          onAddWorkspace={onAddWorkspace}
          triggerVariant="desktop"
        />
        {/* <div className="socials flex flex-row gap-4">
          {socialAccounts?.map((account) => (
            <div key={account.social_id} className="relative">
              <img
                src={account.profile_photo ? `${account.profile_photo}` : "/default-avatar.png"}
                alt={`${selectedWorkspace ?? "workspace"} Logo`}
                className="w-9  h-9 rounded-full"
              />
              <img
                src={`/icons/${account.platform}.svg`}
                alt={account.platform}
                className="w-4 h-4 absolute -bottom-1 -right-1"
              />
            </div>
          ))}
        </div> */}
      </div>

      <div className="right flex flex-row items-center gap-4">
        {hasSocialAccounts ? (
          <img
            src="/Performance/Vector.svg"
            alt=""
            className="w-6 h-6 mr-8 cursor-pointer"
            onClick={onGoGoals}
          />
        ) : (
          <div className="group relative">
            <img
              src="/Performance/Vector.svg"
              alt=""
              className="w-6 h-6 mr-8 opacity-50 cursor-pointer"
            />
            <div className="absolute left-1/2 translate-x-[-55%] top-full mb-2 z-50 hidden group-hover:block bg-gray-800 text-white text-sm p-2 rounded-md whitespace-nowrap min-w-[200px]">
              <div className="absolute left-1/2 translate-x-[-55%] -top-2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-gray-800"></div>
              You should have social accounts to access goals <br />
              <Link
                href="/dashboard/no-social"
                className="text-blue-400 hover:underline"
              >
                Add social account
              </Link>
            </div>
          </div>
        )}

        {hasSocialAccounts ? (
          <AnimatedTooltip items={tooltipItems} />
        ) : (
          <div className="group relative">
            <div className="opacity-50 cursor-pointer">
              <AnimatedTooltip items={[]} />
            </div>
            <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 z-50 hidden group-hover:block bg-gray-800 text-white text-sm p-2 rounded-md whitespace-nowrap min-w-[200px]">
              <div className="absolute left-1/2 translate-x-[55%] -top-2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-gray-800"></div>
              You should have social accounts to manage team <br />
              <Link
                href="/dashboard/no-social"
                className="text-blue-400 hover:underline"
              >
                Add social account
              </Link>
            </div>
          </div>
        )}

        {hasSocialAccounts ? (
          <div
            onClick={onOpenTeam}
            className="w-10 h-10 -translate-x-[1.8rem] bg-gray-300 rounded-full flex items-center justify-center relative cursor-pointer hover:bg-gray-400 transition-colors -ml-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-gray-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
          </div>
        ) : (
          <div className="group relative -ml-2">
            <div className="absolute left-1/2 -translate-x-1/2 top-full mt-2 z-50 hidden group-hover:block bg-gray-800 text-white text-sm p-2 rounded-md whitespace-nowrap min-w-[200px]">
              <div className="absolute left-1/2 translate-x-[-200%] top-[-8px] w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-gray-800"></div>
              You should have social accounts to manage team <br />
              <Link
                href="/dashboard/no-social"
                className="text-blue-400 hover:underline"
              >
                Add social account
              </Link>
            </div>
            <div className="w-10 h-10 -translate-x-[1.8rem] bg-gray-300 rounded-full flex items-center justify-center relative opacity-50 cursor-not-allowed">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 text-gray-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </div>
          </div>
        )}

        <div className="hidden md:block h-8 w-px bg-gray-300 mx-4"></div>

        {hasSocialAccounts ? (
          <CreatePostBtn link="/dashboard/new-post" onClick={onCreatePost} />
        ) : (
          <div className="group relative">
            <button className="newpost text-white font-semibold py-2 px-4 rounded-full flex items-center justify-center opacity-50 cursor-pointer shrink-0 whitespace-nowrap leading-none min-h-[40px]">
              <span className="truncate">Create New Post</span>
              {/* Plus icon size comes from CreatePostBtn normally, keeping this button consistent */}
            </button>
            <div className="absolute left-1/2 -translate-x-1/2 top-full mb-2 z-50 hidden group-hover:block bg-gray-800 text-white text-sm p-2 rounded-md  max-w-[300px]">
              <div className="absolute left-1/2 -translate-x-1/2 -top-2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-gray-800"></div>
              You should have social accounts to create posts <br />
              <Link
                href="/dashboard/no-social"
                className="text-blue-400 hover:underline"
              >
                Add social account
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HeaderBar;
