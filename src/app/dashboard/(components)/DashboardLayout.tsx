"use client";

import { CreatePostBtn } from "~/components/createpostbtn";
import { useWebSocket } from "../../../hooks/useWebSocket";
import { useEffect, useState, useRef } from "react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { AnimatePresence, motion } from "framer-motion";
import { useUserStore } from "~/store/userStore";
import Loadingscreen from "../../../components/loadingscreen";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import AddSocialAccountModal from "~/components/addsocialaccountmodal";
import { showErrorToast, showSuccessToast } from "~/components/toasts";
import { useCookies } from "react-cookie";
import { signOut } from "next-auth/react";
import axios from "axios";
import type { DashboardWorkspaceDetails } from "./WorkspaceSelectorDialog";
import WorkspaceDropdown from "./WorkspaceDropdown";
import HeaderBar from "./HeaderBar";
import Sidebar from "./Sidebar";

interface SocialAccount {
  social_id: string;
  platform: string;
  social_name: string;
  profile_photo?: string;
  username?: string;
}

interface WorkspaceMember {
  first_name: string;
  last_name: string;
  profile_photo?: string;
  workspaces?: Array<{
    workspace_name: string;
    role: string;
  }>;
}

interface DashboardWorkspace {
  workspace_name: string;
  logo: string;
  social_accounts: SocialAccount[];
  members?: WorkspaceMember[];
}

interface WorkspaceDetails extends DashboardWorkspace {
  // Add any additional properties specific to workspace details
}

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { isLoading, userId } = useUserStore();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [unreadMessagesCount, setUnreadMessagesCount] = useState(0);
  
  const pathname = usePathname();
  const currentPath = pathname ?? "";
  const [showAddSocialModal, setShowAddSocialModal] = useState(false);
  

  const [cookies, setCookie, removeCookie] = useCookies();

  const { sendSelectedWorkspace, getProfile, getAnalytics, socket, getCache } =
    useWebSocket();

  const {
    email,
    firstName,
    lastName,
    profilePhoto,
    walletTokens,
    workspaces,
    plan,
    planExpireDate,
    walletValue,
    status,
    workspacesDetails,
    selectedWorkspace,
    selectedSocial,
    initialLoad,
    newMemberAdded,
    selectedWorkspaceDetails,
    getWorkspaceDone,
    clearUser,
    setUser,
  } = useUserStore();

  useEffect(() => {
    // Calculate date range for analytics (default 30 days)
    const endTime = new Date();
    const startTime = new Date();
    startTime.setDate(endTime.getDate() - 30);

    const formatDate = (date: Date) => {
      return date.toISOString().split("T")[0];
    };

    getAnalytics({
      workspace_name: selectedWorkspaceDetails.workspace_name,
      social_id: selectedSocial?.social_id,
      start_time: formatDate(startTime),
      end_time: formatDate(endTime),
    }).then((res: any) => {
      // Save analytics data to localStorage
      if (
        res &&
        res.success &&
        selectedWorkspace &&
        selectedSocial?.social_id
      ) {
        const storageKey = `analytics_${selectedWorkspace}_${selectedSocial.social_id}`;
        localStorage.setItem(
          storageKey,
          JSON.stringify({
            timestamp: new Date().getTime(),
            data: res.data,
          })
        );
      }
      console.log("Analytics", res);
    });
  }, [isLoading, selectedSocial]);

  // Load saved values from localStorage on initial render
  useEffect(() => {
    const savedHasSocialAccounts = localStorage.getItem("hasSocialAccounts");
    if (savedHasSocialAccounts) {
      setHasSocialAccounts(JSON.parse(savedHasSocialAccounts));
    }
  }, []);

  useEffect(() => {
    if (newMemberAdded) {
      showSuccessToast("New member added to your workspace", "dashboard-toast");
      setTimeout(() => {
        setUser({ newMemberAdded: false });
      }, 1000);
    }
  }, [newMemberAdded]);

  const userPic = profilePhoto || "/default-profile-pic.png";
  const userName = `${firstName} ${lastName}` || "User";
  const userTokens = walletTokens || "0";
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [socialMenuOpen, setSocialMenuOpen] = useState(false);
  

  const router = useRouter();
  const [hasSocialAccounts, setHasSocialAccounts] = useState<boolean>(false);
  const swd = (selectedWorkspaceDetails ||
    {}) as unknown as DashboardWorkspaceDetails;

  const tooltipItems =
    swd.members
      ?.filter((member) =>
        member.workspaces?.some(
          (workspace) =>
            workspace.workspace_name === selectedWorkspace &&
            workspace.role !== "owner"
        )
      )
      .map((member, index) => ({
        id: index,
        name: `${member.first_name} ${member.last_name}`,
        designation: "Team Member",
        image: member.profile_photo || "/default-avatar.png",
      })) ?? [];

  

  useEffect(() => {
    // If no workspace is selected, redirect to no-workspace page
    // if (!selectedWorkspace) {
    //     router.push('/dashboard/no-workspaces');
    //     return;
    // }

    // If workspace has no social accounts, redirect to no-social page
    // Only redirect if we're not already on the no-social page
    if (getWorkspaceDone) {
      setTimeout(() => {
        if (
          selectedWorkspace &&
          (!swd.social_accounts || swd.social_accounts.length === 0) &&
          ![
            "/dashboard/no-social",
            "/dashboard/user-profile",
            "/dashboard/workspaces",
            "/dashboard/onboarding",
          ].some((path) => window.location.pathname.includes(path)) &&
          !window.location.pathname.startsWith("/dashboard/team/") &&
          !window.location.search.includes("payment_id")
        ) {
          if (!isLoading && !currentPath.includes("/onboarding")) {
            if (
              !workspacesDetails?.length &&
              Number(walletTokens) === 0 &&
              !swd?.social_accounts?.length
            ) {
              router.push("/dashboard/onboarding");
              return;
            } else {
              showErrorToast(
                "Please add a social media account first",
                "dashboard-toast"
              );
            }
          }

          if (
            workspacesDetails?.length === 1 &&
            Number(walletTokens) === 0 &&
            !swd?.social_accounts?.length
          ) {
            router.push("/dashboard/onboarding");
            return;
          }

          router.push("/dashboard/no-social");
          return;
        }
      }, 600);

      const newHasSocialAccounts = (swd.social_accounts?.length ?? 0) > 0;
      setHasSocialAccounts(newHasSocialAccounts);

      // Save to localStorage
      localStorage.setItem(
        "hasSocialAccounts",
        JSON.stringify(newHasSocialAccounts)
      );
      // Save to sessionStorage
      sessionStorage.setItem(
        "hasSocialAccounts",
        JSON.stringify(newHasSocialAccounts)
      );
    }
  }, [
    selectedWorkspace,
    getWorkspaceDone,
    workspacesDetails,
    selectedWorkspaceDetails,
    pathname,
    workspaces,
    workspacesDetails,
  ]);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleRouteChange = () => {
      setUser({ isLoading: true, imageLoading: false, videoLoading: false });
    };

    // Check authentication only once on initial component mount
    const accessToken = sessionStorage.getItem("accessToken");
    const refreshToken = sessionStorage.getItem("refreshToken");

    if (!accessToken && !refreshToken) {
      console.log("No authentication tokens found, redirecting to login");
      router.push("/login");
      return;
    }

    // Initialize WebSocket connection if we have tokens
    if (accessToken && socket === null) {
      console.log("Initializing WebSocket connection with tokens");
      // The WebSocket connection will be handled by the useWebSocket hook
    }

    handleRouteChange();
  }, [router, socket]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(event.target as Node)
      ) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleWorkspaceSelect = (workspace: DashboardWorkspaceDetails) => {
    sendSelectedWorkspace(workspace.workspace_name);
    getProfile();
    setUser({
      selectedWorkspaceDetails: workspace as any,
      workspaceLogo: workspace.logo || "/default-workspace-logo.svg",
    });
    window.location.reload();
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };
  const handleLogout = async () => {
    try {
      // 1. Clear localStorage and sessionStorage first
      localStorage.clear();
      sessionStorage.clear();

      // 2. Clear Zustand store
      clearUser();

      // 3. Call backend logout endpoint
      try {
        await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/logout/`, {
          headers: {
            Authorization: `Bearer ${null}`,
          },
        });
      } catch (error) {
        // Continue logout even if API call fails
        console.warn(
          "Backend logout call failed, continuing with local logout"
        );
      }

      // 4. Complete next-auth signOut and wait for it to finish
      await signOut({ redirect: false });

      // 5. Only after all tokens are cleared, redirect to login
      window.location.href = "/login";
    } catch (error) {
      console.error("Error during logout:", error);
      // Force redirect to login as fallback
      window.location.href = "/login";
    }
  };

  useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        if (isLoading) {
          window.location.reload();
        }
      }, 12000);
      return () => clearTimeout(timer);
    }
  }, [isLoading]);

  // Effect to fetch unread messages from cache
  useEffect(() => {
    if (socket && selectedWorkspace) {
      // Fetch unread messages from cache
      getCache({ workspace_name: selectedWorkspace })
        .then((response: any) => {
          if (response && response.success) {
            // Process the new cache structure
            if (response.caches) {
              // Count unread messages
              let totalUnreadCount = 0;

              // Process Instagram unread messages
              const instagramNewMessages =
                response.caches.instagram?.newMessage || [];
              if (instagramNewMessages.length > 0) {
                totalUnreadCount += instagramNewMessages.length;
              }

              // Process Facebook unread messages
              const facebookNewMessages =
                response.caches.facebook?.newMessage || [];
              if (facebookNewMessages.length > 0) {
                totalUnreadCount += facebookNewMessages.length;
              }

              // Update state
              setUnreadMessagesCount(totalUnreadCount);

              console.log(`Found ${totalUnreadCount} unread messages in cache`);
            }
          }
        })
        .catch((error) => {
          console.error("Error fetching cache:", error);
        });
    }
  }, [socket, selectedWorkspace, getCache]);

  useEffect(() => {
    if (socket) {
      const handleWebSocketMessage = (event: MessageEvent) => {
        try {
          const data = JSON.parse(event.data);

          // Handle get_cache job response
          if (data.job === "get_cache" && data.caches) {
            // Count unread messages
            let totalUnreadCount = 0;

            // Process Instagram unread messages
            const instagramNewMessages =
              data.caches.instagram?.newMessage || [];
            if (instagramNewMessages.length > 0) {
              totalUnreadCount += instagramNewMessages.length;
            }

            // Process Facebook unread messages
            const facebookNewMessages = data.caches.facebook?.newMessage || [];
            if (facebookNewMessages.length > 0) {
              totalUnreadCount += facebookNewMessages.length;
            }

            // Update state
            setUnreadMessagesCount(totalUnreadCount);
          }
          // Check if it's a webhook message for new content
          else if (data.object === "instagram" && data.entry) {
            // Fetch updated cache to get accurate unread count
            if (selectedWorkspace) {
              getCache({ workspace_name: selectedWorkspace })
                .then((response: any) => {
                  if (response && response.success && response.caches) {
                    // Count unread messages
                    let totalUnreadCount = 0;

                    // Process Instagram unread messages
                    const instagramNewMessages =
                      response.caches.instagram?.newMessage || [];
                    if (instagramNewMessages.length > 0) {
                      totalUnreadCount += instagramNewMessages.length;
                    }

                    // Process Facebook unread messages
                    const facebookNewMessages =
                      response.caches.facebook?.newMessage || [];
                    if (facebookNewMessages.length > 0) {
                      totalUnreadCount += facebookNewMessages.length;
                    }

                    // Update state
                    setUnreadMessagesCount(totalUnreadCount);
                  }
                })
                .catch((error) => {
                  console.error("Error fetching cache after webhook:", error);
                });
            }
          }
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      };

      socket.addEventListener("message", handleWebSocketMessage);

      return () => {
        socket.removeEventListener("message", handleWebSocketMessage);
      };
    }
  }, [socket, selectedWorkspace, getCache]);

  useEffect(() => {
    if (pathname === "/dashboard/inbox") {
      setUnreadMessagesCount(0);
    }
  }, [pathname]);

  if (isLoading) {
    return <Loadingscreen />;
  }

  if (pathname === "/dashboard/onboarding") {
    return <main className="h-dvh w-screen">{children}</main>;
  }

  return (
    <div className="flex h-dvh w-screen">
      {/* Mobile Hamburger Menu */}
      <div className="md:hidden w-screen absolute h-[8vh] flex justify-between items-center top-0 left-0 z-50 p-4 bg-[#2c3e50] mb-4">
        <div className="flex flex-row items-center">
          <button
            onClick={toggleMobileMenu}
            className="text-white hover:text-gray-700"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>

          <WorkspaceDropdown
            selectedWorkspace={selectedWorkspace ?? undefined}
            workspacesDetails={(workspacesDetails as unknown as DashboardWorkspaceDetails[]) ?? []}
            onSelectWorkspace={handleWorkspaceSelect}
            onAddWorkspace={() => router.push("/dashboard/workspaces?mode=new")}
            triggerVariant="mobile"
          />
        </div>

        {(pathname === "/dashboard/analytics" ||
          pathname === "/dashboard" ||
          pathname === "/dashboard/new-post" ||
          pathname === "/dashboard/goals" ||
          pathname === "/dashboard/planner") && (
          <div className="flex items-center gap-2">
            <div className="relative" ref={dropdownRef}>
              <DropdownMenu.Root open={socialMenuOpen} onOpenChange={setSocialMenuOpen}>
                <DropdownMenu.Trigger asChild>
                  <button
                    className="flex items-center gap-2 text-white p-2 rounded-lg hover:bg-gray-700 max-w-[9rem] sm:max-w-none"
                    aria-label="Select social account"
                  >
                    {selectedSocial ? (
                      <div className="flex flex-row items-center gap-2">
                        <img
                          src={`/icons/${selectedSocial.platform}.svg`}
                          alt={selectedSocial.platform}
                          className="w-5 h-5"
                        />
                        <span className="text-xs sm:text-sm text-white truncate">
                          {selectedSocial.username}
                        </span>
                      </div>
                    ) : (
                      <span className="text-white text-xs sm:text-sm">Select Platform</span>
                    )}
                    <motion.svg
                      className="w-4 h-4 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                      animate={{ rotate: socialMenuOpen ? 180 : 0 }}
                      transition={{ duration: 0.15, ease: "easeOut" }}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </motion.svg>
                  </button>
                </DropdownMenu.Trigger>

                <DropdownMenu.Portal>
                  <AnimatePresence>
                    {socialMenuOpen && (
                      <DropdownMenu.Content
                        asChild
                        align="end"
                        sideOffset={6}
                      >
                        <motion.div
                          className="z-[99] w-36 sm:w-40 rounded-lg bg-[#2c3e50] text-white shadow-lg focus:outline-none"
                          initial={{ opacity: 0, scale: 0.96, y: -8 }}
                          animate={{ opacity: 1, scale: 1, y: 0 }}
                          exit={{ opacity: 0, scale: 0.98, y: -6 }}
                          transition={{ type: "spring", stiffness: 320, damping: 24, mass: 0.2 }}
                        >
                    {swd.social_accounts?.map((account: SocialAccount) => (
                      <DropdownMenu.Item
                        key={account.social_id}
                        onSelect={() => {
                          setUser({
                            selectedSocial: {
                              platform: account.platform,
                              social_id: account.social_id,
                              social_name: account.social_name,
                              username: account.username ?? "",
                            },
                          });
                          setSocialMenuOpen(false);
                        }}
                        className="flex cursor-pointer items-center gap-2 w-full select-none p-2 rounded-lg
                          text-white/90 hover:text-white
                          hover:bg-gray-500 data-[highlighted]:bg-gray-500
                          focus:outline-none focus-visible:ring-2 focus-visible:ring-white/30
                          active:bg-gray-400"
                      >
                        <img
                          src={`/icons/${account.platform}.svg`}
                          alt={account.platform}
                          className="w-5 h-5"
                        />
                        <span className="text-sm">{account.username}</span>
                      </DropdownMenu.Item>
                    ))}

                    <DropdownMenu.Item
                      onSelect={() => setShowAddSocialModal(true)}
                      className="flex cursor-pointer items-center gap-2 w-full select-none p-2 rounded-lg
                        text-white/90 hover:text-white
                        hover:bg-gray-500 data-[highlighted]:bg-gray-500
                        focus:outline-none focus-visible:ring-2 focus-visible:ring-white/30
                        active:bg-gray-400"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                        />
                      </svg>
                      <span className="text-sm">Add Social</span>
                    </DropdownMenu.Item>

                          <DropdownMenu.Arrow className="fill-[#2c3e50]" />
                        </motion.div>
                      </DropdownMenu.Content>
                    )}
                  </AnimatePresence>
                </DropdownMenu.Portal>
              </DropdownMenu.Root>
            </div>
          </div>
        )}
      </div>

      {showAddSocialModal && (
        <AddSocialAccountModal
          onClose={() => setShowAddSocialModal(false)}
          dim={true}
          onAddAccount={(platform) => {
            console.log(`Adding ${platform} account`);
            setShowAddSocialModal(false);
          }}
        />
      )}

      {/* Sidebar */}
      <div ref={mobileMenuRef}>
        <Sidebar
          isMobileMenuOpen={isMobileMenuOpen}
          setIsMobileMenuOpen={setIsMobileMenuOpen}
          isExpanded={isExpanded}
          setIsExpanded={setIsExpanded}
          currentPath={currentPath}
          hasSocialAccounts={hasSocialAccounts}
          unreadMessagesCount={unreadMessagesCount}
          userPic={userPic}
          userName={userName}
          userTokens={userTokens}
          onLogout={handleLogout}
        />
      </div>

      {/* Main content area */}
      <div className={"flex-1 flex flex-col overflow-hidden md:ml-24"}>
        {/* Header - hidden on mobile */}
        <HeaderBar
          selectedWorkspace={selectedWorkspace}
          workspacesDetails={(workspacesDetails as unknown as DashboardWorkspaceDetails[]) ?? []}
          onSelectWorkspace={handleWorkspaceSelect}
          onAddWorkspace={() => router.push("/dashboard/workspaces?mode=new")}
          socialAccounts={swd.social_accounts ?? []}
          hasSocialAccounts={hasSocialAccounts}
          tooltipItems={tooltipItems}
          onGoGoals={() => router.push("/dashboard/goals")}
          onOpenTeam={() => router.push("/dashboard/team")}
          onCreatePost={() => router.push("/dashboard/new-post")}
        />
        

        {/* Main content */}
        <main className="overflow-x-hidden overflow-y-auto bg-gray-100 h-full">
          {children}
        </main>

        {/* <ToastWrapper containerId="dashboard-toast"/> */}
      </div>
    </div>
  );
};

export default DashboardLayout;
