"use client";

import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { ChevronDownIcon, PlusIcon, CheckIcon } from "@radix-ui/react-icons";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import React from "react";
// Removed FallbackImage in favor of native img for workspace logos
import type { DashboardWorkspaceDetails } from "./WorkspaceSelectorDialog";

// Normalize a URL or path by prefixing with NEXT_PUBLIC_API_URL only when needed
const normalizeUrl = (path?: string | null): string => {
  if (!path) return "";
  // Already absolute or data URL
  if (/^https?:\/\//i.test(path) || path.startsWith("data:")) {
    return path;
  }
  const base = process.env.NEXT_PUBLIC_API_URL || "";
  if (!base) return path; // Fallback: return as-is if no base is set
  const needsSlash = !base.endsWith("/") && !path.startsWith("/");
  const dedupSlash = base.endsWith("/") && path.startsWith("/") ? path.slice(1) : path;
  return needsSlash ? `${base}/${path}` : `${base}${dedupSlash}`;
};

interface WorkspaceDropdownProps {
  selectedWorkspace?: string | null;
  workspacesDetails?: DashboardWorkspaceDetails[] | null;
  onSelectWorkspace: (ws: DashboardWorkspaceDetails) => void;
  onAddWorkspace: () => void;
  triggerVariant?: "mobile" | "desktop";
}

// Desktop-only dropdown
export const WorkspaceDropdownDesktop: React.FC<
  Omit<WorkspaceDropdownProps, "triggerVariant">
> = ({
  selectedWorkspace,
  workspacesDetails,
  onSelectWorkspace,
  onAddWorkspace,
}) => {
  // map platform to icon path in public/
  const getPlatformIcon = React.useCallback((platform?: string) => {
    const key = (platform || "").toLowerCase();
    switch (key) {
      case "facebook":
        return "/icons/facebook.svg";
      case "instagram":
        return "/icons/instagram.svg";
      case "linkedin":
        return "/icons/linkedin.svg";
      case "tiktok":
        return "/icons/tiktok.svg";
      case "twitter":
        return "/twitter.svg"; // fallback present in public/
      case "youtube":
        return "/youtube.svg"; // present in public/
      default:
        return undefined;
    }
  }, []);
  const [open, setOpen] = React.useState(false);
  const triggerRef = React.useRef<HTMLDivElement | null>(null);
  const [triggerWidth, setTriggerWidth] = React.useState<number>(0);

  React.useEffect(() => {
    const measure = () => {
      const el = triggerRef.current as HTMLElement | null;
      if (el) setTriggerWidth(el.offsetWidth);
    };
    measure();
    window.addEventListener("resize", measure);
    return () => window.removeEventListener("resize", measure);
  }, []);

  // sizes tuned so trigger matches option rows
  const triggerClass = `flex items-center justify-around gap-3 cursor-pointer px-4 py-2 rounded-lg min-w-72 bg-white text-black hover:bg-gray-50 active:bg-gray-100 border border-gray-200 shadow-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-200`;
  const contentClass =
    "rounded-lg bg-white text-black w-72 mt-1 shadow-xl border border-gray-200 z-100";
  const textClass = "text-sm";
  const plusSizeClass = "w-5 h-5";

  const selectedWs = React.useMemo(() => {
    if (!selectedWorkspace || !Array.isArray(workspacesDetails))
      return undefined;
    return workspacesDetails.find(
      (w) => w.workspace_name === selectedWorkspace
    );
  }, [selectedWorkspace, workspacesDetails]);

  return (
    <DropdownMenu.Root open={open} onOpenChange={setOpen}>
      <DropdownMenu.Trigger asChild>
        <div
          ref={triggerRef as any}
          className={`${triggerClass} ${open ? "bg-gray-50" : ""}`}
          aria-label="Workspace switcher"
          title={
            selectedWs?.workspace_name ||
            selectedWorkspace ||
            "Workspace switcher"
          }
        >
          <Image
            src={
              selectedWs?.logo
                ? normalizeUrl(selectedWs.logo)
                : "/default-workspace-logo.svg"
            }
            alt={selectedWs?.workspace_name || selectedWorkspace || "Workspace"}
            width={40}
            height={40}
            className="w-7 h-7 lg:w-9 lg:h-9 rounded-full object-cover"
          />
          <div className="flex flex-col -space-y-0.5">
            <span className="text-[10px] uppercase tracking-widest text-gray-500">
              Workspace
            </span>
            <span className="font-semibold tracking-wide text-sm text-black">
              {selectedWorkspace || "Please select a workspace"}
            </span>
          </div>
          <div className="ml-auto flex items-center gap-2">
            {Array.isArray(selectedWs?.social_accounts) &&
              selectedWs!.social_accounts.length > 0 && (
                <div className="flex items-center gap-2 mx-2">
                  {selectedWs!.social_accounts.slice(0, 5).map((sa, idx) => {
                    const icon = getPlatformIcon(sa.platform);
                    return (
                      <div key={`trigger-sa-${idx}`} className="relative w-8 h-8">
                        {sa.profile_photo ? (
                          <img
                            src={normalizeUrl(sa.profile_photo as string)}
                            alt={sa.social_name || sa.platform}
                            width={40}
                            height={40}
                            className="w-8 h-8 rounded-full  object-cover"
                            onError={(e) => {
                              const target = e.currentTarget as HTMLImageElement;
                              const name = sa.username || sa.social_name || sa.platform || "Account";
                              target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random&color=fff`;
                            }}
                          />
                        ) : (
                          <div className="w-7 h-7 rounded-full border border-gray-200 bg-gray-50 flex items-center justify-center">
                            {icon && (
                              <img
                                src={icon}
                                alt={`${sa.platform} icon`}
                                className="w-4 h-4 opacity-80"
                              />
                            )}
                          </div>
                        )}
                        {icon && (
                          <img
                            src={icon}
                            alt={`${sa.platform} icon`}
                            width={20}
                            height={20}
                            className="w-4 h-4 absolute -bottom-1 -right-1 rounded-full "
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            <motion.span
              initial={false}
              animate={{ rotate: open ? 180 : 0 }}
              transition={{ type: "spring", stiffness: 220, damping: 28, mass: 0.8 }}
              className="w-5 h-5 text-gray-700 inline-flex"
            >
              <ChevronDownIcon className="w-5 h-5 text-gray-700" />
            </motion.span>
          </div>
        </div>
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal forceMount>
        <AnimatePresence mode="wait" initial={false}>
          {open && (
            <DropdownMenu.Content
              asChild
              align="start"
              side="bottom"
              className={contentClass}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.98, y: -6 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.98, y: -6 }}
                transition={{ duration: 0.42, ease: [0.22, 1, 0.36, 1] }}
              >
                {Array.isArray(workspacesDetails) &&
                workspacesDetails.length > 0 ? (
                  workspacesDetails.map((ws) => {
                    const isSelected = ws.workspace_name === selectedWorkspace;
                    return (
                      <DropdownMenu.Item
                        key={ws.workspace_name}
                        onSelect={(e) => {
                          e.preventDefault();
                          setOpen(false);
                          onSelectWorkspace(ws);
                        }}
                        className={`w-full select-none flex items-center justify-around gap-3 px-3 py-2 rounded-none first:rounded-t-lg last:rounded-b-lg outline-none cursor-pointer transition-colors hover:bg-gray-50 data-highlighted:bg-gray-50 data-[state=highlighted]:bg-gray-50 focus:bg-gray-50 text-black ${
                          isSelected ? "bg-gray-100" : ""
                        }`}
                      >
                        <Image
                          src={
                            ws.logo ? normalizeUrl(ws.logo) : "/default-workspace-logo.svg"
                          }
                          alt={ws.workspace_name}
                          width={40}
                          height={40}
                          className={`rounded-full object-cover w-7 h-7 lg:w-9 lg:h-9`}
                        />
                        <div className="flex-1 min-w-0">
                          <span
                            className={`${textClass} ${
                              isSelected ? "font-semibold" : ""
                            } text-black truncate`}
                          >
                            {ws.workspace_name}
                          </span>
                        </div>
                        {Array.isArray(ws.social_accounts) &&
                          ws.social_accounts.length > 0 && (
                            <div className="flex items-center gap-2 mr-2">
                              {ws.social_accounts.slice(0, 5).map((sa, idx) => {
                                const icon = getPlatformIcon(sa.platform);
                                return (
                                  <div
                                    key={`${ws.workspace_name}-sa-${idx}`}
                                    className="relative w-8 h-8"
                                  >
                                    {sa.profile_photo ? (
                                      <img
                                        src={normalizeUrl(sa.profile_photo as string)}
                                        alt={sa.social_name || sa.platform}
                                        width={28}
                                        height={28}
                                        className="w-8 h-8 rounded-full  object-cover"
                                        onError={(e) => {
                                          const target = e.currentTarget as HTMLImageElement;
                                          const name = sa.username || sa.social_name || sa.platform || "Account";
                                          target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random&color=fff`;
                                        }}
                                      />
                                    ) : (
                                      <div className="w-7 h-7 rounded-full border border-gray-200 bg-gray-50 flex items-center justify-center">
                                        {icon && (
                                          <img
                                            src={icon}
                                            alt={`${sa.platform} icon`}
                                            className="w-4 h-4 opacity-80"
                                          />
                                        )}
                                      </div>
                                    )}
                                    {icon && (
                                      <img
                                        src={icon}
                                        alt={`${sa.platform} icon`}
                                        width={20}
                                        height={20}
                                        className="ww-4 h-4 absolute -bottom-1 -right-1 rounded-full "
                                      />
                                    )}
                                  </div>
                                );
                              })}
                            </div>
                          )}
                        {isSelected && (
                          <CheckIcon className={`w-4 h-4 text-black`} />
                        )}
                      </DropdownMenu.Item>
                    );
                  })
                ) : (
                  <div className={`px-2 py-1.5 text-sm text-gray-600`}>
                    No workspaces
                  </div>
                )}
                <DropdownMenu.Separator className="my-1 h-px bg-gray-200" />
                <DropdownMenu.Item
                  onSelect={(e) => {
                    e.preventDefault();
                    setOpen(false);
                    onAddWorkspace();
                  }}
                  className={`flex items-center gap-2 px-3 -mx-1 py-3.5 rounded-none first:rounded-t-lg last:rounded-b-lg outline-none cursor-pointer transition-colors hover:bg-gray-50 focus:bg-gray-50 text-black`}
                >
                  <PlusIcon className={plusSizeClass} />
                  <span className={`${textClass} text-black`}>
                    Add workspace
                  </span>
                </DropdownMenu.Item>
              </motion.div>
            </DropdownMenu.Content>
          )}
        </AnimatePresence>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
};

// Mobile-only dropdown
export const WorkspaceDropdownMobile: React.FC<
  Omit<WorkspaceDropdownProps, "triggerVariant">
> = ({
  selectedWorkspace,
  workspacesDetails,
  onSelectWorkspace,
  onAddWorkspace,
}) => {
  const [open, setOpen] = React.useState(false);
  const triggerRef = React.useRef<HTMLButtonElement | null>(null);
  const [triggerWidth, setTriggerWidth] = React.useState<number>(0);

  React.useEffect(() => {
    const measure = () => {
      const el = triggerRef.current as HTMLElement | null;
      if (el) setTriggerWidth(el.offsetWidth);
    };
    measure();
    window.addEventListener("resize", measure);
    return () => window.removeEventListener("resize", measure);
  }, []);

  const triggerClass = `text-white text-xs text-left ml-2 lg:ml-1 text-nowrap font-semibold flex items-center gap-2 px-2 py-1.5 rounded-md transition-colors bg-transparent border-0 hover:bg-white/10`;
  const contentClass =
    "rounded-lg bg-[#2c3e50] text-white p-1 mt-2 lg:mt-4 shadow-xl border border-white/10 z-100";
  const textClass = "text-xs";
  const plusSizeClass = "w-4 h-4";

  const selectedWs = React.useMemo(() => {
    if (!selectedWorkspace || !Array.isArray(workspacesDetails))
      return undefined;
    return workspacesDetails.find(
      (w) => w.workspace_name === selectedWorkspace
    );
  }, [selectedWorkspace, workspacesDetails]);

  return (
    <DropdownMenu.Root open={open} onOpenChange={setOpen}>
      <DropdownMenu.Trigger asChild>
        <button
          ref={triggerRef as any}
          className={`${triggerClass} ${open ? "bg-white/10" : ""}`}
          aria-label="Workspace switcher"
          title={
            selectedWs?.workspace_name ||
            selectedWorkspace ||
            "Workspace switcher"
          }
        >
          <Image
            src={
              selectedWs?.logo
                ? normalizeUrl(selectedWs.logo)
                : "/default-workspace-logo.svg"
            }
            alt={selectedWs?.workspace_name || selectedWorkspace || "Workspace"}
            width={32}
            height={32}
            className="w-7 h-7 lg:w-9 lg:h-9 rounded-full object-cover"
          />
          {selectedWorkspace || "Please select a workspace"}
          <motion.span
            initial={false}
            animate={{ rotate: open ? 180 : 0 }}
            transition={{ type: "spring", stiffness: 220, damping: 28, mass: 0.8 }}
            className="inline-flex"
          >
            <ChevronDownIcon className="w-4 h-4 text-white" />
          </motion.span>
        </button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal forceMount>
        <AnimatePresence mode="wait" initial={false}>
          {open && (
            <DropdownMenu.Content
              asChild
              align="start"
              side="bottom"
              className={contentClass}
              style={{ width: triggerWidth || undefined }}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.98, y: -6 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.98, y: -6 }}
                transition={{ duration: 0.42, ease: [0.22, 1, 0.36, 1] }}
              >
                {Array.isArray(workspacesDetails) &&
                workspacesDetails.length > 0 ? (
                  workspacesDetails.map((ws) => {
                    const isSelected = ws.workspace_name === selectedWorkspace;
                    return (
                      <DropdownMenu.Item
                        key={ws.workspace_name}
                        onSelect={(e) => {
                          e.preventDefault();
                          setOpen(false);
                          onSelectWorkspace(ws);
                        }}
                        className={`w-full select-none flex items-center justify-between gap-3 px-3 py-2.5 rounded-none first:rounded-t-lg last:rounded-b-lg outline-none cursor-pointer hover:bg-white/10 data-highlighted:bg-white/10 data-[state=highlighted]:bg-white/10 focus:bg-white/10 text-white transition-colors ${
                          isSelected ? "bg-white/15" : ""
                        }`}
                      >
                        <Image
                          src={
                            ws.logo ? normalizeUrl(ws.logo) : "/default-workspace-logo.svg"
                          }
                          alt={ws.workspace_name}
                          width={32}
                          height={32}
                          className={`rounded-full object-cover w-7 h-7 lg:w-9 lg:h-9`}
                        />
                        <span
                          className={`${textClass} ${
                            isSelected ? "font-semibold" : ""
                          }`}
                        >
                          {ws.workspace_name}
                        </span>
                        {isSelected && (
                          <CheckIcon className="w-5 h-5 ml-auto text-white" />
                        )}
                      </DropdownMenu.Item>
                    );
                  })
                ) : (
                  <div className="px-2 py-1.5 text-sm text-white/80">
                    No workspaces
                  </div>
                )}
                <DropdownMenu.Separator className="my-1 h-0.5 bg-white/15" />
                <DropdownMenu.Item
                  onSelect={(e) => {
                    e.preventDefault();
                    setOpen(false);
                    onAddWorkspace();
                  }}
                  className={`flex items-center gap-2 px-3 -mx-1 py-2 rounded-none first:rounded-t-lg last:rounded-b-lg outline-none cursor-pointer hover:bg-white/10 focus:bg-white/10 text-white transition-colors`}
                >
                  <PlusIcon className={plusSizeClass} />
                  <span className={textClass}>Add workspace</span>
                </DropdownMenu.Item>
              </motion.div>
            </DropdownMenu.Content>
          )}
        </AnimatePresence>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
};

// Thin wrapper that decides which variant to render
export const WorkspaceDropdown: React.FC<WorkspaceDropdownProps> = ({
  triggerVariant = "desktop",
  ...props
}) => {
  if (triggerVariant === "mobile") {
    return <WorkspaceDropdownMobile {...props} />;
  }
  return <WorkspaceDropdownDesktop {...props} />;
};

export default WorkspaceDropdown;
