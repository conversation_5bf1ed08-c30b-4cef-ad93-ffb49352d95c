// Re-export existing types
export * from "./auth";
export * from "./workspace";

// Export new drag-and-drop and chart types
export * from "./drag-drop";
export * from "./chart-types";

// Type guards and utility functions
export const isDragDropError = (
  error: any
): error is import("./drag-drop").DragDropError => {
  return (
    error && typeof error === "object" && "type" in error && "message" in error
  );
};

export const isChartError = (
  error: any
): error is import("./chart-types").ChartError => {
  return (
    error &&
    typeof error === "object" &&
    "type" in error &&
    "message" in error &&
    "timestamp" in error
  );
};

export const isValidWidgetSize = (
  size: any
): size is import("./drag-drop").WidgetSize => {
  return (
    size &&
    typeof size === "object" &&
    typeof size.width === "number" &&
    typeof size.height === "number" &&
    size.width > 0 &&
    size.height > 0
  );
};

export const isValidGridPosition = (
  position: any
): position is import("./drag-drop").GridPosition => {
  return (
    position &&
    typeof position === "object" &&
    typeof position.row === "number" &&
    typeof position.col === "number" &&
    position.row >= 0 &&
    position.col >= 0
  );
};
