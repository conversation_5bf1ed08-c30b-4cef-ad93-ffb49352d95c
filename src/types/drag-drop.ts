import { ReactNode } from "react";

// Core drag and drop types
export interface DragDropContextType {
  widgets: WidgetLayout[];
  moveWidget: (dragId: string, hoverId: string) => void;
  addWidget: (widget: WidgetConfig) => void;
  removeWidget: (widgetId: string) => void;
  updateWidgetSize: (widgetId: string, size: WidgetSize) => void;
  updateWidgetPosition: (widgetId: string, position: GridPosition) => void;
  isDragging: boolean;
  draggedWidget: string | null;
}

// Widget layout and positioning
export interface WidgetLayout {
  id: string;
  type: string;
  position: GridPosition;
  size: WidgetSize;
  config: WidgetConfig;
  zIndex?: number;
}

export interface GridPosition {
  row: number;
  col: number;
}

export interface WidgetSize {
  width: number; // Grid columns (1-12)
  height: number; // Grid rows
}

export interface ResponsiveWidgetSize {
  xs: WidgetSize;
  sm: WidgetSize;
  md: WidgetSize;
  lg: WidgetSize;
  xl: WidgetSize;
}

// Widget configuration and registry
export interface WidgetConfig {
  type: string;
  title: string;
  description?: string;
  props: Record<string, any>;
  minSize: WidgetSize;
  maxSize: WidgetSize;
  defaultSize: WidgetSize;
  category: string;
  tags?: string[];
}

export interface WidgetRegistryItem {
  component: React.ComponentType<any>;
  config: WidgetConfig;
  preview: React.ComponentType<any>;
  category: string;
  isAvailable: boolean;
}

export interface WidgetRegistry {
  [key: string]: WidgetRegistryItem;
}

// Grid system types
export interface GridSystemProps {
  columns: number; // Default 12-column grid
  gap: string; // Grid gap
  children: ReactNode;
  onLayoutChange?: (layout: WidgetLayout[]) => void;
  className?: string;
}

export interface GridItemProps {
  id: string;
  position: GridPosition;
  size: WidgetSize;
  isDragging?: boolean;
  isDropTarget?: boolean;
  children: ReactNode;
  className?: string;
}

export interface GridConfig {
  columns: number;
  gap: string;
  breakpoints: Record<string, number>;
  minRowHeight: number;
}

// Draggable widget types
export interface DraggableWidgetProps {
  id: string;
  children: ReactNode;
  isDragDisabled?: boolean;
  dragHandle?: boolean;
  onDragStart?: (id: string) => void;
  onDragEnd?: (id: string) => void;
  className?: string;
}

export interface DragPreviewProps {
  widget: WidgetLayout;
  isDragging: boolean;
}

// Drop zone types
export interface DropZoneProps {
  id: string;
  position: GridPosition;
  size: WidgetSize;
  isActive: boolean;
  isOver: boolean;
  canDrop: boolean;
  onDrop: (draggedId: string, targetPosition: GridPosition) => void;
  className?: string;
}

export interface DropZoneHighlight {
  position: GridPosition;
  size: WidgetSize;
  isValid: boolean;
}

// Layout persistence types
export interface DashboardLayout {
  id: string;
  userId?: string;
  name: string;
  widgets: WidgetLayout[];
  gridConfig: GridConfig;
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

export interface LayoutPersistenceOptions {
  autoSave: boolean;
  debounceMs: number;
  storageKey: string;
  enableVersioning: boolean;
}

// Action types for drag-drop reducer
export type DragDropAction =
  | { type: "MOVE_WIDGET"; payload: { dragId: string; hoverId: string } }
  | {
      type: "ADD_WIDGET";
      payload: { widget: WidgetConfig; position?: GridPosition };
    }
  | { type: "REMOVE_WIDGET"; payload: { widgetId: string } }
  | {
      type: "UPDATE_WIDGET_SIZE";
      payload: { widgetId: string; size: WidgetSize };
    }
  | {
      type: "UPDATE_WIDGET_POSITION";
      payload: { widgetId: string; position: GridPosition };
    }
  | {
      type: "SET_DRAGGING";
      payload: { isDragging: boolean; draggedWidget: string | null };
    }
  | { type: "LOAD_LAYOUT"; payload: { layout: WidgetLayout[] } }
  | { type: "RESET_LAYOUT" }
  | { type: "UNDO_LAYOUT" }
  | { type: "REDO_LAYOUT" };

export interface DragDropState {
  widgets: WidgetLayout[];
  isDragging: boolean;
  draggedWidget: string | null;
  history: WidgetLayout[][];
  historyIndex: number;
  maxHistorySize: number;
}

// Error handling types
export interface DragDropError {
  type: "DRAG_ERROR" | "DROP_ERROR" | "LAYOUT_ERROR" | "PERSISTENCE_ERROR";
  message: string;
  widgetId?: string;
  position?: GridPosition;
  originalError?: Error;
}

export interface DragDropErrorHandler {
  onDragError: (error: DragDropError) => void;
  onDropError: (error: DragDropError) => void;
  onLayoutError: (error: DragDropError) => void;
  fallbackPosition: (widgetId: string) => GridPosition;
}

// Collision detection types
export interface CollisionDetection {
  checkCollision: (
    widget: WidgetLayout,
    otherWidgets: WidgetLayout[]
  ) => boolean;
  resolveCollision: (
    widget: WidgetLayout,
    otherWidgets: WidgetLayout[]
  ) => WidgetLayout[];
  findNearestValidPosition: (
    widget: WidgetLayout,
    gridConfig: GridConfig
  ) => GridPosition;
}

// Animation and visual feedback types
export interface DragAnimationConfig {
  duration: number;
  easing: string;
  scale: number;
  opacity: number;
  zIndex: number;
}

export interface DropAnimationConfig {
  duration: number;
  easing: string;
  highlightColor: string;
  borderWidth: string;
}

// Widget modal types
export interface WidgetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectWidget: (widgetType: string) => void;
  availableWidgets: string[];
  currentWidgets: string[];
  categories?: string[];
  searchQuery?: string;
}

export interface WidgetPreviewProps {
  widgetType: string;
  config: WidgetConfig;
  isSelected?: boolean;
  onClick: () => void;
  className?: string;
}

// Performance optimization types
export interface LazyLoadingConfig {
  enabled: boolean;
  threshold: number;
  rootMargin: string;
  priority: "high" | "medium" | "low";
}

export interface VirtualizationConfig {
  enabled: boolean;
  itemHeight: number;
  overscan: number;
  windowSize: number;
}

// Utility types
export type WidgetCategory =
  | "analytics"
  | "demographics"
  | "performance"
  | "engagement"
  | "custom";

export type GridBreakpoint = "xs" | "sm" | "md" | "lg" | "xl";

export type DragDirection = "up" | "down" | "left" | "right";

export type LayoutMode = "edit" | "view" | "preview";

// Event handler types
export interface DragEventHandlers {
  onDragStart?: (event: DragStartEvent) => void;
  onDragMove?: (event: DragMoveEvent) => void;
  onDragEnd?: (event: DragEndEvent) => void;
  onDragCancel?: (event: DragCancelEvent) => void;
}

// Re-export @dnd-kit types for convenience
import type {
  DragStartEvent,
  DragMoveEvent,
  DragEndEvent,
  DragCancelEvent,
  DragOverEvent,
  UniqueIdentifier,
  Active,
  Over,
} from "@dnd-kit/core";

import type {
  SortableContext as DndSortableContext,
  SortingStrategy,
} from "@dnd-kit/sortable";

export type {
  DragStartEvent,
  DragMoveEvent,
  DragEndEvent,
  DragCancelEvent,
  DragOverEvent,
  UniqueIdentifier,
  Active,
  Over,
  DndSortableContext,
  SortingStrategy,
};
