import { ReactNode } from "react";
import { WidgetSize, GridPosition } from "./drag-drop";

// Enhanced BaseChart component types
export interface BaseChartProps {
  title: string;
  description?: string;
  value?: string | number;
  children: ReactNode;
  actions?: ReactNode;
  className?: string;
  selectedSocial?: SocialAccount;

  // New responsive props for drag-and-drop
  aspectRatio?: number; // width/height ratio
  minHeight?: string;
  maxHeight?: string;
  responsive?: boolean;

  // Drag and drop props
  isDraggable?: boolean;
  dragHandle?: boolean;
  widgetId?: string;
  onResize?: (size: WidgetSize) => void;
  onMove?: (position: GridPosition) => void;

  // Loading and error states
  isLoading?: boolean;
  error?: Error | null;
  onRetry?: () => void;
}

// Chart configuration types
export interface ChartConfig {
  type: "line" | "bar" | "pie" | "area" | "scatter" | "radar";
  responsive: boolean;
  aspectRatio?: number;
  colors: string[];
  animations: boolean;
  theme?: "light" | "dark";
  grid?: boolean;
  legend?: boolean;
  tooltip?: boolean;
}

// Chart data types
export interface ChartDataPoint {
  timestamp: string;
  value: number;
  label?: string;
  metadata?: Record<string, any>;
}

export interface ChartSeries {
  name: string;
  data: ChartDataPoint[];
  color?: string;
  type?: ChartConfig["type"];
}

export interface ChartData {
  series: ChartSeries[];
  categories?: string[];
  title?: string;
  subtitle?: string;
  xAxisLabel?: string;
  yAxisLabel?: string;
}

// Chart error handling
export interface ChartError {
  type: "DATA_ERROR" | "RENDER_ERROR" | "CONFIG_ERROR" | "NETWORK_ERROR";
  message: string;
  details?: any;
  timestamp: Date;
}

export interface ChartErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ChartErrorFallbackProps>;
  onError?: (error: ChartError, errorInfo: React.ErrorInfo) => void;
}

export interface ChartErrorFallbackProps {
  error: ChartError;
  retry: () => void;
  resetError: () => void;
}

// Chart loading states
export interface ChartLoadingProps {
  type?: "skeleton" | "spinner" | "pulse";
  height?: string;
  className?: string;
}

// Import existing SocialAccount type from workspace
import type { SocialAccount } from "./workspace";

// Responsive chart sizing
export interface ResponsiveChartConfig {
  breakpoints: {
    xs: { width: number; height: number };
    sm: { width: number; height: number };
    md: { width: number; height: number };
    lg: { width: number; height: number };
    xl: { width: number; height: number };
  };
  maintainAspectRatio: boolean;
  minWidth: number;
  minHeight: number;
  maxWidth?: number;
  maxHeight?: number;
}

// Chart performance optimization
export interface ChartPerformanceConfig {
  lazyLoading: boolean;
  virtualization: boolean;
  debounceResize: number;
  throttleAnimation: number;
  maxDataPoints: number;
}

// Chart accessibility
export interface ChartAccessibilityConfig {
  ariaLabel?: string;
  ariaDescription?: string;
  keyboardNavigation: boolean;
  highContrast: boolean;
  screenReaderSupport: boolean;
}

// Chart export options
export interface ChartExportOptions {
  format: "png" | "jpg" | "svg" | "pdf";
  width?: number;
  height?: number;
  quality?: number;
  filename?: string;
}

// Chart interaction types
export interface ChartInteractionConfig {
  zoom: boolean;
  pan: boolean;
  selection: boolean;
  crosshair: boolean;
  tooltip: boolean;
  legend: boolean;
}

export interface ChartInteractionEvent {
  type: "click" | "hover" | "zoom" | "pan" | "select";
  data: any;
  position: { x: number; y: number };
  timestamp: Date;
}
