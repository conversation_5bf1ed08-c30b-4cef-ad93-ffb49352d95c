export interface SocialAccount {
    social_id: string;
    platform: string;
    social_name: string;
}

export interface Workspace {
    workspace_name: string;
    social_accounts: SocialAccount[];
}

export interface WorkspaceState {
    workspaces: Workspace[];
    selectedWorkspace: string;
    selectedSocial: SocialAccount | null;
}

export interface WorkspaceDetails {
    id: string;
    workspace_name: string;
    logo: string;
    social_accounts: SocialAccount[];
    industry: string;
    website: string;
    workspace_members: string;
    created_at: string;
    last_activity: string;
    social_media_count: string | number;
} 