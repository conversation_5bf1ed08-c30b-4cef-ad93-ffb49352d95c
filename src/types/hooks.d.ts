declare module "../../../hooks/useWorkspace" {
  export function useWorkspace(): {
    selectedWorkspace: string;
    selectedWorkspaceDetails: any;
    selectedSocial: any;
    workspaceLogo: string;
    workspaces: string[];
    workspacesDetails: any[];
  };
}

declare module "../../../hooks/useLogger" {
  export function useLogger(): {
    log: (message: string, data?: any) => void;
    error: (message: string, error?: any) => void;
    warn: (message: string, data?: any) => void;
    info: (message: string, data?: any) => void;
  };
}

declare module "../../../hooks/useToast" {
  export function useToast(): {
    showToast: (message: string, type?: 'success' | 'error' | 'info') => void;
  };
}

declare module "../../../hooks/useApi" {
  export function useApi(): {
    getDirectConversations: (params: any) => Promise<any>;
    getDirectMessages: (params: any) => Promise<any>;
    sendDirectMessage: (params: any) => Promise<any>;
    getComments: (params: any) => Promise<any>;
    getPostComments: (params: any) => Promise<any>;
    replyToComment: (params: any) => Promise<any>;
    getCache: (params: any) => Promise<any>;
    seenCache: (params: any) => Promise<any>;
  };
}
