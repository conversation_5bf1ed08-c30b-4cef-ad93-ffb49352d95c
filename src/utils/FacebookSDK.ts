// Add Facebook SDK types
declare global {
  interface Window {
    FB: {
      init: (params: {
        appId: string;
        cookie: boolean;
        xfbml: boolean;
        version: string;
      }) => void;
      login: (
        callback: (response: FacebookLoginResponse) => void,
        params: { scope: string; return_scopes: boolean }
      ) => void;
      getLoginStatus: (
        callback: (response: FacebookLoginResponse) => void
      ) => void;
      api: (
        path: string,
        params: any,
        callback: (response: any) => void
      ) => void;
    };
    fbAsyncInit: () => void;
  }
}

export interface FacebookLoginResponse {
  status: "connected" | "not_authorized" | "unknown";
  authResponse?: {
    accessToken: string;
    userID: string;
    expiresIn: number;
    signedRequest: string;
    graphDomain: string;
    data_access_expiration_time: number;
  };
}

export interface FacebookUserData {
  id: string;
  name: string;
  email: string;
  picture?: {
    data: {
      url: string;
      width: number;
      height: number;
    };
  };
}

export interface FacebookLoginResult {
  accessToken: string;
  userId: string;
}

export const initFacebookSdk = (): Promise<void> => {
  console.log("🚀 Initializing Facebook SDK...");
  return new Promise<void>((resolve, reject) => {
    // Check if SDK is already loaded
    if (window.FB) {
      console.log("✅ Facebook SDK already loaded");
      resolve();
      return;
    }

    // Load the Facebook SDK asynchronously
    window.fbAsyncInit = function () {
      try {
        const appId = process.env.NEXT_PUBLIC_FACEBOOK_APP_ID;
        console.log("📱 Facebook App ID:", appId);

        if (!appId) {
          console.error(
            "❌ Facebook App ID is missing in environment variables"
          );
          throw new Error(
            "Facebook App ID is not configured in environment variables"
          );
        }

        window.FB.init({
          appId,
          cookie: true,
          xfbml: true,
          version: "v21.0",
        });
        console.log("✅ Facebook SDK initialized successfully");

        // Check if user is already logged in
        window.FB.getLoginStatus((response: FacebookLoginResponse) => {
          console.log("🔍 Facebook login status:", response);
          if (response.status === "connected") {
            console.log("✅ User already logged in to Facebook");
          }
          resolve();
        });
      } catch (error) {
        console.error("❌ Error initializing Facebook SDK:", error);
        reject(error);
      }
    };

    // Load the SDK
    try {
      console.log("📥 Loading Facebook SDK script...");
      const script = document.createElement("script");
      script.async = true;
      script.defer = true;
      script.crossOrigin = "anonymous";
      script.src = "https://connect.facebook.net/en_US/sdk.js";
      script.onerror = (error) => {
        console.error("❌ Error loading Facebook SDK script:", error);
        reject(error);
      };
      document.head.appendChild(script);
      console.log("✅ Facebook SDK script added to document");
    } catch (error) {
      console.error("❌ Error loading Facebook SDK script:", error);
      reject(error);
    }
  });
};

export const handleFacebookLogin =
  async (): Promise<FacebookLoginResult | null> => {
    console.log("🔑 Initiating Facebook login...");
    return new Promise((resolve, reject) => {
      try {
        window.FB.login(
          (response: FacebookLoginResponse) => {
            console.log("📡 Facebook login response:", response);

            if (response.status === "connected" && response.authResponse) {
              console.log("✅ Facebook login successful");
              console.log(
                "🎫 Access Token:",
                response.authResponse.accessToken
              );
              console.log("👤 User ID:", response.authResponse.userID);

              resolve({
                accessToken: response.authResponse.accessToken,
                userId: response.authResponse.userID,
              });
            } else {
              console.error("❌ Facebook login failed:", response);
              resolve(null);
            }
          },
          {
            scope: "email,public_profile,commerce_account_read_orders",
            return_scopes: true,
          }
        );
      } catch (error) {
        console.error("❌ Error during Facebook login:", error);
        reject(error);
      }
    });
  };

export const getFacebookUserData = async (
  accessToken: string
): Promise<FacebookUserData | null> => {
  console.log("👤 Fetching Facebook user data...");
  console.log("🎫 Using access token:", accessToken);

  return new Promise((resolve, reject) => {
    try {
      window.FB.api(
        "/me",
        {
          fields: "id,name,email,picture",
          access_token: accessToken,
        },
        (response: FacebookUserData | { error: any }) => {
          if ("error" in response) {
            console.error("❌ Error getting user data:", response.error);
            reject(response.error);
          } else {
            console.log("✅ Successfully fetched user data:", response);
            resolve(response);
          }
        }
      );
    } catch (error) {
      console.error("❌ Error calling Facebook API:", error);
      reject(error);
    }
  });
};

// Helper function to validate Facebook response
export const validateFacebookResponse = (response: any): boolean => {
  console.log("🔍 Validating Facebook response:", response);
  const isValid =
    response &&
    response.status === "connected" &&
    response.authResponse &&
    response.authResponse.accessToken &&
    response.authResponse.userID;
  console.log("✅ Response validation result:", isValid);
  return isValid;
};
