export function parseToDate(value: unknown): Date | null {
  if (value === null || value === undefined) return null;
  if (value instanceof Date) return isNaN(value.getTime()) ? null : value;

  if (typeof value === "number") {
    const milliseconds = value > 1e12 ? value : value * 1000;
    const date = new Date(milliseconds);
    return isNaN(date.getTime()) ? null : date;
  }

  if (typeof value === "string") {
    const trimmed = value.trim();
    if (!trimmed) return null;

    // Numeric string (timestamp seconds or ms)
    const numeric = Number(trimmed);
    if (!Number.isNaN(numeric) && /^(\d{10}|\d{13})$/.test(trimmed)) {
      const milliseconds = trimmed.length === 13 ? numeric : numeric * 1000;
      const date = new Date(milliseconds);
      return isNaN(date.getTime()) ? null : date;
    }

    // Normalize common SQL format "YYYY-MM-DD HH:mm:ss" to ISO-like
    const normalized = trimmed.includes("T")
      ? trimmed
      : trimmed.replace(" ", "T");
    const date = new Date(normalized);
    return isNaN(date.getTime()) ? null : date;
  }

  return null;
}

export function formatDateDisplay(
  value: unknown,
  options?: { fallback?: string; locale?: string }
): string {
  const fallback = options?.fallback ?? "N/A";
  const locale = options?.locale ?? "en-CA";
  const date = parseToDate(value);
  if (!date) return fallback;
  try {
    // en-CA gives YYYY-MM-DD by default
    const formatted = new Intl.DateTimeFormat(locale, {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    }).format(date);
    // Replace dashes with slashes to match existing UI style
    return formatted.replace(/-/g, "/");
  } catch {
    return fallback;
  }
}
