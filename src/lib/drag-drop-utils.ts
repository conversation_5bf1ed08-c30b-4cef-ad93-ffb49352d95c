import {
  WidgetLayout,
  WidgetSize,
  Grid<PERSON><PERSON>ition,
  GridConfig,
  CollisionDetection,
  DragDropError,
} from "@/types";
import {
  WIDGET_SIZE_CONSTRAINTS,
  GRID_POSITION_CONSTRAINTS,
  DEFAULT_GRID_CONFIG,
} from "./drag-drop-config";

/**
 * Validates if a widget size is within acceptable constraints
 */
export const isValidWidgetSize = (size: WidgetSize): boolean => {
  return (
    size.width >= WIDGET_SIZE_CONSTRAINTS.MIN_WIDTH &&
    size.width <= WIDGET_SIZE_CONSTRAINTS.MAX_WIDTH &&
    size.height >= WIDGET_SIZE_CONSTRAINTS.MIN_HEIGHT &&
    size.height <= WIDGET_SIZE_CONSTRAINTS.MAX_HEIGHT
  );
};

/**
 * Validates if a grid position is within acceptable bounds
 */
export const isValidGridPosition = (
  position: GridPosition,
  gridConfig: GridConfig = DEFAULT_GRID_CONFIG
): boolean => {
  return (
    position.row >= GRID_POSITION_CONSTRAINTS.MIN_ROW &&
    position.col >= GRID_POSITION_CONSTRAINTS.MIN_COL &&
    position.col <= GRID_POSITION_CONSTRAINTS.MAX_COL
  );
};

/**
 * Checks if two widgets overlap based on their positions and sizes
 */
export const doWidgetsOverlap = (
  widget1: WidgetLayout,
  widget2: WidgetLayout
): boolean => {
  const w1End = {
    row: widget1.position.row + widget1.size.height - 1,
    col: widget1.position.col + widget1.size.width - 1,
  };

  const w2End = {
    row: widget2.position.row + widget2.size.height - 1,
    col: widget2.position.col + widget2.size.width - 1,
  };

  return !(
    widget1.position.row > w2End.row ||
    w1End.row < widget2.position.row ||
    widget1.position.col > w2End.col ||
    w1End.col < widget2.position.col
  );
};

/**
 * Finds the next available position for a widget in the grid
 */
export const findNextAvailablePosition = (
  widget: WidgetLayout,
  existingWidgets: WidgetLayout[],
  gridConfig: GridConfig = DEFAULT_GRID_CONFIG
): GridPosition => {
  const maxCols = gridConfig.columns;

  for (let row = 0; row < 100; row++) {
    // Reasonable upper limit
    for (let col = 0; col <= maxCols - widget.size.width; col++) {
      const testPosition: GridPosition = { row, col };
      const testWidget: WidgetLayout = {
        ...widget,
        position: testPosition,
      };

      const hasCollision = existingWidgets.some(
        (existingWidget) =>
          existingWidget.id !== widget.id &&
          doWidgetsOverlap(testWidget, existingWidget)
      );

      if (!hasCollision) {
        return testPosition;
      }
    }
  }

  // Fallback to bottom of grid
  return { row: 0, col: 0 };
};

/**
 * Calculates the grid area CSS property for a widget
 */
export const getGridArea = (widget: WidgetLayout): string => {
  const startRow = widget.position.row + 1; // CSS Grid is 1-indexed
  const endRow = startRow + widget.size.height;
  const startCol = widget.position.col + 1;
  const endCol = startCol + widget.size.width;

  return `${startRow} / ${startCol} / ${endRow} / ${endCol}`;
};

/**
 * Converts pixel coordinates to grid position
 */
export const pixelToGridPosition = (
  x: number,
  y: number,
  containerRect: DOMRect,
  gridConfig: GridConfig = DEFAULT_GRID_CONFIG
): GridPosition => {
  const relativeX = x - containerRect.left;
  const relativeY = y - containerRect.top;

  const colWidth = containerRect.width / gridConfig.columns;
  const rowHeight = gridConfig.minRowHeight;

  const col = Math.floor(relativeX / colWidth);
  const row = Math.floor(relativeY / rowHeight);

  return {
    row: Math.max(0, row),
    col: Math.max(0, Math.min(col, gridConfig.columns - 1)),
  };
};

/**
 * Snaps a position to the nearest valid grid position
 */
export const snapToGrid = (
  position: GridPosition,
  widgetSize: WidgetSize,
  gridConfig: GridConfig = DEFAULT_GRID_CONFIG
): GridPosition => {
  const maxCol = gridConfig.columns - widgetSize.width;

  return {
    row: Math.max(0, position.row),
    col: Math.max(0, Math.min(position.col, maxCol)),
  };
};

/**
 * Compacts the layout by moving widgets up to fill empty spaces
 */
export const compactLayout = (widgets: WidgetLayout[]): WidgetLayout[] => {
  const sortedWidgets = [...widgets].sort((a, b) => {
    if (a.position.row !== b.position.row) {
      return a.position.row - b.position.row;
    }
    return a.position.col - b.position.col;
  });

  const compactedWidgets: WidgetLayout[] = [];

  for (const widget of sortedWidgets) {
    let newPosition = { ...widget.position };

    // Try to move the widget up as much as possible
    while (newPosition.row > 0) {
      const testPosition = { ...newPosition, row: newPosition.row - 1 };
      const testWidget = { ...widget, position: testPosition };

      const hasCollision = compactedWidgets.some((existingWidget) =>
        doWidgetsOverlap(testWidget, existingWidget)
      );

      if (hasCollision) {
        break;
      }

      newPosition = testPosition;
    }

    compactedWidgets.push({
      ...widget,
      position: newPosition,
    });
  }

  return compactedWidgets;
};

/**
 * Generates a unique widget ID
 */
export const generateWidgetId = (type: string): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${type}-${timestamp}-${random}`;
};

/**
 * Validates a complete widget layout
 */
export const validateLayout = (
  widgets: WidgetLayout[],
  gridConfig: GridConfig = DEFAULT_GRID_CONFIG
): DragDropError[] => {
  const errors: DragDropError[] = [];

  for (const widget of widgets) {
    // Check widget size constraints
    if (!isValidWidgetSize(widget.size)) {
      errors.push({
        type: "LAYOUT_ERROR",
        message: `Widget ${widget.id} has invalid size`,
        widgetId: widget.id,
      });
    }

    // Check position constraints
    if (!isValidGridPosition(widget.position, gridConfig)) {
      errors.push({
        type: "LAYOUT_ERROR",
        message: `Widget ${widget.id} has invalid position`,
        widgetId: widget.id,
        position: widget.position,
      });
    }

    // Check if widget extends beyond grid
    if (widget.position.col + widget.size.width > gridConfig.columns) {
      errors.push({
        type: "LAYOUT_ERROR",
        message: `Widget ${widget.id} extends beyond grid width`,
        widgetId: widget.id,
        position: widget.position,
      });
    }
  }

  // Check for overlapping widgets
  for (let i = 0; i < widgets.length; i++) {
    for (let j = i + 1; j < widgets.length; j++) {
      const widget1 = widgets[i];
      const widget2 = widgets[j];
      if (widget1 && widget2 && doWidgetsOverlap(widget1, widget2)) {
        errors.push({
          type: "LAYOUT_ERROR",
          message: `Widgets ${widget1.id} and ${widget2.id} overlap`,
          widgetId: widget1.id,
        });
      }
    }
  }

  return errors;
};

/**
 * Collision detection implementation
 */
export const collisionDetection: CollisionDetection = {
  checkCollision: (
    widget: WidgetLayout,
    otherWidgets: WidgetLayout[]
  ): boolean => {
    return otherWidgets.some(
      (otherWidget) =>
        otherWidget.id !== widget.id && doWidgetsOverlap(widget, otherWidget)
    );
  },

  resolveCollision: (
    widget: WidgetLayout,
    otherWidgets: WidgetLayout[]
  ): WidgetLayout[] => {
    const conflictingWidgets = otherWidgets.filter(
      (otherWidget) =>
        otherWidget.id !== widget.id && doWidgetsOverlap(widget, otherWidget)
    );

    if (conflictingWidgets.length === 0) {
      return otherWidgets;
    }

    // Move conflicting widgets down
    const resolvedWidgets = otherWidgets.map((otherWidget) => {
      if (conflictingWidgets.includes(otherWidget)) {
        const newPosition = findNextAvailablePosition(otherWidget, [
          ...otherWidgets.filter((w) => w.id !== otherWidget.id),
          widget,
        ]);
        return { ...otherWidget, position: newPosition };
      }
      return otherWidget;
    });

    return resolvedWidgets;
  },

  findNearestValidPosition: (
    widget: WidgetLayout,
    gridConfig: GridConfig
  ): GridPosition => {
    return findNextAvailablePosition(widget, [], gridConfig);
  },
};

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle function for performance optimization
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};
