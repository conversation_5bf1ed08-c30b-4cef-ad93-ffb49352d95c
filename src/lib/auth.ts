import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import { useUserStore } from "~/store/userStore";
import { SignUpData, VerifyEmailData } from "~/types/auth";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Get CSRF token
          // const csrfResponse = await fetch(
          //   `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
          //   {
          //     method: "GET",
          //   },
          // );
          // const csrfData = (await csrfResponse.json()) as { csrf: string };
          // const csrfToken: string = csrfData.csrf;
          const csrfToken = useUserStore.getState().csrfToken;

          // Perform login
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/api/login/`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                csrf_token: csrfToken,
              },
              body: JSON.stringify({
                email: credentials.email,
                password: credentials.password,
              }),
            },
          );

          const user = (await response.json()) as {
            success: boolean;
            email: string;
            access: string;
            refresh: string;
            isVerified: boolean;
          };

          if (
            response.ok &&
            user &&
            typeof user === "object" &&
            "success" in user &&
            user.success
          ) {
            return {
              id: user.email,
              email: user.email,
              accessToken: user.access,
              refreshToken: user.refresh,
              isVerified: user.isVerified,
            };
          }

          return null;
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      },
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      if (user && "accessToken" in user && "refreshToken" in user) {
        token.accessToken = user.accessToken as string;
        token.refreshToken = user.refreshToken as string;
        if ("isVerified" in user) {
          token.isVerified = user.isVerified as boolean;
        }
      }
      if (account && account.provider === "google" && account.access_token) {
        // You might want to make an API call to your backend to create or update the user
        // and get the access and refresh tokens
        // For now, we'll just add the access_token from Google to the JWT
        token.accessToken = account.access_token;
      }
      return token;
    },
    async session({ session, token }) {
      return {
        ...session,
        accessToken: token.accessToken as string,
        refreshToken: token.refreshToken as string,
        isVerified: token.isVerified as boolean,
      };
    },
  },
  pages: {
    signIn: "/login",
  },
  session: {
    strategy: "jwt",
  },
};

export function setAuthTokens(accessToken: string, refreshToken: string) {
  if (typeof window !== "undefined") {
    sessionStorage.setItem("accessToken", accessToken);
    sessionStorage.setItem("refreshToken", refreshToken);
  }
}

export function getAuthTokens() {
  if (typeof window !== "undefined") {
    return {
      accessToken: sessionStorage.getItem("accessToken"),
      refreshToken: sessionStorage.getItem("refreshToken"),
    };
  }
  return { accessToken: null, refreshToken: null };
}

export function clearAuthTokens() {
  if (typeof window !== "undefined") {
    sessionStorage.removeItem("accessToken");
    sessionStorage.removeItem("refreshToken");
  }
}

export async function signUp(data: SignUpData) {
  const response = await fetch("/api/auth/signup", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error("Signup failed");
  }

  const result = (await response.json()) as {
    success: boolean;
    accessToken?: string;
    refreshToken?: string;
  };
  if (result.success && result.accessToken && result.refreshToken) {
    setAuthTokens(result.accessToken, result.refreshToken);
  }
  return result;
}

export async function verifyEmail(data: VerifyEmailData): Promise<{ success: boolean; message: string }> {
  const { accessToken } = getAuthTokens();
  const response = await fetch("/api/auth/verify", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error("Email verification failed");
  }

  return response.json() as Promise<{ success: boolean; message: string }>;
}
