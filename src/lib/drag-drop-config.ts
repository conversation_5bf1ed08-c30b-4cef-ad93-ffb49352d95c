import {
  GridConfig,
  DragAnimationConfig,
  DropAnimationConfig,
  LayoutPersistenceOptions,
  LazyLoadingConfig,
  VirtualizationConfig,
  ChartPerformanceConfig,
  ResponsiveChartConfig,
} from "@/types";

// Default grid configuration
export const DEFAULT_GRID_CONFIG: GridConfig = {
  columns: 12,
  gap: "1rem",
  breakpoints: {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 6,
  },
  minRowHeight: 200,
};

// Default drag animation settings
export const DEFAULT_DRAG_ANIMATION: DragAnimationConfig = {
  duration: 200,
  easing: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
  scale: 1.05,
  opacity: 0.8,
  zIndex: 1000,
};

// Default drop animation settings
export const DEFAULT_DROP_ANIMATION: DropAnimationConfig = {
  duration: 300,
  easing: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
  highlightColor: "#3b82f6",
  borderWidth: "2px",
};

// Default layout persistence options
export const DEFAULT_PERSISTENCE_OPTIONS: LayoutPersistenceOptions = {
  autoSave: true,
  debounceMs: 1000,
  storageKey: "dashboard-layout",
  enableVersioning: true,
};

// Default lazy loading configuration
export const DEFAULT_LAZY_LOADING: LazyLoadingConfig = {
  enabled: true,
  threshold: 0.1,
  rootMargin: "50px",
  priority: "medium",
};

// Default virtualization configuration
export const DEFAULT_VIRTUALIZATION: VirtualizationConfig = {
  enabled: false, // Disabled by default, enable for large widget lists
  itemHeight: 300,
  overscan: 5,
  windowSize: 10,
};

// Default chart performance configuration
export const DEFAULT_CHART_PERFORMANCE: ChartPerformanceConfig = {
  lazyLoading: true,
  virtualization: false,
  debounceResize: 250,
  throttleAnimation: 16, // 60fps
  maxDataPoints: 1000,
};

// Default responsive chart configuration
export const DEFAULT_RESPONSIVE_CHART: ResponsiveChartConfig = {
  breakpoints: {
    xs: { width: 300, height: 200 },
    sm: { width: 400, height: 250 },
    md: { width: 500, height: 300 },
    lg: { width: 600, height: 350 },
    xl: { width: 800, height: 400 },
  },
  maintainAspectRatio: true,
  minWidth: 250,
  minHeight: 150,
  maxWidth: 1200,
  maxHeight: 800,
};

// Widget size constraints
export const WIDGET_SIZE_CONSTRAINTS = {
  MIN_WIDTH: 1,
  MAX_WIDTH: 12,
  MIN_HEIGHT: 1,
  MAX_HEIGHT: 8,
  DEFAULT_WIDTH: 3,
  DEFAULT_HEIGHT: 2,
};

// Grid position constraints
export const GRID_POSITION_CONSTRAINTS = {
  MIN_ROW: 0,
  MIN_COL: 0,
  MAX_COL: 11, // 0-indexed for 12-column grid
};

// Drag and drop sensor configurations
export const DND_SENSORS_CONFIG = {
  activationConstraint: {
    distance: 8, // Minimum distance to start dragging
  },
  coordinateGetter: {
    // Custom coordinate getter for better touch support
    tolerance: 5,
  },
};

// Widget categories configuration
export const WIDGET_CATEGORIES = {
  ANALYTICS: "analytics",
  DEMOGRAPHICS: "demographics",
  PERFORMANCE: "performance",
  ENGAGEMENT: "engagement",
  CUSTOM: "custom",
} as const;

// Default widget configurations by type
export const DEFAULT_WIDGET_CONFIGS = {
  "gender-demographics": {
    minSize: { width: 2, height: 2 },
    maxSize: { width: 6, height: 4 },
    defaultSize: { width: 3, height: 2 },
    category: WIDGET_CATEGORIES.DEMOGRAPHICS,
  },
  "content-performance": {
    minSize: { width: 3, height: 2 },
    maxSize: { width: 8, height: 5 },
    defaultSize: { width: 4, height: 3 },
    category: WIDGET_CATEGORIES.PERFORMANCE,
  },
  "account-reach": {
    minSize: { width: 3, height: 2 },
    maxSize: { width: 8, height: 5 },
    defaultSize: { width: 4, height: 3 },
    category: WIDGET_CATEGORIES.ANALYTICS,
  },
} as const;

// Error messages
export const ERROR_MESSAGES = {
  DRAG_FAILED: "Failed to move widget. Please try again.",
  DROP_FAILED: "Failed to place widget. Please try again.",
  LAYOUT_SAVE_FAILED: "Failed to save layout. Changes may not persist.",
  LAYOUT_LOAD_FAILED: "Failed to load saved layout. Using default layout.",
  WIDGET_LOAD_FAILED: "Failed to load widget. Please refresh the page.",
  COLLISION_DETECTED: "Cannot place widget here due to overlap.",
  INVALID_POSITION: "Invalid position. Widget moved to nearest valid location.",
} as const;

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  MAX_WIDGETS: 50,
  MAX_HISTORY_SIZE: 20,
  DEBOUNCE_SAVE_MS: 1000,
  THROTTLE_DRAG_MS: 16,
  LAZY_LOAD_THRESHOLD: 0.1,
} as const;
