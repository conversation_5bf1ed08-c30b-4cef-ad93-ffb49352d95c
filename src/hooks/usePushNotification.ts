import { useState, useEffect } from 'react';

const isPWA = () => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(display-mode: standalone)').matches || 
         (window.navigator as any).standalone || // iOS
         document.referrer.includes('android-app://'); // Android
};

const urlBase64ToUint8Array = (base64String: string): Uint8Array => {
  try {
    const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    console.log('Processing VAPID key:', {
      original: base64String,
      padded: base64String + padding,
      final: base64
    });

    // Basic sanity checks: VAPID public keys are typically ~43-88 URL-safe base64 chars
    // If we get something way larger, fail fast to avoid massive allocations
    if (base64String.length === 0) {
      throw new Error('Empty VAPID key');
    }
    if (base64String.length > 256) {
      throw new Error(`VAPID key appears too long (${base64String.length}). Check NEXT_PUBLIC_VAPID_PUBLIC_KEY.`);
    }
    // Ensure only URL-safe base64 characters are present
    if (!/^[A-Za-z0-9\-_]+=*$/.test(base64String)) {
      throw new Error('VAPID key contains invalid characters. It must be URL-safe base64.');
    }

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }

    console.log('Converted VAPID key to Uint8Array:', outputArray);
    return outputArray;
  } catch (error) {
    console.error('Error converting VAPID key:', error);
    throw error;
  }
};

export const usePushNotification = () => {
  const [subscription, setSubscription] = useState<PushSubscription | null>(null);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [error, setError] = useState<string>('');
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [isPWAMode, setIsPWAMode] = useState(false);

  // Your VAPID public key from your backend
  const publicVapidKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY;

  useEffect(() => {
    if ('Notification' in window) {
      setPermission(Notification.permission);
    }
    setIsPWAMode(isPWA());

    // Listen for display mode changes
    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    const handleDisplayModeChange = (e: MediaQueryListEvent) => {
      setIsPWAMode(e.matches);
    };
    mediaQuery.addEventListener('change', handleDisplayModeChange);

    return () => {
      mediaQuery.removeEventListener('change', handleDisplayModeChange);
    };
  }, []);

  const subscribe = async (reg: ServiceWorkerRegistration) => {
    try {
      if (!publicVapidKey) {
        throw new Error('VAPID public key is not configured');
      }

      console.log('Subscribing to push notifications...');
      console.log('Using VAPID key:', publicVapidKey);

      const applicationServerKey = urlBase64ToUint8Array(publicVapidKey);
      
      const sub = await reg.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey
      });

      console.log('Push notification subscription successful:', sub);
      setSubscription(sub);

      // Send the subscription to your backend
      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sub),
      });

      const data = await response.json();
      if (!data.success) {
        console.error('Failed to save subscription on server:', data);
      }

      return sub;
    } catch (err) {
      if (err instanceof Error) {
        console.error('Failed to subscribe to push notifications:', err);
        setError('Failed to subscribe: ' + err.message);
      }
      throw err;
    }
  };

  useEffect(() => {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      console.log('Push notifications not supported');
      return;
    }

    if (!publicVapidKey) {
      console.error('VAPID public key is not configured');
      return;
    }

    // Service workers disabled globally; skip registration for push notifications
    return;

    const setupServiceWorker = async () => {
      try {
        console.log('Setting up service worker...');
        
        // First, register the service worker
        const swRegistration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service worker registered:', swRegistration);

        // Wait for the service worker to be ready
        const reg = await navigator.serviceWorker.ready;
        console.log('Service worker ready:', reg);
        setRegistration(reg);

        // Check for existing subscription
        const existingSub = await reg.pushManager.getSubscription();
        if (existingSub) {
          console.log('Found existing push subscription:', existingSub);
          setSubscription(existingSub);
        } else if (permission === 'granted') {
          // If permission is granted but no subscription exists, create one
          console.log('Permission granted but no subscription found. Creating new subscription...');
          await subscribe(reg);
        }
      } catch (err) {
        console.error('Error setting up push notifications:', err);
        setError('Failed to initialize push notification: ' + (err instanceof Error ? err.message : String(err)));
      }
    };

    // Only set up the service worker if we don't already have a registration
    if (!registration) {
      setupServiceWorker();
    }
  }, [permission, publicVapidKey, registration]);

  const unsubscribe = async () => {
    try {
      if (!subscription) {
        throw new Error('No subscription to unsubscribe from');
      }

      await subscription.unsubscribe();
      setSubscription(null);

      // Notify your backend about the unsubscription
      const response = await fetch('/api/notifications/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription),
      });

      const data = await response.json();
      if (!data.success) {
        console.error('Failed to remove subscription on server:', data);
      }
    } catch (err) {
      if (err instanceof Error) {
        console.error('Failed to unsubscribe:', err);
        setError('Failed to unsubscribe: ' + err.message);
      }
      throw err;
    }
  };

  const requestPermission = async () => {
    if (!('Notification' in window)) {
      setError('This browser does not support notifications');
      return false;
    }

    if (!publicVapidKey) {
      setError('Push notifications are not configured');
      return false;
    }

    try {
      console.log('Requesting notification permission...');
      const result = await Notification.requestPermission();
      console.log('Permission request result:', result);
      setPermission(result);
      
      if (result === 'granted' && registration) {
        await subscribe(registration);
        return true;
      }
      
      return false;
    } catch (err) {
      console.error('Error requesting permission:', err);
      if (err instanceof Error) {
        setError('Failed to request permission: ' + err.message);
      }
      return false;
    }
  };

  return {
    subscription,
    permission,
    error,
    isPWAMode,
    subscribe: registration ? () => subscribe(registration) : undefined,
    unsubscribe,
    requestPermission,
  };
}; 