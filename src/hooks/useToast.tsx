"use client";

import toast from 'react-hot-toast';
import { useEnhancedToast } from '~/components/enhanced-toast-utils';

export function useToast() {
  const enhancedToast = useEnhancedToast();

  const showToast = (
    message: string,
    type: 'success' | 'error' | 'info' | 'warning' = 'info',
    subMessage?: string
  ) => {
    switch (type) {
      case 'success':
        return enhancedToast.showSuccess(message, subMessage);
      case 'error':
        return enhancedToast.showError(message, subMessage);
      case 'warning':
        return enhancedToast.showWarning(message, subMessage);
      case 'info':
      default:
        return enhancedToast.showInfo(message, subMessage);
    }
  };

  return {
    showToast,
    showSuccess: enhancedToast.showSuccess,
    showError: enhancedToast.showError,
    showWarning: enhancedToast.showWarning,
    showInfo: enhancedToast.showInfo,
    dismiss: enhancedToast.dismiss,
    remove: enhancedToast.remove
  };
}
