"use client";

export function useLogger() {
  const log = (message: string, data?: any) => {
    console.log(`[Logger] ${message}`, data || '');
  };

  const error = (message: string, error?: any) => {
    console.error(`[Logger] ${message}`, error || '');
  };

  const warn = (message: string, data?: any) => {
    console.warn(`[Logger] ${message}`, data || '');
  };

  const info = (message: string, data?: any) => {
    console.info(`[Logger] ${message}`, data || '');
  };

  return {
    log,
    error,
    warn,
    info
  };
}
