/* eslint-disable */
import { createServer } from "https";
import { parse } from "url";
import next from "next";
import fs from "fs";
import path from "path";

const dev = false; // Development mode
const app = next({ dev });
const handle = app.getRequestHandler();

// SSL certificate paths
const httpsOptions = {
  key: fs.readFileSync("/etc/letsencrypt/live/businessinsight.ai/privkey.pem"),
  cert: fs.readFileSync("/etc/letsencrypt/live/businessinsight.ai/fullchain.pem"),
};

app.prepare().then(() => {
  createServer(httpsOptions, (req, res) => {
    // Add error handling for undefined URLs
    const parsedUrl = parse(req.url || '', true);
    handle(req, res, parsedUrl);
  }).listen(8443, (err) => {
    if (err) throw err;
    console.log(
      "> Ready on https://businessinsight.ai",
    );
  });
});
