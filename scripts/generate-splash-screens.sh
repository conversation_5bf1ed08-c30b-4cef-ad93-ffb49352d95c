#!/bin/bash

# Create splash screen directory if it doesn't exist
mkdir -p public/splash

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "Error: ImageMagick is not installed"
    echo "Please install ImageMagick first:"
    echo "On Ubuntu/Debian: sudo apt-get install imagemagick"
    echo "On MacOS: brew install imagemagick"
    echo "On Windows: https://imagemagick.org/script/download.php"
    exit 1
fi

# Base logo path - using the PWA icon since it's guaranteed to exist
LOGO="public/pwa/ios/192.png"

# Check if logo exists
if [ ! -f "$LOGO" ]; then
    echo "Error: Logo file not found at $LOGO"
    exit 1
fi

# Function to generate splash screen
generate_splash() {
    local size="$1"
    local output="$2"
    local bgcolor="#2c3e50"
    local logo_size="96x96"  # Size of the logo on splash screen
    
    echo "Generating $output with size $size..."
    
    convert -size "$size" xc:"$bgcolor" \
        \( "$LOGO" -resize "$logo_size" \) -gravity center -composite \
        "public/splash/$output"
    
    if [ $? -eq 0 ]; then
        echo "✓ Successfully generated $output"
    else
        echo "✗ Failed to generate $output"
    fi
}

echo "Starting splash screen generation..."

# iPhone 5, SE (1st generation)
generate_splash "640x1136" "iphone5.png"

# iPhone 6, 6s, 7, 8
generate_splash "750x1334" "iphone6.png"

# iPhone 6+, 7+, 8+
generate_splash "1242x2208" "iphoneplus.png"

# iPhone X, Xs, 11 Pro
generate_splash "1125x2436" "iphonex.png"

# iPad
generate_splash "1536x2048" "ipad.png"

# Generate the main splash screen images referenced in manifest.json
generate_splash "640x1136" "splash-640x1136.png"
generate_splash "640x1136" "splash-dark-640x1136.png"

echo "Splash screen generation complete!" 