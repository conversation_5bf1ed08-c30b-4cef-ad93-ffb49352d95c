{"name": "Business-Insight-AI", "version": "0.1.15", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev --port 3000 --turbopack", "dev2": "node dev.js", "develop": " next dev --port 3000", "lint": "next lint", "start": "next start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --maxWorkers=2"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/react-fontawesome": "^3.0.0", "@gsap/react": "^2.1.2", "@headlessui/react": "^2.2.7", "@hookform/resolvers": "^5.2.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.14.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-icons": "^1.3.2", "@t3-oss/env-nextjs": "^0.13.8", "@types/react-beautiful-dnd": "^13.1.8", "@types/recordrtc": "^5.6.14", "@types/uuid": "^10.0.0", "@types/wavesurfer.js": "^6.0.12", "@wojtekmaj/react-timerange-picker": "^7.0.0", "antd": "^5.27.1", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "bcrypt": "^6.0.0", "case-sensitive-paths-webpack-plugin": "^2.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "devtools-detect": "^4.0.2", "devtools-detector": "^2.0.23", "eslint-config-next": "15.5.0", "faye-websocket": "^0.11.4", "framer-motion": "^12.23.12", "geist": "^1.4.2", "lucide-react": "^0.541.0", "motion": "^12.23.12", "net": "^1.0.2", "next": "15.5.0", "next-auth": "^4.24.11", "postcss": "^8.5.6", "prisma": "^6.14.0", "radix-ui": "^1.4.3", "react": "^19.1.1", "react-big-calendar": "^1.19.4", "react-confetti": "^6.4.0", "react-cookie": "^8.0.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.1", "react-grid-layout": "^1.5.2", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "react-icons": "^5.5.0", "react-is": "^19.1.1", "react-masonry-css": "^1.0.16", "react-player": "^3.3.1", "react-router-dom": "^7.8.2", "react-scroll": "^1.9.3", "react-time-picker": "^8.0.2", "react-use": "^17.6.0", "recharts": "^3.1.2", "recordrtc": "^5.6.2", "swapy": "^1.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "url": "^0.11.4", "uuid": "^11.1.0", "wave-recorder": "^0.0.0", "wavesurfer.js": "^7.10.1", "web-push": "^3.6.7", "yallist": "^5.0.0", "zod": "^4.1.1", "zustand": "^5.0.8"}, "devDependencies": {"@babel/core": "^7.28.3", "@babel/preset-env": "^7.28.3", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@tailwindcss/postcss": "^4.1.12", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/react-big-calendar": "^1.16.2", "@types/web-push": "^3.6.4", "babel-jest": "^30.0.5", "eslint": "^9.34.0", "eslint-plugin-react-compiler": "^19.1.0-rc.2", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "msw": "^2.10.5", "ts-jest": "^29.4.1", "typescript": "5.9.2"}, "ct3aMetadata": {"initVersion": "7.37.0"}, "packageManager": "npm@11.5.2", "overrides": {"recharts": {"react": "$react", "react-dom": "$react-dom"}}}