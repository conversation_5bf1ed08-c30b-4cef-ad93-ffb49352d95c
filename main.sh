#!/bin/bash

# Change to the src directory
cd src

# Make all files in components folder lowercase
if [ -d "components" ]; then
    cd components
    for file in *; do
        if [ -f "$file" ]; then
            lowercase_name=$(echo "$file" | tr '[:upper:]' '[:lower:]')
            if [ "$file" != "$lowercase_name" ]; then
                mv "$file" "$lowercase_name"
                echo "Renamed $file to $lowercase_name"
            fi
        fi
    done
    cd ..
fi

# this is a test

# Return to the original directory
cd ..

echo "File renaming and import updates complete."
